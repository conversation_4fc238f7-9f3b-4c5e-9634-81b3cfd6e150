// Response type for API calls
export interface ApiResponse<T> {
  code: string;
  data: T;
  msg: string;
}

// User type
export interface User {
  id: string;
  username: string;
  token: string;
  role: string;
}

// Menu item type
export interface MenuItem {
  id: string;
  name: string;
  path: string;
  type: 'directory' | 'page';
  icon?: string;
  children?: MenuItem[];
}

// Login form data
export interface LoginFormData {
  username: string;
  password: string;
}

// Dictionary item type
export interface DictionaryItem {
  label: string;
  value: string;
}

// System dictionary types
export type ModelType = DictionaryItem[];
export type ModelStatus = DictionaryItem[];
export type DatabaseType = DictionaryItem[];
export type DatabaseStatus = DictionaryItem[];
export type MCPStatus = DictionaryItem[];
export type ToolType = DictionaryItem[];
export type ToolStatus = DictionaryItem[];
export type FlowType = DictionaryItem[];
export type FlowStatus = DictionaryItem[];
export type RAGType = DictionaryItem[];
export type RAGStatus = DictionaryItem[];
export type AgentType = DictionaryItem[];
export type AgentStatus = DictionaryItem[];
export type DeviceType = DictionaryItem[];
export type DeviceStatus = DictionaryItem[];

// System dictionary cache
export interface SystemDictionary {
  modelTypes: ModelType;
  modelStatuses: ModelStatus;
  databaseTypes: DatabaseType;
  databaseStatuses: DatabaseStatus;
  mcpStatuses: MCPStatus;
  toolTypes: ToolType;
  toolStatuses: ToolStatus;
  flowTypes: FlowType;
  flowStatuses: FlowStatus;
  ragTypes: RAGType;
  ragStatuses: RAGStatus;
  agentTypes: AgentType;
  agentStatuses: AgentStatus;
  deviceTypes: DeviceType;
  deviceStatuses: DeviceStatus;
}
