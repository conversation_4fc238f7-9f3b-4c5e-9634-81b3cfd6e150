import React from 'react';
import { useLocation } from 'react-router-dom';

const EmptyPage: React.FC = () => {
  const location = useLocation();
  
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Page Content</h2>
        <p className="text-gray-500">
          This is a placeholder for the content of <span className="font-medium">{location.pathname}</span>
        </p>
      </div>
    </div>
  );
};

export default EmptyPage;
