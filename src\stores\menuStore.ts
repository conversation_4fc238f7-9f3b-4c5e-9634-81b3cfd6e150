import { create } from 'zustand';
import type { MenuItem } from '@/types';

interface MenuState {
  menuItems: MenuItem[];
  activeMenuItem: string | null;
  setMenuItems: (items: MenuItem[]) => void;
  setActiveMenuItem: (id: string) => void;
}

export const useMenuStore = create<MenuState>((set) => ({
  menuItems: [],
  activeMenuItem: null,
  setMenuItems: (items: MenuItem[]) => set({ menuItems: items }),
  setActiveMenuItem: (id: string) => set({ activeMenuItem: id }),
}));
