Data Grid \- Sorting
====================

Easily sort your rows based on one or several criteria.


Tailor workflows, harness data and tools to plan, execute, and release high\-impact products, fast.

ads via Carbon



Sorting is enabled by default to the Data Grid users and works out of the box without any explicit configuration.
Users can set a sorting rule simply by clicking on a column header.
Following clicks change the column's sorting direction. You can see the applied direction on the header's arrow indicator.


NameRatingCountryCreated onIs admin?<PERSON> 3Russian Federation2024/7/25Olga Paul 4India2024/6/16<PERSON><PERSON><PERSON> 3<PERSON><PERSON> and Principe2025/5/<PERSON><PERSON><PERSON> 1Malta2024/6/23Sean Stevenson 1Belarus2024/8/7Ann Wilkerson 3Macao2024/9/21Blanche Moore 4Dominican Republic2025/4/23Marion McCoy 4Guinea2024/10/5Kenneth Walters 3Saint Lucia2024/6/21Vera Lyons 2United Republic of Tanzania2025/2/8E<PERSON><PERSON> 1Singapore2025/2/13Abbie Colon 4Canada2024/10/19Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function BasicExampleDataGrid {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} loading={loading} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function BasicExampleDataGrid {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} loading\={loading} /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- From planning to production, GitLab brings teams together. Try it our for free.ad by CarbonSingle and multi\-sorting
-------------------------


The Data Grid can only sort the rows according to one criterion at a time.


To use multi\-sorting, you need to upgrade to Pro plan or above.


Multi\-sorting
--------------

The following demo lets you sort the rows according to several criteria at the same time.


Hold down the `Ctrl` or `Shift` (use `⌘ Command` on macOS) key while clicking the column header.


NameRatingCountryCreated onIs admin?Abbie Carter 2Brazil2024/9/23Chad Dunn 5Sint Maarten (Dutch part)2024/11/26Cornelia Rodgers 3Myanmar2024/10/10Bernice Dean 1Cambodia2024/7/9Virginia Ruiz 3Marshall Islands2025/1/4Maggie Parsons 3Poland2025/1/19Logan Hansen 5Maldives2025/2/9Donald Hawkins 2Vanuatu2025/4/25Melvin Green 2Vietnam2024/10/26Total Rows: 100JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function BasicExampleDataGridPro {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro {...data} loading={loading} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function BasicExampleDataGridPro {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro {...data} loading\={loading} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonPass sorting rules to the Data Grid
-----------------------------------

### Structure of the model

The sort model is a list of sorting items.
Each item represents a sorting rule and is composed of several elements:


* `sortingItem.field`: the field on which the rule applies.
* `sortingItem.sort`: the direction of the sorting (`'asc'`, `'desc'`, `null` or `undefined`). If `null` or `undefined`, the rule doesn't apply.


### Initialize the sort model

Sorting is enabled by default to the user.
But if you want to set an initial sorting order, simply provide the model to the `initialState` prop.



```
<DataGrid
  initialState={{
    sorting: {
      sortModel: [{ field: 'rating', sort: 'desc' }],
    },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
NameRatingCountryCreated onIs admin?Augusta Drake 5Puerto Rico2024/10/5Beulah Hardy 5Russian Federation2024/12/21Verna Phelps 5Guyana2025/4/28Betty Patterson 5Isle of Man2024/7/28Roy Welch 5Guinea\-Bissau2025/5/18Mabel Ballard 5Somalia2024/10/9Susie Carlson 5Serbia2024/8/17Pearl Bryant 5Sri Lanka2025/2/16Ricardo Garza 5Somalia2025/3/28Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function InitialSort {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        initialState={{
          ...data.initialState,
          sorting: {
            ...data.initialState?.sorting,
            sortModel: [
              {
                field: 'rating',
                sort: 'desc',
              },
            ],
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function InitialSort {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 initialState\={{
 ...data.initialState,
 sorting: {
 ...data.initialState?.sorting,
 sortModel: \[
 {
 field: 'rating',
 sort: 'desc',
 },
 ],
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by Carbon### Controlled sort model

Use the `sortModel` prop to control the state of the sorting rules.


You can use the `onSortModelChange` prop to listen to changes in the sorting rules and update the prop accordingly.


NameRatingCountryCreated onIs admin?Stella Maxwell 5British Indian Ocean Territory2025/1/2Jerry Underwood 5Argentina2024/6/6Clifford Leonard 5Samoa2024/11/3Susie Porter 5Saint Kitts and Nevis2024/12/15Helen Sandoval 5Uzbekistan2024/10/19Marie Malone 5Syrian Arab Republic2025/2/9Maurice Ramsey 5Lesotho2024/9/17Ray Dawson 5Chad2025/4/29Emma Obrien 5Anguilla2025/2/5Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridSortModel } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function ControlledSort {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  const [sortModel, setSortModel] = React.useState<GridSortModel>([
    {
      field: 'rating',
      sort: 'desc',
    },
  ]);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        sortModel={sortModel}
        onSortModelChange={(newSortModel) => setSortModel(newSortModel)}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridSortModel } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function ControlledSort {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 const \[sortModel, setSortModel] \= React.useState\<GridSortModel\>(\[
 {
 field: 'rating',
 sort: 'desc',
 },
 ]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 sortModel\={sortModel}
 onSortModelChange\={(newSortModel) \=\> setSortModel(newSortModel)}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by CarbonDisable the sorting
-------------------

### For all columns

Sorting is enabled by default, but you can easily disable this feature by setting the `disableColumnSorting` prop.



```
<DataGrid disableColumnSorting />

```
CopyCopied(or Ctrl \+ C)
NameRatingCountryCreated onIs admin?Alvin Higgins 4Niger2024/7/20Nelle Franklin 4Montserrat2024/7/9Connor Stanley 5Gambia2024/6/21Howard Clarke 3Palau2025/1/5Mayme Christensen 1Sri Lanka2024/7/5Ann Bradley 1Puerto Rico2025/3/27Iva Cannon 4Czech Republic2025/5/28Curtis Gordon 2Iran, Islamic Republic of2024/6/16Timothy Thompson 2Cape Verde2024/7/7Marie Allen 1Seychelles2024/7/16Ada Curtis 2Guam2025/2/3Adelaide McGee 3Lao People's Democratic Republic2025/1/14Jayden Harper 4Equatorial Guinea2024/10/2Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function DisableSortingGridAllColumns {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} loading={loading} disableColumnSorting />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function DisableSortingGridAllColumns {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} loading\={loading} disableColumnSorting /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon### For some columns

By default, all columns are sortable.
To disable sorting on a column, set the `sortable` property of `GridColDef` to `false`.
In the following demo, the user cannot sort the *rating* column from the UI.



```
<DataGrid columns={[...columns, { field: 'rating', sortable: false }]} />

```
CopyCopied(or Ctrl \+ C)
NameRatingCountryCreated onIs admin?Susan Simpson 2Estonia2025/5/16Michael Underwood 3Fiji2024/9/1Della Daniels 3Bolivia2024/6/30Viola Garza 3Morocco2024/9/29Amy Stevenson 5Zambia2024/12/8Glenn Shaw 3Sao Tome and Principe2024/10/13Ian Welch 3Gibraltar2024/10/23Alejandro Collins 1Chad2024/12/20Lillie Lloyd 2Dominican Republic2024/11/25Lucile Frazier 3Bouvet Island2025/1/6Russell Miles 5Guyana2024/7/26Sally Underwood 4Bolivia2024/7/31Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function DisableSortingGrid {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  const columns = React.useMemo(
     =>
      data.columns.map((col) =>
        col.field === 'rating' ? { ...col, sortable: false } : col,
      ),
    [data.columns],
  );
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} loading={loading} columns={columns} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function DisableSortingGrid {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 const columns \= React.useMemo(
  \=\>
 data.columns.map((col) \=\>
 col.field \=\=\= 'rating' ? { ...col, sortable: false } : col,
 ),
 \[data.columns],
 );
 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} loading\={loading} columns\={columns} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by Carbon### Sorting non\-sortable columns programmatically

The columns with `colDef.sortable` set to `false` are not sortable from the grid UI but could still be sorted programmatically. To add a sort rule to such a column, you could initialize the `sortModel`, use the `sortModel` prop, or use the API methods `sortColumn` or `setSortModel`.


In the following demo, the `firstName` column is not sortable by the default grid UI, but it is sorted programmatically by a custom built UI.


AscendingDescendingNoneIDFirst nameLast nameAge6Melisandre1504AryaStark112CerseiLannister315DaenerysTargaryen7FerraraClifford449HarveyRoxie653JaimeLannister311JonSnow14Rows per page:

1001–9 of 9

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridColDef,
  Toolbar,
  ToolbarButton,
  useGridApiContext,
} from '@mui/x-data-grid';
import Button from '@mui/material/Button';

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 90 },
  {
    field: 'firstName',
    headerName: 'First name',
    width: 150,
    editable: true,
    filterable: false,
    sortable: false,
  },
  {
    field: 'lastName',
    headerName: 'Last name',
    width: 150,
  },
  {
    field: 'age',
    headerName: 'Age',
    type: 'number',
    width: 110,
  },
];

const rows = [
  { id: 1, lastName: 'Snow', firstName: 'Jon', age: 14 },
  { id: 2, lastName: 'Lannister', firstName: 'Cersei', age: 31 },
  { id: 3, lastName: 'Lannister', firstName: 'Jaime', age: 31 },
  { id: 4, lastName: 'Stark', firstName: 'Arya', age: 11 },
  { id: 5, lastName: 'Targaryen', firstName: 'Daenerys', age: null },
  { id: 6, lastName: 'Melisandre', firstName: null, age: 150 },
  { id: 7, lastName: 'Clifford', firstName: 'Ferrara', age: 44 },
  { id: 8, lastName: 'Frances', firstName: 'Rossini', age: 36 },
  { id: 9, lastName: 'Roxie', firstName: 'Harvey', age: 65 },
];

function CustomToolbar {
  const apiRef = useGridApiContext;

  return (
    <Toolbar>
      <ToolbarButton
        onClick={ => apiRef.current?.sortColumn('firstName', 'asc')}
        render={<Button />}
      >
        Ascending
      </ToolbarButton>
      <ToolbarButton
        onClick={ => apiRef.current?.sortColumn('firstName', 'desc')}
        render={<Button />}
      >
        Descending
      </ToolbarButton>
      <ToolbarButton
        onClick={ => apiRef.current?.sortColumn('firstName', null)}
        render={<Button />}
      >
        None
      </ToolbarButton>
    </Toolbar>
  );
}

export default function ReadOnlySortingGrid {
  return (
    <div style={{ width: '100%', height: 400 }}>
      <DataGrid
        rows={rows}
        columns={columns}
        initialState={{
          sorting: {
            sortModel: [{ field: 'firstName', sort: 'asc' }],
          },
        }}
        slots={{
          toolbar: CustomToolbar,
        }}
        showToolbar
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGrid,
 GridColDef,
 Toolbar,
 ToolbarButton,
 useGridApiContext,
} from '@mui/x\-data\-grid';
import Button from '@mui/material/Button';

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'ID', width: 90 },
 {
 field: 'firstName',
 headerName: 'First name',
 width: 150,
 editable: true,
 filterable: false,
 sortable: false,
 },
 {
 field: 'lastName',
 headerName: 'Last name',
 width: 150,
 },
 {
 field: 'age',
 headerName: 'Age',
 type: 'number',
 width: 110,
 },
];

const rows \= \[
 { id: 1, lastName: 'Snow', firstName: 'Jon', age: 14 },
 { id: 2, lastName: 'Lannister', firstName: 'Cersei', age: 31 },
 { id: 3, lastName: 'Lannister', firstName: 'Jaime', age: 31 },
 { id: 4, lastName: 'Stark', firstName: 'Arya', age: 11 },
 { id: 5, lastName: 'Targaryen', firstName: 'Daenerys', age: null },
 { id: 6, lastName: 'Melisandre', firstName: null, age: 150 },
 { id: 7, lastName: 'Clifford', firstName: 'Ferrara', age: 44 },
 { id: 8, lastName: 'Frances', firstName: 'Rossini', age: 36 },
 { id: 9, lastName: 'Roxie', firstName: 'Harvey', age: 65 },
];

function CustomToolbar {
 const apiRef \= useGridApiContext;

 return (
 \<Toolbar\>
 \<ToolbarButton
 onClick\={ \=\> apiRef.current?.sortColumn('firstName', 'asc')}
 render\={\<Button /\>}
 \>
 Ascending
 \</ToolbarButton\>
 \<ToolbarButton
 onClick\={ \=\> apiRef.current?.sortColumn('firstName', 'desc')}
 render\={\<Button /\>}
 \>
 Descending
 \</ToolbarButton\>
 \<ToolbarButton
 onClick\={ \=\> apiRef.current?.sortColumn('firstName', null)}
 render\={\<Button /\>}
 \>
 None
 \</ToolbarButton\>
 \</Toolbar\>
 );
}

export default function ReadOnlySortingGrid {
 return (
 \<div style\={{ width: '100%', height: 400 }}\>
 \<DataGrid
 rows\={rows}
 columns\={columns}
 initialState\={{
 sorting: {
 sortModel: \[{ field: 'firstName', sort: 'asc' }],
 },
 }}
 slots\={{
 toolbar: CustomToolbar,
 }}
 showToolbar
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Boost your app’s Identity with Auth0\. Now with Custom Domain, Passwordless, and more!ad by CarbonCustom comparator
-----------------

A comparator determines how two cell values should be sorted.


Each column type comes with a default comparator method.
You can re\-use them by importing the following functions:


* `gridStringOrNumberComparator` (used by the `string` and `singleSelect` columns)
* `gridNumberComparator` (used by the `number` and `boolean` columns)
* `gridDateComparator` (used by the `date` and `date-time` columns)


To extend or modify this behavior in a specific column, you can pass in a custom comparator, and override the `sortComparator` property of the `GridColDef` interface.


### Create a comparator from scratch

In the following demo, the "Created on" column sorting is based on the day of the month of the `createdOn` field.
It is a fully custom sorting comparator.


Created onNameRatingCountryIs admin?2024/6/2Harriet Garza 2Germany2024/10/2Arthur Berry 2Marshall Islands2025/2/2Jeanette Maxwell 2Bhutan2025/1/2Callie Edwards 5US Virgin Islands2024/10/2Lida Warren 3Hungary2024/12/3Mildred Blair 4British Virgin Islands2024/6/3Lela Barker 3Romania2025/4/3Gilbert Ortega 4Zimbabwe2025/4/3Phillip Chambers 3ColombiaRows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { GridColDef, DataGrid, GridComparatorFn } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'isAdmin'];

const dayInMonthComparator: GridComparatorFn<Date> = (v1, v2) =>
  v1.getDate - v2.getDate;

export default function FullyCustomSortComparator {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  const columns = React.useMemo<GridColDef[]>(
     => [
      {
        field: 'dateCreatedCustom',
        valueGetter: (value, row) => row.dateCreated,
        headerName: 'Created on',
        width: 180,
        type: 'date',
        sortComparator: dayInMonthComparator,
      },
      ...data.columns,
    ],
    [data.columns],
  );

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        columns={columns}
        initialState={{
          ...data.initialState,
          sorting: {
            ...data.initialState?.sorting,
            sortModel: [
              {
                field: 'dateCreatedCustom',
                sort: 'asc',
              },
            ],
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { GridColDef, DataGrid, GridComparatorFn } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'isAdmin'];

const dayInMonthComparator: GridComparatorFn\<Date\> \= (v1, v2\) \=\>
 v1\.getDate \- v2\.getDate;

export default function FullyCustomSortComparator {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 const columns \= React.useMemo\<GridColDef\[]\>(
  \=\> \[
 {
 field: 'dateCreatedCustom',
 valueGetter: (value, row) \=\> row.dateCreated,
 headerName: 'Created on',
 width: 180,
 type: 'date',
 sortComparator: dayInMonthComparator,
 },
 ...data.columns,
 ],
 \[data.columns],
 );

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 columns\={columns}
 initialState\={{
 ...data.initialState,
 sorting: {
 ...data.initialState?.sorting,
 sortModel: \[
 {
 field: 'dateCreatedCustom',
 sort: 'asc',
 },
 ],
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Combine built\-in comparators

In the following demo, the "Name" column combines the `name` and `isAdmin` fields.
The sorting is based on `isAdmin` and then on `name`, if necessary. It re\-uses the built\-in sorting comparator.


NameRatingCountryCreated onAiden Bailey 4Nicaragua2024/8/1Alice Fitzgerald 1Tonga2025/1/9Bruce Adams 1Latvia2025/2/24Carlos Robbins 5Belgium2025/3/31Cecilia Jennings 2Togo2025/1/5Charles Lynch 3Netherlands2024/11/12Chase Romero 3Northern Mariana Islands2024/11/13Clyde Fowler 4Gabon2024/8/6Cody Thomas 1Faroe Islands2024/7/18Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  GridColDef,
  DataGrid,
  gridNumberComparator,
  gridStringOrNumberComparator,
  GridComparatorFn,
} from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['rating', 'country', 'dateCreated'];

interface NameAdminCellValue {
  name: string;
  isAdmin: boolean;
}

const nameAdminSortComparator: GridComparatorFn = (v1, v2, param1, param2) => {
  const adminComparatorResult = gridNumberComparator(
    (v1 as NameAdminCellValue).isAdmin,
    (v2 as NameAdminCellValue).isAdmin,
    param1,
    param2,
  );

  // The `isAdmin` values of the two cells are different
  // We can stop here and sort based on the `isAdmin` field.
  if (adminComparatorResult !== 0) {
    return adminComparatorResult;
  }

  return gridStringOrNumberComparator(
    (v1 as NameAdminCellValue).name,
    (v2 as NameAdminCellValue).name,
    param1,
    param2,
  );
};

export default function ExtendedSortComparator {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  const columns = React.useMemo<GridColDef[]>(
     => [
      {
        field: 'nameAdmin',
        headerName: 'Name',
        valueGetter: (value, row) => ({
          name: row.name,
          isAdmin: row.isAdmin,
        }),
        valueFormatter: (value: NameAdminCellValue) => {
          if (value.isAdmin) {
            return `${value.name} (admin)`;
          }

          return value.name;
        },
        sortComparator: nameAdminSortComparator,
        width: 200,
      },
      ...data.columns,
    ],
    [data.columns],
  );

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        columns={columns}
        initialState={{
          ...data.initialState,
          sorting: {
            ...data.initialState?.sorting,
            sortModel: [
              {
                field: 'nameAdmin',
                sort: 'asc',
              },
            ],
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 GridColDef,
 DataGrid,
 gridNumberComparator,
 gridStringOrNumberComparator,
 GridComparatorFn,
} from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['rating', 'country', 'dateCreated'];

interface NameAdminCellValue {
 name: string;
 isAdmin: boolean;
}

const nameAdminSortComparator: GridComparatorFn \= (v1, v2, param1, param2\) \=\> {
 const adminComparatorResult \= gridNumberComparator(
 (v1 as NameAdminCellValue).isAdmin,
 (v2 as NameAdminCellValue).isAdmin,
 param1,
 param2,
 );

 // The \`isAdmin\` values of the two cells are different
 // We can stop here and sort based on the \`isAdmin\` field.
 if (adminComparatorResult !\=\= 0\) {
 return adminComparatorResult;
 }

 return gridStringOrNumberComparator(
 (v1 as NameAdminCellValue).name,
 (v2 as NameAdminCellValue).name,
 param1,
 param2,
 );
};

export default function ExtendedSortComparator {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 const columns \= React.useMemo\<GridColDef\[]\>(
  \=\> \[
 {
 field: 'nameAdmin',
 headerName: 'Name',
 valueGetter: (value, row) \=\> ({
 name: row.name,
 isAdmin: row.isAdmin,
 }),
 valueFormatter: (value: NameAdminCellValue) \=\> {
 if (value.isAdmin) {
 return \`${value.name} (admin)\`;
 }

 return value.name;
 },
 sortComparator: nameAdminSortComparator,
 width: 200,
 },
 ...data.columns,
 ],
 \[data.columns],
 );

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 columns\={columns}
 initialState\={{
 ...data.initialState,
 sorting: {
 ...data.initialState?.sorting,
 sortModel: \[
 {
 field: 'nameAdmin',
 sort: 'asc',
 },
 ],
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**GetStream.io** \- Built by devs, for devs. Start Coding FREE. No CC requiredad by Carbon### Asymmetric comparator

The Data Grid considers the `sortComparator` function symmetric, automatically reversing the return value for descending sorting by multiplying it by `-1`.


While this is sufficient for most use cases, it is possible to define an asymmetric comparator using the `getSortComparator` function – it receives the sorting direction as an argument and returns a comparator function.


In the demo below, the `getSortComparator` function is used in the "Quantity" column to keep the `null` values at the bottom when sorting is applied (regardless of the sorting direction):


CommodityQuantityCocoa78,050MilkWheat5,462Sugar No.11Sugar No.1431,280Rows per page:

1001–5 of 5

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridColDef,
  gridStringOrNumberComparator,
} from '@mui/x-data-grid';
import {
  randomQuantity,
  randomId,
  randomCommodity,
} from '@mui/x-data-grid-generator';

const columns: GridColDef[] = [
  { field: 'commodity', headerName: 'Commodity', width: 200 },
  {
    type: 'number',
    field: 'quantity',
    headerName: 'Quantity',
    getSortComparator: (sortDirection) => {
      const modifier = sortDirection === 'desc' ? -1 : 1;
      return (value1, value2, cellParams1, cellParams2) => {
        if (value1 === null) {
          return 1;
        }
        if (value2 === null) {
          return -1;
        }
        return (
          modifier *
          gridStringOrNumberComparator(value1, value2, cellParams1, cellParams2)
        );
      };
    },
  },
];

const rows = [
  { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
  { id: randomId, commodity: randomCommodity, quantity: null },
  { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
  { id: randomId, commodity: randomCommodity, quantity: null },
  { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
];

export default function GetSortComparator {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid columns={columns} rows={rows} />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGrid,
 GridColDef,
 gridStringOrNumberComparator,
} from '@mui/x\-data\-grid';
import {
 randomQuantity,
 randomId,
 randomCommodity,
} from '@mui/x\-data\-grid\-generator';

const columns: GridColDef\[] \= \[
 { field: 'commodity', headerName: 'Commodity', width: 200 },
 {
 type: 'number',
 field: 'quantity',
 headerName: 'Quantity',
 getSortComparator: (sortDirection) \=\> {
 const modifier \= sortDirection \=\=\= 'desc' ? \-1 : 1;
 return (value1, value2, cellParams1, cellParams2\) \=\> {
 if (value1 \=\=\= null) {
 return 1;
 }
 if (value2 \=\=\= null) {
 return \-1;
 }
 return (
 modifier \*
 gridStringOrNumberComparator(value1, value2, cellParams1, cellParams2\)
 );
 };
 },
 },
];

const rows \= \[
 { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
 { id: randomId, commodity: randomCommodity, quantity: null },
 { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
 { id: randomId, commodity: randomCommodity, quantity: null },
 { id: randomId, commodity: randomCommodity, quantity: randomQuantity },
];

export default function GetSortComparator {
 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid columns\={columns} rows\={rows} /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- A single, unified DevSecOps platform is what you need to deliver software faster. Try it for free.ad by CarbonCustom sort order
-----------------

By default, the sort order cycles between these three different modes:



```
const sortingOrder = ['asc', 'desc', null];

```
CopyCopied(or Ctrl \+ C)
In practice, when you click a column that is not sorted, it will sort ascending (`asc`).
The next click will make it sort descending (`desc`). Another click will remove the sort (`null`), reverting to the order that the data was provided in.


### For all columns

The default sort order can be overridden for all columns with the `sortingOrder` prop.
In the following demo, columns are only sortable in descending or ascending order.


NameRatingCountryCreated onIs admin?Bernard Hammond 3Germany2024/8/30Brett Nash 3Angola2024/5/30Nina Lewis 4Latvia2024/12/10Alice Robbins 4Sint Maarten (Dutch part)2025/5/14Warren Parker 2Indonesia2024/8/14Wayne Tate 4Moldova, Republic of2025/5/23Leona Banks 1Congo, Republic of the2024/7/4Clarence Fitzgerald 5Djibouti2024/8/28Craig Stone 5Belarus2025/5/5Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function OrderSortingGrid {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        sortingOrder={['desc', 'asc']}
        initialState={{
          ...data.initialState,
          sorting: {
            ...data.initialState?.sorting,
            sortModel: [
              {
                field: 'commodity',
                sort: 'asc',
              },
            ],
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function OrderSortingGrid {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 sortingOrder\={\['desc', 'asc']}
 initialState\={{
 ...data.initialState,
 sorting: {
 ...data.initialState?.sorting,
 sortModel: \[
 {
 field: 'commodity',
 sort: 'asc',
 },
 ],
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**SciChart** \- SciChart JS, WPF, iOS \& Android Charts Handle The Most Demanding Requirements.ad by Carbon### Per column

Sort order can be configured (and overridden) on a per\-column basis by setting the `sortingOrder` property of the `GridColDef` interface:



```
const columns: GridColDef = [
  { field: 'rating', sortingOrder: ['desc', 'asc', null] },
];

```
CopyCopied(or Ctrl \+ C)
NameRatingCountryCreated onIs admin?Ina Marshall 4Fiji2025/1/24Eliza Nguyen 1Saint Lucia2025/2/8Marion Weber 5Eritrea2024/8/14Hattie Bailey 2Saint Vincent and the Grenadines2025/3/11Allen Foster 4Pitcairn2025/5/11Hunter Austin 5Yemen2025/1/24Rosetta Harvey 4Turks and Caicos Islands2024/8/31Jeff Fields 4Botswana2024/10/8Adele Wolfe 2Niue2024/12/1Rows per page:

1001–100 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function OrderSortingPerColumnGrid {
  const { data, loading } = useDemoData({
    dataSet: 'Employee',
    visibleFields: VISIBLE_FIELDS,
    rowLength: 100,
  });

  const columns = React.useMemo<GridColDef[]>(
     =>
      data.columns.map((column) => {
        if (column.field === 'rating') {
          return {
            ...column,
            sortingOrder: ['desc', 'asc', null],
          };
        }

        return column;
      }),
    [data.columns],
  );

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        columns={columns}
        sortingOrder={['asc', 'desc', null]}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColDef } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

export default function OrderSortingPerColumnGrid {
 const { data, loading } \= useDemoData({
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
 });

 const columns \= React.useMemo\<GridColDef\[]\>(
  \=\>
 data.columns.map((column) \=\> {
 if (column.field \=\=\= 'rating') {
 return {
 ...column,
 sortingOrder: \['desc', 'asc', null],
 };
 }

 return column;
 }),
 \[data.columns],
 );

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 columns\={columns}
 sortingOrder\={\['asc', 'desc', null]}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**AG Grid** \- Create high\-performance JavaScript data grids with advanced features in just a few lines of code.ad by CarbonServer\-side sorting
--------------------

Sorting can be run server\-side by setting the `sortingMode` prop to `server`, and implementing the `onSortModelChange` handler.


NameRatingKatharine Burton 1Devin Barrett 4Martin Austin 5Lucinda McCoy 4Clara Cortez 4Virginia Boone 1Jeffrey Stevens 5Leroy Estrada 1Jeffrey Wallace 3Rows per page:

1001–100 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridSortModel } from '@mui/x-data-grid';
import { UseDemoDataOptions, createFakeServer } from '@mui/x-data-grid-generator';

const VISIBLE_FIELDS = ['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

const DATASET_OPTION: UseDemoDataOptions = {
  dataSet: 'Employee',
  visibleFields: VISIBLE_FIELDS,
  rowLength: 100,
};

const { useQuery, ...data } = createFakeServer(DATASET_OPTION);

export default function ServerSortingGrid {
  const [queryOptions, setQueryOptions] = React.useState({});

  const handleSortModelChange = React.useCallback((sortModel: GridSortModel) => {
    // Here you save the data you need from the sort model
    setQueryOptions({ sortModel: [...sortModel] });
  }, []);

  const { isLoading, rows } = useQuery(queryOptions);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        {...data}
        sortingMode="server"
        onSortModelChange={handleSortModelChange}
        loading={isLoading}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridSortModel } from '@mui/x\-data\-grid';
import { UseDemoDataOptions, createFakeServer } from '@mui/x\-data\-grid\-generator';

const VISIBLE\_FIELDS \= \['name', 'rating', 'country', 'dateCreated', 'isAdmin'];

const DATASET\_OPTION: UseDemoDataOptions \= {
 dataSet: 'Employee',
 visibleFields: VISIBLE\_FIELDS,
 rowLength: 100,
};

const { useQuery, ...data } \= createFakeServer(DATASET\_OPTION);

export default function ServerSortingGrid {
 const \[queryOptions, setQueryOptions] \= React.useState({});

 const handleSortModelChange \= React.useCallback((sortModel: GridSortModel) \=\> {
 // Here you save the data you need from the sort model
 setQueryOptions({ sortModel: \[...sortModel] });
 }, \[]);

 const { isLoading, rows } \= useQuery(queryOptions);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 rows\={rows}
 {...data}
 sortingMode\="server"
 onSortModelChange\={handleSortModelChange}
 loading\={isLoading}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**AG Grid** \- Add JavaScript data grids to your app in minutes with AG Grid: Fast, flexible, and open source.ad by Carbon
You can combine server\-side sorting with server\-side filtering and server\-side pagination to avoid fetching more data than needed, since it's already processed outside of the Data Grid.


apiRef
------


Only use this API as the last option. Give preference to the props to control the Data Grid.


### applySortingApplies the current sort model to the rows.

###### Signature:

Copy(or Ctrl \+ C)
```
applySorting:  => void
```
### getRowIdFromRowIndexGets the `GridRowId` of a row at a specific index. The index is based on the sorted but unfiltered row list.

###### Signature:

Copy(or Ctrl \+ C)
```
getRowIdFromRowIndex: (index: number) => GridRowId
```
### getSortedRowIdsReturns all row ids sorted according to the active sort model.

###### Signature:

Copy(or Ctrl \+ C)
```
getSortedRowIds:  => GridRowId[]
```
### getSortedRowsReturns all rows sorted according to the active sort model.

###### Signature:

Copy(or Ctrl \+ C)
```
getSortedRows:  => GridRowModel[]
```
### getSortModelReturns the sort model currently applied to the grid.

###### Signature:

Copy(or Ctrl \+ C)
```
getSortModel:  => GridSortModel
```
### setSortModelUpdates the sort model and triggers the sorting of rows.

###### Signature:

Copy(or Ctrl \+ C)
```
setSortModel: (model: GridSortModel) => void
```
### sortColumnSorts a column.

###### Signature:

Copy(or Ctrl \+ C)
```
sortColumn: (field: GridColDef['field'], direction?: GridSortDirection, allowMultipleSorting?: boolean) => void
```
Selectors
---------

### gridSortModelSelectorGet the current sorting model.

###### Signature:

Copy(or Ctrl \+ C)
```
gridSortModelSelector: (apiRef: GridApiRef) => GridSortModel
```
###### Example

Copy(or Ctrl \+ C)
```
const sortModel = gridSortModelSelector(apiRef);
```
### gridSortedRowEntriesSelectorGet the id and the model of the rows after the sorting process.

###### Signature:

Copy(or Ctrl \+ C)
```
gridSortedRowEntriesSelector: (apiRef: GridApiRef) => GridRowEntry<GridValidRowModel>[]
```
###### Example

Copy(or Ctrl \+ C)
```
const sortedRowEntries = gridSortedRowEntriesSelector(apiRef);
```
### gridSortedRowIdsSelectorGet the id of the rows after the sorting process.

###### Signature:

Copy(or Ctrl \+ C)
```
gridSortedRowIdsSelector: (apiRef: GridApiRef) => GridRowId[]
```
###### Example

Copy(or Ctrl \+ C)
```
const sortedRowIds = gridSortedRowIdsSelector(apiRef);
```
More information about the selectors and how to use them on the dedicated page


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Recipes editingOverview

---

•

Blog•

Store
