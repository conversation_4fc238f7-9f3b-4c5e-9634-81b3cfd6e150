Dialog
======

Dialogs inform users about a task and can contain critical information, require decisions, or involve multiple tasks.


All the connections, zero limits. Unlimited Okta and social connections on our Free Plan. Sign up →

ads via Carbon



A Dialog is a type of modal window that appears in front of app content to provide critical information or ask for a decision. Dialogs disable all app functionality when they appear, and remain on screen until confirmed, dismissed, or a required action has been taken.


Dialogs are purposefully interruptive, so they should be used sparingly.


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Introduction
------------

Dialogs are implemented using a collection of related components:


* Dialog: the parent component that renders the modal.
* Dialog Title: a wrapper used for the title of a Dialog.
* Dialog Actions: an optional container for a Dialog's Buttons.
* Dialog Content: an optional container for displaying the Dialog's content.
* Dialog Content Text: a wrapper for text inside of `<DialogContent />`.
* Slide: optional Transition used to slide the Dialog in from the edge of the screen.


Selected: <EMAIL>  
Open simple dialogJSTSExpand codeCopy(or Ctrl \+ C)
```
<Typography variant="subtitle1" component="div">
  Selected: {selectedValue}
</Typography>
<br />
<Button variant="outlined" onClick={handleClickOpen}>
  Open simple dialog
</Button>
<SimpleDialog
  selectedValue={selectedValue}
  open={open}
  onClose={handleClose}
/>  

```
\<Typography variant\="subtitle1" component\="div"\>
 Selected: {selectedValue}
\</Typography\>
\<br /\>
\<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open simple dialog
\</Button\>
\<SimpleDialog
 selectedValue\={selectedValue}
 open\={open}
 onClose\={handleClose}
/\>Press `Enter` to start editingBasics
------


```
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';

```
CopyCopied(or Ctrl \+ C)
Alerts
------

Alerts are urgent interruptions, requiring acknowledgement, that inform the user about a situation.


Most alerts don't need titles.
They summarize a decision in a sentence or two by either:


* Asking a question (for example "Delete this conversation?")
* Making a statement related to the action buttons


Use title bar alerts only for high\-risk situations, such as the potential loss of connectivity.
Users should be able to understand the choices based on the title and button text alone.


If a title is required:


* Use a clear question or statement with an explanation in the content area, such as "Erase USB storage?".
* Avoid apologies, ambiguity, or questions, such as "Warning!" or "Are you sure?"


Open alert dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function AlertDialog {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open alert dialog
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Use Google's location service?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Let Google help apps determine location. This means sending anonymous
            location data to Google, even when no apps are running.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Disagree</Button>
          <Button onClick={handleClose} autoFocus>
            Agree
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function AlertDialog {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open alert dialog
 \</Button\>
 \<Dialog
 open\={open}
 onClose\={handleClose}
 aria\-labelledby\="alert\-dialog\-title"
 aria\-describedby\="alert\-dialog\-description"
 \>
 \<DialogTitle id\="alert\-dialog\-title"\>
 {"Use Google's location service?"}
 \</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText id\="alert\-dialog\-description"\>
 Let Google help apps determine location. This means sending anonymous
 location data to Google, even when no apps are running.
 \</DialogContentText\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={handleClose}\>Disagree\</Button\>
 \<Button onClick\={handleClose} autoFocus\>
 Agree
 \</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by CarbonTransitions
-----------

You can also swap out the transition, the next example uses `Slide`.


Slide in alert dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function AlertDialogSlide {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Slide in alert dialog
      </Button>
      <Dialog
        open={open}
        slots={{
          transition: Transition,
        }}
        keepMounted
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>{"Use Google's location service?"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-slide-description">
            Let Google help apps determine location. This means sending anonymous
            location data to Google, even when no apps are running.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Disagree</Button>
          <Button onClick={handleClose}>Agree</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

const Transition \= React.forwardRef(function Transition(
 props: TransitionProps \& {
 children: React.ReactElement\<any, any\>;
 },
 ref: React.Ref\<unknown\>,
) {
 return \<Slide direction\="up" ref\={ref} {...props} /\>;
});

export default function AlertDialogSlide {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Slide in alert dialog
 \</Button\>
 \<Dialog
 open\={open}
 slots\={{
 transition: Transition,
 }}
 keepMounted
 onClose\={handleClose}
 aria\-describedby\="alert\-dialog\-slide\-description"
 \>
 \<DialogTitle\>{"Use Google's location service?"}\</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText id\="alert\-dialog\-slide\-description"\>
 Let Google help apps determine location. This means sending anonymous
 location data to Google, even when no apps are running.
 \</DialogContentText\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={handleClose}\>Disagree\</Button\>
 \<Button onClick\={handleClose}\>Agree\</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonForm dialogs
------------

Form dialogs allow users to fill out form fields within a dialog.
For example, if your site prompts for potential subscribers to fill in their email address, they can fill out the email field and touch 'Submit'.


Open form dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function FormDialog {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open form dialog
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            component: 'form',
            onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
              event.preventDefault;
              const formData = new FormData(event.currentTarget);
              const formJson = Object.fromEntries((formData as any).entries);
              const email = formJson.email;
              console.log(email);
              handleClose;
            },
          },
        }}
      >
        <DialogTitle>Subscribe</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To subscribe to this website, please enter your email address here. We
            will send updates occasionally.
          </DialogContentText>
          <TextField
            autoFocus
            required
            margin="dense"
            id="name"
            name="email"
            label="Email Address"
            type="email"
            fullWidth
            variant="standard"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit">Subscribe</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function FormDialog {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open form dialog
 \</Button\>
 \<Dialog
 open\={open}
 onClose\={handleClose}
 slotProps\={{
 paper: {
 component: 'form',
 onSubmit: (event: React.FormEvent\<HTMLFormElement\>) \=\> {
 event.preventDefault;
 const formData \= new FormData(event.currentTarget);
 const formJson \= Object.fromEntries((formData as any).entries);
 const email \= formJson.email;
 console.log(email);
 handleClose;
 },
 },
 }}
 \>
 \<DialogTitle\>Subscribe\</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText\>
 To subscribe to this website, please enter your email address here. We
 will send updates occasionally.
 \</DialogContentText\>
 \<TextField
 autoFocus
 required
 margin\="dense"
 id\="name"
 name\="email"
 label\="Email Address"
 type\="email"
 fullWidth
 variant\="standard"
 /\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={handleClose}\>Cancel\</Button\>
 \<Button type\="submit"\>Subscribe\</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**AG Grid** \- Create high\-performance JavaScript data grids with advanced features in just a few lines of code.ad by CarbonCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


The dialog has a close button added to aid usability.


Open dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

export default function CustomizedDialogs {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };
  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open dialog
      </Button>
      <BootstrapDialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          Modal title
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: 'absolute',
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <Typography gutterBottom>
            Cras mattis consectetur purus sit amet fermentum. Cras justo odio,
            dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac
            consectetur ac, vestibulum at eros.
          </Typography>
          <Typography gutterBottom>
            Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
            Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor.
          </Typography>
          <Typography gutterBottom>
            Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus
            magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec
            ullamcorper nulla non metus auctor fringilla.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose}>
            Save changes
          </Button>
        </DialogActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons\-material/Close';
import Typography from '@mui/material/Typography';

const BootstrapDialog \= styled(Dialog)(({ theme }) \=\> ({
 '\& .MuiDialogContent\-root': {
 padding: theme.spacing(2\),
 },
 '\& .MuiDialogActions\-root': {
 padding: theme.spacing(1\),
 },
}));

export default function CustomizedDialogs {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };
 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open dialog
 \</Button\>
 \<BootstrapDialog
 onClose\={handleClose}
 aria\-labelledby\="customized\-dialog\-title"
 open\={open}
 \>
 \<DialogTitle sx\={{ m: 0, p: 2 }} id\="customized\-dialog\-title"\>
 Modal title
 \</DialogTitle\>
 \<IconButton
 aria\-label\="close"
 onClick\={handleClose}
 sx\={(theme) \=\> ({
 position: 'absolute',
 right: 8,
 top: 8,
 color: theme.palette.grey\[500],
 })}
 \>
 \<CloseIcon /\>
 \</IconButton\>
 \<DialogContent dividers\>
 \<Typography gutterBottom\>
 Cras mattis consectetur purus sit amet fermentum. Cras justo odio,
 dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac
 consectetur ac, vestibulum at eros.
 \</Typography\>
 \<Typography gutterBottom\>
 Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
 Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor.
 \</Typography\>
 \<Typography gutterBottom\>
 Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus
 magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec
 ullamcorper nulla non metus auctor fringilla.
 \</Typography\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button autoFocus onClick\={handleClose}\>
 Save changes
 \</Button\>
 \</DialogActions\>
 \</BootstrapDialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Gitlab** \- From planning to production, GitLab brings teams together. Try it our for free.ad by CarbonFull\-screen dialogs
--------------------

Open full\-screen dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import ListItemText from '@mui/material/ListItemText';
import ListItemButton from '@mui/material/ListItemButton';
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<unknown>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function FullScreenDialog {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open full-screen dialog
      </Button>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
      >
        <AppBar sx={{ position: 'relative' }}>
          <Toolbar>
            <IconButton
              edge="start"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
            <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
              Sound
            </Typography>
            <Button autoFocus color="inherit" onClick={handleClose}>
              save
            </Button>
          </Toolbar>
        </AppBar>
        <List>
          <ListItemButton>
            <ListItemText primary="Phone ringtone" secondary="Titania" />
          </ListItemButton>
          <Divider />
          <ListItemButton>
            <ListItemText
              primary="Default notification ringtone"
              secondary="Tethys"
            />
          </ListItemButton>
        </List>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import ListItemText from '@mui/material/ListItemText';
import ListItemButton from '@mui/material/ListItemButton';
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons\-material/Close';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

const Transition \= React.forwardRef(function Transition(
 props: TransitionProps \& {
 children: React.ReactElement\<unknown\>;
 },
 ref: React.Ref\<unknown\>,
) {
 return \<Slide direction\="up" ref\={ref} {...props} /\>;
});

export default function FullScreenDialog {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open full\-screen dialog
 \</Button\>
 \<Dialog
 fullScreen
 open\={open}
 onClose\={handleClose}
 TransitionComponent\={Transition}
 \>
 \<AppBar sx\={{ position: 'relative' }}\>
 \<Toolbar\>
 \<IconButton
 edge\="start"
 color\="inherit"
 onClick\={handleClose}
 aria\-label\="close"
 \>
 \<CloseIcon /\>
 \</IconButton\>
 \<Typography sx\={{ ml: 2, flex: 1 }} variant\="h6" component\="div"\>
 Sound
 \</Typography\>
 \<Button autoFocus color\="inherit" onClick\={handleClose}\>
 save
 \</Button\>
 \</Toolbar\>
 \</AppBar\>
 \<List\>
 \<ListItemButton\>
 \<ListItemText primary\="Phone ringtone" secondary\="Titania" /\>
 \</ListItemButton\>
 \<Divider /\>
 \<ListItemButton\>
 \<ListItemText
 primary\="Default notification ringtone"
 secondary\="Tethys"
 /\>
 \</ListItemButton\>
 \</List\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Auth0** \- Boost your app’s Identity with Auth0\. Now with Custom Domain, Passwordless, and more!ad by CarbonOptional sizes
--------------

You can set a dialog maximum width by using the `maxWidth` enumerable in combination with the `fullWidth` boolean.
When the `fullWidth` prop is true, the dialog will adapt based on the `maxWidth` value.


Open max\-width dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import Switch from '@mui/material/Switch';

export default function MaxWidthDialog {
  const [open, setOpen] = React.useState(false);
  const [fullWidth, setFullWidth] = React.useState(true);
  const [maxWidth, setMaxWidth] = React.useState<DialogProps['maxWidth']>('sm');

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  const handleMaxWidthChange = (event: SelectChangeEvent<typeof maxWidth>) => {
    setMaxWidth(
      // @ts-expect-error autofill of arbitrary value is not handled.
      event.target.value,
    );
  };

  const handleFullWidthChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFullWidth(event.target.checked);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open max-width dialog
      </Button>
      <Dialog
        fullWidth={fullWidth}
        maxWidth={maxWidth}
        open={open}
        onClose={handleClose}
      >
        <DialogTitle>Optional sizes</DialogTitle>
        <DialogContent>
          <DialogContentText>
            You can set my maximum width and whether to adapt or not.
          </DialogContentText>
          <Box
            noValidate
            component="form"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              m: 'auto',
              width: 'fit-content',
            }}
          >
            <FormControl sx={{ mt: 2, minWidth: 120 }}>
              <InputLabel htmlFor="max-width">maxWidth</InputLabel>
              <Select
                autoFocus
                value={maxWidth}
                onChange={handleMaxWidthChange}
                label="maxWidth"
                inputProps={{
                  name: 'max-width',
                  id: 'max-width',
                }}
              >
                <MenuItem value={false as any}>false</MenuItem>
                <MenuItem value="xs">xs</MenuItem>
                <MenuItem value="sm">sm</MenuItem>
                <MenuItem value="md">md</MenuItem>
                <MenuItem value="lg">lg</MenuItem>
                <MenuItem value="xl">xl</MenuItem>
              </Select>
            </FormControl>
            <FormControlLabel
              sx={{ mt: 1 }}
              control={
                <Switch checked={fullWidth} onChange={handleFullWidthChange} />
              }
              label="Full width"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import Switch from '@mui/material/Switch';

export default function MaxWidthDialog {
 const \[open, setOpen] \= React.useState(false);
 const \[fullWidth, setFullWidth] \= React.useState(true);
 const \[maxWidth, setMaxWidth] \= React.useState\<DialogProps\['maxWidth']\>('sm');

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 const handleMaxWidthChange \= (event: SelectChangeEvent\<typeof maxWidth\>) \=\> {
 setMaxWidth(
 // @ts\-expect\-error autofill of arbitrary value is not handled.
 event.target.value,
 );
 };

 const handleFullWidthChange \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setFullWidth(event.target.checked);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open max\-width dialog
 \</Button\>
 \<Dialog
 fullWidth\={fullWidth}
 maxWidth\={maxWidth}
 open\={open}
 onClose\={handleClose}
 \>
 \<DialogTitle\>Optional sizes\</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText\>
 You can set my maximum width and whether to adapt or not.
 \</DialogContentText\>
 \<Box
 noValidate
 component\="form"
 sx\={{
 display: 'flex',
 flexDirection: 'column',
 m: 'auto',
 width: 'fit\-content',
 }}
 \>
 \<FormControl sx\={{ mt: 2, minWidth: 120 }}\>
 \<InputLabel htmlFor\="max\-width"\>maxWidth\</InputLabel\>
 \<Select
 autoFocus
 value\={maxWidth}
 onChange\={handleMaxWidthChange}
 label\="maxWidth"
 inputProps\={{
 name: 'max\-width',
 id: 'max\-width',
 }}
 \>
 \<MenuItem value\={false as any}\>false\</MenuItem\>
 \<MenuItem value\="xs"\>xs\</MenuItem\>
 \<MenuItem value\="sm"\>sm\</MenuItem\>
 \<MenuItem value\="md"\>md\</MenuItem\>
 \<MenuItem value\="lg"\>lg\</MenuItem\>
 \<MenuItem value\="xl"\>xl\</MenuItem\>
 \</Select\>
 \</FormControl\>
 \<FormControlLabel
 sx\={{ mt: 1 }}
 control\={
 \<Switch checked\={fullWidth} onChange\={handleFullWidthChange} /\>
 }
 label\="Full width"
 /\>
 \</Box\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={handleClose}\>Close\</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonResponsive full\-screen
-----------------------

You may make a dialog responsively full screen using `useMediaQuery`.



```
import useMediaQuery from '@mui/material/useMediaQuery';

function MyComponent {
  const theme = useTheme;
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  return <Dialog fullScreen={fullScreen} />;
}

```
CopyCopied(or Ctrl \+ C)
Open responsive dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

export default function ResponsiveDialog {
  const [open, setOpen] = React.useState(false);
  const theme = useTheme;
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open responsive dialog
      </Button>
      <Dialog
        fullScreen={fullScreen}
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle id="responsive-dialog-title">
          {"Use Google's location service?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Let Google help apps determine location. This means sending anonymous
            location data to Google, even when no apps are running.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose}>
            Disagree
          </Button>
          <Button onClick={handleClose} autoFocus>
            Agree
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

export default function ResponsiveDialog {
 const \[open, setOpen] \= React.useState(false);
 const theme \= useTheme;
 const fullScreen \= useMediaQuery(theme.breakpoints.down('md'));

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open responsive dialog
 \</Button\>
 \<Dialog
 fullScreen\={fullScreen}
 open\={open}
 onClose\={handleClose}
 aria\-labelledby\="responsive\-dialog\-title"
 \>
 \<DialogTitle id\="responsive\-dialog\-title"\>
 {"Use Google's location service?"}
 \</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText\>
 Let Google help apps determine location. This means sending anonymous
 location data to Google, even when no apps are running.
 \</DialogContentText\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button autoFocus onClick\={handleClose}\>
 Disagree
 \</Button\>
 \<Button onClick\={handleClose} autoFocus\>
 Agree
 \</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**SciChart** \- SciChart JS, WPF, iOS \& Android Charts Handle The Most Demanding Requirements.ad by CarbonConfirmation dialogs
--------------------

Confirmation dialogs require users to explicitly confirm their choice before an option is committed.
For example, users can listen to multiple ringtones but only make a final selection upon touching "OK".


Touching "Cancel" in a confirmation dialog, cancels the action, discards any changes, and closes the dialog.


InterruptionsPhone ringtoneDione

Default notification ringtoneTethys

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import RadioGroup from '@mui/material/RadioGroup';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';

const options = [
  'None',
  'Atria',
  'Callisto',
  'Dione',
  'Ganymede',
  'Hangouts Call',
  'Luna',
  'Oberon',
  'Phobos',
  'Pyxis',
  'Sedna',
  'Titania',
  'Triton',
  'Umbriel',
];

export interface ConfirmationDialogRawProps {
  id: string;
  keepMounted: boolean;
  value: string;
  open: boolean;
  onClose: (value?: string) => void;
}

function ConfirmationDialogRaw(props: ConfirmationDialogRawProps) {
  const { onClose, value: valueProp, open, ...other } = props;
  const [value, setValue] = React.useState(valueProp);
  const radioGroupRef = React.useRef<HTMLElement>(null);

  React.useEffect( => {
    if (!open) {
      setValue(valueProp);
    }
  }, [valueProp, open]);

  const handleEntering =  => {
    if (radioGroupRef.current != null) {
      radioGroupRef.current.focus;
    }
  };

  const handleCancel =  => {
    onClose;
  };

  const handleOk =  => {
    onClose(value);
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue((event.target as HTMLInputElement).value);
  };

  return (
    <Dialog
      sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435 } }}
      maxWidth="xs"
      TransitionProps={{ onEntering: handleEntering }}
      open={open}
      {...other}
    >
      <DialogTitle>Phone Ringtone</DialogTitle>
      <DialogContent dividers>
        <RadioGroup
          ref={radioGroupRef}
          aria-label="ringtone"
          name="ringtone"
          value={value}
          onChange={handleChange}
        >
          {options.map((option) => (
            <FormControlLabel
              value={option}
              key={option}
              control={<Radio />}
              label={option}
            />
          ))}
        </RadioGroup>
      </DialogContent>
      <DialogActions>
        <Button autoFocus onClick={handleCancel}>
          Cancel
        </Button>
        <Button onClick={handleOk}>Ok</Button>
      </DialogActions>
    </Dialog>
  );
}

export default function ConfirmationDialog {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('Dione');

  const handleClickListItem =  => {
    setOpen(true);
  };

  const handleClose = (newValue?: string) => {
    setOpen(false);

    if (newValue) {
      setValue(newValue);
    }
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}>
      <List component="div" role="group">
        <ListItemButton divider disabled>
          <ListItemText primary="Interruptions" />
        </ListItemButton>
        <ListItemButton
          divider
          aria-haspopup="true"
          aria-controls="ringtone-menu"
          aria-label="phone ringtone"
          onClick={handleClickListItem}
        >
          <ListItemText primary="Phone ringtone" secondary={value} />
        </ListItemButton>
        <ListItemButton divider disabled>
          <ListItemText primary="Default notification ringtone" secondary="Tethys" />
        </ListItemButton>
        <ConfirmationDialogRaw
          id="ringtone-menu"
          keepMounted
          open={open}
          onClose={handleClose}
          value={value}
        />
      </List>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import RadioGroup from '@mui/material/RadioGroup';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';

const options \= \[
 'None',
 'Atria',
 'Callisto',
 'Dione',
 'Ganymede',
 'Hangouts Call',
 'Luna',
 'Oberon',
 'Phobos',
 'Pyxis',
 'Sedna',
 'Titania',
 'Triton',
 'Umbriel',
];

export interface ConfirmationDialogRawProps {
 id: string;
 keepMounted: boolean;
 value: string;
 open: boolean;
 onClose: (value?: string) \=\> void;
}

function ConfirmationDialogRaw(props: ConfirmationDialogRawProps) {
 const { onClose, value: valueProp, open, ...other } \= props;
 const \[value, setValue] \= React.useState(valueProp);
 const radioGroupRef \= React.useRef\<HTMLElement\>(null);

 React.useEffect( \=\> {
 if (!open) {
 setValue(valueProp);
 }
 }, \[valueProp, open]);

 const handleEntering \=  \=\> {
 if (radioGroupRef.current !\= null) {
 radioGroupRef.current.focus;
 }
 };

 const handleCancel \=  \=\> {
 onClose;
 };

 const handleOk \=  \=\> {
 onClose(value);
 };

 const handleChange \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setValue((event.target as HTMLInputElement).value);
 };

 return (
 \<Dialog
 sx\={{ '\& .MuiDialog\-paper': { width: '80%', maxHeight: 435 } }}
 maxWidth\="xs"
 TransitionProps\={{ onEntering: handleEntering }}
 open\={open}
 {...other}
 \>
 \<DialogTitle\>Phone Ringtone\</DialogTitle\>
 \<DialogContent dividers\>
 \<RadioGroup
 ref\={radioGroupRef}
 aria\-label\="ringtone"
 name\="ringtone"
 value\={value}
 onChange\={handleChange}
 \>
 {options.map((option) \=\> (
 \<FormControlLabel
 value\={option}
 key\={option}
 control\={\<Radio /\>}
 label\={option}
 /\>
 ))}
 \</RadioGroup\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button autoFocus onClick\={handleCancel}\>
 Cancel
 \</Button\>
 \<Button onClick\={handleOk}\>Ok\</Button\>
 \</DialogActions\>
 \</Dialog\>
 );
}

export default function ConfirmationDialog {
 const \[open, setOpen] \= React.useState(false);
 const \[value, setValue] \= React.useState('Dione');

 const handleClickListItem \=  \=\> {
 setOpen(true);
 };

 const handleClose \= (newValue?: string) \=\> {
 setOpen(false);

 if (newValue) {
 setValue(newValue);
 }
 };

 return (
 \<Box sx\={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}\>
 \<List component\="div" role\="group"\>
 \<ListItemButton divider disabled\>
 \<ListItemText primary\="Interruptions" /\>
 \</ListItemButton\>
 \<ListItemButton
 divider
 aria\-haspopup\="true"
 aria\-controls\="ringtone\-menu"
 aria\-label\="phone ringtone"
 onClick\={handleClickListItem}
 \>
 \<ListItemText primary\="Phone ringtone" secondary\={value} /\>
 \</ListItemButton\>
 \<ListItemButton divider disabled\>
 \<ListItemText primary\="Default notification ringtone" secondary\="Tethys" /\>
 \</ListItemButton\>
 \<ConfirmationDialogRaw
 id\="ringtone\-menu"
 keepMounted
 open\={open}
 onClose\={handleClose}
 value\={value}
 /\>
 \</List\>
 \</Box\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by CarbonNon\-modal dialog
-----------------

Dialogs can also be non\-modal, meaning they don't interrupt user interaction behind it.
Visit the Nielsen Norman Group article for more in\-depth guidance about modal vs. non\-modal dialog usage.


The demo below shows a persistent cookie banner, a common non\-modal dialog use case.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import TrapFocus from '@mui/material/Unstable_TrapFocus';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Container from '@mui/material/Container';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import Paper from '@mui/material/Paper';
import Fade from '@mui/material/Fade';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

export default function CookiesBanner {
  const [bannerOpen, setBannerOpen] = React.useState(true);

  const closeBanner =  => {
    setBannerOpen(false);
  };

  return (
    <React.Fragment>
      <CssBaseline />
      <AppBar position="fixed" component="nav">
        <Toolbar>
          <IconButton size="large" edge="start" color="inherit" aria-label="menu">
            <MenuIcon />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Container component="main" sx={{ pt: 3 }}>
        <Toolbar />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet.
        </Typography>
      </Container>
      <TrapFocus open disableAutoFocus disableEnforceFocus>
        <Fade appear={false} in={bannerOpen}>
          <Paper
            role="dialog"
            aria-modal="false"
            aria-label="Cookie banner"
            square
            variant="outlined"
            tabIndex={-1}
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              m: 0,
              p: 2,
              borderWidth: 0,
              borderTopWidth: 1,
            }}
          >
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              sx={{ justifyContent: 'space-between', gap: 2 }}
            >
              <Box
                sx={{ flexShrink: 1, alignSelf: { xs: 'flex-start', sm: 'center' } }}
              >
                <Typography sx={{ fontWeight: 'bold' }}>
                  This website uses cookies
                </Typography>
                <Typography variant="body2">
                  example.com relies on cookies to improve your experience.
                </Typography>
              </Box>
              <Stack
                direction={{
                  xs: 'row-reverse',
                  sm: 'row',
                }}
                sx={{
                  gap: 2,
                  flexShrink: 0,
                  alignSelf: { xs: 'flex-end', sm: 'center' },
                }}
              >
                <Button size="small" onClick={closeBanner} variant="contained">
                  Allow all
                </Button>
                <Button size="small" onClick={closeBanner}>
                  Reject all
                </Button>
              </Stack>
            </Stack>
          </Paper>
        </Fade>
      </TrapFocus>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import TrapFocus from '@mui/material/Unstable\_TrapFocus';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Container from '@mui/material/Container';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons\-material/Menu';
import Paper from '@mui/material/Paper';
import Fade from '@mui/material/Fade';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

export default function CookiesBanner {
 const \[bannerOpen, setBannerOpen] \= React.useState(true);

 const closeBanner \=  \=\> {
 setBannerOpen(false);
 };

 return (
 \<React.Fragment\>
 \<CssBaseline /\>
 \<AppBar position\="fixed" component\="nav"\>
 \<Toolbar\>
 \<IconButton size\="large" edge\="start" color\="inherit" aria\-label\="menu"\>
 \<MenuIcon /\>
 \</IconButton\>
 \</Toolbar\>
 \</AppBar\>
 \<Container component\="main" sx\={{ pt: 3 }}\>
 \<Toolbar /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet.
 \</Typography\>
 \</Container\>
 \<TrapFocus open disableAutoFocus disableEnforceFocus\>
 \<Fade appear\={false} in\={bannerOpen}\>
 \<Paper
 role\="dialog"
 aria\-modal\="false"
 aria\-label\="Cookie banner"
 square
 variant\="outlined"
 tabIndex\={\-1}
 sx\={{
 position: 'fixed',
 bottom: 0,
 left: 0,
 right: 0,
 m: 0,
 p: 2,
 borderWidth: 0,
 borderTopWidth: 1,
 }}
 \>
 \<Stack
 direction\={{ xs: 'column', sm: 'row' }}
 sx\={{ justifyContent: 'space\-between', gap: 2 }}
 \>
 \<Box
 sx\={{ flexShrink: 1, alignSelf: { xs: 'flex\-start', sm: 'center' } }}
 \>
 \<Typography sx\={{ fontWeight: 'bold' }}\>
 This website uses cookies
 \</Typography\>
 \<Typography variant\="body2"\>
 example.com relies on cookies to improve your experience.
 \</Typography\>
 \</Box\>
 \<Stack
 direction\={{
 xs: 'row\-reverse',
 sm: 'row',
 }}
 sx\={{
 gap: 2,
 flexShrink: 0,
 alignSelf: { xs: 'flex\-end', sm: 'center' },
 }}
 \>
 \<Button size\="small" onClick\={closeBanner} variant\="contained"\>
 Allow all
 \</Button\>
 \<Button size\="small" onClick\={closeBanner}\>
 Reject all
 \</Button\>
 \</Stack\>
 \</Stack\>
 \</Paper\>
 \</Fade\>
 \</TrapFocus\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonDraggable dialog
----------------

You can create a draggable dialog by using react\-draggable.
To do so, you can pass the imported `Draggable` component as the `PaperComponent` of the `Dialog` component.
This will make the entire dialog draggable.


Open draggable dialogJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Paper, { PaperProps } from '@mui/material/Paper';
import Draggable from 'react-draggable';

function PaperComponent(props: PaperProps) {
  const nodeRef = React.useRef<HTMLDivElement>(null);
  return (
    <Draggable
      nodeRef={nodeRef as React.RefObject<HTMLDivElement>}
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} ref={nodeRef} />
    </Draggable>
  );
}

export default function DraggableDialog {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen =  => {
    setOpen(true);
  };

  const handleClose =  => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open draggable dialog
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
      >
        <DialogTitle style={{ cursor: 'move' }} id="draggable-dialog-title">
          Subscribe
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            To subscribe to this website, please enter your email address here. We
            will send updates occasionally.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleClose}>Subscribe</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Paper, { PaperProps } from '@mui/material/Paper';
import Draggable from 'react\-draggable';

function PaperComponent(props: PaperProps) {
 const nodeRef \= React.useRef\<HTMLDivElement\>(null);
 return (
 \<Draggable
 nodeRef\={nodeRef as React.RefObject\<HTMLDivElement\>}
 handle\="\#draggable\-dialog\-title"
 cancel\={'\[class\*\="MuiDialogContent\-root"]'}
 \>
 \<Paper {...props} ref\={nodeRef} /\>
 \</Draggable\>
 );
}

export default function DraggableDialog {
 const \[open, setOpen] \= React.useState(false);

 const handleClickOpen \=  \=\> {
 setOpen(true);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<React.Fragment\>
 \<Button variant\="outlined" onClick\={handleClickOpen}\>
 Open draggable dialog
 \</Button\>
 \<Dialog
 open\={open}
 onClose\={handleClose}
 PaperComponent\={PaperComponent}
 aria\-labelledby\="draggable\-dialog\-title"
 \>
 \<DialogTitle style\={{ cursor: 'move' }} id\="draggable\-dialog\-title"\>
 Subscribe
 \</DialogTitle\>
 \<DialogContent\>
 \<DialogContentText\>
 To subscribe to this website, please enter your email address here. We
 will send updates occasionally.
 \</DialogContentText\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button autoFocus onClick\={handleClose}\>
 Cancel
 \</Button\>
 \<Button onClick\={handleClose}\>Subscribe\</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonScrolling long content
----------------------

When dialogs become too long for the user's viewport or device, they scroll.


* `scroll=paper` the content of the dialog scrolls within the paper element.
* `scroll=body` the content of the dialog scrolls within the body element.


Try the demo below to see what we mean:


scroll\=paperscroll\=bodyJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function ScrollDialog {
  const [open, setOpen] = React.useState(false);
  const [scroll, setScroll] = React.useState<DialogProps['scroll']>('paper');

  const handleClickOpen = (scrollType: DialogProps['scroll']) =>  => {
    setOpen(true);
    setScroll(scrollType);
  };

  const handleClose =  => {
    setOpen(false);
  };

  const descriptionElementRef = React.useRef<HTMLElement>(null);
  React.useEffect( => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef;
      if (descriptionElement !== null) {
        descriptionElement.focus;
      }
    }
  }, [open]);

  return (
    <React.Fragment>
      <Button onClick={handleClickOpen('paper')}>scroll=paper</Button>
      <Button onClick={handleClickOpen('body')}>scroll=body</Button>
      <Dialog
        open={open}
        onClose={handleClose}
        scroll={scroll}
        aria-labelledby="scroll-dialog-title"
        aria-describedby="scroll-dialog-description"
      >
        <DialogTitle id="scroll-dialog-title">Subscribe</DialogTitle>
        <DialogContent dividers={scroll === 'paper'}>
          <DialogContentText
            id="scroll-dialog-description"
            ref={descriptionElementRef}
            tabIndex={-1}
          >
            {[...new Array(50)]
              .map(
                 => `Cras mattis consectetur purus sit amet fermentum.
Cras justo odio, dapibus ac facilisis in, egestas eget quam.
Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
Praesent commodo cursus magna, vel scelerisque nisl consectetur et.`,
              )
              .join('\n')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleClose}>Subscribe</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function ScrollDialog {
 const \[open, setOpen] \= React.useState(false);
 const \[scroll, setScroll] \= React.useState\<DialogProps\['scroll']\>('paper');

 const handleClickOpen \= (scrollType: DialogProps\['scroll']) \=\>  \=\> {
 setOpen(true);
 setScroll(scrollType);
 };

 const handleClose \=  \=\> {
 setOpen(false);
 };

 const descriptionElementRef \= React.useRef\<HTMLElement\>(null);
 React.useEffect( \=\> {
 if (open) {
 const { current: descriptionElement } \= descriptionElementRef;
 if (descriptionElement !\=\= null) {
 descriptionElement.focus;
 }
 }
 }, \[open]);

 return (
 \<React.Fragment\>
 \<Button onClick\={handleClickOpen('paper')}\>scroll\=paper\</Button\>
 \<Button onClick\={handleClickOpen('body')}\>scroll\=body\</Button\>
 \<Dialog
 open\={open}
 onClose\={handleClose}
 scroll\={scroll}
 aria\-labelledby\="scroll\-dialog\-title"
 aria\-describedby\="scroll\-dialog\-description"
 \>
 \<DialogTitle id\="scroll\-dialog\-title"\>Subscribe\</DialogTitle\>
 \<DialogContent dividers\={scroll \=\=\= 'paper'}\>
 \<DialogContentText
 id\="scroll\-dialog\-description"
 ref\={descriptionElementRef}
 tabIndex\={\-1}
 \>
 {\[...new Array(50\)]
 .map(
  \=\> \`Cras mattis consectetur purus sit amet fermentum.
Cras justo odio, dapibus ac facilisis in, egestas eget quam.
Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
Praesent commodo cursus magna, vel scelerisque nisl consectetur et.\`,
 )
 .join('\\n')}
 \</DialogContentText\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={handleClose}\>Cancel\</Button\>
 \<Button onClick\={handleClose}\>Subscribe\</Button\>
 \</DialogActions\>
 \</Dialog\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Gitlab** \- Use GitLab end to end. From security to development to operations. It's all here.ad by CarbonPerformance
-----------

Follow the Modal performance section.


Limitations
-----------

Follow the Modal limitations section.


Supplementary projects
----------------------

For more advanced use cases you might be able to take advantage of:


### material\-ui\-confirm





The package `material-ui-confirm` provides dialogs for confirming user actions without writing boilerplate code.


Accessibility
-------------

Follow the Modal accessibility section.


Experimental APIs \- Toolpad
----------------------------

### useDialogs

You can create and manipulate dialogs imperatively with the `useDialogs` API in `@toolpad/core`. This hook handles


* state management for opening and closing dialogs
* passing data to dialogs and receiving results back from them
* stacking multiple dialogs
* themed, asynchronous versions of `window.alert`, `window.confirm` and `window.prompt`


The following example demonstrates some of these features:


DeleteJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DialogsProvider, useDialogs, DialogProps } from '@toolpad/core/useDialogs';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Alert from '@mui/material/Alert';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';

interface DeleteError {
  id: string | null;
  error: string | null;
}

function MyCustomDialog({ open, onClose, payload }: DialogProps<DeleteError>) {
  return (
    <Dialog fullWidth open={open} onClose={ => onClose}>
      <DialogTitle>Custom Error Handler</DialogTitle>
      <DialogContent>
        <Alert severity="error">
          {`An error occurred while deleting item "${payload.id}":`}
          <pre>{payload.error}</pre>
        </Alert>
      </DialogContent>
      <DialogActions>
        <Button onClick={ => onClose}>Close me</Button>
      </DialogActions>
    </Dialog>
  );
}

const mockApiDelete = async (id: string | null) => {
  return new Promise((resolve, reject) => {
    setTimeout( => {
      if (!id) {
        reject(new Error('ID is required'));
      } else if (parseInt(id, 10) % 2 === 0) {
        console.log('id', parseInt(id, 10));
        resolve(true);
      } else if (parseInt(id, 10) % 2 === 1) {
        reject(new Error('Can not delete odd numbered elements'));
      } else if (Number.isNaN(parseInt(id, 10))) {
        reject(new Error('ID must be a number'));
      } else {
        reject(new Error('Unknown error'));
      }
    }, 1000);
  });
};

function DemoContent {
  const dialogs = useDialogs;
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDelete = async  => {
    const id = await dialogs.prompt('Enter the ID to delete', {
      okText: 'Delete',
      cancelText: 'Cancel',
    });

    if (id) {
      const deleteConfirmed = await dialogs.confirm(
        `Are you sure you want to delete "${id}"?`,
      );
      if (deleteConfirmed) {
        try {
          setIsDeleting(true);
          await mockApiDelete(id);
          dialogs.alert('Deleted!');
        } catch (error) {
          const message = error instanceof Error ? error.message : 'Unknown error';
          await dialogs.open(MyCustomDialog, { id, error: message });
        } finally {
          setIsDeleting(false);
        }
      }
    }
  };
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
      <div style={{ display: 'flex', gap: 16 }}>
        <Button variant="contained" loading={isDeleting} onClick={handleDelete}>
          Delete
        </Button>
      </div>
    </div>
  );
}

export default function ToolpadDialogsNoSnap {
  return (
    <DialogsProvider>
      <DemoContent />
    </DialogsProvider>
  );
}  

```
import \* as React from 'react';
import { DialogsProvider, useDialogs, DialogProps } from '@toolpad/core/useDialogs';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Alert from '@mui/material/Alert';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';

interface DeleteError {
 id: string \| null;
 error: string \| null;
}

function MyCustomDialog({ open, onClose, payload }: DialogProps\<DeleteError\>) {
 return (
 \<Dialog fullWidth open\={open} onClose\={ \=\> onClose}\>
 \<DialogTitle\>Custom Error Handler\</DialogTitle\>
 \<DialogContent\>
 \<Alert severity\="error"\>
 {\`An error occurred while deleting item "${payload.id}":\`}
 \<pre\>{payload.error}\</pre\>
 \</Alert\>
 \</DialogContent\>
 \<DialogActions\>
 \<Button onClick\={ \=\> onClose}\>Close me\</Button\>
 \</DialogActions\>
 \</Dialog\>
 );
}

const mockApiDelete \= async (id: string \| null) \=\> {
 return new Promise((resolve, reject) \=\> {
 setTimeout( \=\> {
 if (!id) {
 reject(new Error('ID is required'));
 } else if (parseInt(id, 10\) % 2 \=\=\= 0\) {
 console.log('id', parseInt(id, 10\));
 resolve(true);
 } else if (parseInt(id, 10\) % 2 \=\=\= 1\) {
 reject(new Error('Can not delete odd numbered elements'));
 } else if (Number.isNaN(parseInt(id, 10\))) {
 reject(new Error('ID must be a number'));
 } else {
 reject(new Error('Unknown error'));
 }
 }, 1000\);
 });
};

function DemoContent {
 const dialogs \= useDialogs;
 const \[isDeleting, setIsDeleting] \= React.useState(false);

 const handleDelete \= async  \=\> {
 const id \= await dialogs.prompt('Enter the ID to delete', {
 okText: 'Delete',
 cancelText: 'Cancel',
 });

 if (id) {
 const deleteConfirmed \= await dialogs.confirm(
 \`Are you sure you want to delete "${id}"?\`,
 );
 if (deleteConfirmed) {
 try {
 setIsDeleting(true);
 await mockApiDelete(id);
 dialogs.alert('Deleted!');
 } catch (error) {
 const message \= error instanceof Error ? error.message : 'Unknown error';
 await dialogs.open(MyCustomDialog, { id, error: message });
 } finally {
 setIsDeleting(false);
 }
 }
 }
 };
 return (
 \<div style\={{ display: 'flex', flexDirection: 'column', gap: 16 }}\>
 \<div style\={{ display: 'flex', gap: 16 }}\>
 \<Button variant\="contained" loading\={isDeleting} onClick\={handleDelete}\>
 Delete
 \</Button\>
 \</div\>
 \</div\>
 );
}

export default function ToolpadDialogsNoSnap {
 return (
 \<DialogsProvider\>
 \<DemoContent /\>
 \</DialogsProvider\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon
```
const handleDelete = async  => {
  const id = await dialogs.prompt('Enter the ID to delete', {
    okText: 'Delete',
    cancelText: 'Cancel',
  });

  if (id) {
    const deleteConfirmed = await dialogs.confirm(
      `Are you sure you want to delete "${id}"?`,
    );
    if (deleteConfirmed) {
      try {
        setIsDeleting(true);
        await mockApiDelete(id);
        dialogs.alert('Deleted!');
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Unknown error';
        await dialogs.open(MyCustomDialog, { id, error: message });
      } finally {
        setIsDeleting(false);
      }
    }
  }
};

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Dialog />`
* `<DialogActions />`
* `<DialogContent />`
* `<DialogContentText />`
* `<DialogTitle />`
* `<Slide />`



