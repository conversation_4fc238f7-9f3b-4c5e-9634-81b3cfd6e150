import { createTheme } from '@mui/material/styles';

// Extend the theme to include custom properties
declare module '@mui/material/styles' {
  interface TypeBackground {
    menu?: string;
    content?: string;
  }
}

// Create a theme instance
const theme = createTheme({
  palette: {
    primary: {
      main: '#3f50b5',
      light: '#757ce8',
      dark: '#002884',
      contrastText: '#fff',
    },
    secondary: {
      main: '#f44336',
      light: '#ff7961',
      dark: '#ba000d',
      contrastText: '#000',
    },
    background: {
      default: '#f5f5f5',
      paper: '#fff',
      menu: '#fff',
      content: '#f5f5f5',
    },
    text: {
      primary: '#000000',
      secondary: '#606266',
    },
  },
  typography: {
    fontFamily: "'Microsoft YaHei', sans-serif",
    fontSize: 14,
    button: {
      textTransform: 'none',
    },
  },
  components: {
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#F9F9F9',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderColor: '#E8E8E8',
          fontSize: '12px',
          padding: '16px',
        },
        head: {
          fontWeight: 'bold',
          color: '#606266',
          fontSize: '14px',
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '4px',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#fff',
          width: 256,
          border: 'none',
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiList: {
      styleOverrides: {
        root: {
          padding: 0,
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          fontFamily: "'Microsoft YaHei', sans-serif",
          fontSize: '14px',
          fontWeight: 400,
          color: '#000000',
          padding: '10px 16px',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
          '&.Mui-selected': {
            backgroundColor: 'rgba(63, 80, 181, 0.1)',
            color: '#3f50b5',
            '&:hover': {
              backgroundColor: 'rgba(63, 80, 181, 0.15)',
            },
          },
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: 36,
          color: '#000000',
        },
      },
    },
    MuiListItemText: {
      styleOverrides: {
        primary: {
          fontSize: '14px',
          fontWeight: 400,
          color: '#000000',
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          margin: '8px 0',
        },
      },
    },
  },
});

export default theme;
