import { useEffect } from 'react';
import { useSystemDictionaryStore } from '@/stores/systemDictionaryStore';

/**
 * Custom hook to access and manage system dictionary data
 * 
 * @param autoFetch - Whether to automatically fetch dictionary data on mount (default: true)
 * @returns The system dictionary store state and actions
 */
export const useSystemDictionary = (autoFetch = true) => {
  const {
    // State
    modelTypes,
    modelStatuses,
    databaseTypes,
    databaseStatuses,
    mcpStatuses,
    toolTypes,
    toolStatuses,
    flowTypes,
    flowStatuses,
    ragTypes,
    ragStatuses,
    agentTypes,
    agentStatuses,
    deviceTypes,
    deviceStatuses,
    isLoading,
    error,
    
    // Actions
    fetchDictionary,
    setModelTypes,
    setModelStatuses,
    setDatabaseTypes,
    setDatabaseStatuses,
    setMCPStatuses,
    setToolTypes,
    setToolStatuses,
    setFlowTypes,
    setFlowStatuses,
    setRAGTypes,
    setRAGStatuses,
    setAgentTypes,
    setAgentStatuses,
    setDeviceTypes,
    setDeviceStatuses,
    reset,
  } = useSystemDictionaryStore();

  // Auto-fetch dictionary data on mount if autoFetch is true
  useEffect(() => {
    if (autoFetch) {
      fetchDictionary();
    }
  }, [autoFetch, fetchDictionary]);

  return {
    // State
    modelTypes,
    modelStatuses,
    databaseTypes,
    databaseStatuses,
    mcpStatuses,
    toolTypes,
    toolStatuses,
    flowTypes,
    flowStatuses,
    ragTypes,
    ragStatuses,
    agentTypes,
    agentStatuses,
    deviceTypes,
    deviceStatuses,
    isLoading,
    error,
    
    // Actions
    fetchDictionary,
    setModelTypes,
    setModelStatuses,
    setDatabaseTypes,
    setDatabaseStatuses,
    setMCPStatuses,
    setToolTypes,
    setToolStatuses,
    setFlowTypes,
    setFlowStatuses,
    setRAGTypes,
    setRAGStatuses,
    setAgentTypes,
    setAgentStatuses,
    setDeviceTypes,
    setDeviceStatuses,
    reset,
  };
};

export default useSystemDictionary;
