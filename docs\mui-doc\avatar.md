Avatar
======

Avatars are found throughout material design with uses in everything from tables to dialog menus.


High\-Performance Charts Across WPF, JavaScript \& Mobile – Try Today For Your Complex Data Projects.

ads via Carbon



* Feedback
* Bundle size
* Source
* Figma
* Sketch

Image avatars
-------------

Image avatars can be created by passing standard `img` props `src` or `srcSet` to the component.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
<Avatar alt="<PERSON> Howard" src="/static/images/avatar/2.jpg" />
<Avatar alt="<PERSON> Baker" src="/static/images/avatar/3.jpg" />  

```
\<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
\<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
\<Avatar alt\="<PERSON> Baker" src\="/static/images/avatar/3\.jpg" /\>Press `Enter` to start editingLetter avatars
--------------

Avatars containing simple characters can be created by passing a string as `children`.


HNOPJSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar>H</Avatar>
<Avatar sx={{ bgcolor: deepOrange[500] }}>N</Avatar>
<Avatar sx={{ bgcolor: deepPurple[500] }}>OP</Avatar>  

```
\<Avatar\>H\</Avatar\>
\<Avatar sx\={{ bgcolor: deepOrange\[500] }}\>N\</Avatar\>
\<Avatar sx\={{ bgcolor: deepPurple\[500] }}\>OP\</Avatar\>Press `Enter` to start editingYou can use different background colors for the avatar.
The following demo generates the color based on the name of the person.


KDJWTNJSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar {...stringAvatar('Kent Dodds')} />
<Avatar {...stringAvatar('Jed Watson')} />
<Avatar {...stringAvatar('Tim Neutkens')} />  

```
\<Avatar {...stringAvatar('Kent Dodds')} /\>
\<Avatar {...stringAvatar('Jed Watson')} /\>
\<Avatar {...stringAvatar('Tim Neutkens')} /\>Press `Enter` to start editingSizes
-----

You can change the size of the avatar with the `height` and `width` CSS properties.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar
  alt="Remy Sharp"
  src="/static/images/avatar/1.jpg"
  sx={{ width: 24, height: 24 }}
/>
<Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
<Avatar
  alt="Remy Sharp"
  src="/static/images/avatar/1.jpg"
  sx={{ width: 56, height: 56 }}
/>  

```
\<Avatar
 alt\="Remy Sharp"
 src\="/static/images/avatar/1\.jpg"
 sx\={{ width: 24, height: 24 }}
/\>
\<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
\<Avatar
 alt\="Remy Sharp"
 src\="/static/images/avatar/1\.jpg"
 sx\={{ width: 56, height: 56 }}
/\>Press `Enter` to start editingIcon avatars
------------

Icon avatars are created by passing an icon as `children`.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar>
  <FolderIcon />
</Avatar>
<Avatar sx={{ bgcolor: pink[500] }}>
  <PageviewIcon />
</Avatar>
<Avatar sx={{ bgcolor: green[500] }}>
  <AssignmentIcon />
</Avatar>  

```
\<Avatar\>
 \<FolderIcon /\>
\</Avatar\>
\<Avatar sx\={{ bgcolor: pink\[500] }}\>
 \<PageviewIcon /\>
\</Avatar\>
\<Avatar sx\={{ bgcolor: green\[500] }}\>
 \<AssignmentIcon /\>
\</Avatar\>Press `Enter` to start editingVariants
--------

If you need square or rounded avatars, use the `variant` prop.


NJSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar sx={{ bgcolor: deepOrange[500] }} variant="square">
  N
</Avatar>
<Avatar sx={{ bgcolor: green[500] }} variant="rounded">
  <AssignmentIcon />
</Avatar>  

```
\<Avatar sx\={{ bgcolor: deepOrange\[500] }} variant\="square"\>
 N
\</Avatar\>
\<Avatar sx\={{ bgcolor: green\[500] }} variant\="rounded"\>
 \<AssignmentIcon /\>
\</Avatar\>Press `Enter` to start editingFallbacks
---------

If there is an error loading the avatar image, the component falls back to an alternative in the following order:


* the provided children
* the first letter of the `alt` text
* a generic avatar icon


BRJSTSExpand codeCopy(or Ctrl \+ C)
```
<Avatar
  sx={{ bgcolor: deepOrange[500] }}
  alt="Remy Sharp"
  src="/broken-image.jpg"
>
  B
</Avatar>
<Avatar
  sx={{ bgcolor: deepOrange[500] }}
  alt="Remy Sharp"
  src="/broken-image.jpg"
/>
<Avatar src="/broken-image.jpg" />  

```
\<Avatar
 sx\={{ bgcolor: deepOrange\[500] }}
 alt\="Remy Sharp"
 src\="/broken\-image.jpg"
\>
 B
\</Avatar\>
\<Avatar
 sx\={{ bgcolor: deepOrange\[500] }}
 alt\="Remy Sharp"
 src\="/broken\-image.jpg"
/\>
\<Avatar src\="/broken\-image.jpg" /\>Press `Enter` to start editingGrouped
-------

`AvatarGroup` renders its children as a stack. Use the `max` prop to limit the number of avatars.


\+2JSTSExpand codeCopy(or Ctrl \+ C)
```
<AvatarGroup max={4}>
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Cindy Baker" src="/static/images/avatar/3.jpg" />
  <Avatar alt="Agnes Walker" src="/static/images/avatar/4.jpg" />
  <Avatar alt="Trevor Henderson" src="/static/images/avatar/5.jpg" />
</AvatarGroup>  

```
\<AvatarGroup max\={4}\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Cindy Baker" src\="/static/images/avatar/3\.jpg" /\>
 \<Avatar alt\="Agnes Walker" src\="/static/images/avatar/4\.jpg" /\>
 \<Avatar alt\="Trevor Henderson" src\="/static/images/avatar/5\.jpg" /\>
\</AvatarGroup\>Press `Enter` to start editing### Total avatars

If you need to control the total number of avatars not shown, you can use the `total` prop.


\+20JSTSExpand codeCopy(or Ctrl \+ C)
```
<AvatarGroup total={24}>
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Agnes Walker" src="/static/images/avatar/4.jpg" />
  <Avatar alt="Trevor Henderson" src="/static/images/avatar/5.jpg" />
</AvatarGroup>  

```
\<AvatarGroup total\={24}\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Agnes Walker" src\="/static/images/avatar/4\.jpg" /\>
 \<Avatar alt\="Trevor Henderson" src\="/static/images/avatar/5\.jpg" /\>
\</AvatarGroup\>Press `Enter` to start editing### Custom surplus

Set the `renderSurplus` prop as a callback to customize the surplus avatar. The callback will receive the surplus number as an argument based on the children and the `max` prop, and should return a `React.ReactNode`.


The `renderSurplus` prop is useful when you need to render the surplus based on the data sent from the server.


\+4kJSTSExpand codeCopy(or Ctrl \+ C)
```
<AvatarGroup
  renderSurplus={(surplus) => <span>+{surplus.toString[0]}k</span>}
  total={4251}
>
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Agnes Walker" src="/static/images/avatar/4.jpg" />
  <Avatar alt="Trevor Henderson" src="/static/images/avatar/5.jpg" />
</AvatarGroup>  

```
\<AvatarGroup
 renderSurplus\={(surplus) \=\> \<span\>\+{surplus.toString\[0]}k\</span\>}
 total\={4251}
\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Agnes Walker" src\="/static/images/avatar/4\.jpg" /\>
 \<Avatar alt\="Trevor Henderson" src\="/static/images/avatar/5\.jpg" /\>
\</AvatarGroup\>Press `Enter` to start editing### Spacing

You can change the spacing between avatars using the `spacing` prop. You can use one of the presets (`"medium"`, the default, or `"small"`) or set a custom numeric value.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<AvatarGroup spacing="medium">
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Cindy Baker" src="/static/images/avatar/3.jpg" />
</AvatarGroup>
<AvatarGroup spacing="small">
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Cindy Baker" src="/static/images/avatar/3.jpg" />
</AvatarGroup>
<AvatarGroup spacing={24}>
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
  <Avatar alt="Cindy Baker" src="/static/images/avatar/3.jpg" />
</AvatarGroup>  

```
\<AvatarGroup spacing\="medium"\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Cindy Baker" src\="/static/images/avatar/3\.jpg" /\>
\</AvatarGroup\>
\<AvatarGroup spacing\="small"\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Cindy Baker" src\="/static/images/avatar/3\.jpg" /\>
\</AvatarGroup\>
\<AvatarGroup spacing\={24}\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
 \<Avatar alt\="Cindy Baker" src\="/static/images/avatar/3\.jpg" /\>
\</AvatarGroup\>Press `Enter` to start editingWith badge
----------

JSTSExpand codeCopy(or Ctrl \+ C)
```
<StyledBadge
  overlap="circular"
  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
  variant="dot"
>
  <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
</StyledBadge>
<Badge
  overlap="circular"
  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
  badgeContent={
    <SmallAvatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
  }
>
  <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
</Badge>  

```
\<StyledBadge
 overlap\="circular"
 anchorOrigin\={{ vertical: 'bottom', horizontal: 'right' }}
 variant\="dot"
\>
 \<Avatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
\</StyledBadge\>
\<Badge
 overlap\="circular"
 anchorOrigin\={{ vertical: 'bottom', horizontal: 'right' }}
 badgeContent\={
 \<SmallAvatar alt\="Remy Sharp" src\="/static/images/avatar/1\.jpg" /\>
 }
\>
 \<Avatar alt\="Travis Howard" src\="/static/images/avatar/2\.jpg" /\>
\</Badge\>Press `Enter` to start editingAvatar upload
-------------

JSTSShow codeAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Avatar />`
* `<AvatarGroup />`
* `<Badge />`



