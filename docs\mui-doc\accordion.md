Accordion
=========

The Accordion component lets users show and hide sections of related content on a page.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Introduction
------------

The Material UI Accordion component includes several complementary utility components to handle various use cases:


* Accordion: the wrapper for grouping related components.
* Accordion Summary: the wrapper for the Accordion header, which expands or collapses the content when clicked.
* Accordion Details: the wrapper for the Accordion content.
* Accordion Actions: an optional wrapper that groups a set of buttons.


### Accordion 1

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.### Accordion 2

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.### Accordion Actions

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.CancelAgreeJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';

export default function AccordionUsage {
  return (
    <div>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">Accordion 1</Typography>
        </AccordionSummary>
        <AccordionDetails>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget.
        </AccordionDetails>
      </Accordion>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <Typography component="span">Accordion 2</Typography>
        </AccordionSummary>
        <AccordionDetails>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget.
        </AccordionDetails>
      </Accordion>
      <Accordion defaultExpanded>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel3-content"
          id="panel3-header"
        >
          <Typography component="span">Accordion Actions</Typography>
        </AccordionSummary>
        <AccordionDetails>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget.
        </AccordionDetails>
        <AccordionActions>
          <Button>Cancel</Button>
          <Button>Agree</Button>
        </AccordionActions>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';
import Button from '@mui/material/Button';

export default function AccordionUsage {
 return (
 \<div\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel1\-content"
 id\="panel1\-header"
 \>
 \<Typography component\="span"\>Accordion 1\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel2\-content"
 id\="panel2\-header"
 \>
 \<Typography component\="span"\>Accordion 2\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion defaultExpanded\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel3\-content"
 id\="panel3\-header"
 \>
 \<Typography component\="span"\>Accordion Actions\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</AccordionDetails\>
 \<AccordionActions\>
 \<Button\>Cancel\</Button\>
 \<Button\>Agree\</Button\>
 \</AccordionActions\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Boost your app’s Identity with Auth0\. Now with Custom Domain, Passwordless, and more!ad by Carbon
This component is no longer documented in the Material Design guidelines, but Material UI will continue to support it.


Basics
------


```
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';

```
CopyCopied(or Ctrl \+ C)
### Expand icon

Use the `expandIcon` prop on the Accordion Summary component to change the expand indicator icon.
The component handles the turning upside\-down transition automatically.


### Accordion 1

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Accordion 2

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

export default function AccordionExpandIcon {
  return (
    <div>
      <Accordion>
        <AccordionSummary
          expandIcon={<ArrowDownwardIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">Accordion 1</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion>
        <AccordionSummary
          expandIcon={<ArrowDropDownIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <Typography component="span">Accordion 2</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ArrowDownwardIcon from '@mui/icons\-material/ArrowDownward';
import ArrowDropDownIcon from '@mui/icons\-material/ArrowDropDown';

export default function AccordionExpandIcon {
 return (
 \<div\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ArrowDownwardIcon /\>}
 aria\-controls\="panel1\-content"
 id\="panel1\-header"
 \>
 \<Typography component\="span"\>Accordion 1\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ArrowDropDownIcon /\>}
 aria\-controls\="panel2\-content"
 id\="panel2\-header"
 \>
 \<Typography component\="span"\>Accordion 2\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by Carbon### Expanded by default

Use the `defaultExpanded` prop on the Accordion component to have it opened by default.


### Expanded by default

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Header

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export default function AccordionExpandDefault {
  return (
    <div>
      <Accordion defaultExpanded>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">Expanded by default</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <Typography component="span">Header</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';

export default function AccordionExpandDefault {
 return (
 \<div\>
 \<Accordion defaultExpanded\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel1\-content"
 id\="panel1\-header"
 \>
 \<Typography component\="span"\>Expanded by default\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel2\-content"
 id\="panel2\-header"
 \>
 \<Typography component\="span"\>Header\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by Carbon### Transition

Use the `slots.transition` and `slotProps.transition` props to change the Accordion's default transition.


### Custom transition using Fade

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Default transition using Collapse

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion, {
  AccordionSlots,
  accordionClasses,
} from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails, {
  accordionDetailsClasses,
} from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Fade from '@mui/material/Fade';

export default function AccordionTransition {
  const [expanded, setExpanded] = React.useState(false);

  const handleExpansion =  => {
    setExpanded((prevExpanded) => !prevExpanded);
  };

  return (
    <div>
      <Accordion
        expanded={expanded}
        onChange={handleExpansion}
        slots={{ transition: Fade as AccordionSlots['transition'] }}
        slotProps={{ transition: { timeout: 400 } }}
        sx={[
          expanded
            ? {
                [`& .${accordionClasses.region}`]: {
                  height: 'auto',
                },
                [`& .${accordionDetailsClasses.root}`]: {
                  display: 'block',
                },
              }
            : {
                [`& .${accordionClasses.region}`]: {
                  height: 0,
                },
                [`& .${accordionDetailsClasses.root}`]: {
                  display: 'none',
                },
              },
        ]}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">Custom transition using Fade</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <Typography component="span">Default transition using Collapse</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion, {
 AccordionSlots,
 accordionClasses,
} from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails, {
 accordionDetailsClasses,
} from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';
import Fade from '@mui/material/Fade';

export default function AccordionTransition {
 const \[expanded, setExpanded] \= React.useState(false);

 const handleExpansion \=  \=\> {
 setExpanded((prevExpanded) \=\> !prevExpanded);
 };

 return (
 \<div\>
 \<Accordion
 expanded\={expanded}
 onChange\={handleExpansion}
 slots\={{ transition: Fade as AccordionSlots\['transition'] }}
 slotProps\={{ transition: { timeout: 400 } }}
 sx\={\[
 expanded
 ? {
 \[\`\& .${accordionClasses.region}\`]: {
 height: 'auto',
 },
 \[\`\& .${accordionDetailsClasses.root}\`]: {
 display: 'block',
 },
 }
 : {
 \[\`\& .${accordionClasses.region}\`]: {
 height: 0,
 },
 \[\`\& .${accordionDetailsClasses.root}\`]: {
 display: 'none',
 },
 },
 ]}
 \>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel1\-content"
 id\="panel1\-header"
 \>
 \<Typography component\="span"\>Custom transition using Fade\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel2\-content"
 id\="panel2\-header"
 \>
 \<Typography component\="span"\>Default transition using Collapse\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- Software. Faster. All in one platform. Give GitLab a try for free.ad by Carbon### Disabled item

Use the `disabled` prop on the Accordion component to disable interaction and focus.


### Accordion 1

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Accordion 2

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Disabled Accordion

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export default function DisabledAccordion {
  return (
    <div>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography component="span">Accordion 1</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <Typography component="span">Accordion 2</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion disabled>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel3-content"
          id="panel3-header"
        >
          <Typography component="span">Disabled Accordion</Typography>
        </AccordionSummary>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';

export default function DisabledAccordion {
 return (
 \<div\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel1\-content"
 id\="panel1\-header"
 \>
 \<Typography component\="span"\>Accordion 1\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel2\-content"
 id\="panel2\-header"
 \>
 \<Typography component\="span"\>Accordion 2\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion disabled\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel3\-content"
 id\="panel3\-header"
 \>
 \<Typography component\="span"\>Disabled Accordion\</Typography\>
 \</AccordionSummary\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by Carbon### Controlled Accordion

The Accordion component can be controlled or uncontrolled.


### General settingsI am an accordion

Nulla facilisi. Phasellus sollicitudin nulla et quam mattis feugiat. Aliquam eget maximus est, id dignissim quam.

### UsersYou are currently not an owner

Donec placerat, lectus sed mattis semper, neque lectus feugiat lectus, varius pulvinar diam eros in elit. Pellentesque convallis laoreet laoreet.

### Advanced settingsFiltering has been entirely disabled for whole web server

Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit amet egestas eros, vitae egestas augue. Duis vel est augue.

### Personal data

Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit amet egestas eros, vitae egestas augue. Duis vel est augue.

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export default function ControlledAccordions {
  const [expanded, setExpanded] = React.useState<string | false>(false);

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  return (
    <div>
      <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bh-content"
          id="panel1bh-header"
        >
          <Typography component="span" sx={{ width: '33%', flexShrink: 0 }}>
            General settings
          </Typography>
          <Typography component="span" sx={{ color: 'text.secondary' }}>
            I am an accordion
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Nulla facilisi. Phasellus sollicitudin nulla et quam mattis feugiat.
            Aliquam eget maximus est, id dignissim quam.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2bh-content"
          id="panel2bh-header"
        >
          <Typography component="span" sx={{ width: '33%', flexShrink: 0 }}>
            Users
          </Typography>
          <Typography component="span" sx={{ color: 'text.secondary' }}>
            You are currently not an owner
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Donec placerat, lectus sed mattis semper, neque lectus feugiat lectus,
            varius pulvinar diam eros in elit. Pellentesque convallis laoreet
            laoreet.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel3bh-content"
          id="panel3bh-header"
        >
          <Typography component="span" sx={{ width: '33%', flexShrink: 0 }}>
            Advanced settings
          </Typography>
          <Typography component="span" sx={{ color: 'text.secondary' }}>
            Filtering has been entirely disabled for whole web server
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit
            amet egestas eros, vitae egestas augue. Duis vel est augue.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'panel4'} onChange={handleChange('panel4')}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel4bh-content"
          id="panel4bh-header"
        >
          <Typography component="span" sx={{ width: '33%', flexShrink: 0 }}>
            Personal data
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit
            amet egestas eros, vitae egestas augue. Duis vel est augue.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';

export default function ControlledAccordions {
 const \[expanded, setExpanded] \= React.useState\<string \| false\>(false);

 const handleChange \=
 (panel: string) \=\> (event: React.SyntheticEvent, isExpanded: boolean) \=\> {
 setExpanded(isExpanded ? panel : false);
 };

 return (
 \<div\>
 \<Accordion expanded\={expanded \=\=\= 'panel1'} onChange\={handleChange('panel1')}\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel1bh\-content"
 id\="panel1bh\-header"
 \>
 \<Typography component\="span" sx\={{ width: '33%', flexShrink: 0 }}\>
 General settings
 \</Typography\>
 \<Typography component\="span" sx\={{ color: 'text.secondary' }}\>
 I am an accordion
 \</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Nulla facilisi. Phasellus sollicitudin nulla et quam mattis feugiat.
 Aliquam eget maximus est, id dignissim quam.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion expanded\={expanded \=\=\= 'panel2'} onChange\={handleChange('panel2')}\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel2bh\-content"
 id\="panel2bh\-header"
 \>
 \<Typography component\="span" sx\={{ width: '33%', flexShrink: 0 }}\>
 Users
 \</Typography\>
 \<Typography component\="span" sx\={{ color: 'text.secondary' }}\>
 You are currently not an owner
 \</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Donec placerat, lectus sed mattis semper, neque lectus feugiat lectus,
 varius pulvinar diam eros in elit. Pellentesque convallis laoreet
 laoreet.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion expanded\={expanded \=\=\= 'panel3'} onChange\={handleChange('panel3')}\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel3bh\-content"
 id\="panel3bh\-header"
 \>
 \<Typography component\="span" sx\={{ width: '33%', flexShrink: 0 }}\>
 Advanced settings
 \</Typography\>
 \<Typography component\="span" sx\={{ color: 'text.secondary' }}\>
 Filtering has been entirely disabled for whole web server
 \</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit
 amet egestas eros, vitae egestas augue. Duis vel est augue.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion expanded\={expanded \=\=\= 'panel4'} onChange\={handleChange('panel4')}\>
 \<AccordionSummary
 expandIcon\={\<ExpandMoreIcon /\>}
 aria\-controls\="panel4bh\-content"
 id\="panel4bh\-header"
 \>
 \<Typography component\="span" sx\={{ width: '33%', flexShrink: 0 }}\>
 Personal data
 \</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Nunc vitae orci ultricies, auctor nunc in, volutpat nisl. Integer sit
 amet egestas eros, vitae egestas augue. Duis vel est augue.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by Carbon
* A component is **controlled** when it's managed by its parent using props.
* A component is **uncontrolled** when it's managed by its own local state.


Learn more about controlled and uncontrolled components in the React documentation.


Customization
-------------

### Only one expanded at a time

Use the `expanded` prop with React's `useState` hook to allow only one Accordion item to be expanded at a time.
The demo below also shows a bit of visual customization.


### Collapsible Group Item \#1

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Collapsible Group Item \#2

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

### Collapsible Group Item \#3

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import MuiAccordionSummary, {
  AccordionSummaryProps,
  accordionSummaryClasses,
} from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&::before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: 'rgba(0, 0, 0, .03)',
  flexDirection: 'row-reverse',
  [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
    {
      transform: 'rotate(90deg)',
    },
  [`& .${accordionSummaryClasses.content}`]: {
    marginLeft: theme.spacing(1),
  },
  ...theme.applyStyles('dark', {
    backgroundColor: 'rgba(255, 255, 255, .05)',
  }),
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: '1px solid rgba(0, 0, 0, .125)',
}));

export default function CustomizedAccordions {
  const [expanded, setExpanded] = React.useState<string | false>('panel1');

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : false);
    };

  return (
    <div>
      <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <Typography component="span">Collapsible Group Item #1</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
            sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
            sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
        <AccordionSummary aria-controls="panel2d-content" id="panel2d-header">
          <Typography component="span">Collapsible Group Item #2</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
            sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
            sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
        <AccordionSummary aria-controls="panel3d-content" id="panel3d-header">
          <Typography component="span">Collapsible Group Item #3</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
            sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
            sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}  

```
import \* as React from 'react';
import { styled } from '@mui/material/styles';
import ArrowForwardIosSharpIcon from '@mui/icons\-material/ArrowForwardIosSharp';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import MuiAccordionSummary, {
 AccordionSummaryProps,
 accordionSummaryClasses,
} from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';

const Accordion \= styled((props: AccordionProps) \=\> (
 \<MuiAccordion disableGutters elevation\={0} square {...props} /\>
))(({ theme }) \=\> ({
 border: \`1px solid ${theme.palette.divider}\`,
 '\&:not(:last\-child)': {
 borderBottom: 0,
 },
 '\&::before': {
 display: 'none',
 },
}));

const AccordionSummary \= styled((props: AccordionSummaryProps) \=\> (
 \<MuiAccordionSummary
 expandIcon\={\<ArrowForwardIosSharpIcon sx\={{ fontSize: '0\.9rem' }} /\>}
 {...props}
 /\>
))(({ theme }) \=\> ({
 backgroundColor: 'rgba(0, 0, 0, .03\)',
 flexDirection: 'row\-reverse',
 \[\`\& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}\`]:
 {
 transform: 'rotate(90deg)',
 },
 \[\`\& .${accordionSummaryClasses.content}\`]: {
 marginLeft: theme.spacing(1\),
 },
 ...theme.applyStyles('dark', {
 backgroundColor: 'rgba(255, 255, 255, .05\)',
 }),
}));

const AccordionDetails \= styled(MuiAccordionDetails)(({ theme }) \=\> ({
 padding: theme.spacing(2\),
 borderTop: '1px solid rgba(0, 0, 0, .125\)',
}));

export default function CustomizedAccordions {
 const \[expanded, setExpanded] \= React.useState\<string \| false\>('panel1');

 const handleChange \=
 (panel: string) \=\> (event: React.SyntheticEvent, newExpanded: boolean) \=\> {
 setExpanded(newExpanded ? panel : false);
 };

 return (
 \<div\>
 \<Accordion expanded\={expanded \=\=\= 'panel1'} onChange\={handleChange('panel1')}\>
 \<AccordionSummary aria\-controls\="panel1d\-content" id\="panel1d\-header"\>
 \<Typography component\="span"\>Collapsible Group Item \#1\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
 sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
 sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion expanded\={expanded \=\=\= 'panel2'} onChange\={handleChange('panel2')}\>
 \<AccordionSummary aria\-controls\="panel2d\-content" id\="panel2d\-header"\>
 \<Typography component\="span"\>Collapsible Group Item \#2\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
 sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
 sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \<Accordion expanded\={expanded \=\=\= 'panel3'} onChange\={handleChange('panel3')}\>
 \<AccordionSummary aria\-controls\="panel3d\-content" id\="panel3d\-header"\>
 \<Typography component\="span"\>Collapsible Group Item \#3\</Typography\>
 \</AccordionSummary\>
 \<AccordionDetails\>
 \<Typography\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
 malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum dolor
 sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
 sit amet blandit leo lobortis eget.
 \</Typography\>
 \</AccordionDetails\>
 \</Accordion\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- You asked, we delivered! Our Free Plan now includes a Custom Domain, 5 Actions, and 25,000 MAUs.ad by Carbon### Changing heading level

By default, the Accordion uses an `h3` element for the heading. You can change the heading element using the `slotProps.heading.component` prop to ensure the correct heading hierarchy in your document.



```
<Accordion slotProps={{ heading: { component: 'h4' } }}>
  <AccordionSummary
    expandIcon={<ExpandMoreIcon />}
    aria-controls="panel1-content"
    id="panel1-header"
  >
    Accordion
  </AccordionSummary>
  <AccordionDetails>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada
    lacus ex, sit amet blandit leo lobortis eget.
  </AccordionDetails>
</Accordion>

```
CopyCopied(or Ctrl \+ C)
Performance
-----------

The Accordion content is mounted by default even if it's not expanded.
This default behavior has server\-side rendering and SEO in mind.


If you render the Accordion Details with a big component tree nested inside, or if you have many Accordions, you may want to change this behavior by setting `unmountOnExit` to `true` inside the `slotProps.transition` prop to improve performance:



```
<Accordion slotProps={{ transition: { unmountOnExit: true } }} />

```
CopyCopied(or Ctrl \+ C)
Accessibility
-------------

The WAI\-ARIA guidelines for accordions recommend setting an `id` and `aria-controls`, which in this case would apply to the Accordion Summary component.
The Accordion component then derives the necessary `aria-labelledby` and `id` from its content.



```
<Accordion>
  <AccordionSummary id="panel-header" aria-controls="panel-content">
    Header
  </AccordionSummary>
  <AccordionDetails>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
  </AccordionDetails>
</Accordion>

```
CopyCopied(or Ctrl \+ C)
Anatomy
-------

The Accordion component consists of a root `<div>` that contains the Accordion Summary, Accordion Details, and optional Accordion Actions for action buttons.



```
<div class="MuiAccordion-root">
  <h3 class="MuiAccordion-heading">
    <button class="MuiButtonBase-root MuiAccordionSummary-root" aria-expanded="">
      <!-- Accordion summary goes here -->
    </button>
  </h3>
  <div class="MuiAccordion-region" role="region">
    <div class="MuiAccordionDetails-root">
      <!-- Accordion content goes here -->
    </div>
  </div>
</div>

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Accordion />`
* `<AccordionActions />`
* `<AccordionDetails />`
* `<AccordionSummary />`



