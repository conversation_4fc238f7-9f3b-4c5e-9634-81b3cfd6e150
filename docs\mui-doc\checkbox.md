Checkbox
========

Checkboxes allow the user to select one or more items from a set.**Premium Themes**. Kickstart your application development with a ready\-made theme.ad by MUI


Checkboxes can be used to turn an option on or off.


If you have multiple options appearing in a list,
you can preserve space by using checkboxes instead of on/off switches.
If you have a single option, avoid using a checkbox and use an on/off switch instead.


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Basic checkboxes
----------------

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Checkbox {...label} defaultChecked />
<Checkbox {...label} />
<Checkbox {...label} disabled />
<Checkbox {...label} disabled checked />  

```
\<Checkbox {...label} defaultChecked /\>
\<Checkbox {...label} /\>
\<Checkbox {...label} disabled /\>
\<Checkbox {...label} disabled checked /\>Press `Enter` to start editingLabel
-----

You can provide a label to the `Checkbox` thanks to the `FormControlLabel` component.


LabelRequired \*DisabledJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormGroup>
  <FormControlLabel control={<Checkbox defaultChecked />} label="Label" />
  <FormControlLabel required control={<Checkbox />} label="Required" />
  <FormControlLabel disabled control={<Checkbox />} label="Disabled" />
</FormGroup>  

```
\<FormGroup\>
 \<FormControlLabel control\={\<Checkbox defaultChecked /\>} label\="Label" /\>
 \<FormControlLabel required control\={\<Checkbox /\>} label\="Required" /\>
 \<FormControlLabel disabled control\={\<Checkbox /\>} label\="Disabled" /\>
\</FormGroup\>Press `Enter` to start editingSize
----

Use the `size` prop or customize the font size of the svg icons to change the size of the checkboxes.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Checkbox {...label} defaultChecked size="small" />
<Checkbox {...label} defaultChecked />
<Checkbox
  {...label}
  defaultChecked
  sx={{ '& .MuiSvgIcon-root': { fontSize: 28 } }}
/>  

```
\<Checkbox {...label} defaultChecked size\="small" /\>
\<Checkbox {...label} defaultChecked /\>
\<Checkbox
 {...label}
 defaultChecked
 sx\={{ '\& .MuiSvgIcon\-root': { fontSize: 28 } }}
/\>Press `Enter` to start editingColor
-----

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Checkbox {...label} defaultChecked />
<Checkbox {...label} defaultChecked color="secondary" />
<Checkbox {...label} defaultChecked color="success" />
<Checkbox {...label} defaultChecked color="default" />
<Checkbox
  {...label}
  defaultChecked
  sx={{
    color: pink[800],
    '&.Mui-checked': {
      color: pink[600],
    },
  }}
/>  

```
\<Checkbox {...label} defaultChecked /\>
\<Checkbox {...label} defaultChecked color\="secondary" /\>
\<Checkbox {...label} defaultChecked color\="success" /\>
\<Checkbox {...label} defaultChecked color\="default" /\>
\<Checkbox
 {...label}
 defaultChecked
 sx\={{
 color: pink\[800],
 '\&.Mui\-checked': {
 color: pink\[600],
 },
 }}
/\>Press `Enter` to start editingIcon
----

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Checkbox {...label} icon={<FavoriteBorder />} checkedIcon={<Favorite />} />
<Checkbox
  {...label}
  icon={<BookmarkBorderIcon />}
  checkedIcon={<BookmarkIcon />}
/>  

```
\<Checkbox {...label} icon\={\<FavoriteBorder /\>} checkedIcon\={\<Favorite /\>} /\>
\<Checkbox
 {...label}
 icon\={\<BookmarkBorderIcon /\>}
 checkedIcon\={\<BookmarkIcon /\>}
/\>Press `Enter` to start editingControlled
----------

You can control the checkbox with the `checked` and `onChange` props:


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Checkbox
  checked={checked}
  onChange={handleChange}
  inputProps={{ 'aria-label': 'controlled' }}
/>  

```
\<Checkbox
 checked\={checked}
 onChange\={handleChange}
 inputProps\={{ 'aria\-label': 'controlled' }}
/\>Press `Enter` to start editingIndeterminate
-------------

A checkbox input can only have two states in a form: checked or unchecked.
It either submits its value or doesn't.
Visually, there are **three** states a checkbox can be in: checked, unchecked, or indeterminate.


You can change the indeterminate icon using the `indeterminateIcon` prop.


ParentChild 1Child 2JSTSExpand codeCopy(or Ctrl \+ C)
```
<FormControlLabel
  label="Parent"
  control={
    <Checkbox
      checked={checked[0] && checked[1]}
      indeterminate={checked[0] !== checked[1]}
      onChange={handleChange1}
    />
  }
/>
{children}  

```
\<FormControlLabel
 label\="Parent"
 control\={
 \<Checkbox
 checked\={checked\[0] \&\& checked\[1]}
 indeterminate\={checked\[0] !\=\= checked\[1]}
 onChange\={handleChange1}
 /\>
 }
/\>
{children}Press `Enter` to start editing
When indeterminate is set, the value of the `checked` prop only impacts the form submitted values.
It has no accessibility or UX implications.


FormGroup
---------

`FormGroup` is a helpful wrapper used to group selection control components.


Assign responsibilityGilad GrayJason KillianAntoine LlorcaBe careful

Pick two \*Gilad GrayJason KillianAntoine LlorcaYou can display an error

JSTSShow codeLabel placement
---------------

You can change the placement of the label:


Label placementBottomEndJSTSShow codeCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<BpCheckbox />
<BpCheckbox defaultChecked />
<BpCheckbox disabled />  

```
\<BpCheckbox /\>
\<BpCheckbox defaultChecked /\>
\<BpCheckbox disabled /\>Press `Enter` to start editing🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


When to use
-----------

* Checkboxes vs. Radio Buttons
* Checkboxes vs. Switches


Accessibility
-------------

(WAI\-ARIA: 


* All form controls should have labels, and this includes radio buttons, checkboxes, and switches. In most cases, this is done by using the `<label>` element (FormControlLabel).
* When a label can't be used, it's necessary to add an attribute directly to the input component.
In this case, you can apply the additional attribute (for example `aria-label`, `aria-labelledby`, `title`) via the `inputProps` prop.



```
<Checkbox
  value="checkedA"
  inputProps={{
    'aria-label': 'Checkbox A',
  }}
/>

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Checkbox />`
* `<FormControl />`
* `<FormControlLabel />`
* `<FormGroup />`
* `<FormLabel />`



