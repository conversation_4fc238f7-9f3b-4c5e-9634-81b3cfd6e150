Data Grid \- Cell selection
===========================

Let users select individual cells or a range of cells.


Enabling cell selection
-----------------------

By default, the Data Grid lets users select individual rows.
With the Data Grid Premium, you can apply the `cellSelection` prop to let users select individual cells or ranges of cells.



```
<DataGridPremium cellSelection />

```
CopyCopied(or Ctrl \+ C)
Selecting cells
---------------

With the `cellSelection` prop applied, users can select a single cell by clicking on it, or by pressing `Shift+Space` when the cell is in focus.
Select multiple cells by holding `Cmd` (or `Ctrl` on Windows) while clicking on them.
Hold `Cmd` (or `Ctrl` on Windows) and click on a selected cell to deselect it.


To select a range of cells, users can:


* Click on a cell, drag the mouse over nearby cells, and then release.
* Click on a cell, then hold `Shift` and click on another cell. If a third cell is clicked then the selection will restart from the last clicked cell.
* Use the arrow keys to focus on a cell, then hold `Shift` and navigate to another cell—if `Shift` is released and pressed again then the selection will restart from the last focused cell.


Try out the various actions to select cells in the demo below—you can toggle row selection on and off to see how these two selection features can work in parallel.


Toggle row selectionDeskCommodityTrader NameTrader EmailQuantityD\-7834<NAME_EMAIL>63,016D\-3983<NAME_EMAIL>63,863D\-7628WheatKate Alexanderjagkuwomu@bihoc.pm36,640D\-3770<NAME_EMAIL>56,354D\-4576<NAME_EMAIL>44,834D\-6539Sugar No.14Ricky Schwartzzolak@mannej.mq58,024D\-3485SoybeansInez Piercefel@zel.mo12,995D\-5089Sugar No.11Cole Elliotttomfij@dizokune.gq25,861D\-1887MilkAlice Loganla@rissinmos.np59,604Total Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import { DataGridPremium } from '@mui/x-data-grid-premium';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function CellSelectionGrid {
  const [rowSelection, setRowSelection] = React.useState(false);

  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  return (
    <div style={{ width: '100%' }}>
      <Button sx={{ mb: 1 }} onClick={ => setRowSelection(!rowSelection)}>
        Toggle row selection
      </Button>
      <div style={{ height: 400 }}>
        <DataGridPremium
          rowSelection={rowSelection}
          checkboxSelection={rowSelection}
          cellSelection
          {...data}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import { DataGridPremium } from '@mui/x\-data\-grid\-premium';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function CellSelectionGrid {
 const \[rowSelection, setRowSelection] \= React.useState(false);

 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 return (
 \<div style\={{ width: '100%' }}\>
 \<Button sx\={{ mb: 1 }} onClick\={ \=\> setRowSelection(!rowSelection)}\>
 Toggle row selection
 \</Button\>
 \<div style\={{ height: 400 }}\>
 \<DataGridPremium
 rowSelection\={rowSelection}
 checkboxSelection\={rowSelection}
 cellSelection
 {...data}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editingControlling cell selection
--------------------------

You can control which cells are selected using the `cellSelectionModel` prop.
This prop accepts an object with keys corresponding to the row IDs that contain selected cells.
The value of each key is itself an object, which has a column field for a key and a boolean value for its selection state.
You can set this to `true` to select a cell or `false` to deselect it.
Removing the field from the object also deselects the cell.



```
// Selects the cell with field=name from row with id=1
<DataGridPremium cellSelectionModel={{ 1: { name: true } }} />

// Unselects the cell with field=name from row with id=1
<DataGridPremium cellSelectionModel={{ 1: { name: false } }} />

```
CopyCopied(or Ctrl \+ C)
When a new selection is made, the callback passed to the `onCellSelectionModelChange` prop is called with the updated model.
Use this value to update the current model.


The following demo shows how these props can be combined to create an Excel\-like formula field—try updating multiple cells at once by selecting them and entering a new value in the field at the top.


Selected cell valueSelected cell valueUpdate selected cellsDeskCommodityTrader NameTrader EmailQuantityD\-7834<NAME_EMAIL>63,016D\-3983<NAME_EMAIL>63,863D\-7628WheatKate Alexanderjagkuwomu@bihoc.pm36,640D\-3770<NAME_EMAIL>56,354D\-4576<NAME_EMAIL>44,834D\-6539Sugar No.14Ricky Schwartzzolak@mannej.mq58,024D\-3485SoybeansInez Piercefel@zel.mo12,995D\-5089Sugar No.11Cole Elliotttomfij@dizokune.gq25,861D\-1887MilkAlice Loganla@rissinmos.np59,604Total Rows: 10JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import {
  DataGridPremium,
  GridCellSelectionModel,
  GridRowModelUpdate,
  useGridApiRef,
} from '@mui/x-data-grid-premium';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function CellSelectionFormulaField {
  const apiRef = useGridApiRef;
  const [value, setValue] = React.useState('');
  const [cellSelectionModel, setCellSelectionModel] =
    React.useState<GridCellSelectionModel>({});
  const [numberOfSelectedCells, setNumberOfSelectedCells] = React.useState(0);

  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  const handleCellSelectionModelChange = React.useCallback(
    (newModel: GridCellSelectionModel) => {
      setCellSelectionModel(newModel);
    },
    [],
  );

  const handleValueChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setValue(event.target.value);
    },
    [],
  );

  const updateSelectedCells = React.useCallback( => {
    const updates: GridRowModelUpdate[] = [];

    Object.entries(cellSelectionModel).forEach(([id, fields]) => {
      const updatedRow = { ...apiRef.current?.getRow(id) };

      Object.entries(fields).forEach(([field, isSelected]) => {
        if (isSelected) {
          updatedRow[field] = value;
        }
      });

      updates.push(updatedRow);
    });

    apiRef.current?.updateRows(updates);
  }, [apiRef, cellSelectionModel, value]);

  React.useEffect( => {
    if (apiRef.current === null) {
      return;
    }

    const selectedCells = apiRef.current.getSelectedCellsAsArray;
    setNumberOfSelectedCells(selectedCells.length);

    if (selectedCells.length > 1) {
      setValue('(multiple values)');
    } else if (selectedCells.length === 1) {
      setValue(
        apiRef.current.getCellValue(selectedCells[0].id, selectedCells[0].field),
      );
    } else {
      setValue('');
    }
  }, [apiRef, cellSelectionModel]);

  return (
    <div style={{ width: '100%' }}>
      <Stack sx={{ mb: 1 }} direction="row" spacing={2}>
        <TextField
          label="Selected cell value"
          disabled={numberOfSelectedCells === 0}
          value={value}
          onChange={handleValueChange}
          fullWidth
        />
        <Button disabled={numberOfSelectedCells === 0} onClick={updateSelectedCells}>
          Update selected cells
        </Button>
      </Stack>
      <div style={{ height: 400 }}>
        <DataGridPremium
          apiRef={apiRef}
          rowSelection={false}
          cellSelectionModel={cellSelectionModel}
          onCellSelectionModelChange={handleCellSelectionModelChange}
          cellSelection
          {...data}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import {
 DataGridPremium,
 GridCellSelectionModel,
 GridRowModelUpdate,
 useGridApiRef,
} from '@mui/x\-data\-grid\-premium';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function CellSelectionFormulaField {
 const apiRef \= useGridApiRef;
 const \[value, setValue] \= React.useState('');
 const \[cellSelectionModel, setCellSelectionModel] \=
 React.useState\<GridCellSelectionModel\>({});
 const \[numberOfSelectedCells, setNumberOfSelectedCells] \= React.useState(0\);

 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 const handleCellSelectionModelChange \= React.useCallback(
 (newModel: GridCellSelectionModel) \=\> {
 setCellSelectionModel(newModel);
 },
 \[],
 );

 const handleValueChange \= React.useCallback(
 (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setValue(event.target.value);
 },
 \[],
 );

 const updateSelectedCells \= React.useCallback( \=\> {
 const updates: GridRowModelUpdate\[] \= \[];

 Object.entries(cellSelectionModel).forEach((\[id, fields]) \=\> {
 const updatedRow \= { ...apiRef.current?.getRow(id) };

 Object.entries(fields).forEach((\[field, isSelected]) \=\> {
 if (isSelected) {
 updatedRow\[field] \= value;
 }
 });

 updates.push(updatedRow);
 });

 apiRef.current?.updateRows(updates);
 }, \[apiRef, cellSelectionModel, value]);

 React.useEffect( \=\> {
 if (apiRef.current \=\=\= null) {
 return;
 }

 const selectedCells \= apiRef.current.getSelectedCellsAsArray;
 setNumberOfSelectedCells(selectedCells.length);

 if (selectedCells.length \> 1\) {
 setValue('(multiple values)');
 } else if (selectedCells.length \=\=\= 1\) {
 setValue(
 apiRef.current.getCellValue(selectedCells\[0].id, selectedCells\[0].field),
 );
 } else {
 setValue('');
 }
 }, \[apiRef, cellSelectionModel]);

 return (
 \<div style\={{ width: '100%' }}\>
 \<Stack sx\={{ mb: 1 }} direction\="row" spacing\={2}\>
 \<TextField
 label\="Selected cell value"
 disabled\={numberOfSelectedCells \=\=\= 0}
 value\={value}
 onChange\={handleValueChange}
 fullWidth
 /\>
 \<Button disabled\={numberOfSelectedCells \=\=\= 0} onClick\={updateSelectedCells}\>
 Update selected cells
 \</Button\>
 \</Stack\>
 \<div style\={{ height: 400 }}\>
 \<DataGridPremium
 apiRef\={apiRef}
 rowSelection\={false}
 cellSelectionModel\={cellSelectionModel}
 onCellSelectionModelChange\={handleCellSelectionModelChange}
 cellSelection
 {...data}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editingCustomizing range styles
------------------------

When multiple selected cells form a continuous range of any size, the following class names are applied to the cells at the edges:


* `MuiDataGrid-cell--rangeTop`: to all cells in the first row of the range
* `MuiDataGrid-cell--rangeBottom`: to all cells in the last row of the range
* `MuiDataGrid-cell--rangeLeft`: to all cells in the first column of the range
* `MuiDataGrid-cell--rangeRight`: to all cells in the last column of the range



When a single cell is selected, all classes above are applied to that element.


You can use these classes to create CSS selectors targeting specific corners of each range—for example, the demo below adds a border around the outside of the range.


DeskCommodityTrader NameTrader EmailQuantityD\-7834<NAME_EMAIL>63,016D\-3983<NAME_EMAIL>63,863D\-7628WheatKate Alexanderjagkuwomu@bihoc.pm36,640D\-3770<NAME_EMAIL>56,354D\-4576<NAME_EMAIL>44,834D\-6539Sugar No.14Ricky Schwartzzolak@mannej.mq58,024D\-3485SoybeansInez Piercefel@zel.mo12,995D\-5089Sugar No.11Cole Elliotttomfij@dizokune.gq25,861D\-1887MilkAlice Loganla@rissinmos.np59,604Total Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, lighten, darken, alpha } from '@mui/material/styles';
import { DataGridPremium, gridClasses } from '@mui/x-data-grid-premium';
import { useDemoData } from '@mui/x-data-grid-generator';

const StyledDataGridPremium = styled(DataGridPremium)(({ theme }) => {
  const lightBorderColor = lighten(alpha(theme.palette.divider, 1), 0.88);
  const darkBorderColor = darken(alpha(theme.palette.divider, 1), 0.68);

  const selectedCellBorder = alpha(theme.palette.primary.main, 0.5);

  return {
    [`& .${gridClasses.cell}`]: {
      border: `1px solid transparent`,
      borderRight: `1px solid ${lightBorderColor}`,
      borderBottom: `1px solid ${lightBorderColor}`,
      ...theme.applyStyles('dark', {
        borderRightColor: `${darkBorderColor}`,
        borderBottomColor: `${darkBorderColor}`,
      }),
    },
    [`& .${gridClasses.cell}.Mui-selected`]: {
      borderColor: alpha(theme.palette.primary.main, 0.1),
    },
    [`& .${gridClasses.cell}.Mui-selected.${gridClasses['cell--rangeTop']}`]: {
      borderTopColor: selectedCellBorder,
    },
    [`& .${gridClasses.cell}.Mui-selected.${gridClasses['cell--rangeBottom']}`]: {
      borderBottomColor: selectedCellBorder,
    },
    [`& .${gridClasses.cell}.Mui-selected.${gridClasses['cell--rangeLeft']}`]: {
      borderLeftColor: selectedCellBorder,
    },
    [`& .${gridClasses.cell}.Mui-selected.${gridClasses['cell--rangeRight']}`]: {
      borderRightColor: selectedCellBorder,
    },
  };
});

export default function CellSelectionRangeStyling {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <StyledDataGridPremium rowSelection={false} cellSelection {...data} />
    </div>
  );
}  

```
import \* as React from 'react';
import { styled, lighten, darken, alpha } from '@mui/material/styles';
import { DataGridPremium, gridClasses } from '@mui/x\-data\-grid\-premium';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const StyledDataGridPremium \= styled(DataGridPremium)(({ theme }) \=\> {
 const lightBorderColor \= lighten(alpha(theme.palette.divider, 1\), 0\.88\);
 const darkBorderColor \= darken(alpha(theme.palette.divider, 1\), 0\.68\);

 const selectedCellBorder \= alpha(theme.palette.primary.main, 0\.5\);

 return {
 \[\`\& .${gridClasses.cell}\`]: {
 border: \`1px solid transparent\`,
 borderRight: \`1px solid ${lightBorderColor}\`,
 borderBottom: \`1px solid ${lightBorderColor}\`,
 ...theme.applyStyles('dark', {
 borderRightColor: \`${darkBorderColor}\`,
 borderBottomColor: \`${darkBorderColor}\`,
 }),
 },
 \[\`\& .${gridClasses.cell}.Mui\-selected\`]: {
 borderColor: alpha(theme.palette.primary.main, 0\.1\),
 },
 \[\`\& .${gridClasses.cell}.Mui\-selected.${gridClasses\['cell\-\-rangeTop']}\`]: {
 borderTopColor: selectedCellBorder,
 },
 \[\`\& .${gridClasses.cell}.Mui\-selected.${gridClasses\['cell\-\-rangeBottom']}\`]: {
 borderBottomColor: selectedCellBorder,
 },
 \[\`\& .${gridClasses.cell}.Mui\-selected.${gridClasses\['cell\-\-rangeLeft']}\`]: {
 borderLeftColor: selectedCellBorder,
 },
 \[\`\& .${gridClasses.cell}.Mui\-selected.${gridClasses\['cell\-\-rangeRight']}\`]: {
 borderRightColor: selectedCellBorder,
 },
 };
});

export default function CellSelectionRangeStyling {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<StyledDataGridPremium rowSelection\={false} cellSelection {...data} /\>
 \</div\>
 );
}Press `Enter` to start editingapiRef
------

The Data Grid exposes a set of methods via the `apiRef` object that are used internally in the implementation of the cell selection feature.
The reference below describes the relevant functions.
See API object for more details.



This API should only be used as a last resort when the Data Grid's built\-in props aren't sufficient for your specific use case.


### getCellSelectionModelReturns an object containing the selection state of the cells. The keys of the object correspond to the row IDs. The value of each key is also an object, which has a column field for a key and a boolean value for its selection state.

###### Signature:

Copy(or Ctrl \+ C)
```
getCellSelectionModel:  => GridCellSelectionModel
```
### getSelectedCellsAsArrayReturns an array containing only the selected cells. Each item is an object with the ID and field of the cell.

###### Signature:

Copy(or Ctrl \+ C)
```
getSelectedCellsAsArray:  => GridCellCoordinates[]
```
### isCellSelectedDetermines if a cell is selected or not.

###### Signature:

Copy(or Ctrl \+ C)
```
isCellSelected: (id: GridRowId, field: GridColDef['field']) => boolean
```
### selectCellRangeSelects all cells that are inside the range given by `start` and `end` coordinates.

###### Signature:

Copy(or Ctrl \+ C)
```
selectCellRange: (start: GridCellCoordinates, end: GridCellCoordinates, keepOtherSelected?: boolean) => void
```
### setCellSelectionModelUpdates the cell selection model according to the value passed to the `newModel` argument. Any cell already selected will be unselected.

###### Signature:

Copy(or Ctrl \+ C)
```
setCellSelectionModel: (newModel: GridCellSelectionModel) => void
```
API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Row selectionVirtualization

---

•

Blog•

Store
