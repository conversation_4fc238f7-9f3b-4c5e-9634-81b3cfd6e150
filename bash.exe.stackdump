Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF97F0) msys-2.0.dll+0x1FE8E
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210286019, 0007FFFFA7A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA8F0  000210068E24 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABD0  00021006A225 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA3BB40000 ntdll.dll
7FFA39990000 KERNEL32.DLL
7FFA394B0000 KERNELBASE.dll
7FFA3AA90000 USER32.dll
7FFA39840000 win32u.dll
000210040000 msys-2.0.dll
7FFA3A0F0000 GDI32.dll
7FFA39870000 gdi32full.dll
7FFA39410000 msvcp_win.dll
7FFA39140000 ucrtbase.dll
7FFA3ADC0000 advapi32.dll
7FFA39B40000 msvcrt.dll
7FFA3AFF0000 sechost.dll
7FFA3B090000 RPCRT4.dll
7FFA38840000 CRYPTBASE.DLL
7FFA39260000 bcryptPrimitives.dll
7FFA3B2F0000 IMM32.DLL
