Data Grid \- Column ordering
============================

Drag and drop your columns to reorder them.



There is a known issue with Firefox v129 that impacts this feature.
Reordering does not work on that specific version of Firefox because the value for `event.dataTransfer` is `null` which results in an error.


You must upgrade to Firefox v130 or higher to avoid this issue.


By default, columns are ordered according to the order they are included in the `columns` array.


By default, Data Grid Pro allows all column reordering by dragging the header cells and moving them left or right.


DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledStatusD\-1529<NAME_EMAIL>7,20762\.425 %RejectedD\-8646<NAME_EMAIL>29,01553\.731 %RejectedD\-4884<NAME_EMAIL>89,46258\.97 %OpenD\-3807Cotton No.2Jason Freemanror@gop.mr97,58987\.82 %RejectedD\-9321Robusta coffeeJoshua <PERSON>@lovat.pt82,72490\.1 %Partially FilledD\-2071Sugar No.14Vera Simpsonbohap@javfa.hu68,12762\.3 %RejectedD\-8493SoybeansNora Butlerise@satne.pt93,48911\.73 %OpenD\-2608<NAME_EMAIL>35,38465\.651 %FilledD\-6349RapeseedEmilie Collinscabic@larif.fi45,49060\.98 %FilledTotal Rows: 20JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function ColumnOrderingGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 20,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro {...data} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function ColumnOrderingGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 20,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro {...data} /\>
 \</div\>
 );
}Press `Enter` to start editingTo disable reordering on all columns, set the prop `disableColumnReorder={true}`.


To disable reordering in a specific column, set the `disableReorder` property to true in the `GridColDef` of the respective column.


idusernameage1@MUI20Total Rows: 1JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';

const rows = [
  {
    id: 1,
    username: '@MUI',
    age: 20,
  },
];

export default function ColumnOrderingDisabledGrid {
  return (
    <div style={{ height: 250, width: '100%' }}>
      <DataGridPro
        columns={[
          { field: 'id' },
          { field: 'username' },
          { field: 'age', disableReorder: true },
        ]}
        rows={rows}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';

const rows \= \[
 {
 id: 1,
 username: '@MUI',
 age: 20,
 },
];

export default function ColumnOrderingDisabledGrid {
 return (
 \<div style\={{ height: 250, width: '100%' }}\>
 \<DataGridPro
 columns\={\[
 { field: 'id' },
 { field: 'username' },
 { field: 'age', disableReorder: true },
 ]}
 rows\={rows}
 /\>
 \</div\>
 );
}Press `Enter` to start editingIn addition, column reordering emits the following events that can be imported:


* `columnHeaderDragStart`: emitted when dragging of a header cell starts.
* `columnHeaderDragEnter`: emitted when the cursor enters another header cell while dragging.
* `columnHeaderDragOver`: emitted when dragging a header cell over another header cell.
* `columnHeaderDragEnd`: emitted when dragging of a header cell stops.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Column groupsColumn pinning

---

•

Blog•

Store
