TITLE: Applying a Custom Theme with ThemeProvider in JSX
DESCRIPTION: This snippet demonstrates how to create a custom theme using createTheme and apply it to a React application using the ThemeProvider component from MUI. It sets a custom primary color.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_12

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { red } from '@mui/material/colors';
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: red[500],
    },
  },
});

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

----------------------------------------

TITLE: Install Material UI with different package managers
DESCRIPTION: Commands to install the core Material UI package and its required peer dependencies (@emotion/react, @emotion/styled) using npm, pnpm, or yarn.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/docs/codeblock.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/material @emotion/react @emotion/styled
# `@emotion/react` and `@emotion/styled` are peer dependencies
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/material @emotion/react @emotion/styled
# `@emotion/react` and `@emotion/styled` are peer dependencies
```

LANGUAGE: bash
CODE:
```
yarn add @mui/material @emotion/react @emotion/styled
# `@emotion/react` and `@emotion/styled` are peer dependencies
```

----------------------------------------

TITLE: Adding Responsive Viewport Meta Tag (HTML)
DESCRIPTION: Adds the responsive viewport meta tag to the HTML `<head>` element. This is crucial for ensuring proper rendering and touch zooming behavior across various devices, especially mobile, aligning with Material UI's mobile-first design philosophy.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/usage/usage.md#_snippet_0

LANGUAGE: html
CODE:
```
<meta name="viewport" content="initial-scale=1, width=device-width" />
```

----------------------------------------

TITLE: Enabling System Preference Dark Mode with colorSchemes (MUI, JS)
DESCRIPTION: This snippet demonstrates how to configure Material UI to automatically switch between light and dark modes based on the user's system preference. It uses the `createTheme` helper with the `colorSchemes` property, setting `dark` to `true`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_2

LANGUAGE: js
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme({
  colorSchemes: {
    dark: true,
  },
});

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

----------------------------------------

TITLE: Using MUI System Shorthands with Box Component (JSX)
DESCRIPTION: Shows examples of using various MUI System shorthands within the `sx` prop of a `Box` component. It illustrates mapping theme values for `boxShadow`, `color`, `margin` (`m`), `padding` (`p`) with breakpoints, and `zIndex`, simplifying common styling tasks.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_3

LANGUAGE: JSX
CODE:
```
<Box
  sx={{  boxShadow: 1, // theme.shadows[1]
    color: 'primary.main', // theme.palette.primary.main
    m: 1, // margin: theme.spacing(1)
    p: {
      xs: 1, // [theme.breakpoints.up('xs')]: { padding: theme.spacing(1) }
    },
    zIndex: 'tooltip', // theme.zIndex.tooltip
  }}
>

```

----------------------------------------

TITLE: Rendering a Link in ListItemButton (JSX)
DESCRIPTION: Demonstrates how to render a link within a ListItemButton component by setting the `component` prop to 'a' and providing an `href`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/lists/lists.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<ListItemButton component="a" href="#simple-list">
  <ListItemText primary="Spam" />
</ListItemButton>
```

----------------------------------------

TITLE: Installing Material UI with npm
DESCRIPTION: This command demonstrates the simple first step required to install the Material UI library using the npm package manager, minimizing onboarding friction.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v1-is-out.md#_snippet_0

LANGUAGE: npm
CODE:
```
npm install @mui/material
```

----------------------------------------

TITLE: Applying Global CSS Reset with CssBaseline (JSX)
DESCRIPTION: Demonstrates how to use the `CssBaseline` component to apply a global CSS reset to the entire application, similar to normalize.css. Wrap the main application content within `<React.Fragment>` and include `<CssBaseline />` at the top level.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/css-baseline/css-baseline.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';

export default function MyApp() {
  return (
    <React.Fragment>
      <CssBaseline />
      {/* The rest of your application */}
    </React.Fragment>
  );
}
```

----------------------------------------

TITLE: Migrating makeStyles to styled utility in React
DESCRIPTION: This diff illustrates how to transition from `makeStyles` to MUI's `styled` utility. This method is suitable for creating dedicated styled components, which helps in organizing styles and can prevent increasing CSS specificity compared to using class names.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migrating-from-jss.md#_snippet_3

LANGUAGE: diff
CODE:
```
-import makeStyles from '@mui/styles/makeStyles';
+import { styled } from '@mui/material/styles';

-const useStyles = makeStyles((theme) => ({
-  root: {
-    display: 'flex',
-    alignItems: 'center',
-    borderRadius: 20,
-    background: theme.palette.grey[50],
-  },
-  label: {
-    color: theme.palette.primary.main,
-  }
-}))
+const Root = styled('div')(({ theme }) => ({
+  display: 'flex',
+  alignItems: 'center',
+  borderRadius: 20,
+  background: theme.palette.grey[50],
+}))

+const Label = styled('span')(({ theme }) => ({
+  color: theme.palette.primary.main,
+}))

 function Status({ label }) {
-  const classes = useStyles();
   return (
-    <div className={classes.root}>
-      {icon}
-      <span className={classes.label}>{label}</span>
-    </div>
+    <Root>
+      {icon}
+      <Label>{label}</Label>
+    </Root>
   )
 }
```

----------------------------------------

TITLE: Installing Material UI with npm
DESCRIPTION: Installs the core Material UI package (@mui/material) and its required peer dependencies (@emotion/react, @emotion/styled) using the npm package manager.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-material/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/material @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Install MUI System Default Dependencies (Bash)
DESCRIPTION: Installs the core MUI System package along with the default Emotion styling engine dependencies using different package managers.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/installation/installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/system @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/system @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
yarn add @mui/system @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Applying Vertical Margin with sx prop in MUI
DESCRIPTION: Demonstrates how to use the `sx` prop on a MUI component (Slider) to apply vertical margin using the theme's spacing units. The `my: 1` syntax applies 1 unit of vertical margin (top and bottom).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Slider sx={{ my: 1 }} />
```

----------------------------------------

TITLE: Install Joy UI Core Packages (Bash)
DESCRIPTION: Commands to install the main Joy UI library along with its required peer dependencies, @emotion/react and @emotion/styled, using different package managers.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/installation/installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/joy @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/joy @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
yarn add @mui/joy @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Styling Components with MUI System sx Prop (JSX)
DESCRIPTION: This snippet demonstrates how to apply styles directly to a MUI `Box` component using the `sx` prop. It shows a more concise way to define styles for a UI element by passing a style object to the `sx` prop, leveraging theme values and shorthand properties.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box
  sx={{
    bgcolor: 'background.paper',
    boxShadow: 1,
    borderRadius: 1,
    p: 2,
    minWidth: 300,
  }}
>
  <Box sx={{ color: 'text.secondary' }}>Sessions</Box>
  <Box sx={{ color: 'text.primary', fontSize: 34, fontWeight: 'medium' }}>
    98.3 K
  </Box>
  <Box
    component={TrendingUpIcon}
    sx={{ color: 'success.dark', fontSize: 16, verticalAlign: 'sub' }}
  />
  <Box
    sx={{ color: 'success.dark', display: 'inline', fontWeight: 'medium', mx: 0.5 }}
  >
    18.77%
  </Box>
  <Box sx={{ color: 'text.secondary', display: 'inline', fontSize: 12 }}>
    vs. last week
  </Box>
</Box>
```

----------------------------------------

TITLE: Implementing Accessible Form Fields with Material UI Components (JSX)
DESCRIPTION: Shows how to build an accessible form field using Material UI components (`FormControl`, `InputLabel`, `Input`, `FormHelperText`), ensuring proper linking via `htmlFor`, `id`, and `aria-describedby` props to achieve the necessary accessible DOM structure.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/text-fields/text-fields.md#_snippet_9

LANGUAGE: jsx
CODE:
```
<FormControl>
  <InputLabel htmlFor="my-input">Email address</InputLabel>
  <Input id="my-input" aria-describedby="my-helper-text" />
  <FormHelperText id="my-helper-text">We'll never share your email.</FormHelperText>
</FormControl>
```

----------------------------------------

TITLE: Configure RTL with Emotion (JSX)
DESCRIPTION: Integrate `stylis-plugin-rtl` with Emotion by creating a new cache instance using `createCache` and providing `rtlPlugin` to `stylisPlugins`. Wrap your application tree with `CacheProvider` using this RTL cache.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/right-to-left/right-to-left.md#_snippet_7

LANGUAGE: jsx
CODE:
```
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { prefixer } from 'stylis';
import rtlPlugin from 'stylis-plugin-rtl';

// Create rtl cache
const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [prefixer, rtlPlugin],
});

function Rtl(props) {
  return <CacheProvider value={cacheRtl}>{props.children}</CacheProvider>;
}
```

----------------------------------------

TITLE: Applying Margin to MUI Slider using sx Prop in JSX
DESCRIPTION: This snippet demonstrates how to apply vertical margin to a Material UI Slider component using the `sx` prop. The `sx={{ my: 1 }}` syntax applies a margin of 1 unit (typically 8px) on the top and bottom (my stands for margin-y).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/blog/blog-custom-card.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// add margin: 8px 0px;
<Slider sx={{ my: 1 }} />
```

----------------------------------------

TITLE: Configuring Global Theme Link with React Router (tsx)
DESCRIPTION: This snippet demonstrates how to integrate Material UI components like Link and ButtonBase with react-router by configuring the theme. It defines a LinkBehavior component that maps Material UI's href prop to react-router's to prop and sets this behavior as the default component for MuiLink and MuiButtonBase in the theme. This allows consistent navigation handling across the application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/routing/routing.md#_snippet_0

LANGUAGE: tsx
CODE:
```
import { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router';
import { LinkProps } from '@mui/material/Link';

const LinkBehavior = React.forwardRef<
  HTMLAnchorElement,
  Omit<RouterLinkProps, 'to'> & { href: RouterLinkProps['to'] }
>((props, ref) => {
  const { href, ...other } = props;
  // Map href (Material UI) -> to (react-router)
  return <RouterLink ref={ref} to={href} {...other} />;
});

const theme = createTheme({
  components: {
    MuiLink: {
      defaultProps: {
        component: LinkBehavior,
      } as LinkProps,
    },
    MuiButtonBase: {
      defaultProps: {
        LinkComponent: LinkBehavior,
      },
    },
  },
});
```

----------------------------------------

TITLE: Using MUI System Keys for Margin Bottom (JSX)
DESCRIPTION: Demonstrates how to apply `margin-bottom` using the system keys `mb` or `marginBottom` via the `sx` prop on components like `Button` or directly on system components like `Box`. These keys provide a shorthand for applying spacing values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/properties/properties.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Button sx={{ mb: 3 }}>
// or
<Box mb={3}>
// or
<Box marginBottom={3}>
```

----------------------------------------

TITLE: Creating Material UI Theme (JavaScript)
DESCRIPTION: Defines a Material UI theme object using `createTheme` from `@mui/material/styles`. This theme includes a custom color palette and is intended to be shared between the client and server for consistent styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { createTheme } from '@mui/material/styles';
import { red } from '@mui/material/colors';

// Create a theme instance.
const theme = createTheme({
  palette: {
    primary: {
      main: '#556cd6',
    },
    secondary: {
      main: '#19857b',
    },
    error: {
      main: red.A400,
    },
  },
});

export default theme;
```

----------------------------------------

TITLE: Extending Joy UI Theme with Custom Secondary Palette - JavaScript
DESCRIPTION: This snippet demonstrates how to use `extendTheme` from `@mui/joy/styles` to add a new `secondary` color palette to both the light and dark color schemes of a Joy UI theme. It includes defining color steps (50-900) and custom variant tokens for the new palette.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_4

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        secondary: {
          // Credit:
          // https://github.com/tailwindlabs/tailwindcss/blob/master/src/public/colors.js
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
          // Adjust the global variant tokens as you'd like.
          // The tokens should be the same for all color schemes.
          solidBg: 'var(--joy-palette-secondary-400)',
          solidActiveBg: 'var(--joy-palette-secondary-500)',
          outlinedBorder: 'var(--joy-palette-secondary-500)',
          outlinedColor: 'var(--joy-palette-secondary-700)',
          outlinedActiveBg: 'var(--joy-palette-secondary-100)',
          softColor: 'var(--joy-palette-secondary-800)',
          softBg: 'var(--joy-palette-secondary-200)',
          softActiveBg: 'var(--joy-palette-secondary-300)',
          plainColor: 'var(--joy-palette-secondary-700)',
          plainActiveBg: 'var(--joy-palette-secondary-100)'
        }
      }
    },
    dark: {
      palette: {
        secondary: {
          // Credit:
          // https://github.com/tailwindlabs/tailwindcss/blob/master/src/public/colors.js
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
          // Adjust the global variant tokens as you'd like.
          // The tokens should be the same for all color schemes.
          solidBg: 'var(--joy-palette-secondary-400)',
          solidActiveBg: 'var(--joy-palette-secondary-500)',
          outlinedBorder: 'var(--joy-palette-secondary-700)',
          outlinedColor: 'var(--joy-palette-secondary-600)',
          outlinedActiveBg: 'var(--joy-palette-secondary-900)',
          softColor: 'var(--joy-palette-secondary-500)',
          softBg: 'var(--joy-palette-secondary-900)',
          softActiveBg: 'var(--joy-palette-secondary-800)',
          plainColor: 'var(--joy-palette-secondary-500)',
          plainActiveBg: 'var(--joy-palette-secondary-900)'
        }
      }
    }
  }
});

// Then, pass it to `<CssVarsProvider theme={theme}>`.
```

----------------------------------------

TITLE: Using the sx Prop for Inline Styles in Pigment CSS
DESCRIPTION: Illustrates how to use the `sx` prop to apply one-off inline custom styles to HTML elements or React components. The `sx` prop accepts a style object or an array of style objects/functions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/experimental-api/pigment-css/pigment-css.md#_snippet_16

LANGUAGE: jsx
CODE:
```
<div sx={{ display: 'flex', flexDirection: 'column' }}>

<AnyComponent sx={{ fontSize: 12, color: 'red' }} />;
```

----------------------------------------

TITLE: Installing Material UI dependencies
DESCRIPTION: Install the necessary packages for integrating Material UI with Next.js App Router, including `@mui/material-nextjs` and `@emotion/cache`, using your preferred package manager.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/material-nextjs @emotion/cache
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/material-nextjs @emotion/cache
```

LANGUAGE: bash
CODE:
```
yarn add @mui/material-nextjs @emotion/cache
```

----------------------------------------

TITLE: Using TypeScript Interfaces in Component and Styled Component Definitions
DESCRIPTION: Applies the previously defined TypeScript interfaces (`StatProps`, `StatOwnerState`) to the styled component (`StatRoot`) and the React component (`Stat`) definitions. This ensures type-safe usage of props, `ownerState`, and integration with `useThemeProps`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_8

LANGUAGE: js
CODE:
```
const StatRoot = styled('div', {
  name: 'JoyStat',
  slot: 'root',
})<{ ownerState: StatOwnerState }>(({ theme, ownerState }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  padding: theme.spacing(3, 4),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
  letterSpacing: '-0.025em',
  fontWeight: 600,
  // typed-safe access to the `variant` prop
  ...(ownerState.variant === 'outlined' && {
    border: `2px solid ${theme.palette.divider}`,
    boxShadow: 'none',
  }),
}));

// …do the same for other slots

const Stat = React.forwardRef<HTMLDivElement, StatProps>(function Stat(inProps, ref) {
  const props = useThemeProps({ props: inProps, name: 'JoyStat' });
  const { value, unit, variant, ...other } = props;

  const ownerState = { ...props, variant };

  return (
    <StatRoot ref={ref} ownerState={ownerState} {...other}>
      <StatValue ownerState={ownerState}>{value}</StatValue>
      <StatUnit ownerState={ownerState}>{unit}</StatUnit>
    </StatRoot>
  );
});
```

----------------------------------------

TITLE: Installing Material UI Next.js Dependencies
DESCRIPTION: Commands to install the necessary packages (`@mui/material-nextjs`, `@emotion/cache`, `@emotion/server`) for integrating Material UI with Next.js Pages Router using different package managers.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_7

LANGUAGE: bash
CODE:
```
npm install @mui/material-nextjs @emotion/cache @emotion/server
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/material-nextjs @emotion/cache @emotion/server
```

LANGUAGE: bash
CODE:
```
yarn add @mui/material-nextjs @emotion/cache @emotion/server
```

----------------------------------------

TITLE: Migrating JSS makeStyles to MUI styled API - JavaScript
DESCRIPTION: Demonstrates the code changes needed to replace JSS `makeStyles` with the `styled` API from `@mui/material/styles`. It shows how to define styles using the `styled` function and apply them to a component, including handling class names and theme access.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_204

LANGUAGE: diff
CODE:
```
 import Typography from '@material-ui/core/Typography';
-import makeStyles from '@material-ui/styles/makeStyles';
+import { styled } from '@material-ui/core/styles';

-const useStyles = makeStyles((theme) => ({
-  root: {
-    display: 'flex',
-    alignItems: 'center',
-    backgroundColor: theme.palette.primary.main
-  },
-  cta: {
-    borderRadius: theme.shape.radius
-  },
-  content: {
-    color: theme.palette.common.white,
-    fontSize: 16,
-    lineHeight: 1.7
-  },
-}))
+const PREFIX = 'MyCard';
+const classes = {
+  root: `${PREFIX}-root`,
+  cta: `${PREFIX}-cta`,
+  content: `${PREFIX}-content`,
+}
+const Root = styled('div')((theme) => ({
+  [`&.${classes.root}`]: {
+    display: 'flex',
+    alignItems: 'center',
+    backgroundColor: theme.palette.primary.main
+  },
+  [`& .${classes.cta}`]: {
+    borderRadius: theme.shape.radius
+  },
+  [`& .${classes.content}`]: {
+    color: theme.palette.common.white,
+    fontSize: 16,
+    lineHeight: 1.7
+  },
+}))

 export const MyCard = () => {
   const classes = useStyles();
   return (
-    <div className={classes.root}>
+    <Root className={classes.root}>
       <Typography className={classes.content}>...</Typography>
       <Button className={classes.cta}>Go</Button>
-    </div>
+    </Root>
   )
 }
```

----------------------------------------

TITLE: Install Material Icons package
DESCRIPTION: Installs the @mui/icons-material package using npm, pnpm, or yarn. This package provides a large collection of prebuilt SVG Material Icons for use with Material UI components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/installation/installation.md#_snippet_6

LANGUAGE: bash
CODE:
```
npm install @mui/icons-material
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/icons-material
```

LANGUAGE: bash
CODE:
```
yarn add @mui/icons-material
```

----------------------------------------

TITLE: Fixing MUI Server-Side Rendering Class Name Mismatch (Node.js/JS)
DESCRIPTION: This snippet illustrates how to fix a React class name hydration mismatch issue in server-rendered Material UI applications. The solution is to create a new instance of the class name generator (createGenerateClassName) for each server request within the rendering function (handleRender). This ensures that the server and client generate identical class names for the same component tree on each request.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_16

LANGUAGE: diff
CODE:
```
-// Create a new class name generator.
-const generateClassName = createGenerateClassName();

 function handleRender(req, res) {
+  // Create a new class name generator.
+  const generateClassName = createGenerateClassName();

   //…

   // Render the component to a string.
   const html = ReactDOMServer.renderToString(
```

----------------------------------------

TITLE: Migrating to theme.applyStyles() in Material UI v6
DESCRIPTION: Compares the new `theme.applyStyles()` method for defining mode-specific styles (like dark mode) with the previous `theme.palette.mode === 'dark'` condition, highlighting how the new method helps fix SSR flickering issues.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_8

LANGUAGE: JSX
CODE:
```
const StyledInput = styled(InputBase)(({ theme }) => ({
  padding: 10,
  width: '100%',
  borderBottom: '1px solid #eaecef',
  ...theme.applyStyles('dark', {
    borderBottom: '1px solid #30363d',
  }),
  '& input': {
    borderRadius: 4,
    backgroundColor: '#fff',
    ...theme.applyStyles('dark', {
      backgroundColor: '#0d1117',
    }),
  },
}));
```

LANGUAGE: JSX
CODE:
```
const StyledInput = styled(InputBase)(({ theme }) => ({
  padding: 10,
  width: '100%',
  borderBottom:
    theme.palette.mode === 'dark' ? '1px solid #30363d' : '1px solid #eaecef',
  '& input': {
    borderRadius: 4,
    backgroundColor: theme.palette.mode === 'dark' ? '#0d1117' : '#fff',
  },
}));
```

----------------------------------------

TITLE: Running MUI Date/Time Picker Migration Codemod (Bash)
DESCRIPTION: Provides the command to execute the official MUI codemod using `npx`. This codemod automates the process of updating import paths for Date and Time Pickers from `@mui/lab` to the appropriate `@mui/x-date-pickers` or `@mui/x-date-pickers-pro` packages within a specified codebase path.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/lab-date-pickers-to-mui-x.md#_snippet_1

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v5.0.0/date-pickers-moved-to-x <path>
```

----------------------------------------

TITLE: Defining TypeScript Interfaces for Component Props and ownerState
DESCRIPTION: Provides TypeScript interface definitions for a component's public props (`StatProps`) and the internal state/props intended for styling slots via `ownerState` (`StatOwnerState`). This enables type safety when using the component and styling its slots.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_7

LANGUAGE: js
CODE:
```
interface StatProps {
  value: number | string;
  unit: string;
  variant?: 'outlined';
}

interface StatOwnerState extends StatProps {
  // …key value pairs for the internal state that you want to style the slot
  // but don't want to expose to the users
}
```

----------------------------------------

TITLE: Define Self-Hosted Font Face with CssBaseline (JSX)
DESCRIPTION: This snippet illustrates how to define a global `@font-face` rule for a self-hosted font ('Raleway') using the `styleOverrides` property of `MuiCssBaseline` within a Material UI theme. It also shows how to apply the theme and the font to a component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import RalewayWoff2 from './fonts/Raleway-Regular.woff2';

const theme = createTheme({
  typography: {
    fontFamily: 'Raleway, Arial',
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'Raleway';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          src: local('Raleway'), local('Raleway-Regular'), url(${RalewayWoff2}) format('woff2');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF;
        }
      `,
    },
  },
});

// ...
return (
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <Box sx={{ fontFamily: 'Raleway' }}>Raleway</Box>
  </ThemeProvider>
);
```

----------------------------------------

TITLE: Using Button with NextLinkComposed in Next.js (tsx)
DESCRIPTION: This example shows how to use a Material UI Button component as a link in a Next.js Pages Router application. It utilizes a custom NextLinkComposed component (which wraps Next.js's Link) by passing it to the component prop of the Button. This allows the Button to handle Next.js routing while accepting complex to objects.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/routing/routing.md#_snippet_2

LANGUAGE: tsx
CODE:
```
import Button from '@mui/material/Button';
import { NextLinkComposed } from '../src/Link';

export default function Index() {
  return (
    <Button
      component={NextLinkComposed}
      to={{
        pathname: '/about',
        query: { name: 'test' },
      }}
    >
      Button link
    </Button>
  );
}
```

----------------------------------------

TITLE: Creating React 19 Compatible forwardRef Shim (TSX)
DESCRIPTION: Implements a compatibility shim for `forwardRef` that ensures the `ref` prop is always present in the props object, even in older React versions. This prevents referential instability issues when spreading props after the ref in React 19 and provides type safety.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/react-19-update.md#_snippet_2

LANGUAGE: TSX
CODE:
```
// Compatibility shim that ensures stable props object for forwardRef components
// Fixes https://github.com/facebook/react/issues/31613
// We ensure that the ref is always present in the props object (even if that's not the case for older versions of React) to avoid the footgun of spreading props over the ref in the newer versions of React.
export const forwardRef = <T, P = {}>( render: React.ForwardRefRenderFunction<T, P & { ref: React.Ref<T> }>,
) => {
  if (reactMajor >= 19) {
    const Component = (props: any) => render(props, props.ref ?? null);
    Component.displayName = render.displayName ?? render.name;
    return Component as React.ForwardRefExoticComponent<P>;
  }
  return React.forwardRef(
    render as React.ForwardRefRenderFunction<T, React.PropsWithoutRef<P>>,
  );
};
```

----------------------------------------

TITLE: Migrating from System Props to sx Prop (JSX)
DESCRIPTION: Illustrates the transition from using deprecated system props directly on the Box component to using the recommended `sx` prop for applying system styles like margin top (`mt`).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/box/box.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box mt={2} />
```

LANGUAGE: jsx
CODE:
```
<Box sx={{ mt: 2 }} />
```

----------------------------------------

TITLE: Importing Basic List Components (JSX)
DESCRIPTION: Imports the core `List` and `ListItem` components from the `@mui/material` package, which are necessary for building basic lists.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/lists/lists.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
```

----------------------------------------

TITLE: Labeling MUI Select for Accessibility (JSX)
DESCRIPTION: Illustrates how to properly label a Material UI `Select` component for accessibility. An `InputLabel` with a unique `id` is used, and the `Select` component's `labelId` prop is set to match this `id`, creating an accessible association between the label and the select input.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/selects/selects.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<InputLabel id="label">Age</InputLabel>
<Select labelId="label" id="select" value="20">
  <MenuItem value="10">Ten</MenuItem>
  <MenuItem value="20">Twenty</MenuItem>
</Select>
```

----------------------------------------

TITLE: Using `sx` Prop with `styled` Components in MUI
DESCRIPTION: Demonstrates the basic usage of the `sx` prop to apply styles directly to components created with the `styled` API, including a custom component and a built-in Box component. Shows how to define styles using the shorthand notation.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/callback-support-in-style-overrides.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';

const Label = styled('span')({
  fontWeight: 'bold',
  fontSize: '0.875rem',
})

<Box sx={{ display: 'flex' }}>
  <Label sx={{ color: 'text.secondary' }}>Label</Label>
</Box>;
```

----------------------------------------

TITLE: Adding Custom MUI Component Variant via Theme (JSX/TS)
DESCRIPTION: Illustrates how to define a new custom variant (`dashed`) for a component (Button) within the theme's `components` section, specifying styles for specific prop combinations. It also includes updating the TypeScript type definitions to allow using the new variant via the `variant` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_4

LANGUAGE: jsx
CODE:
```
import { createTheme, Button } from '@mui/material';

// 1. Extend the theme.
const theme = createTheme({
  components: {
    MuiButton: {
      variants: [
        {
          props: { variant: 'dashed', color: 'error' },
          style: {
            border: '1px dashed red',
            color: 'red',
          }
        }
      ]
    }
  }
});

// 2. Update the Button's color prop options
declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    dashed: true;
  }
}

// 3. Profit
<Button variant="dashed" color="error">
  dashed
</Button>
```

----------------------------------------

TITLE: Adding Accessibility Label to MUI Switch (JSX)
DESCRIPTION: Demonstrates how to add an accessibility label to the Switch component using the `inputProps` prop to set the `aria-label` attribute. This is necessary when a separate `<label>` element cannot be used, improving accessibility for assistive technologies.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/switches/switches.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Switch value="checkedA" inputProps={{ 'aria-label': 'Switch A' }} />
```

----------------------------------------

TITLE: Forwarding Ref in Functional Component for MUI component prop (Diff)
DESCRIPTION: Shows the change required to make a functional component compatible with Material UI components that require a ref, by wrapping it with `React.forwardRef`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_9

LANGUAGE: diff
CODE:
```
-const MyButton = () => <div role="button" />;
+const MyButton = React.forwardRef((props, ref) =>
+  <div role="button" {...props} ref={ref} />);

 <Button component={MyButton} />;
```

----------------------------------------

TITLE: Add InitColorSchemeScript and CssVarsProvider to Next.js App Router Root Layout (JSX)
DESCRIPTION: Shows the code for the root layout file (`app/layout.js`) in a Next.js App Router application. It imports `InitColorSchemeScript`, `CssVarsProvider`, and `CssBaseline` from `@mui/joy` and includes `InitColorSchemeScript` before `CssVarsProvider` and the main content (`props.children`) within the `<body>` to prevent flickering and set up CSS variables.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/dark-mode/dark-mode.md#_snippet_4

LANGUAGE: jsx
CODE:
```
import InitColorSchemeScript from '@mui/joy/InitColorSchemeScript';
import { CssVarsProvider } from '@mui/joy/styles';
import CssBaseline from '@mui/joy/CssBaseline';

export default function RootLayout(props) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body>
        <InitColorSchemeScript />
        <CssVarsProvider>
          <CssBaseline />
          {props.children}
        </CssVarsProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: HTML Template for SSR (JavaScript)
DESCRIPTION: Defines a function that generates the full HTML page structure. It takes the server-rendered application HTML and the extracted critical CSS as arguments and injects them into the appropriate places within the HTML template.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_4

LANGUAGE: js
CODE:
```
function renderFullPage(html, css) {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8" />
        <title>My page</title>
        ${css}
        <meta name="viewport" content="initial-scale=1, width=device-width" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"
        />
      </head>
      <body>
        <div id="root">${html}</div>
      </body>
    </html>
  `;
}
```

----------------------------------------

TITLE: Using Callback in sx to Access Theme (JSX)
DESCRIPTION: Explains how to use a callback function as the value for the `sx` prop. The callback receives the theme object as an argument, allowing access to complex theme values like `theme.typography.body` or `theme.palette.primary.main` for dynamic styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_15

LANGUAGE: jsx
CODE:
```
<Box
  sx={(theme) => ({
    ...theme.typography.body,
    color: theme.palette.primary.main,
  })}
/>
```

----------------------------------------

TITLE: Updating Accordion Props to Use Slots (Diff)
DESCRIPTION: Demonstrates how to update the Accordion component from using `TransitionComponent` and `TransitionProps` to the new standardized `slots` and `slotProps` pattern for greater flexibility and consistency in replacing or modifying inner elements.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v7-is-here.md#_snippet_0

LANGUAGE: diff
CODE:
```
<Accordion
-  TransitionComponent={CustomTransition}
-  TransitionProps={{ unmountOnExit: true }}
+  slots={{ transition: CustomTransition }}
+  slotProps={{ transition: { unmountOnExit: true } }}
 />
```

----------------------------------------

TITLE: Calculating Grid Item Width with CSS Variables (v2) - JavaScript
DESCRIPTION: Illustrates how the new Grid v2 calculates the width of a grid item using CSS variables (`--Grid-columns`) and the `calc()` function. This approach delegates layout calculation to CSS, reducing JavaScript logic compared to v1.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/build-layouts-faster-with-grid-v2.md#_snippet_1

LANGUAGE: js
CODE:
```
{
  // --Grid-columns is defined in the grid container
  width: `calc(100% * ${value} / var(--Grid-columns))`,
}
```

----------------------------------------

TITLE: Use sx prop for system props with Typography
DESCRIPTION: Apply system props like margin top (`mt`) using the recommended `sx` prop instead of the deprecated direct prop usage on the Typography component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/typography/typography.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Typography sx={{ mt: 2 }} />
```

----------------------------------------

TITLE: Making Dialog Full Screen Responsively with useMediaQuery - MUI React
DESCRIPTION: Demonstrates how to use the `useMediaQuery` hook from Material UI in conjunction with the `theme.breakpoints` to make a Dialog component full screen responsively on smaller screen sizes (down to the 'md' breakpoint).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/dialogs/dialogs.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import useMediaQuery from '@mui/material/useMediaQuery';

function MyComponent() {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  return <Dialog fullScreen={fullScreen} />;
}
```

----------------------------------------

TITLE: Using aria-labelledby for Tabs Accessibility (JS)
DESCRIPTION: Associate a text label with the Material UI Tabs component for screen readers by adding an `id` to a nearby text element (like Typography) and referencing it with the `aria-labelledby` attribute on the Tabs component. Ensure the text element provides a meaningful label.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/tabs/tabs.md#_snippet_1

LANGUAGE: js
CODE:
```
<Typography id="tabs-accessibility-label">Meaningful label</Typography>
<Tabs aria-labelledby="tabs-accessibility-label">...</Tabs>
```

----------------------------------------

TITLE: Express Server with MUI SSR (JavaScript/JSX)
DESCRIPTION: Sets up an Express server to handle incoming requests. It uses `ReactDOMServer.renderToString` to server-render the React application wrapped in Emotion's `CacheProvider` and MUI's `ThemeProvider`. It extracts critical CSS using `createEmotionServer` and sends the rendered HTML and CSS to the client.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import express from 'express';
import * as React from 'react';
import * as ReactDOMServer from 'react-dom/server';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import createEmotionServer from '@emotion/server/create-instance';
import App from './App';
import theme from './theme';
import createEmotionCache from './createEmotionCache';

function handleRender(req, res) {
  const cache = createEmotionCache();
  const { extractCriticalToChunks, constructStyleTagsFromChunks } =
    createEmotionServer(cache);

  // Render the component to a string.
  const html = ReactDOMServer.renderToString(
    <CacheProvider value={cache}>
      <ThemeProvider theme={theme}>
        {/* CssBaseline kickstart an elegant, consistent, and simple baseline
            to build upon. */}
        <CssBaseline />
        <App />
      </ThemeProvider>
    </CacheProvider>,
  );

  // Grab the CSS from emotion
  const emotionChunks = extractCriticalToChunks(html);
  const emotionCss = constructStyleTagsFromChunks(emotionChunks);

  // Send the rendered page back to the client.
  res.send(renderFullPage(html, emotionCss));
}

const app = express();

app.use('/build', express.static('build'));

// This is fired every time the server-side receives a request.
app.use(handleRender);

const port = 3000;
app.listen(port);
```

----------------------------------------

TITLE: Implementing MUI Mode Switcher with useColorScheme Hook - React/JSX
DESCRIPTION: Demonstrates how to use the `useColorScheme` hook from `@mui/material/styles` to get and set the current color mode, providing a UI element (a select dropdown) to allow users to switch between 'system', 'light', and 'dark' modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_5

LANGUAGE: jsx
CODE:
```
import { useColorScheme } from '@mui/material/styles';

function ModeSwitcher() {
  const { mode, setMode } = useColorScheme();

  if (!mode) {
    return null;
  }

  return (
    <select
      value={mode}
      onChange={(event) => {
        setMode(event.target.value);
        // For TypeScript, cast `event.target.value as 'light' | 'dark' | 'system'`:
      }}
    >
      <option value="system">System</option>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
    </select>
  );
}
```

----------------------------------------

TITLE: TypeScript Module Augmentation for Custom Theme with sx (TSX)
DESCRIPTION: Shows how to use TypeScript module augmentation to add custom properties (like `status`) to the MUI `Theme` type. It then demonstrates how to create a theme with these custom properties and access them within an `sx` prop callback function.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_18

LANGUAGE: tsx
CODE:
```
import * as React from 'react';
import Box from '@mui/material/Box';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { orange } from '@mui/material/colors';

declare module '@mui/system' {
  interface Theme {
    status: {
      warning: string;
    };
  }
}

const theme = createTheme({
  status: {
    warning: orange[500],
  },
});

export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={(theme) => ({
          bgcolor: theme.status.warning,
        })}
      >
        Example
      </Box>
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Applying Material UI v6 Container Queries with sx prop
DESCRIPTION: Illustrates how to use Material UI v6 container queries directly within the `sx` prop of a component, providing a concise way to apply responsive styles based on container width, and showing the simplified CSS output.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_7

LANGUAGE: JSX
CODE:
```
<Card
  sx={{
    '@sm': {
      flexDirection: 'row',
    },
    '@400/sidebar': {
      flexDirection: 'row',
    },
  }}
/>
```

LANGUAGE: CSS
CODE:
```
/* Simplified CSS Output */

.MuiCard-root-dn383 {
  @container (min-width: 600px) {
    flexDirection: 'row';
  }
  @container sidebar (min-width: 400px) {
    flexDirection: 'row';
  }
}
```

----------------------------------------

TITLE: Horizontal Centering with Spacing in MUI
DESCRIPTION: Shows how to horizontally center an element using the `mx: 'auto'` shorthand within the `sx` prop, combined with a specified width, leveraging the spacing utility.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/spacing/spacing.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Box sx={{ mx: 'auto', width: 200 }}>…
```

----------------------------------------

TITLE: Typing Specific Custom Component with MUI component prop (TypeScript)
DESCRIPTION: Demonstrates how to type a custom component that accepts the `component` prop and is specifically intended to render as an `<a>` element, using `TypographyProps` with type arguments.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_6

LANGUAGE: ts
CODE:
```
import { TypographyProps } from '@mui/material/Typography';

function CustomComponent(props: TypographyProps<'a', { component: 'a' }>) {
  /* ... */
}
// ...
<CustomComponent component="a" />;
```

----------------------------------------

TITLE: Extend Sheet Variants with Theme & Augmentation - JS/TS/JSX
DESCRIPTION: Illustrates how to add a custom variant value ('glass') to the Joy UI Sheet component by extending the theme's `components.JoySheet.styleOverrides.root` in JavaScript and providing type safety with TypeScript module augmentation via `SheetPropsVariantOverrides`. Includes an example of using the new variant in JSX.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_9

LANGUAGE: javascript
CODE:
```
extendTheme({
  components: {
    JoySheet: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          ...(ownerState.variant === 'glass' && {
            color: theme.vars.palette.text.primary,
            background: 'rgba(255, 255, 255, 0.14)',
            backdropFilter: 'blur(5px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          }),
        }),
      },
    },
  },
});
```

LANGUAGE: jsx
CODE:
```
<Sheet variant="glass">Glassmorphism</Sheet>
```

LANGUAGE: typescript
CODE:
```
// This part could be declared in your theme file
declare module '@mui/joy/Sheet' {
  interface SheetPropsVariantOverrides {
    glass: true;
  }
}

// typed-safe
<Sheet variant="glass" />;
```

----------------------------------------

TITLE: Labeling MUI Select using TextField (JSX)
DESCRIPTION: Shows how using a `TextField` with the `select` prop simplifies accessibility labeling for a Material UI select input. The `TextField` automatically generates the necessary markup and associates the `label` prop with the underlying select element using matching `id` attributes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/selects/selects.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<TextField id="select" label="Age" value="20" select>
  <MenuItem value="10">Ten</MenuItem>
  <MenuItem value="20">Twenty</MenuItem>
</TextField>
```

----------------------------------------

TITLE: Adding Start Icon to MUI Button (JSX)
DESCRIPTION: Demonstrates how to use the `startIcon` prop on a Material UI Button component to easily prepend an icon. Requires importing the desired icon component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/september-2019-update.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import DeleteIcon from '@mui/icons-material/Delete';

<Button startIcon={<DeleteIcon />}>Delete</Button>;
```

----------------------------------------

TITLE: Setting Fluid Width with maxWidth in Material UI Container (JSX)
DESCRIPTION: Demonstrates how to create a fluid Material UI Container whose maximum width is bounded by the specified `maxWidth` prop value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/container/container.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Container maxWidth="sm">
```

----------------------------------------

TITLE: Using the sx prop with Box in Material UI v5 - TSX
DESCRIPTION: This snippet demonstrates how to use the `sx` prop on a Material UI Box component to apply custom styles. It shows setting basic dimensions and background color, as well as applying hover styles using the '&:hover' selector and using an array for opacity.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5-migration-update.md#_snippet_0

LANGUAGE: tsx
CODE:
```
import * as React from 'react';
import Box from '@mui/material/Box';

export default function BoxSx() {
  return (
    <Box
      sx={{
        width: 300,
        height: 300,
        backgroundColor: 'primary.dark',
        '&:hover': {
          backgroundColor: 'primary.main',
          opacity: [0.9, 0.8, 0.7],
        },
      }}
    />
  );
}
```

----------------------------------------

TITLE: Labeling MUI NativeSelect for Accessibility (JSX)
DESCRIPTION: Demonstrates how to label a Material UI `NativeSelect` component for accessibility. An `InputLabel` is used, and its `htmlFor` attribute is set to match the `id` of the `NativeSelect` element, creating an accessible association between the label and the native select input.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/selects/selects.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<InputLabel htmlFor="select">Age</InputLabel>
<NativeSelect id="select">
  <option value="10">Ten</option>
  <option value="20">Twenty</option>
</NativeSelect>
```

----------------------------------------

TITLE: Creating Joy UI ThemeRegistry Component in Next.js App Router (TSX)
DESCRIPTION: Defines a client component `ThemeRegistry` that sets up Emotion's cache and Joy UI's CSS variables provider. It uses `useServerInsertedHTML` to inject server-rendered styles, ensuring correct styling with React Server Components. This component is essential for integrating Joy UI themes and styles in the Next.js App Router.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/next-js-app-router/next-js-app-router.md#_snippet_0

LANGUAGE: tsx
CODE:
```
// app/ThemeRegistry.tsx
'use client';
import createCache from '@emotion/cache';
import { useServerInsertedHTML } from 'next/navigation';
import { CacheProvider } from '@emotion/react';
import { CssVarsProvider } from '@mui/joy/styles';
import CssBaseline from '@mui/joy/CssBaseline';
import theme from '/path/to/custom/theme'; // OPTIONAL

// This implementation is from emotion-js
// https://github.com/emotion-js/emotion/issues/2928#issuecomment-**********
export default function ThemeRegistry(props) {
  const { options, children } = props;

  const [{ cache, flush }] = React.useState(() => {
    const cache = createCache(options);
    cache.compat = true;
    const prevInsert = cache.insert;
    let inserted: string[] = [];
    cache.insert = (...args) => {
      const serialized = args[1];
      if (cache.inserted[serialized.name] === undefined) {
        inserted.push(serialized.name);
      }
      return prevInsert(...args);
    };
    const flush = () => {
      const prevInserted = inserted;
      inserted = [];
      return prevInserted;
    };
    return { cache, flush };
  });

  useServerInsertedHTML(() => {
    const names = flush();
    if (names.length === 0) {
      return null;
    }
    let styles = '';
    for (const name of names) {
      styles += cache.inserted[name];
    }
    return (
      <style
        key={cache.key}
        data-emotion={`${cache.key} ${names.join(' ')}`}
        dangerouslySetInnerHTML={{
          __html: styles,
        }}
      />
    );
  });

  return (
    <CacheProvider value={cache}>
      <CssVarsProvider theme={theme}>
        {/* the custom theme is optional */}
        <CssBaseline />
        {children}
      </CssVarsProvider>
    </CacheProvider>
  );
}
```

----------------------------------------

TITLE: Passing Ref to Custom Components for MUI Transitions (Correct)
DESCRIPTION: Use `React.forwardRef` when defining a custom component that will be used as a child of a Material UI transition component. This ensures the ref is passed down and attached to the correct DOM node.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/troubleshooting.md#_snippet_7

LANGUAGE: jsx
CODE:
```
// Ex. 2-2 ✅ Add `React.forwardRef` to forward `ref` to the DOM:
const CustomComponent = React.forwardRef(function CustomComponent(props, ref) {
  return (
    <div ref={ref}>
      ...
    </div>
  )
})

<Fade in>
  <CustomComponent />
</Fade>

```

----------------------------------------

TITLE: Customizing Material UI Palette Colors Directly (JS)
DESCRIPTION: Shows how to override default palette colors (primary and secondary) by providing custom 'main', 'light', and 'contrastText' values directly within the `createTheme` function. Demonstrates that optional values are calculated automatically if not provided. Requires `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_1

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#FF5733',
      // light: will be calculated from palette.primary.main,
      // dark: will be calculated from palette.primary.main,
      // contrastText: will be calculated to contrast with palette.primary.main
    },
    secondary: {
      main: '#E0C2FF',
      light: '#F5EBFF',
      // dark: will be calculated from palette.secondary.main,
      contrastText: '#47008F',
    },
  },
});
```

----------------------------------------

TITLE: Running MUI v5 preset-safe Codemod (Bash)
DESCRIPTION: Command to execute the `preset-safe` codemod provided by MUI. This codemod applies a collection of necessary transformations for migrating to v5 and should be run once per project folder.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migration-v4.md#_snippet_9

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v5.0.0/preset-safe <path>
```

----------------------------------------

TITLE: Override Root Slot with component Prop (Demo)
DESCRIPTION: This demo illustrates how to replace the default HTML element used for a component's root slot (e.g., changing a Button's root from <button> to <a>) by using the `component` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/overriding-component-structure/overriding-component-structure.md#_snippet_0

LANGUAGE: text
CODE:
```
{{ "demo": "OverridingRootSlot.js" }}
```

----------------------------------------

TITLE: Declare Public MUI Component with forwardRef and ThemeProps (TS)
DESCRIPTION: Demonstrates how to declare a public MUI component using `React.forwardRef` for ref forwarding, `useThemeProps` to handle theme props and default props, and how to construct and pass `ownerState` to a styled component for styling purposes. Mentions the importance of naming the function for devtools.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/TYPESCRIPT_CONVENTION.md#_snippet_9

LANGUAGE: TypeScript
CODE:
```
const Foo = React.forwardRef<HTMLSpanElement, FooProps>(function Foo(inProps, ref) => {
  // pass args like this, otherwise will get error about theme at return section
  const props = useThemeProps<Theme, FooProps, 'MuiFoo'>({
    props: inProps,
    name: 'MuiFoo',
  });
  const { children, className, ...other } = props

  // ...implementation

  const ownerState = { ...props, ...otherValue }

  const classes = useUtilityClasses(ownerState);

  return (
    <FooRoot
      ref={ref}
      className={clsx(classes.root, className)}
      ownerState={ownerState}
      {...other}
    >
      {children}
    </FooRoot>
  )
})
```

----------------------------------------

TITLE: Handling Custom Functional Component Child in MUI Tooltip (JSX)
DESCRIPTION: Demonstrates how to create a custom functional React component and ensure it correctly forwards props and refs to its underlying DOM element, allowing it to be used as a child of the MUI Tooltip component. This is necessary for the Tooltip to attach event listeners.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/tooltips/tooltips.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const MyComponent = React.forwardRef(function MyComponent(props, ref) {
  //  Spread the props to the underlying DOM element.
  return (
    <div {...props} ref={ref}>
      Bin
    </div>
  );
});

// ...

<Tooltip title="Delete">
  <MyComponent />
</Tooltip>;
```

----------------------------------------

TITLE: Adding Custom Variables to MUI Theme (JSX)
DESCRIPTION: Demonstrates how to add custom properties like `status` to the Material UI theme object using `createTheme`. This allows accessing these custom variables throughout the application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_0

LANGUAGE: JSX
CODE:
```
const theme = createTheme({
  status: {
    danger: orange[500],
  },
});
```

----------------------------------------

TITLE: Importing Joy UI Tabs Components - JSX
DESCRIPTION: This snippet shows the necessary import statements to use the core Tabs, TabList, and Tab components from the `@mui/joy` library in a React application using JSX. These components are fundamental for building a tabbed interface.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/tabs/tabs.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Tabs from '@mui/joy/Tabs';
import TabList from '@mui/joy/TabList';
import Tab from '@mui/joy/Tab';
```

----------------------------------------

TITLE: Prevent Hydration Mismatch in SSR with useEffect (React Diff)
DESCRIPTION: Illustrates how to modify a React component (`ModeToggle`) that uses `useColorScheme` to prevent hydration errors in server-side rendering. It adds a `mounted` state and a `React.useEffect` hook to set `mounted` to true only after the component mounts on the client, rendering a placeholder until then.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/dark-mode/dark-mode.md#_snippet_2

LANGUAGE: diff
CODE:
```
 function ModeToggle() {
   const { mode, setMode } = useColorScheme();
   const [mounted, setMounted] = React.useState(false);

+  React.useEffect(() => {
+    setMounted(true);
+  }, []);
+
+  if (!mounted) {
+    // to avoid layout shift, render a placeholder button
+    return <Button variant="outlined" color="neutral" sx={{ width: 120 }} />;
+  }

   return (
     <Button
       variant="outlined"
       color="neutral"
       onClick={() => setMode(mode === 'dark' ? 'light' : 'dark')}
     >
       {mode === 'dark' ? 'Turn light' : 'Turn dark'}
     </Button>
   );
 };
```

----------------------------------------

TITLE: Adding an Accessible Icon Button (JS)
DESCRIPTION: This example demonstrates how to create an accessible Icon Button by providing a meaningful `aria-label` attribute, which is crucial for users of assistive technology, especially when the button contains only an icon.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/button/button.md#_snippet_1

LANGUAGE: js
CODE:
```
<IconButton aria-label="Add to favorites">
  <FavoriteBorder />
</IconButton>
```

----------------------------------------

TITLE: Updating Testing Library Interactions for Ripple Effect - MUI v6
DESCRIPTION: Explains the need to wrap `fireEvent` calls for components with ripple effects inside `act` and `await` when using `@testing-library/react` to handle asynchronous updates and avoid React warnings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_20

LANGUAGE: javascript
CODE:
```
await act(async () => fireEvent.mouseDown(button));
```

----------------------------------------

TITLE: Applying Pseudo-Selectors with MUI System sx Prop (JSX)
DESCRIPTION: Demonstrates how to use CSS pseudo-selectors like `:hover` within the `sx` prop to apply styles based on element states. This example changes the `boxShadow` on hover, allowing state-dependent styling directly in the `sx` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_4

LANGUAGE: JSX
CODE:
```
<Box
  sx={{    // some styles
    ":hover": {
      boxShadow: 6,
    },
  }}
>

```

----------------------------------------

TITLE: Using sx prop with shortcut (Correct)
DESCRIPTION: Shows the correct way to use an `sx` prop shortcut like `mx` (margin on x-axis) by applying it directly to a Material UI component via the `sx` prop. This shortcut is specific to the `sx` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_4

LANGUAGE: js
CODE:
```
import Button from '@mui/material/Button';

const MyStyledButton = (props) => (
  <Button
    sx={{
      mx: 1, // ✔️ this shortcut is specific to the `sx` prop,
    }}
  >
    {props.children}
  </Button>
);
```

----------------------------------------

TITLE: Including Styles with Nonce in SSR (JSX)
DESCRIPTION: Renders a <style> tag in Server-Side Rendering (SSR). It includes Emotion's data attributes, applies the generated nonce, and injects the CSS content using dangerouslySetInnerHTML. The nonce allows the browser to execute this specific style block under the CSP.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/content-security-policy/content-security-policy.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<style
  data-emotion={`${style.key} ${style.ids.join(' ')}`}
  nonce={nonce}
  dangerouslySetInnerHTML={{ __html: style.css }}
/>
```

----------------------------------------

TITLE: Applying Styles with sx Prop on DOM Elements (React)
DESCRIPTION: Illustrates the use of the `sx` prop in Pigment CSS to apply theme-aware styles directly to standard HTML DOM elements like `<section>`, `<h1>`, and `<p>`, without needing wrapper components like MUI's Box.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/introducing-pigment-css.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<section sx={{ p: 2, border: '1px solid', borderColor: 'divider' }}>
  <h1 sx={{ fontSize: '2rem', fontWeight: 700, mb: 1 }}>
    Introducing Pigment CSS: the next generation of CSS-in-JS
  </h1>
  <p sx={{ color: 'text.primary', fontWeight: 500 }}>
    Pigment CSS offers significant performance gains along with RSC
  </p>
</section>
```

----------------------------------------

TITLE: Adding Accessibility Attributes to Checkbox (JSX)
DESCRIPTION: Demonstrates how to apply accessibility attributes like `aria-label` directly to the underlying input element of the Checkbox component using the `inputProps` prop when a standard label element cannot be used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/checkboxes/checkboxes.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Checkbox
  value="checkedA"
  inputProps={{
    'aria-label': 'Checkbox A',
  }}
/>
```

----------------------------------------

TITLE: Implementing prefers-color-scheme with Material UI v6
DESCRIPTION: Demonstrates configuring the Material UI v6 theme with `cssVariables` and `colorSchemes` to enable automatic dark mode based on the user's `prefers-color-scheme` setting, showing the corresponding CSS output with root variables and media queries.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_5

LANGUAGE: JSX
CODE:
```
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  cssVariables: true,
  colorSchemes: { dark: true },
});

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

LANGUAGE: CSS
CODE:
```
:root {
  --mui-palette-primary-main: #1976d2;
  --mui-palette-background-default: #fff;
  ...
}

@media (prefers-color-scheme: dark) {
  :root {
    --mui-palette-primary-main: #90caf9;
    --mui-palette-background-default: #121212;
    ...
  }
}
```

----------------------------------------

TITLE: Customizing MUI Components with Global Class Names (TSX)
DESCRIPTION: Presents three methods for styling the notched outline of an MUI OutlinedInput (used within TextField): using a direct global class name string, using the `outlinedInputClasses` helper for type safety, and using the older `classes` prop approach. It highlights the simplicity and type safety benefits of the global class name options in v5.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_5

LANGUAGE: tsx
CODE:
```
import TextField from '@mui/material/TextField';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import { styled } from '@mui/material/styles';

// Option 1: global class
const CustomizedTextField1 = styled(TextField)({
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: 'red',
  },
});

// Option 2: global class + const
const CustomizedTextField2 = styled(TextField)({
  [`& .${outlinedInputClasses.notchedOutline}`]: {
    borderColor: 'red',
  },
});

// Option 3: classes prop (before)
const CustomizedTextField3 = styled((props) => (
  <TextField
    {...props}
    variant="outlined"
    InputProps={{ classes: { notchedOutline: 'foo' } }}
  />
))({
  '& .foo': {
    borderColor: 'red',
  },
}) as typeof TextField;
```

----------------------------------------

TITLE: Implementing Accessibility with Joy UI Form Control - JavaScript
DESCRIPTION: Shows how the Joy UI Form Control component automatically generates a unique ID to link an Input element with its corresponding Form Label and Form Helper Text, ensuring compliance with accessibility guidelines.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/overview/overview.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
../../components/input/InputField.js
```

----------------------------------------

TITLE: Install Material UI with Emotion
DESCRIPTION: Installs the core Material UI package along with the default Emotion styling engine. These commands add the necessary dependencies (@mui/material, @emotion/react, @emotion/styled) using npm, pnpm, or yarn.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/installation/installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/material @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/material @emotion/react @emotion/styled
```

LANGUAGE: bash
CODE:
```
yarn add @mui/material @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Adding Accessibility Labels to MUI Modal (JSX)
DESCRIPTION: Demonstrates how to add accessible names and descriptions to a MUI Modal component using the `aria-labelledby` and `aria-describedby` props for improved accessibility. The `aria-labelledby` prop points to the ID of the element providing the modal's title, and `aria-describedby` points to the ID of the element providing its description.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/modal/modal.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Modal aria-labelledby="modal-title" aria-describedby="modal-description">
  <h2 id="modal-title">My Title</h2>
  <p id="modal-description">My Description</p>
</Modal>
```

----------------------------------------

TITLE: Adding aria-label to Material UI Radio Input (JSX)
DESCRIPTION: Demonstrates how to add an `aria-label` directly to the underlying input element of a Material UI Radio component using the `slotProps.input` prop, which is useful for accessibility when a standard label cannot be used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/radio-button/radio-button.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Radio
  value="radioA"
  slotProps={{
    input: {
      'aria-label': 'Radio A',
    },
  }}
/>
```

----------------------------------------

TITLE: Applying Spacing with sx prop in MUI
DESCRIPTION: Shows how to set the `margin` property using the `sx` prop. The provided value is multiplied by the `theme.spacing` value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_11

LANGUAGE: jsx
CODE:
```
<Box sx={{ margin: 2 }} />
// equivalent to margin: theme => theme.spacing(2)
```

----------------------------------------

TITLE: Client-side Hydration with MUI (JavaScript/JSX)
DESCRIPTION: Initializes the client-side application. It uses the same Emotion cache as the server and wraps the root component in `CacheProvider` and `ThemeProvider`. `ReactDOM.hydrateRoot` is used to attach the React application to the server-rendered HTML, enabling interactivity.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_5

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import App from './App';
import theme from './theme';
import createEmotionCache from './createEmotionCache';

const cache = createEmotionCache();

function Main() {
  return (
    <CacheProvider value={cache}>
      <ThemeProvider theme={theme}>
        {/* CssBaseline kickstart an elegant, consistent, and simple baseline
            to build upon. */}
        <CssBaseline />
        <App />
      </ThemeProvider>
    </CacheProvider>
  );
}

ReactDOM.hydrateRoot(document.querySelector('#root'), <Main />);
```

----------------------------------------

TITLE: Basic Autocomplete Usage in Joy UI
DESCRIPTION: Demonstrates the minimal setup required to use the Autocomplete component from Joy UI, importing it and rendering it with a simple array of options.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/autocomplete/autocomplete.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Autocomplete from '@mui/joy/Autocomplete';
import Input from '@mui/joy/Input';

export default function App() {
  return <Autocomplete options={['Option 1', 'Option 2']} />;
}
```

----------------------------------------

TITLE: Applying Custom Class and Disabled State to Button
DESCRIPTION: Shows how to apply a custom class name (`"Button"`) and the `disabled` prop to a Material UI `Button` component, corresponding to the CSS example for the disabled state.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/how-to-customize/how-to-customize.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Button disabled className="Button">
```

----------------------------------------

TITLE: Basic Usage of Joy UI Menu in React
DESCRIPTION: Demonstrates the fundamental structure for creating a Joy UI menu. It shows how to import and compose the core components: Dropdown, MenuButton, Menu, and MenuItem. The Dropdown component acts as a wrapper to connect the MenuButton (anchor) with the Menu (popup list).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/menu/menu.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Menu from '@mui/joy/Menu';
import MenuButton from '@mui/joy/MenuButton';
import MenuItem from '@mui/joy/MenuItem';
import Dropdown from '@mui/joy/Dropdown';

export default function MyApp() {
  return (
    <Dropdown>
      <MenuButton>Actions</MenuButton>
      <Menu>
        <MenuItem>Add item</MenuItem>
      </Menu>
    </Dropdown>
  );
}
```

----------------------------------------

TITLE: Defining Danger Palette and Theme Colors (JS)
DESCRIPTION: This snippet defines a custom danger color scale and uses `extendTheme` to apply these colors within the light and dark color schemes of the theme. It configures the danger palette properties, including variations for different component states (plain, outlined, soft, solid).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_8

LANGUAGE: js
CODE:
```
const danger = {
  50: '#FFF8F6',
  100: '#FFE9E8',
  200: '#FFC7C5',
  300: '#FF9192',
  400: '#FA5255',
  500: '#D3232F',
  600: '#A10E25',
  700: '#77061B',
  800: '#580013',
  900: '#39000D'
};

extendTheme({
  colorSchemes: {
    light: {
      palette: {
        danger: {
          ...danger,
          plainColor: `var(--joy-palette-danger-600)`,
          plainHoverBg: `var(--joy-palette-danger-100)`,
          plainActiveBg: `var(--joy-palette-danger-200)`,
          plainDisabledColor: `var(--joy-palette-danger-200)`,

          outlinedColor: `var(--joy-palette-danger-500)`,
          outlinedBorder: `var(--joy-palette-danger-200)`,
          outlinedHoverBg: `var(--joy-palette-danger-100)`,
          outlinedHoverBorder: `var(--joy-palette-danger-300)`,
          outlinedActiveBg: `var(--joy-palette-danger-200)`,
          outlinedDisabledColor: `var(--joy-palette-danger-100)`,
          outlinedDisabledBorder: `var(--joy-palette-danger-100)`,

          softColor: `var(--joy-palette-danger-600)`,
          softBg: `var(--joy-palette-danger-100)`,
          softHoverBg: `var(--joy-palette-danger-200)`,
          softActiveBg: `var(--joy-palette-danger-300)`,
          softDisabledColor: `var(--joy-palette-danger-300)`,
          softDisabledBg: `var(--joy-palette-danger}-)50`,

          solidColor: '#fff',
          solidBg: `var(--joy-palette-danger-500)`,
          solidHoverBg: `var(--joy-palette-danger-600)`,
          solidActiveBg: `var(--joy-palette-danger-700)`,
          solidDisabledColor: `#fff`,
          solidDisabledBg: `var(--joy-palette-danger-200)`
        }
      }
    },
    dark: {
      palette: {
        danger: {
          ...danger,
          plainColor: `var(--joy-palette-danger-300)`,
          plainHoverBg: `var(--joy-palette-danger-800)`,
          plainActiveBg: `var(--joy-palette-danger-700)`,
          plainDisabledColor: `var(--joy-palette-danger-800)`,

          outlinedColor: `var(--joy-palette-danger-200)`,
          outlinedBorder: `var(--joy-palette-danger-700)`,
          outlinedHoverBg: `var(--joy-palette-danger-800)`,
          outlinedHoverBorder: `var(--joy-palette-danger-600)`
        }
      }
    }
  }
});
```

----------------------------------------

TITLE: Mapping fontWeight to Theme Typography Simplified Key (JSX)
DESCRIPTION: Illustrates a simplified way to map the `fontWeight` CSS property to a theme typography value using the `sx` prop. By omitting the 'fontWeight' prefix, the value 'light' is automatically mapped to `theme.typography.fontWeightLight`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_13

LANGUAGE: jsx
CODE:
```
<Box sx={{ fontWeight: 'light' }} />
// equivalent to fontWeight: theme.typography.fontWeightLight
```

----------------------------------------

TITLE: Setting align-content property with MUI Box in JSX
DESCRIPTION: Shows how to align lines of flex items within a multi-line MUI `Box` flex container using the `alignContent` property in the `sx` prop. This property is used when `flex-wrap` is set to `wrap` or `wrap-reverse` and there is extra space in the cross axis.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Box sx={{ alignContent: 'flex-start' }}>…
<Box sx={{ alignContent: 'flex-end' }}>…
<Box sx={{ alignContent: 'center' }}>…
<Box sx={{ alignContent: 'space-between' }}>…
<Box sx={{ alignContent: 'space-around' }}>…
<Box sx={{ alignContent: 'stretch' }}>…
```

----------------------------------------

TITLE: Applying Box Shadows with sx Prop (JSX)
DESCRIPTION: This snippet demonstrates how to apply different levels of box shadows (0, 1, 2, 3) to a Material UI Box component using the `boxShadow` property within the `sx` prop. This controls the visual depth or elevation of the element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/shadows/shadows.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ boxShadow: 0 }}>…
<Box sx={{ boxShadow: 1 }}>…
<Box sx={{ boxShadow: 2 }}>…
<Box sx={{ boxShadow: 3 }}>…
```

----------------------------------------

TITLE: Removing System Props and Adding to SX Prop JSX
DESCRIPTION: Demonstrates how the `system-props` codemod migrates component usage by removing direct system props like `ml`, `py`, and `color` and consolidating them within the `sx` prop object.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_149

LANGUAGE: diff
CODE:
```
-<Box ml="2px" py={1} color="primary.main" />
+<Box sx={{ ml: '2px', py: 1, color: 'primary.main' }} />
```

----------------------------------------

TITLE: MUI Box Max-Width Breakpoint Example - JSX
DESCRIPTION: Shows how to set the `maxWidth` property using a theme breakpoint key like 'md' within the MUI `Box` component's `sx` prop, which resolves to a specific pixel value defined in the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/sizing/sizing.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Box sx={{ maxWidth: 'md' }}>…
```

----------------------------------------

TITLE: Installing Joy UI and Dependencies (npm)
DESCRIPTION: Installs the core Joy UI package along with required peer dependencies for styling (@emotion/react, @emotion/styled) using npm.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-joy/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/joy @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Implement Custom Input Component for MUI TextField (JSX)
DESCRIPTION: Provides an example of creating a custom React component that implements the `InputElement` interface using `React.forwardRef` and `React.useImperativeHandle`. This component acts as a wrapper for a third-party input library component and can be passed to the `inputComponent` prop of a Material UI TextField to enable integration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/text-fields/text-fields.md#_snippet_7

LANGUAGE: jsx
CODE:
```
const MyInputComponent = React.forwardRef((props, ref) => {
  const { component: Component, ...other } = props;

  // implement `InputElement` interface
  React.useImperativeHandle(ref, () => ({
    focus: () => {
      // logic to focus the rendered component from 3rd party belongs here
    },
    // hiding the value e.g. react-stripe-elements
  }));

  // `Component` will be your `SomeThirdPartyComponent` from below
  return <Component {...other} />;
});

// usage
<TextField
  slotProps={{
    input: {
      inputComponent: MyInputComponent,
      inputProps: {
        component: SomeThirdPartyComponent,
      },
    },
  }}
/>;
```

----------------------------------------

TITLE: Importing the DialogContent component (Joy UI)
DESCRIPTION: Imports the DialogContent component from the Joy UI library, used to create scrollable content areas within the Drawer.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/drawer/drawer.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import DialogContent from '@mui/joy/DialogContent';
```

----------------------------------------

TITLE: Setting justify-content property with MUI Box in JSX
DESCRIPTION: Demonstrates various `justify-content` values (`flex-start`, `flex-end`, `center`, `space-between`, `space-around`, `space-evenly`) applied to a MUI `Box` component using the `sx` prop to align flex items along the main axis. Various alignment strategies like spacing items out or grouping them are shown.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Box sx={{ justifyContent: 'flex-start' }}>…
<Box sx={{ justifyContent: 'flex-end' }}>…
<Box sx={{ justifyContent: 'center' }}>…
<Box sx={{ justifyContent: 'space-between' }}>…
<Box sx={{ justifyContent: 'space-around' }}>…
<Box sx={{ justifyContent: 'space-evenly' }}>…
```

----------------------------------------

TITLE: Importing Joy UI Typography Component (JSX)
DESCRIPTION: Imports the `Typography` component from the `@mui/joy/Typography` module. `Typography` is often used within Breadcrumbs items, particularly for the last item representing the current page, which is typically not a clickable link.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/breadcrumbs/breadcrumbs.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import Typography from '@mui/joy/Typography';
```

----------------------------------------

TITLE: Updating FormControlLabel Props - JSX Usage
DESCRIPTION: Demonstrates migrating from the deprecated `componentsProps` prop to `slotProps` for customizing subcomponents of the MUI FormControlLabel in JSX.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_52

LANGUAGE: jsx
CODE:
```
 <FormControlLabel
-  componentsProps={{ typography: typographyProps }}
+  slotProps={{ typography: typographyProps }}
 />
```

----------------------------------------

TITLE: Add Responsive Viewport Meta Tag (HTML)
DESCRIPTION: To ensure proper rendering and touch zooming across all devices, especially for Joy UI's mobile-first design, include this meta tag in the <head> section of your HTML document.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/usage/usage.md#_snippet_0

LANGUAGE: html
CODE:
```
<meta name="viewport" content="initial-scale=1, width=device-width" />
```

----------------------------------------

TITLE: Applying Box Shadow with sx prop in MUI
DESCRIPTION: Illustrates setting the `boxShadow` property using the `sx` prop by mapping the value to an index in the `theme.shadows` array.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<Box sx={{ boxShadow: 1 }} />
// equivalent to boxShadow: theme => theme.shadows[1]
```

----------------------------------------

TITLE: Accessing MUI Theme with `useTheme` Hook (JSX)
DESCRIPTION: Illustrates how to use the `useTheme` hook from `@mui/material/styles` within a functional React component to access the current theme object and its properties, such as `theme.spacing`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_3

LANGUAGE: JSX
CODE:
```
import { useTheme } from '@mui/material/styles';

function DeepChild() {
  const theme = useTheme();
  return <sp an>{`spacing ${theme.spacing}`}</sp an>;
}
```

----------------------------------------

TITLE: Extend Button Colors with Module Augmentation - TypeScript/JSX
DESCRIPTION: Demonstrates how to add custom color values ('secondary', 'tertiary') to the Joy UI Button component's 'color' prop using TypeScript module augmentation and how to use them in JSX. This requires declaring the new values in the `@mui/joy/Button` module's `ButtonPropsColorOverrides` interface.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_7

LANGUAGE: typescript
CODE:
```
// This part could be declared in your theme file
declare module '@mui/joy/Button' {
  interface ButtonPropsColorOverrides {
    secondary: true;
    tertiary: true;
  }
}

// typed-safe
<Button color="secondary" />
<Button color="tertiary" />
```

----------------------------------------

TITLE: Extending MUI Theme with Custom Primary Palette (JavaScript)
DESCRIPTION: This snippet defines a custom primary color palette using hex codes and extends the MUI theme's light color scheme to incorporate these custom colors. It configures specific states like plain, hover, active, and disabled using CSS variables, demonstrating how to integrate custom color definitions into the theme structure for consistent styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/docs/markdown.md#_snippet_0

LANGUAGE: js
CODE:
```
const primary = {
  50: '#F4FAFF',
  100: '#DDF1FF',
  200: '#ADDBFF'
};

extendTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: {
          ...primary,
          plainColor: `var(--joy-palette-primary-600)`,
          plainHoverBg: `var(--joy-palette-primary-100)`,
          plainActiveBg: `var(--joy-palette-primary-200)`,
          plainDisabledColor: `var(--joy-palette-primary-200)`
        }
      }
    }
  }
});
```

----------------------------------------

TITLE: Globally change Typography variant mapping in theme
DESCRIPTION: Modify the default mapping between Typography variants and their corresponding semantic HTML elements globally within your Material UI theme configuration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/typography/typography.md#_snippet_6

LANGUAGE: js
CODE:
```
const theme = createTheme({
  components: {
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          h1: 'h2',
          h2: 'h2',
          h3: 'h2',
          h4: 'h2',
          h5: 'h2',
          h6: 'h2',
          subtitle1: 'h2',
          subtitle2: 'h2',
          body1: 'span',
          body2: 'span'
        }
      }
    }
  }
});
```

----------------------------------------

TITLE: Setting display property with MUI Box in JSX
DESCRIPTION: Explains how to make an element a flex container using `display: 'flex'` or `display: 'inline-flex'` via MUI's `Box` component and `sx` prop. This is the first step to enabling flexbox layout for its children.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ display: 'flex' }}>…
<Box sx={{ display: 'inline-flex' }}>…
```

----------------------------------------

TITLE: JSX CSS Injection Order (StyledEngineProvider)
DESCRIPTION: Demonstrates how to change the CSS injection order in Material UI using `StyledEngineProvider` with the `injectFirst` prop to ensure custom styles take precedence over Material UI's default styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_7

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { StyledEngineProvider } from '@mui/material/styles';

export default function GlobalCssPriority() {
  return (
    <StyledEngineProvider injectFirst>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </StyledEngineProvider>
  );
}
```

----------------------------------------

TITLE: Changing CSS Injection Order with StyledEngineProvider (JSX)
DESCRIPTION: Demonstrates how to use Material UI's StyledEngineProvider with the injectFirst prop to ensure that custom styles take precedence over Material UI's default styles, removing the need for !important.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_17

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { StyledEngineProvider } from '@mui/material/styles';

export default function GlobalCssPriority() {
  return (
    <StyledEngineProvider injectFirst>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </StyledEngineProvider>
  );
}
```

----------------------------------------

TITLE: Applying Text Transformation in MUI JSX
DESCRIPTION: Illustrates how to modify the capitalization of text using the `textTransform` CSS property via the `sx` prop on a `Box` component. Examples cover capitalizing, lowercasing, and uppercasing text.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Box sx={{ textTransform: 'capitalize' }}>…
<Box sx={{ textTransform: 'lowercase' }}>…
<Box sx={{ textTransform: 'uppercase' }}>…
```

----------------------------------------

TITLE: Applying Sizing with sx prop in MUI
DESCRIPTION: Demonstrates how the `width` property in the `sx` prop handles different value types. Values between (0, 1] are treated as percentages, while others are treated as pixel values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_10

LANGUAGE: jsx
CODE:
```
<Box sx={{ width: 1/2 }} /> // equivalent to width: '50%'
<Box sx={{ width: 20 }} /> // equivalent to width: '20px'
```

----------------------------------------

TITLE: Extending Styled Component Interface - TypeScript
DESCRIPTION: Shows how to extend the interface of a styled component (BarRoot) using generics to allow passing additional props like component and ownerState while maintaining type safety.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/TYPESCRIPT_CONVENTION.md#_snippet_8

LANGUAGE: ts
CODE:
```
const BarRoot = styled(Typography)<{
  component?: React.ElementType;
  ownerState: BarProps;
}>(({ theme, ownerState }) => ({
  // styling
}));
// passing `component` to BarRoot is safe and we don't forget to pass ownerState
// <BarRoot component="span" ownerState={ownerState} />
```

----------------------------------------

TITLE: Accessing Props with styled
DESCRIPTION: Demonstrates the standard way to access component props within the style definition function provided to the `styled` utility. The props are passed as the first argument to the top-level function.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_7

LANGUAGE: js
CODE:
```
const MyStyledButton = styled('button')((props) => ({
  backgroundColor: props.myBackgroundColor,
}));
```

----------------------------------------

TITLE: Defining Joy UI Component Slots with Styled API (JS)
DESCRIPTION: This snippet demonstrates how to use Joy UI's `styled` API to create individual styled elements (slots) for a custom component. It specifies the component name (`JoyStat`) and slot name (`root`, `value`, `unit`) to enable theme customization via `styleOverrides`. It defines basic styling based on the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_0

LANGUAGE: js
CODE:
```
import * as React from 'react';
import { styled } from '@mui/joy/styles';

const StatRoot = styled('div', {
  name: 'JoyStat', // The component name
  slot: 'root', // The slot name
})(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  padding: theme.spacing(3, 4),
  backgroundColor: theme.vars.palette.background.surface,
  borderRadius: theme.vars.radius.sm,
  boxShadow: theme.vars.shadow.md,
}));

const StatValue = styled('div', {
  name: 'JoyStat',
  slot: 'value',
})(({ theme }) => ({
  ...theme.typography.h2,
}));

const StatUnit = styled('div', {
  name: 'JoyStat',
  slot: 'unit',
})(({ theme }) => ({
  ...theme.typography['body-sm'],
  color: theme.vars.palette.text.tertiary,
}));
```

----------------------------------------

TITLE: Adding ARIA Labels and Descriptions to MUI Modal (JSX)
DESCRIPTION: This snippet shows how to add accessibility attributes `aria-labelledby` and `aria-describedby` to the MUI Modal component. These attributes link the modal to its title and description elements, improving accessibility for screen reader users. The IDs referenced by these attributes should match the IDs of the corresponding elements within the modal content.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/modal/modal.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Modal aria-labelledby="modal-title" aria-describedby="modal-description">
  <h2 id="modal-title">My Title</h2>
  <p id="modal-description">My Description</p>
</Modal>
```

----------------------------------------

TITLE: Configuring MUI Localization with Theme and LocalizationProvider (JSX)
DESCRIPTION: This snippet demonstrates how to configure localization for various MUI components, including DataGrid, Core, and Date Pickers, by creating a custom theme and using the LocalizationProvider. It shows importing locale data and applying it to the theme configuration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/date-pickers-stable-v5.md#_snippet_0

LANGUAGE: JSX
CODE:
```
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { DataGrid, bgBG as dataGridBgBG } from '@mui/x-data-grid';
import { bgBG as coreBgBG } from '@mui/material/locale';
import bgLocale from 'date-fns/locale/bg';
import { CalendarPicker, LocalizationProvider, bgBG } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

const theme = createTheme(
  {
    palette: {
      primary: { main: '#1976d2' },
    },
  },
  bgBG, // x-date-pickers translations
  dataGridBgBG, // x-data-grid translations
  coreBgBG // core translations
);
```

----------------------------------------

TITLE: Integrating Joy UI Link with Next.js Link (JSX)
DESCRIPTION: Shows how to use the Joy UI Link component in conjunction with Next.js's `NextLink` component for client-side routing in a Next.js Pages Router application. The `passHref` prop is used to ensure the `href` is passed down to the underlying `<a>` tag rendered by Joy UI Link.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/link/link.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import NextLink from 'next/link';
import Link from '@mui/joy/Link';

<NextLink href="/docs" passHref>
  <Link>Read doc</Link>
</NextLink>;
```

----------------------------------------

TITLE: Integrating Theme and Font in RootLayout
DESCRIPTION: Import the custom theme and the Next.js font object into your `app/layout.tsx`, apply the font variable CSS class to the `<html>` tag, and wrap the content with Material UI's `ThemeProvider`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_4

LANGUAGE: diff
CODE:
```
 import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
+import { Roboto } from 'next/font/google';
+import { ThemeProvider } from '@mui/material/styles';
+import theme from '../theme';

+const roboto = Roboto({
+  weight: ['300', '400', '500', '700'],
+  subsets: ['latin'],
+  display: 'swap',
+  variable: '--font-roboto',
+});

 export default function RootLayout(props) {
   const { children } = props;
   return (
+    <html lang="en" className={roboto.variable}>
       <body>
          <AppRouterCacheProvider>
+           <ThemeProvider theme={theme}>
              {children}
+           </ThemeProvider>
          </AppRouterCacheProvider>
       </body>
     </html>
   );
 }
```

----------------------------------------

TITLE: Semantic Font Icon with Visually Hidden Text (MUI)
DESCRIPTION: Illustrates how to provide an accessible text alternative for a semantic font icon by using a Box component with the `visuallyHidden` utility from `@mui/utils`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_8

LANGUAGE: jsx
CODE:
```
import Box from '@mui/material/Box';
import Icon from '@mui/material/Icon';
import { visuallyHidden } from '@mui/utils';

// ...

<Icon>add_circle</Icon>
<Box component="span" sx={visuallyHidden}>Create a user</Box>
```

----------------------------------------

TITLE: Using Standalone DateField in MUI X Pickers (JSX)
DESCRIPTION: This example shows how to import and use the `DateField` component as a standalone input field. It provides the new "Fields" behavior for date input, replacing the legacy masked input approach.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v6.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import { DateField } from '@mui/x-date-pickers/DateField';

<DateField label="My first field" />;
```

----------------------------------------

TITLE: Styling MUI Slider with SX Prop (JSX)
DESCRIPTION: This snippet demonstrates how to apply vertical margin to a Material UI Slider component using the `sx` prop. The `sx={{ my: 1 }}` syntax applies a margin of 1 unit (typically 8px) on the top and bottom (vertical axis). This requires the MUI Slider component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/blog/blog.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// add margin: 8px 0px;
<Slider sx={{ my: 1 }} />
```

----------------------------------------

TITLE: Isolated Demo Custom Theme CSS Variables - JavaScript
DESCRIPTION: This snippet demonstrates creating an isolated demo with a custom theme that includes specific palettes for light and dark color schemes, utilizing CSS variables for mode toggling. It defines the custom palettes within the colorSchemes configuration of the theme created by createTheme and applies it via the ThemeProvider.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/docs/demos.md#_snippet_2

LANGUAGE: js
CODE:
```
import { ThemeProvider, createTheme, useColorScheme } from '@mui/material/styles';

export default function DemoModeToggleCustomTheme(props) {
  const theme = createTheme({
    cssVariables: {
      cssVarPrefix: props.cssVarPrefix,
      colorSchemeSelector: props.colorSchemeSelector || 'class',
    },
    colorSchemes: {
      light: {
        palette: {
          // ...custom palette
        },
      },
      dark: {
        palette: {
          // ...custom palette
        },
      },
    },
  });
  return (
    <ThemeProvider {...props} theme={theme}>
      ...
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Applying Full Typography Variant with sx (JSX)
DESCRIPTION: Shows how to use the special `typography` key within the `sx` prop to apply all styles defined for a specific typography variant (e.g., 'body1') from the theme. This is equivalent to spreading the variant's styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_14

LANGUAGE: jsx
CODE:
```
<Box sx={{ typography: 'body1' }} />
// equivalent to { ...theme.typography.body1 }
```

----------------------------------------

TITLE: Applying Mode-Specific Styles with getColorSchemeSelector in Joy UI
DESCRIPTION: Demonstrates the recommended approach for applying different styles based on the active color scheme (light or dark) in Joy UI using the `theme.getColorSchemeSelector` utility. This method leverages CSS attribute selectors for better performance compared to conditional logic. It shows how to set a default style and then override it for a specific mode like 'dark'.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_10

LANGUAGE: js
CODE:
```
extendTheme({
  components: {
    JoyChip: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          // for the default color scheme (light)
          boxShadow: theme.vars.shadow.sm,

          // the result is `[data-joy-color-scheme="dark"] &`
          [theme.getColorSchemeSelector('dark')]: {
            boxShadow: 'none',
          },
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Setting MUI Theme Contrast Threshold (JavaScript)
DESCRIPTION: Shows how to create a custom Material UI theme using `createTheme` and set the `contrastThreshold` value within the `palette` options. This helps meet WCAG 2.1 contrast requirements by adjusting how contrast text colors are calculated.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_10

LANGUAGE: JavaScript
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    contrastThreshold: 4.5,
  },
});
```

----------------------------------------

TITLE: Integrate MUI useThemeProps in Custom Component (React/JS/MUI)
DESCRIPTION: This snippet illustrates how to use the `useThemeProps` hook within a custom component (`Stat`) to allow users to customize its default props via the MUI theme configuration. It shows importing the hook and wrapping the component's input props with `useThemeProps`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_5

LANGUAGE: diff
CODE:
```
+ import { useThemeProps } from '@mui/material/styles';

- const Stat = React.forwardRef(function Stat(props, ref) {
+ const Stat = React.forwardRef(function Stat(inProps, ref) {
+   const props = useThemeProps({ props: inProps, name: 'MuiStat' });
    const { value, unit, ...other } = props;

    return (
      <StatRoot ref={ref} {...other}>
        <StatValue>{value}</StatValue>
        <StatUnit>{unit}</StatUnit>
      </StatRoot>
    );
  });
```

----------------------------------------

TITLE: Setting Responsive Spacing in Joy UI Button Group (JSX)
DESCRIPTION: Demonstrates how to apply responsive spacing to the Button Group component using the `spacing` prop with an object. The object keys correspond to theme breakpoints (e.g., `xs`, `sm`, `md`), and the values can be numbers (interpreted by `theme.spacing`) or CSS length strings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/button-group/button-group.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<ButtonGroup spacing={{ xs: 0, sm: 1, md: '2rem' }}>...</ButtonGroup>
```

----------------------------------------

TITLE: Using component Prop with React Router Link (TSX)
DESCRIPTION: Shows how to integrate Material UI components with external libraries like React Router by passing a React component (`Link`) to the `component` prop. This allows the Material UI Button to render as a React Router Link.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_5

LANGUAGE: tsx
CODE:
```
import { Link } from 'react-router';
import Button from '@mui/material/Button';

function Demo() {
  return (
    <Button component={Link} to="/react-router">
      React router link
    </Button>
  );
}
```

----------------------------------------

TITLE: Basic createTheme Usage with Custom Palette (JS)
DESCRIPTION: This example demonstrates the basic usage of `createTheme` to create a theme object with a custom color palette, defining primary and secondary main colors using imported color values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_8

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material/styles';
import { green, purple } from '@mui/material/colors';

const theme = createTheme({
  palette: {
    primary: {
      main: purple[500],
    },
    secondary: {
      main: green[500],
    },
  },
});
```

----------------------------------------

TITLE: Using default theme.spacing() in Material UI (JS)
DESCRIPTION: Demonstrates the default behavior of `theme.spacing()` which uses an 8px scaling factor. Shows how to call it with a single argument to get a calculated pixel value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/spacing/spacing.md#_snippet_0

LANGUAGE: js
CODE:
```
const theme = createTheme();

theme.spacing(2); // `${8 * 2}px` = '16px'
```

----------------------------------------

TITLE: Styling Joy UI Sheet with the sx Prop
DESCRIPTION: Demonstrates how to apply custom styles to the Sheet component using the `sx` prop, including setting dimensions, margins, padding, display, flex properties, border radius, and box shadow.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Sheet
  sx={{
    width: 300,
    mx: 'auto', // margin left & right
    my: 4, // margin top & bottom
    py: 3, // padding top & bottom
    px: 2, // padding left & right
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    borderRadius: 'sm',
    boxShadow: 'md',
  }}
>
  Welcome!
</Sheet>
```

----------------------------------------

TITLE: Accessing CSS Theme Variables in Styled Components (JSX/CSS)
DESCRIPTION: Demonstrates how to access generated CSS variables from the `theme.vars` object within a styled component in JSX and shows the corresponding output CSS using standard CSS variables. Requires the `cssVariables` flag to be enabled in the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_1

LANGUAGE: JSX
CODE:
```
const CustomComponent = styled('div')(({ theme }) => ({
  backgroundColor: theme.vars.palette.background.default,
  color: theme.vars.palette.text.primary,
}));
```

LANGUAGE: CSS
CODE:
```
.CustomComponent-ae73f {
  background-color: var(--mui-palette-background-default);
  color: var(--mui-palette-text-primary);
}
```

----------------------------------------

TITLE: Configuring default Stack props with Joy UI Theme
DESCRIPTION: Demonstrates how to set default props, such as `useFlexGap`, for all instances of the Joy UI Stack component by extending the theme using `extendTheme` and `CssVarsProvider`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/stack/stack.md#_snippet_1

LANGUAGE: js
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';
import Stack from '@mui/joy/Stack';

const theme = extendTheme({
  components: {
    JoyStack: {
      defaultProps: {
        useFlexGap: true,
      },
    },
  },
});

function App() {
  return (
    <CssVarsProvider theme={theme}>
      <Stack>…</Stack> {/* uses flexbox gap by default */}
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Creating MUI Theme with Imported Color Objects
DESCRIPTION: Shows how to create a Material UI theme using `createTheme`, referencing imported color objects like `purple[500]` for the primary color and a hex value for the secondary color. Only the `main` shade is required.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/color/color.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import { createTheme } from '@mui/material/styles';
import { purple } from '@mui/material/colors';

const theme = createTheme({
  palette: {
    primary: {
      main: purple[500],
    },
    secondary: {
      main: '#f44336',
    },
  },
});
```

----------------------------------------

TITLE: Importing MUI Icons - JavaScript
DESCRIPTION: Compares the performance of barrel imports versus path-based imports for '@mui/icons-material'. It highlights that path-based imports are significantly faster during development.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/minimizing-bundle-size/minimizing-bundle-size.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// 🐌 Slower in dev
import { Delete } from '@mui/icons-material';

// 🚀 Faster in dev
import Delete from '@mui/icons-material/Delete';
```

----------------------------------------

TITLE: Using MUI Color Channel Tokens for Translucency - JS
DESCRIPTION: Demonstrates how to use Material UI color channel tokens within an `rgba()` function to create translucent background colors in component style overrides.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_5

LANGUAGE: JS
CODE:
```
const theme = createTheme({
  cssVariables: true,
  components: {
    MuiChip: {
      styleOverrides: {
        root: ({ theme }) => ({
          variants: [
            {
              props: { variant: 'outlined', color: 'primary' },
              style: {
                backgroundColor: `rgba(${theme.vars.palette.primary.mainChannel} / 0.12)`,
              },
            },
          ],
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Customizing Default Joy UI Shadows (JS)
DESCRIPTION: Override the default T-shirt sized shadow tokens (xs, sm, md, lg, xl) in the Joy UI theme by providing custom CSS box-shadow values to the `shadow` node when extending the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-shadow/theme-shadow.md#_snippet_0

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  shadow: {
    xs: '{CSS box-shadow}',
    sm: '{CSS box-shadow}',
    md: '{CSS box-shadow}',
    lg: '{CSS box-shadow}',
    xl: '{CSS box-shadow}',
  },
});

// Then, pass it to `<CssVarsProvider theme={theme}>`.
```

----------------------------------------

TITLE: Applying Custom Class via slotProps (JS)
DESCRIPTION: Illustrates how a user would pass additional `slotProps` to the `CustomTooltip` component. This demonstrates the concatenation of class names when using `mergeSlotProps` for the same slot property.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_2

LANGUAGE: js
CODE:
```
<CustomTooltip slotProps={{ popper: { className: 'foo' } }} />
```

----------------------------------------

TITLE: API examples for theme.containerQueries methods
DESCRIPTION: Illustrates the usage of various `theme.containerQueries` methods (`up`, `down`, `only`, `between`, `not`) with default breakpoint keys to generate different types of CSS container query strings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/container-queries/container-queries.md#_snippet_2

LANGUAGE: js
CODE:
```
// For default breakpoints
theme.containerQueries.up('sm'); // => '@container (min-width: 600px)'
theme.containerQueries.down('md'); // => '@container (max-width: 900px)'
theme.containerQueries.only('md'); // => '@container (min-width: 600px) and (max-width: 900px)'
theme.containerQueries.between('sm', 'lg'); // => '@container (min-width: 600px) and (max-width: 1200px)'
theme.containerQueries.not('sm'); // => '@container (max-width: 600px)'
```

----------------------------------------

TITLE: Style MUI Custom Component Slot with ownerState (JS/MUI Styled)
DESCRIPTION: This snippet demonstrates how to access the `ownerState` prop within a styled component (`StatRoot`) to apply conditional styles. It shows how to add `ownerState` to the styled component's arguments and use it to apply a border based on the `variant` property within `ownerState.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_4

LANGUAGE: diff
CODE:
```
  const StatRoot = styled('div', {
    name: 'MuiStat',
    slot: 'root',
-  })(({ theme }) => ({
+  })(({ theme, ownerState }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(0.5),
    padding: theme.spacing(3, 4),
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[2],
    letterSpacing: '-0.025em',
    fontWeight: 600,
+   ...ownerState.variant === 'outlined' && {
+    border: `2px solid ${theme.palette.divider}`,
+   },
  }));
```

----------------------------------------

TITLE: Setting Default Dark Mode with createTheme (MUI, JS)
DESCRIPTION: This snippet demonstrates how to configure Material UI to use the dark theme as the default mode. It utilizes `createTheme` with `palette.mode` set to 'dark' and wraps the application content within `ThemeProvider` and `CssBaseline` to apply the theme and background styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_0

LANGUAGE: js
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
  },
});

export default function App() {
  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <main>This app is using the dark mode</main>
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Assembling Custom MUI Component with Styled Slots
DESCRIPTION: Combines the previously defined styled slots (`StatRoot`, `StatValue`, `StatUnit`) into a functional React component (`Stat`) using `React.forwardRef`. It accepts `value` and `unit` props and renders them within the respective styled slots, making the component ready for use and theming.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_1

LANGUAGE: js
CODE:
```
// /path/to/Stat.js
import * as React from 'react';

const StatRoot = styled('div', {
  name: 'MuiStat',
  slot: 'root',
})(…);

const StatValue = styled('div', {
  name: 'MuiStat',
  slot: 'value',
})(…);

const StatUnit = styled('div', {
  name: 'MuiStat',
  slot: 'unit',
})(…);

const Stat = React.forwardRef(function Stat(props, ref) {
  const { value, unit, ...other } = props;

  return (
    <StatRoot ref={ref} {...other}>
      <StatValue>{value}</StatValue>
      <StatUnit>{unit}</StatUnit>
    </StatRoot>
  );
});

export default Stat;
```

----------------------------------------

TITLE: Enabling CSS Variables in MUI Theme (JSX)
DESCRIPTION: To generate CSS variables from the theme, set `cssVariables` to `true` in the theme configuration object passed to `createTheme`, and then provide the resulting theme object to the `ThemeProvider` component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_4

LANGUAGE: jsx
CODE:
```
const theme = createTheme({
  cssVariables: true,
});

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

----------------------------------------

TITLE: Applying Spacing with Array Theme Value in MUI
DESCRIPTION: Illustrates how the spacing utility transforms a numeric value when the theme's `spacing` value is an array. The input value is used as an index into the array to determine the final CSS value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/spacing/spacing.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const theme = {
  spacing: [0, 2, 3, 5, 8],
}

<Box sx={{ m: -2 }} /> // margin: -3px;
<Box sx={{ m: 0 }} /> // margin: 0px;
<Box sx={{ m: 2 }} /> // margin: 3px;
```

----------------------------------------

TITLE: Apply Dark Mode Styles with sx Prop (MUI, JSX)
DESCRIPTION: Shows how to use `theme.applyStyles` within the `sx` prop array to apply mode-specific styles (e.g., dark mode) to a MUI component, combining them with default styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_12

LANGUAGE: jsx
CODE:
```
import Button from '@mui/material/Button';

<Button
  sx={[
    (theme) => ({
      color: '#fff',
      backgroundColor: theme.palette.primary.main,
      '&:hover': {
        boxShadow: theme.shadows[3],
        backgroundColor: theme.palette.primary.dark,
      },
    }),
    (theme) =>
      theme.applyStyles('dark', {
        backgroundColor: theme.palette.secondary.main,
        '&:hover': {
          backgroundColor: theme.palette.secondary.dark,
        },
      }),
  ]}
>
  Submit
</Button>;
```

----------------------------------------

TITLE: Applying responsiveFontSizes to a Theme (JS)
DESCRIPTION: The `responsiveFontSizes` function takes an existing theme object and returns a new theme object with responsive typography settings applied, adjusting font sizes based on defined breakpoints.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_10

LANGUAGE: js
CODE:
```
import { createTheme, responsiveFontSizes } from '@mui/material/styles';

let theme = createTheme();
theme = responsiveFontSizes(theme);
```

----------------------------------------

TITLE: Accessing Props with sx prop
DESCRIPTION: Shows how to access component props when styling with the `sx` prop. Props are accessed directly within the component's render function and passed as values to the `sx` object.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_8

LANGUAGE: js
CODE:
```
import Button from '@mui/material/Button';

const MyStyledButton = (props) => (
  <Button sx={{ backgroundColor: props.myCustomColor }}>{props.children}</Button>
);
```

----------------------------------------

TITLE: Create Custom Rich Tree View Item Component (JS)
DESCRIPTION: Provides an example of creating a custom Tree Item component for the Rich Tree View using the new customization hook. It demonstrates how to leverage the `useTreeItem` hook and compose the item structure using provided context and root/content/label props.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v8.md#_snippet_3

LANGUAGE: js
CODE:
```
const CustomTreeItemComponent = React.forwardRef(function CustomTreeItemComponent(
  { id, itemId, label, disabled, children }: TreeItemProps,
  ref: React.Ref<HTMLLIElement>,
) {
  const treeItemData = useTreeItem({ id, itemId, children, label, disabled, rootRef: ref });

  return (
    <TreeItemProvider {...treeItemData.getContextProviderProps()}>
      <TreeItemRoot {...treeItemData.getRootProps()}>
        <TreeItemContent {...treeItemData.getContentProps()}>
          <TreeItemLabel {...treeItemData.getLabelProps()} />
        </TreeItemContent>
        {children && <TreeItemGroupTransition {...treeItemData.getGroupTransitionProps()} />}
      </TreeItemRoot>
    </TreeItemProvider>
  );
}
```

----------------------------------------

TITLE: Applying Border Radius with MUI sx Prop (JSX)
DESCRIPTION: Illustrates how to apply border radius to a MUI Box component using the `sx` prop. Examples show using percentage values ('50%'), theme spacing units (1, which maps to `theme.shape.borderRadius * 1`), and explicit pixel values ('16px').
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/borders/borders.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Box sx={{ borderRadius: '50%' }}>…
<Box sx={{ borderRadius: 1 }}>… // theme.shape.borderRadius * 1
<Box sx={{ borderRadius: '16px' }}>…
```

----------------------------------------

TITLE: Styling Deeper MUI Slider Elements with Plain CSS (Custom Classes via slotProps)
DESCRIPTION: Demonstrates how to style child elements of a Material UI component using plain CSS and custom class names provided via the `slotProps` API. This approach allows applying custom class names to specific slots like the Slider's thumb.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_4

LANGUAGE: css
CODE:
```
.slider {
  color: #20b2aa;
}

.slider:hover {
  color: #2e8b57;
}

.slider .thumb {
  border-radius: 1px;
}
```

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import Slider from '@mui/material/Slider';
import './PlainCssSliderDeep2.css';

export default function PlainCssSliderDeep2() {
  return (
    <div>
      <Slider defaultValue={30} />
      <Slider
        defaultValue={30}
        className="slider"
        slotProps={{ thumb: { className: 'thumb' } }}
      />
    </div>
  );
}
```

----------------------------------------

TITLE: Using createFilterOptions with Autocomplete JSX
DESCRIPTION: Demonstrates how to use the `createFilterOptions` factory function to create a custom filter. It configures the filter to match options only from the start and uses the `title` property of the option for stringification, then applies this filter to the `filterOptions` prop of the `Autocomplete` component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_7

LANGUAGE: jsx
CODE:
```
const filterOptions = createFilterOptions({
  matchFrom: 'start',
  stringify: (option) => option.title,
});

<Autocomplete filterOptions={filterOptions} />;
```

----------------------------------------

TITLE: Overriding Dark Palette Colors with createTheme (MUI, JSX)
DESCRIPTION: This snippet shows how to customize the default dark mode palette in Material UI. It extends the `createTheme` configuration by providing a `palette` object with `mode: 'dark'` and custom color values for specific palette properties, such as `primary.main`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#ff5252',
    },
  },
});
```

----------------------------------------

TITLE: Using useMediaQuery with Theme Breakpoint Helper (Object Syntax) - React/JSX
DESCRIPTION: Demonstrates how to use the `useMediaQuery` hook with Material UI's theme breakpoint helpers by accessing the theme object directly. Requires the `useTheme` hook.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/use-media-query/use-media-query.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

function MyComponent() {
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.up('sm'));

  return <span>{`theme.breakpoints.up('sm') matches: ${matches}`}</span>;
}
```

----------------------------------------

TITLE: Using Joy UI ThemeRegistry in Next.js Root Layout (TSX)
DESCRIPTION: Demonstrates how to wrap the root `layout.tsx` with the custom `ThemeRegistry` component. This ensures that the Joy UI theme, CSS variables, and CSS baseline are applied to the entire application, enabling consistent styling across pages.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/next-js-app-router/next-js-app-router.md#_snippet_1

LANGUAGE: tsx
CODE:
```
// app/layout.tsx
export default function RootLayout(props) {
  return (
    <html lang="en">
      <body>
        <ThemeRegistry options={{ key: 'joy' }}>{props.children}</ThemeRegistry>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Implementing Accordion Accessibility with ARIA Attributes - JSX
DESCRIPTION: Illustrates how to apply WAI-ARIA guidelines by setting `id` and `aria-controls` on the Accordion Summary component, allowing the Accordion component to derive necessary `aria-labelledby` and `id` attributes for improved accessibility.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/accordion/accordion.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Accordion>
  <AccordionSummary id="panel-header" aria-controls="panel-content">
    Header
  </AccordionSummary>
  <AccordionDetails>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
  </AccordionDetails>
</Accordion>
```

----------------------------------------

TITLE: Customizing theme.spacing() with a number in Material UI (JS)
DESCRIPTION: Shows how to customize the spacing factor by providing a number during theme creation. This number becomes the new base unit for the spacing calculations.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/spacing/spacing.md#_snippet_1

LANGUAGE: js
CODE:
```
const theme = createTheme({
  spacing: 4,
});

theme.spacing(2); // `${4 * 2}px` = '8px'
```

----------------------------------------

TITLE: Applying Spacing with Number Theme Value in MUI
DESCRIPTION: Demonstrates how the spacing utility transforms a numeric value provided to the `sx` prop when the theme's `spacing` value is a number. The input value is multiplied by the theme's spacing unit.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/spacing/spacing.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const theme = {
  spacing: 8,
}

<Box sx={{ m: -2 }} /> // margin: -16px;
<Box sx={{ m: 0 }} /> // margin: 0px;
<Box sx={{ m: 0.5 }} /> // margin: 4px;
<Box sx={{ m: 2 }} /> // margin: 16px;
```

----------------------------------------

TITLE: Configuring _document.tsx for SSR/SSG
DESCRIPTION: Modifies `pages/_document.tsx` to import and use `documentGetInitialProps` and `DocumentHeadTags` from `@mui/material-nextjs/v15-pagesRouter` to handle server-side rendering of styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_8

LANGUAGE: diff
CODE:
```
+import {
+  DocumentHeadTags,
+  documentGetInitialProps,
+} from '@mui/material-nextjs/v15-pagesRouter';
 // or `v1X-pagesRouter` if you are using Next.js v1X

 export default function MyDocument(props) {
   return (
     <Html lang="en">
       <Head>
+        <DocumentHeadTags {...props} />
         ...
       </Head>
       <body>
         <Main />
         <NextScript />
       </body>
     </Html>
   );
 }

+MyDocument.getInitialProps = async (ctx) => {
+  const finalProps = await documentGetInitialProps(ctx);
+  return finalProps;
+};
```

----------------------------------------

TITLE: Setting up Providers with Theme Scoping (JS)
DESCRIPTION: Demonstrates how to nest Joy UI's CssVarsProvider inside Material UI's ThemeProvider. It uses THEME_ID to scope the Material UI theme and includes CssBaseline for consistent styling. This setup is necessary to prevent theme conflicts when using both libraries.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/material-ui/material-ui.md#_snippet_0

LANGUAGE: js
CODE:
```
import {
  createTheme,
  ThemeProvider,
  THEME_ID as MATERIAL_THEME_ID,
} from '@mui/material/styles';
import { CssVarsProvider as JoyCssVarsProvider } from '@mui/joy/styles';
import CssBaseline from '@mui/material/CssBaseline';

const materialTheme = createTheme();

export default function App() {
  return (
    <ThemeProvider theme={{ [MATERIAL_THEME_ID]: materialTheme }}>
      <JoyCssVarsProvider>
        <CssBaseline enableColorScheme />
        ...Material UI and Joy UI components
      </JoyCssVarsProvider>
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Applying Style Overrides Based on Component Props (ownerState) - JavaScript
DESCRIPTION: Explains how to use a callback function within `styleOverrides` that receives `ownerState` (component props and internal state) and `theme` as arguments. This allows conditionally applying styles based on the component's current props, using `JoyChip` as an example.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
extendTheme({
  components: {
    JoyChip: {
      styleOverrides: {
        // `ownerState` contains the component props and internal state
        root: ({ ownerState, theme }) => ({
          ...(ownerState.size === 'sm' && {
            borderRadius: theme.vars.radius.xs,
          }),
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Adding Toolbar Offset for Fixed AppBar - React/MUI
DESCRIPTION: Demonstrates how to prevent content from being hidden behind a fixed AppBar by rendering an additional Toolbar component below it.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/app-bar/app-bar.md#_snippet_0

LANGUAGE: jsx
CODE:
```
function App() {
  return (
    <React.Fragment>
      <AppBar position="fixed">
        <Toolbar>{/* content */}</Toolbar>
      </AppBar>
      <Toolbar />
    </React.Fragment>
  );
}
```

----------------------------------------

TITLE: Adding Custom Color to MUI Theme Palette (JSX)
DESCRIPTION: Demonstrates how to extend the default MUI theme to include a custom color in the palette, update TypeScript definitions for type safety, and then use the custom color with a component like Button. This new feature removes the need to create a wrapper component for custom colors.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/2021-q1-update.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import { createMuiTheme, Button } from '@mui/material';

// 1. Extend the theme.
const theme = createMuiTheme({
  palette: {
    neutral: {
      main: '#5c6ac4',
    },
  },
});

// 2. Notify TypeScript about the new color in the palette
declare module '@mui/material/styles' {
  interface Palette {
    neutral: Palette['primary'];
  }interface PaletteOptions {
    neutral: PaletteOptions['primary'];
  }
}

// 3. Profit
<Button color="neutral"  />
```

----------------------------------------

TITLE: Applying Breakpoints in CSS-in-JS with MUI
DESCRIPTION: Demonstrates how to use Material UI's `theme.breakpoints.up` and `theme.breakpoints.down` helpers within a CSS-in-JS styling solution to apply different styles based on screen width.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/breakpoints/breakpoints.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const styles = (theme) => ({
  root: {
    padding: theme.spacing(1),
    [theme.breakpoints.down('md')]: {
      backgroundColor: theme.palette.secondary.main,
    },
    [theme.breakpoints.up('md')]: {
      backgroundColor: theme.palette.primary.main,
    },
    [theme.breakpoints.up('lg')]: {
      backgroundColor: green[500],
    },
  },
});
```

----------------------------------------

TITLE: Applying System Props and Text Color to Typography (Joy UI)
DESCRIPTION: Demonstrates using MUI System properties like `color`, `fontSize`, and `fontWeight` with the Joy UI Typography component. Highlights the difference between the palette `color` prop and the specific text color using `textColor`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/typography/typography.md#_snippet_1

LANGUAGE: jsx
CODE:
```
// Using the neutral color palette that defaults to the 500 value
<Typography color="neutral" fontSize="sm" fontWeight="lg" />

// Changing the specific element's color to neutral
<Typography textColor="neutral.300" fontSize="sm" fontWeight="lg" >
```

----------------------------------------

TITLE: Using sx prop instead of deprecated system props
DESCRIPTION: Shows the recommended way to apply spacing or other system props to the Stack component using the `sx` prop instead of deprecated direct props like `mt`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/stack/stack.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Stack sx={{ mt: 2 }} />
```

----------------------------------------

TITLE: Using Array of Objects in sx for Conditional Styling (JSX)
DESCRIPTION: Illustrates how to provide an array of style objects to the `sx` prop. Styles defined in later objects in the array override styles from earlier objects, allowing for conditional styling based on boolean values (e.g., `foo && { ... }`).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_19

LANGUAGE: jsx
CODE:
```
<Box
  sx={[
    {
      '&:hover': {
        color: 'red',
        backgroundColor: 'white',
      },
    },
    foo && {
      '&:hover': { backgroundColor: 'grey' },
    },
    bar && {
      '&:hover': { backgroundColor: 'yellow' },
    },
  ]}
/>
```

----------------------------------------

TITLE: Defining Custom Color Manually in MUI Theme (JSX)
DESCRIPTION: Shows how to add a custom color like 'ochre' to the MUI theme palette by manually providing all required tokens (main, light, dark, contrastText). Requires `createTheme` from `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_2

LANGUAGE: JSX
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    ochre: {
      main: '#E3D026',
      light: '#E9DB5D',
      dark: '#A29415',
      contrastText: '#242105',
    },
  },
});
```

----------------------------------------

TITLE: Define TypeScript Interfaces for Custom Component Props and ownerState (TypeScript)
DESCRIPTION: This snippet shows how to define TypeScript interfaces (`StatProps`, `StatOwnerState`) for a custom component. `StatProps` defines the public props, while `StatOwnerState` extends `StatProps` to include any additional internal state or props passed via `ownerState` for styling purposes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_7

LANGUAGE: js
CODE:
```
interface StatProps {
  value: number | string;
  unit: string;
  variant?: 'outlined';
}

interface StatOwnerState extends StatProps {
  // …key value pairs for the internal state that you want to style the slot
  // but don't want to expose to the users
}
```

----------------------------------------

TITLE: Styling Slider Deep Elements with styled-components (JSX)
DESCRIPTION: Demonstrates how to style child elements of a Material UI component, specifically the Slider's thumb, using styled-components. It utilizes the `slotProps` API to apply a custom class name to the thumb, allowing for targeted styling with increased specificity. Requires `@mui/material/styles` and `@mui/material/Slider`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_12

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Slider from '@mui/material/Slider';

const CustomizedSlider = styled((props) => (
  <Slider slotProps={{ thumb: { className: 'thumb' } }} {...props} />
))`
  color: #20b2aa;

  :hover {
    color: #2e8b57;
  }

  & .thumb {
    border-radius: 1px;
  }
`;

export default function StyledComponentsDeep2() {
  return (
    <div>
      <Slider defaultValue={30} />
      <CustomizedSlider defaultValue={30} />
    </div>
  );
}
```

----------------------------------------

TITLE: Setting align-items property with MUI Box in JSX
DESCRIPTION: Explains how to align flex items along the cross axis of a MUI `Box` flex container using the `alignItems` property in the `sx` prop. This controls how items are aligned relative to the cross-start, cross-end, or center of the container.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Box sx={{ alignItems: 'flex-start' }}>…
<Box sx={{ alignItems: 'flex-end' }}>…
<Box sx={{ alignItems: 'center' }}>…
<Box sx={{ alignItems: 'stretch' }}>…
<Box sx={{ alignItems: 'baseline' }}>…
```

----------------------------------------

TITLE: Customizing MUI Avatar img slot with Next.js Image (TSX)
DESCRIPTION: Demonstrates how to replace the default `img` slot of an MUI `Avatar` component with a Next.js `Image` component. It shows how to handle type safety for `slotProps` using TypeScript's `satisfies` operator and type casting. Requires `@mui/material/Avatar` and `next/image`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/overriding-component-structure/overriding-component-structure.md#_snippet_5

LANGUAGE: tsx
CODE:
```
import Image, { ImageProps } from 'next/image';
import Avatar, { AvatarProps } from '@mui/material/Avatar';

<Avatar
  slots={{
    img: Image,
  }}
  slotProps={
    {
      img: {
        src: 'https://example.com/image.jpg',
        alt: 'Image',
        width: 40,
        height: 40,
        blurDataURL: 'data:image/png;base64',
      } satisfies ImageProps,
    } as AvatarProps['slotProps']
  }
/>;
```

----------------------------------------

TITLE: Configure Yarn Resolutions for styled-components
DESCRIPTION: Modify the `package.json` file to use yarn's package resolutions feature. This replaces the default `@mui/styled-engine` dependency with `@mui/styled-engine-sc`, ensuring that styled-components is used as the styling engine for Material UI.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/styled-components/styled-components.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "dependencies": {
-    "@mui/styled-engine": "latest"
+    "@mui/styled-engine": "npm:@mui/styled-engine-sc@latest"
  },
+  "resolutions": {
+    "@mui/styled-engine": "npm:@mui/styled-engine-sc@latest"
+  }
}
```

----------------------------------------

TITLE: Styling Selected MenuItem State with Material UI Class
DESCRIPTION: Illustrates how to style the selected state of a `MenuItem` component using the Material UI state class (`.Mui-selected`) and increasing specificity.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/how-to-customize/how-to-customize.md#_snippet_2

LANGUAGE: css
CODE:
```
.MenuItem {
  color: black;
}

/* Increase the specificity */
.MenuItem.Mui-selected {
  color: blue;
}
```

----------------------------------------

TITLE: Configuring Joy UI Shadow Colors (JS)
DESCRIPTION: Customize the base color for shadows by setting the `shadowChannel` token within the Joy UI theme's color schemes. The value should be RGB channels (e.g., '12 12 12'), configurable for light and dark modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-shadow/theme-shadow.md#_snippet_4

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  colorSchemes: {
    light: {
      shadowChannel: '12 12 12',
    },
    dark: {
      shadowChannel: '0 0 0',
    },
  },
});

// Then, pass it to `<CssVarsProvider theme={theme}>`.
```

----------------------------------------

TITLE: Applying Dynamic Style Overrides with Callback in MUI v5 JSX
DESCRIPTION: This snippet demonstrates how to use a callback function within the `styleOverrides` property of a Material UI component (MuiChip) in the theme. The callback receives `ownerState` (component props and internal state) and `theme` objects, allowing dynamic styling based on runtime values like size and variant. It requires `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/callback-support-in-style-overrides.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';

<ThemeProvider
  theme={createTheme({
    components: {
      MuiChip: {
        styleOverrides: {
          // you can now use the theme without creating the initial theme!
          root: ({ ownerState, theme }) => ({
            padding: {
              small: '8px 4px',
              medium: '12px 6px',
              large: '16px 8px',
            }[ownerState.size],
            ...(ownerState.variant === 'outlined' && {
              borderWidth: '2px',
              ...(ownerState.variant === 'primary' && {
                borderColor: theme.palette.primary.light,
              }),
            }),
          }),
          label: {
            padding: 0,
          },
        },
      },
    },
  })}
>
  ...your app
</ThemeProvider>;
```

----------------------------------------

TITLE: Applying Theme Customizations to Joy UI Component (JoyChip) - JavaScript
DESCRIPTION: Shows the basic structure for applying theme customizations to a specific Joy UI component using `extendTheme` within `CssVarsProvider`. It demonstrates setting `defaultProps` and applying `styleOverrides` to the `root` slot.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  components: {
    JoyChip: {
      defaultProps: {
        size: 'sm',
      },
      styleOverrides: {
        root: {
          borderRadius: '4px',
        },
      },
    },
  },
});

function App() {
  return <CssVarsProvider theme={theme}>...</CssVarsProvider>;
}
```

----------------------------------------

TITLE: Customizing Joy UI Component Styles via Theme
DESCRIPTION: Shows how to modify the default styles of a specific Joy UI component, such as changing the font size of a Button, by targeting it directly within the theme's `components` section using `extendTheme`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/approaches/approaches.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';
import Button from '@mui/joy/Button';

const theme = extendTheme({
  components: {
    // The component identifier always start with `Joy${ComponentName}`.
    JoyButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          // theme.vars.* return the CSS variables.
          fontSize: theme.vars.fontSize.lg // 'var(--joy-fontSize-lg)'
        }),
      },
    },
  },
});

function MyApp() {
  return (
    <CssVarsProvider theme={theme}>
      <Button>Text</Button>
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Deprecated vs. Recommended sx Callback Usage (Diff)
DESCRIPTION: Compares the deprecated method of using a callback for a single property value within the `sx` object with the recommended approach of using a callback for the entire `sx` prop value. The recommended approach provides the theme object to the callback.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_16

LANGUAGE: diff
CODE:
```
```diff
- sx={{ height: (theme) => theme.spacing(10) }}
+ sx={(theme) => ({ height: theme.spacing(10) })}
```
```

----------------------------------------

TITLE: Applying Nested Selectors with MUI System sx Prop (JSX)
DESCRIPTION: Illustrates the use of nested CSS selectors, like `& .ChildSelector`, within the `sx` prop to style child elements based on their class names relative to the current component. This enables targeting specific descendants for styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_6

LANGUAGE: JSX
CODE:
```
<Box
  sx={{    // some styles
    '& .ChildSelector': {
      bgcolor: 'primary.main',
    },
  }}
>

```

----------------------------------------

TITLE: Add Custom Prop to Interior Slot with slotProps (JSX)
DESCRIPTION: Demonstrates how to pass custom HTML attributes or props to a specific interior slot of a component by using the `slotProps` object, targeting the desired slot by name.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/overriding-component-structure/overriding-component-structure.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Autocomplete slotProps={{ popper: { 'data-testid': 'my-popper' } }} />
```

----------------------------------------

TITLE: Using DataGrid apiRef to control pagination (TSX)
DESCRIPTION: This snippet demonstrates how to use the DataGrid's apiRef to programmatically control its features. It shows a React component that obtains the apiRef using `useGridApiRef` and uses it to set the DataGrid's current page to the first page (index 0) when a button is clicked. This feature, previously commercial, is now available in the MIT version.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v6.md#_snippet_0

LANGUAGE: tsx
CODE:
```
function CustomDataGrid(props) {
  const apiRef = useGridApiRef();

  return (
    <div>
      <Button onClick={() => apiRef.current.setPage(0)}>
        Go to page first page
      </Button>
      <DataGrid apiRef={apiRef} {...other} />
    </div>
  );
}
```

----------------------------------------

TITLE: Positioned Popper - MUI React
DESCRIPTION: This snippet illustrates how to control the placement of the Popper relative to its anchor element using the `placement` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/popper/popper.md#_snippet_3

LANGUAGE: JSX
CODE:
```
import * as React from 'react';
import Popper from '@mui/material/Popper';
import Button from '@mui/material/Button';

export default function PositionedPopper() {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'positioned-popper' : undefined;

  return (
    <div>
      <Button aria-describedby={id} type="button" onClick={handleClick}>
        Toggle Popper (bottom-start)
      </Button>
      <Popper id={id} open={open} anchorEl={anchorEl} placement="bottom-start">
        <div style={{ border: '1px solid grey', padding: 16, backgroundColor: 'white' }}>
          Popper positioned at bottom-start.
        </div>
      </Popper>
    </div>
  );
}
```

----------------------------------------

TITLE: Add Custom Component to MUI Theme TypeScript Types (TypeScript/MUI)
DESCRIPTION: This snippet shows how to extend the MUI theme's TypeScript types to include the custom component (`MuiStat`). This involves declaring the component name in `ComponentNameToClassKey`, its props in `ComponentsPropsList`, and its structure (defaultProps, styleOverrides, variants) in the `Components` interface, enabling type-safe theme customization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_9

LANGUAGE: ts
CODE:
```
import {
  ComponentsOverrides,
  ComponentsVariants,
  Theme as MuiTheme,
} from '@mui/material/styles';
import { StatProps } from 'path/to/Stat';

type Theme = Omit<MuiTheme, 'components'>;

declare module '@mui/material/styles' {
  interface ComponentNameToClassKey {
    MuiStat: 'root' | 'value' | 'unit';
  }

  interface ComponentsPropsList {
    MuiStat: Partial<StatProps>;
  }

  interface Components {
    MuiStat?: {
      defaultProps?: ComponentsPropsList['MuiStat'];
      styleOverrides?: ComponentsOverrides<Theme>['MuiStat'];
      variants?: ComponentsVariants['MuiStat'];
    };
  }
}
```

----------------------------------------

TITLE: TypeScript Augmentation for Custom MUI Breakpoints
DESCRIPTION: Provides the necessary TypeScript module augmentation declaration to inform the compiler about custom breakpoint names defined in the Material UI theme, enabling type safety when using these custom breakpoints.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/breakpoints/breakpoints.md#_snippet_3

LANGUAGE: ts
CODE:
```
declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xs: false; // removes the `xs` breakpoint
    sm: false;
    md: false;
    lg: false;
    xl: false;
    mobile: true; // adds the `mobile` breakpoint
    tablet: true;
    laptop: true;
    desktop: true;
  }
}
```

----------------------------------------

TITLE: Using Array with Object and Callback in sx (JSX)
DESCRIPTION: Shows that an array provided to the `sx` prop can contain a mix of style objects and callback functions. This allows combining static styles with dynamic styles that depend on the theme object.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_20

LANGUAGE: jsx
CODE:
```
<Box
  sx={[
    { mr: 2, color: 'red' },
    (theme) => ({
      '&:hover': {
        color: theme.palette.primary.main,
      },
    }),
  ]}
/>
```

----------------------------------------

TITLE: Applying Styles to MUI Chip Delete Icon (Low Specificity CSS)
DESCRIPTION: Illustrates a CSS rule with low specificity targeting only the custom 'green' class. This rule will not apply the style to the MUI Chip delete icon because the default MUI styles have higher specificity.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migration-v4.md#_snippet_8

LANGUAGE: css
CODE:
```
.green {
  color: green;
}
```

----------------------------------------

TITLE: Importing MUI Date Pickers Adapter for date-fns v2/v3 (JSX)
DESCRIPTION: Provides import statements for integrating MUI X Date and Time Pickers with either version 2 or version 3 of the `date-fns` library, allowing users to choose the appropriate adapter based on their project's dependency.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v7.md#_snippet_5

LANGUAGE: jsx
CODE:
```
// date-fns v2.x
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// date-fns v3.x
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
```

----------------------------------------

TITLE: Shorthand vs Full Padding Top Prop in MUI
DESCRIPTION: Illustrates the equivalence between using the shorthand `pt` prop and the full `paddingTop` prop within the `sx` prop for applying top padding.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/spacing/spacing.md#_snippet_7

LANGUAGE: diff
CODE:
```
-<Box sx={{ pt: 2 }} />
+<Box sx={{ paddingTop: 2 }} />
```

----------------------------------------

TITLE: Applying sx prop with unstable_styleFunctionSx in React
DESCRIPTION: Demonstrates how to enable the `sx` prop functionality on a custom React component using the `unstable_styleFunctionSx` utility from `@mui/system`. This allows applying system styles directly to non-MUI components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/custom-components/custom-components.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import * as React from 'react';
import { styled, unstable_styleFunctionSx } from '@mui/system';

// Define a custom component (e.g., a div wrapper)
const CustomDiv = styled('div')(unstable_styleFunctionSx);

export default function StyleFunctionSxDemo() {
  return (
    <CustomDiv
      sx={{
        width: 300,
        height: 300,
        bgcolor: 'primary.dark',
        '&:hover': {
          bgcolor: 'primary.main',
          opacity: [0.9, 0.8, 0.7],
        },
      }}
    >
      This is a custom div with sx prop
    </CustomDiv>
  );
}
```

----------------------------------------

TITLE: Applying System Spacing Props to MUI Button
DESCRIPTION: Illustrates the usage of system utility props, specifically 'mt' for margin-top, directly on a Material UI component like Button. This allows for applying responsive spacing styles using the theme's spacing scale and breakpoints.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v4-is-out.md#_snippet_6

LANGUAGE: jsx
CODE:
```
import { Button } from '@mui/material';

<Button mt={{ xs: 2, md: 3 }}>Hello worlds</Button>;
```

----------------------------------------

TITLE: Integrating Joy UI Link with React Router Link (JSX)
DESCRIPTION: Illustrates how to integrate the Joy UI Link component with React Router's `Link` component. By assigning the React Router `Link` (aliased as `RouterLink`) to the Joy UI Link's `component` prop, the Joy UI styling and features can be applied to the React Router navigation element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/link/link.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import { Link as RouterLink } from 'react-router';
import Link from '@mui/joy/Link';

<Link component={RouterLink} to="/docs">
  Read doc
</Link>;
```

----------------------------------------

TITLE: Disabling Global Ripple Effect in MUI Theme (JS)
DESCRIPTION: This snippet shows how to disable the ripple effect for all Material UI buttons and components based on `MuiButtonBase` by configuring the `defaultProps` for `MuiButtonBase` within the theme using `createTheme`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_0

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material';

const theme = createTheme({
  components: {
    // Name of the component ⚛️
    MuiButtonBase: {
      defaultProps: {
        // The props to apply
        disableRipple: true, // No more ripple, on the whole application 💣!
      },
    },
  },
});
```

----------------------------------------

TITLE: Styling MUI Slider Deeply with CSS Modules Custom Class (CSS)
DESCRIPTION: Provides a CSS Modules example showing how to style a Material UI Slider and its nested thumb element using custom class names. This approach requires applying the custom class to the thumb via the slotProps API in the JSX.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_21

LANGUAGE: css
CODE:
```
.slider {
  color: #20b2aa;
}

.slider:hover {
  color: #2e8b57;
}

.slider .thumb {
  border-radius: 1px;
}
```

----------------------------------------

TITLE: Use theme.vars in themed components
DESCRIPTION: Demonstrates how to access CSS variables using theme.vars.* when defining custom styles for Joy UI components within the extendTheme function's components section.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_4

LANGUAGE: jsx
CODE:
```
extendTheme({
  components: {
    JoyButton: {
      root: ({ theme }) => ({
        // Outputs 'var(--joy-fontFamily-display)'
        fontFamily: theme.vars.fontFamily.display,
      }),
    },
  },
});
```

----------------------------------------

TITLE: MUI Grid Responsive Spacing (JSX)
DESCRIPTION: Shows how to use responsive values for the `spacing` prop on the MUI Grid container, applying different spacing based on breakpoints.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<Grid container spacing={{ xs: 2, md: 3 }} />
```

----------------------------------------

TITLE: Setting Grid Column Placement/Span JSX
DESCRIPTION: Uses the `gridColumn` shorthand property via the MUI `sx` prop to define the size and location of a grid item within the grid columns. Examples show setting start/end lines or spanning a number of columns.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/grid/grid.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box sx={{ gridColumn: '1 / 3' }}>…
```

LANGUAGE: jsx
CODE:
```
<Box sx={{ gridColumn: 'span 2' }}>…
```

----------------------------------------

TITLE: Configure Joy UI Theme for Media Query Dark Mode (JS)
DESCRIPTION: Demonstrates how to create a Joy UI theme using `extendTheme` and set `colorSchemeSelector` to 'media' to enable dark mode based on the user's system preference via `@media (prefers-color-scheme)`. Wraps the application with `CssVarsProvider` using this theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/dark-mode/dark-mode.md#_snippet_0

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  colorSchemeSelector: 'media',
});

function App() {
  return <CssVarsProvider theme={theme}>...</CssVarsProvider>;
}
```

----------------------------------------

TITLE: Including CSS Selectors in Theme Style Overrides Callback - JavaScript
DESCRIPTION: Demonstrates how to incorporate standard CSS selectors, including nested ones like `:hover`, within the style overrides callback function. This allows applying styles based on component state or interactions directly within the theme definition.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
extendTheme({
  components: {
    JoyChip: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          ...(ownerState.variant === 'solid' &&
            ownerState.clickable && {
              color: 'rgba(255 255 255 / 0.72)',
              '&:hover': {
                color: '#fff',
              },
            }),
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Defining Solid Variant Tokens in Joy UI Theme (JavaScript)
DESCRIPTION: This snippet illustrates the structure within a Joy UI theme's `colorSchemes` and `palette` where the tokens for the `solid` variant are defined. It shows how properties like background (`solidBg`), text color (`solidColor`), and hover/active states (`solidHoverBg`, `solidActiveBg`) are configured using CSS variables for the light color scheme and primary/neutral palettes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/main-features/global-variants/global-variants.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{
  colorSchemes: {
    light: {
      palette: {
        primary: {
          solidBg: 'var(--joy-palette-primary-600)',       // the initial background
          solidColor: '#fff',                              // the initial color
          solidHoverBg: 'var(--joy-palette-primary-700)',  // the :hover background
          solidActiveBg: 'var(--joy-palette-primary-800)', // the :active background
          // ...other tokens
        },
        neutral: {
          solidBg: 'var(--joy-palette-primary-700)',
          solidColor: '#fff',
          solidHoverBg: 'var(--joy-palette-primary-800)',
          solidActiveBg: 'var(--joy-palette-primary-900)',
          // ...other tokens
        },
        // ...other palettes
      }
    },
    dark: {
      palette: {
        // similar structure but different values
      }
    }
  }
}
```

----------------------------------------

TITLE: Augmenting MUI Joy Theme Types for Custom Component
DESCRIPTION: Extends the MUI Joy theme's TypeScript types using module augmentation. This adds the custom component (`JoyStat`) to the theme's `Components` interface, allowing type-safe definition of its `defaultProps` and `styleOverrides` within the theme configuration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_9

LANGUAGE: ts
CODE:
```
import { Theme, StyleOverrides } from '@mui/joy/styles';
import { StatProps, StatOwnerState } from '/path/to/Stat';

declare module '@mui/joy/styles' {
  interface Components {
    JoyStat?: {
      defaultProps?: Partial<StatProps>;
      styleOverrides?: StyleOverrides<StatProps, StatOwnerState, Theme>;
    };
  }
}
```

----------------------------------------

TITLE: Pass Prop to Root Slot with slotProps.root (JSX)
DESCRIPTION: Illustrates an alternative method to pass props to the root slot by explicitly using the `slotProps.root` object, which is equivalent to placing the prop directly on the component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/overriding-component-structure/overriding-component-structure.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Autocomplete slotProps={{ root: { id: 'badge1' } } }}>
```

----------------------------------------

TITLE: Customizing Tree Item with useTreeItem2 Hook (JSX)
DESCRIPTION: This React component demonstrates how to create a custom Tree Item using the `useTreeItem2` hook. It leverages the hook's returned props getters to compose the root, content, icon container, and label elements, allowing for full control over the item's structure and appearance while retaining core Tree View behaviors.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v7.md#_snippet_2

LANGUAGE: jsx
CODE:
```
const CustomTreeItem = React.forwardRef(function CustomTreeItem(
  props: TreeItem2Props,
  ref: React.Ref<HTMLLIElement>,
) {
  const { id, nodeId, label, disabled, children, ...other } = props;

  const {
    getRootProps,
    getContentProps,
    getIconContainerProps,
    getLabelProps,
    getGroupTransitionProps,
    status,
  } = useTreeItem2({ id, nodeId, children, label, disabled, rootRef: ref });

  // Compose and implement your tree item as you need
  return (
    <TreeItem2Provider nodeId={nodeId}>
      <TreeItem2Root {...getRootProps(other)}>
        <TreeItemContent {...getContentProps()}>
          <TreeItem2IconContainer {...getIconContainerProps()}>
            <TreeItem2Icon status={status} />
          </TreeItem2IconContainer>
          <Box sx={{ flexGrow: 1, display: 'flex', gap: 1 }}>
              {(label as string)[0]}
            </Avatar>
            <TreeItem2Label {...getLabelProps()} />
          </Box>
        </TreeItemContent>
        {children && <TreeItem2GroupTransition {...getGroupTransitionProps()} />}
      </TreeItem2Root>
    </TreeItem2Provider>
  );
});
```

----------------------------------------

TITLE: Setting autoComplete to Avoid Autofill in MUI TextField (JSX)
DESCRIPTION: This snippet demonstrates how to set the `autoComplete` attribute on the underlying input element of a MUI TextField component to 'new-password' as a potential workaround to discourage browser autofill suggestions, such as saved addresses or payment details. This is achieved by passing the attribute via the `inputProps` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_10

LANGUAGE: jsx
CODE:
```
<TextField
  {...params}
  inputProps={{
    ...params.inputProps,
    autoComplete: 'new-password',
  }}
/>
```

----------------------------------------

TITLE: Example Autocomplete Options Data (JavaScript)
DESCRIPTION: Shows JavaScript examples of arrays representing the two default structures for options: an array of objects with 'label' properties and an array of strings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_1

LANGUAGE: js
CODE:
```
const options = [
  { label: 'The Godfather', id: 1 },
  { label: 'Pulp Fiction', id: 2 },
];
// or
const options = ['The Godfather', 'Pulp Fiction'];
```

----------------------------------------

TITLE: Creating MUI Theme with Custom Hex Colors
DESCRIPTION: Demonstrates how to create a custom Material UI theme using `createTheme` and defining primary and secondary palette colors with specific hex values for light, main, dark, and contrastText shades.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/color/color.md#_snippet_0

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      light: '#757ce8',
      main: '#3f50b5',
      dark: '#002884',
      contrastText: '#fff',
    },
    secondary: {
      light: '#ff7961',
      main: '#f44336',
      dark: '#ba000d',
      contrastText: '#000',
    },
  },
});
```

----------------------------------------

TITLE: Using theme.breakpoints.up Helper in MUI Styles
DESCRIPTION: Demonstrates how to use the `theme.breakpoints.up(key)` helper within a CSS-in-JS style object to apply styles that are active for screen widths greater than or equal to the specified breakpoint.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/breakpoints/breakpoints.md#_snippet_4

LANGUAGE: js
CODE:
```
const styles = (theme) => ({
  root: {
    backgroundColor: 'blue',
    // Match [md, ∞)
    //       [900px, ∞)
    [theme.breakpoints.up('md')]: {
      backgroundColor: 'red',
    },
  },
});
```

----------------------------------------

TITLE: Removing Borders with MUI sx Prop (JSX)
DESCRIPTION: Shows how to remove borders from a MUI Box component using the `sx` prop. Examples illustrate removing all borders or specific sides by setting the border width to 0.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/borders/borders.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box sx={{ border: 0 }}>…
<Box sx={{ borderTop: 0 }}>…
<Box sx={{ borderRight: 0 }}>…
<Box sx={{ borderBottom: 0 }}>…
<Box sx={{ borderLeft: 0 }}>…
```

----------------------------------------

TITLE: Get Current and System Color Modes with useColorScheme Hook (JS)
DESCRIPTION: Shows how to use the `useColorScheme` hook from `@mui/joy/styles` within a React component to access the current `mode` (e.g., "system") and the user's system preference `systemMode` ("light" or "dark"). Logs these values to the console. Requires the component to be inside `<CssVarsProvider>`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/dark-mode/dark-mode.md#_snippet_1

LANGUAGE: js
CODE:
```
import { useColorScheme } from '@mui/joy/styles';

function SomeComponent() {
  const { mode, systemMode } = useColorScheme();
  console.log(mode); // "system"
  console.log(systemMode); // "light" | "dark" based on the user's preference.
}
```

----------------------------------------

TITLE: Extending Component Colors via Theme Style Overrides (JoyButton) - JavaScript
DESCRIPTION: Provides an example of how to add support for custom color values (like 'secondary') to a component by defining the corresponding styles within the `styleOverrides` callback based on the `ownerState.color`. This allows using these custom colors via the component's `color` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
extendTheme({
  components: {
    JoyButton: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          ...(ownerState.color === 'secondary' && {
            color: theme.vars.palette.text.secondary,
            backgroundColor: theme.vars.palette.background.level1,
          }),
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Configuring _app.tsx for SSR/SSG
DESCRIPTION: Wraps the application content in `pages/_app.tsx` with the `AppCacheProvider` component from `@mui/material-nextjs/v15-pagesRouter` to ensure styles are collected and injected correctly.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_9

LANGUAGE: diff
CODE:
```
+import { AppCacheProvider } from '@mui/material-nextjs/v15-pagesRouter';
 // Or `v1X-pages` if you are using Next.js v1X

 export default function MyApp(props) {
   return (
+    <AppCacheProvider {...props}>
       <Head>
         ...
       </Head>
       ...
+    </AppCacheProvider>
   );
 }
```

----------------------------------------

TITLE: Extending MUI Theme Palette with New Color (JSX/TS)
DESCRIPTION: Demonstrates how to add a custom color to the MUI theme palette and update the TypeScript type definitions for a component (Button) to recognize this new color as a valid prop value. This allows using the new color directly via the `color` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import { createTheme, Button } from '@mui/material';

// 1. Extend the theme.
const theme = createTheme({
  palette: {
    neutral: {
      main: '#d79b4a',
    },
  },
});

// 2. Notify TypeScript about the new color in the palette
declare module '@mui/material/styles' {
  interface Palette {
    neutral: Palette['primary'];
  }
  interface PaletteOptions {
    neutral: PaletteOptions['primary'];
  }
}

// 3. Update the Button's color prop options
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    neutral: true;
  }
}

// 4. Profit
<Button color="neutral"  />
```

----------------------------------------

TITLE: Applying visuallyHidden style with JSX
DESCRIPTION: This snippet demonstrates how to import and apply the `visuallyHidden` style object from `@mui/utils` directly to an element's style prop in JSX. This method hides the element visually but keeps it accessible to screen readers.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/screen-readers/screen-readers.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import { visuallyHidden } from '@mui/utils';

<div style={visuallyHidden}>about how to visually hide elements</div>;
```

----------------------------------------

TITLE: Mapping fontWeight to Theme Typography Key (JSX)
DESCRIPTION: Demonstrates how to use the `sx` prop in MUI to map a CSS property like `fontWeight` directly to a specific key defined within the `theme.typography` object. This allows using theme-defined values for styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_12

LANGUAGE: jsx
CODE:
```
<Box sx={{ fontWeight: 'fontWeightLight' }} />
// equivalent to fontWeight: theme.typography.fontWeightLight
```

----------------------------------------

TITLE: Overriding ButtonBase Role for Link Components (jsx)
DESCRIPTION: When using a component based on Material UI's ButtonBase to render a link (e.g., using react-router's Link), ButtonBase might add role="button". This snippet shows how to explicitly set role={undefined} after spreading other props to remove this attribute, ensuring the element behaves correctly as a link.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/routing/routing.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const LinkBehavior = React.forwardRef((props, ref) => (
  <RouterLink ref={ref} to="/" {...props} role={undefined} />
));
```

----------------------------------------

TITLE: Overriding MUI Color Scheme Palettes (JavaScript)
DESCRIPTION: Shows how to customize the palette tokens for specific color schemes (light and dark) within the `colorSchemes` configuration when creating a Material UI theme. This allows defining distinct color palettes for different modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_12

LANGUAGE: JavaScript
CODE:
```
const theme = createTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: {
          main: '#FF5733',
        },
        // ...other tokens
      },
    },
    dark: {
      palette: {
        primary: {
          main: '#E0C2FF',
        },
        // ...other tokens
      },
    },
  },
});
```

----------------------------------------

TITLE: Using match-sorter for Custom Filtering with Autocomplete JSX
DESCRIPTION: Shows how to integrate the `match-sorter` library for advanced filtering logic. It defines a `filterOptions` function that uses `matchSorter` to filter options based on the input value and applies this function to the `filterOptions` prop of the `Autocomplete` component. Requires the `match-sorter` dependency.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_8

LANGUAGE: jsx
CODE:
```
import { matchSorter } from 'match-sorter';

const filterOptions = (options, { inputValue }) => matchSorter(options, inputValue);

<Autocomplete filterOptions={filterOptions} />;
```

----------------------------------------

TITLE: Handling Disabled Native Button with MUI Tooltip (JSX)
DESCRIPTION: Explains how to make a MUI Tooltip work with a disabled native HTML button. It involves wrapping the button in an element like `span` and applying `pointer-events: none` to the button when disabled to allow the wrapper to receive events.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/tooltips/tooltips.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Tooltip title="You don't have permission to do this">
  <span>
    <button disabled={disabled} style={disabled ? { pointerEvents: 'none' } : {}}>
      A disabled button
    </button>
  </span>
</Tooltip>
```

----------------------------------------

TITLE: Importing Material UI Components (JavaScript)
DESCRIPTION: This snippet demonstrates the syntax for importing specific components like Button and TextField directly from the '@mui/material' package. This approach is recommended for minimizing bundle size by allowing bundlers to perform tree-shaking, as discussed in the 'Minimizing Bundle Size' guide.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/july-2019-update.md#_snippet_0

LANGUAGE: js
CODE:
```
import { Button, TextField } from '@mui/material';
```

----------------------------------------

TITLE: Importing styled() Utility (TypeScript)
DESCRIPTION: Demonstrates the two common import paths for the `styled()` utility, depending on whether you are using `@mui/system` directly or importing via `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { styled } from '@mui/system';
// If you are using @mui/material
import { styled } from '@mui/material/styles';
```

----------------------------------------

TITLE: Semantic SVG Icon with IconButton (MUI)
DESCRIPTION: Demonstrates how to make a semantic SVG icon accessible when used within an interactive element like an IconButton by applying the `aria-label` prop to the button.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_7

LANGUAGE: jsx
CODE:
```
import IconButton from '@mui/material/IconButton';
import SvgIcon from '@mui/material/SvgIcon';

// ...

<IconButton aria-label="delete">
  <SvgIcon>
    <path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z" />
  </SvgIcon>
</IconButton>;
```

----------------------------------------

TITLE: Adding Custom Color Tokens to Default MUI Color (JSX)
DESCRIPTION: Demonstrates how to extend a default palette color (e.g., 'primary') by adding custom tokens beyond the standard `light`, `main`, `dark`, and `contrastText`. In this example, a `darker` token is added using a specific shade from `@mui/material/colors`. Requires `createTheme` and color imports.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_8

LANGUAGE: JSX
CODE:
```
import { createTheme } from '@mui/material/styles';
import { blue } from '@mui/material/colors';

const theme = createTheme({
  palette: {
    primary: {
      light: blue[300],
      main: blue[500],
      dark: blue[700],
      darker: blue[900],
    },
  },
});
```

----------------------------------------

TITLE: Apply Box system props using sx prop (JSX)
DESCRIPTION: Box system props like `border`, `p` (padding), and `m` (margin) can now be applied using the `sx` prop as an alternative API. This allows grouping multiple system styles within a single prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_18

LANGUAGE: jsx
CODE:
```
<Box border="1px dashed grey" p={[2, 3, 4]} m={2}>
<Box sx={{ border: "1px dashed grey", p: [2, 3, 4], m: 2 }}>
```

----------------------------------------

TITLE: Applying Text Color with sx prop in MUI
DESCRIPTION: Illustrates setting the `color` property using the `sx` prop by referencing a color path within the MUI theme's palette.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Box sx={{ color: 'primary.main' }} />
// equivalent to color: theme => theme.palette.primary.main
```

----------------------------------------

TITLE: Configuring Default Stack Props with Theme (JS)
DESCRIPTION: Demonstrates how to create a custom Material UI theme to set default props, such as useFlexGap, for all instances of the Stack component within the ThemeProvider scope.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/stack/stack.md#_snippet_1

LANGUAGE: js
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';

const theme = createTheme({
  components: {
    MuiStack: {
      defaultProps: {
        useFlexGap: true,
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <Stack>…</Stack> {/* uses flexbox gap by default */}
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Applying Styles Based on Color Scheme - JavaScript
DESCRIPTION: Provides an alternative approach for runtime calculations when CSS is not sufficient. It accesses color values directly from `theme.colorSchemes` and applies different styles for light and dark modes using `theme.applyStyles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v7/upgrade-to-v7.md#_snippet_13

LANGUAGE: javascript
CODE:
```
const Custom = styled('div')(({ theme }) => ({
  color: alpha(theme.colorSchemes.light.palette.text.primary, 0.5),
  ...theme.applyStyles('dark', {
    color: alpha(theme.colorSchemes.dark.palette.text.primary, 0.5),
  }),
}));
```

----------------------------------------

TITLE: Controlling HTML Input with slotProps.htmlInput (JSX)
DESCRIPTION: Demonstrates how to pass attributes directly to the underlying HTML <input> element rendered by the TextField component using the `slotProps.htmlInput` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/text-fields/text-fields.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<TextField slotProps={{ htmlInput: { 'data-testid': '…' } }} />
```

----------------------------------------

TITLE: Creating SVG Icons with createSvgIcon (JSX)
DESCRIPTION: Demonstrates how to use the `createSvgIcon` utility to generate React components from SVG data. Shows examples for creating an icon from a simple SVG path and from a complete SVG element. Requires the `@mui/material` library.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const HomeIcon = createSvgIcon(
  <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />,
  'Home',
);

// or with custom SVG
const PlusIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className="h-6 w-6"
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>,
  'Plus',
);
```

----------------------------------------

TITLE: Conditional Class Names with clsx Utility (JSX)
DESCRIPTION: Illustrates how to use the `clsx` library to simplify the process of conditionally applying CSS class names. It shows passing a base class string and an object where keys are class names and values are booleans determining inclusion.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_9

LANGUAGE: jsx
CODE:
```
import clsx from 'clsx';

return (
  <div
    className={clsx('MuiButton-root', {
      'Mui-disabled': disabled,
      'Mui-selected': selected,
    })}
  />
);
```

----------------------------------------

TITLE: Apply Dark Mode Styles with styled Function (MUI, JSX)
DESCRIPTION: Demonstrates how to use `theme.applyStyles` within the `styled` function to apply mode-specific styles (e.g., dark mode) alongside default styles for a component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_11

LANGUAGE: jsx
CODE:
```
import { styled } from '@mui/material/styles';

const MyComponent = styled('div')(({ theme }) => [
  {
    color: '#fff',
    backgroundColor: theme.palette.primary.main,
    '&:hover': {
      boxShadow: theme.shadows[3],
      backgroundColor: theme.palette.primary.dark,
    },
  },
  theme.applyStyles('dark', {
    backgroundColor: theme.palette.secondary.main,
    '&:hover': {
      backgroundColor: theme.palette.secondary.dark,
    },
  }),
]);
```

----------------------------------------

TITLE: Configure CSS Layers in Vite/SPA main.tsx (TSX)
DESCRIPTION: Configures a Vite or other SPA project's entry file (`main.tsx`) to integrate Material UI with Tailwind CSS v4. It wraps the application with `StyledEngineProvider` setting `enableCssLayer` to `true` and includes `GlobalStyles` with the `@layer` directive to define the CSS layer order, allowing Tailwind utilities to override MUI styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/tailwindcss/tailwindcss-v4.md#_snippet_4

LANGUAGE: tsx
CODE:
```
import { StyledEngineProvider } from '@mui/material/styles';
import GlobalStyles from '@mui/material/GlobalStyles';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <StyledEngineProvider enableCssLayer>
      <GlobalStyles styles="@layer theme, base, mui, components, utilities;" />
      {/* Your app */}
    </StyledEngineProvider>
  </React.StrictMode>,
);
```

----------------------------------------

TITLE: Configuring Theme for Density in Material UI (JS)
DESCRIPTION: This code snippet demonstrates how to create a custom Material UI theme that applies density settings to the default props of various components. It sets properties like 'size', 'margin', 'dense', and 'variant' to smaller or denser values for components like Button, Input, List, Table, and Toolbar. This theme is intended for demonstration purposes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/density/density.md#_snippet_0

LANGUAGE: js
CODE:
```
const theme = createTheme({
  components: {
    MuiButton: {
      defaultProps: {
        size: 'small'
      }
    },
    MuiFilledInput: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiFormControl: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiFormHelperText: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiIconButton: {
      defaultProps: {
        size: 'small'
      }
    },
    MuiInputBase: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiInputLabel: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiListItem: {
      defaultProps: {
        dense: true
      }
    },
    MuiOutlinedInput: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiFab: {
      defaultProps: {
        size: 'small'
      }
    },
    MuiTable: {
      defaultProps: {
        size: 'small'
      }
    },
    MuiTextField: {
      defaultProps: {
        margin: 'dense'
      }
    },
    MuiToolbar: {
      defaultProps: {
        variant: 'dense'
      }
    }
  }
});
```

----------------------------------------

TITLE: Matching ThemeProvider defaultMode with InitColorSchemeScript defaultMode
DESCRIPTION: This caveat advises setting the `defaultMode` prop on the `ThemeProvider` component to match the `defaultMode` prop used on the `InitColorSchemeScript` component. This ensures consistency in the initial mode applied by both the script and the React theme context.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/init-color-scheme-script/init-color-scheme-script.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
<ThemeProvider theme={theme} defaultMode="dark">
```

----------------------------------------

TITLE: Customizing Base UI Components with slots and slotProps (TSX)
DESCRIPTION: This snippet demonstrates the current API for customizing Base UI components using the `slots` and `slotProps` props. The `slots` prop allows changing the underlying DOM element for a component part, while `slotProps` allows passing props (like className) to specific slots.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/base-ui-2024-plans.md#_snippet_0

LANGUAGE: tsx
CODE:
```
// Example of the slots prop
<Select slots={{ listbox: 'ol' }} defaultValue="First option">
  <Option value="First option">First option</Option>
  <Option value="Second option">Second option</Option>
</Select>

// Example of the slotProps prop
<Badge slotProps={{ badge: { className: 'my-badge' } }} />
```

----------------------------------------

TITLE: Replace Interior Slot with slots Prop (Demo)
DESCRIPTION: This demo shows how to replace specific interior slots within a complex component (like the popper in Autocomplete) using the `slots` prop to alter its internal structure or behavior.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/overriding-component-structure/overriding-component-structure.md#_snippet_1

LANGUAGE: text
CODE:
```
{{ "demo": "OverridingInternalSlot.js" }}
```

----------------------------------------

TITLE: Change semantic element for Typography component
DESCRIPTION: Override the default semantic element for a specific Typography instance using the `component` prop, for example, rendering an `h1` variant as an `h2` element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/typography/typography.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Typography variant="h1" component="h2">
  h1. Heading
</Typography>
```

----------------------------------------

TITLE: Integration with react-router - Material UI - JavaScript
DESCRIPTION: Shows how to integrate the Material UI Breadcrumbs component with react-router for dynamic navigation based on the current route.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/breadcrumbs/breadcrumbs.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
RouterBreadcrumbs.js
```

----------------------------------------

TITLE: Applying Theme Typography to Custom Component (Joy UI)
DESCRIPTION: Demonstrates creating a custom styled component using `@mui/joy/styles` and applying theme typography styles (e.g., `body-sm`) directly from the theme object.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-typography/theme-typography.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import { styled } from '@mui/joy/styles';

const Tag = styled('span')((theme) => ({
  ...theme.typography['body-sm'],
  color: 'inherit',
  borderRadius: theme.vars.radius.xs,
  boxShadow: theme.vars.shadow.sm,
  padding: '0.125em 0.375em',
}));
```

----------------------------------------

TITLE: Creating Styled Component with MUI Styled Utility (JSX)
DESCRIPTION: Demonstrates how to create a custom styled component using the `styled` utility from `@mui/material/styles`. This allows adding the `sx` prop functionality to custom HTML elements or components, enabling system properties on non-MUI elements.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_2

LANGUAGE: JSX
CODE:
```
import { styled } from '@mui/material/styles';

const Div = styled('div')``;
```

----------------------------------------

TITLE: Accessing System Color Scheme Preference with useMediaQuery (MUI, JSX)
DESCRIPTION: This snippet illustrates how to detect the user's preferred color scheme (light or dark) using Material UI's `useMediaQuery` hook. It queries the `(prefers-color-scheme: dark)` media feature to determine if the user has set a dark mode preference in their operating system or browser.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

function App() {
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  return <div>prefersDarkMode: {prefersDarkMode.toString()}</div>;
}
```

----------------------------------------

TITLE: Forwarding Ref in Functional Component for MUI children (Diff)
DESCRIPTION: Demonstrates using `React.forwardRef` to make a functional component compatible when used as `children` by a Material UI component (like `Tooltip`) that requires ref access.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_10

LANGUAGE: diff
CODE:
```
-const SomeContent = props => <div {...props}>Hello, World!</div>;
+const SomeContent = React.forwardRef((props, ref) =>
+  <div {...props} ref={ref}>Hello, World!</div>);

 <Tooltip title="Hello again."><SomeContent /></Tooltip>;
```

----------------------------------------

TITLE: Using Joy UI Typography for Text Hierarchy
DESCRIPTION: Shows how to use the Typography component with different `level` props ('h4', 'body-sm') to define text hierarchy and the `component` prop ('h1') to override the default HTML element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<div>
  <Typography level="h4" component="h1">
    Welcome!
  </Typography>
  <Typography level="body-sm">Sign in to continue.</Typography>
</div>
```

----------------------------------------

TITLE: Style Definition with sx prop (Padding)
DESCRIPTION: Shows how the `sx` prop interprets numeric values for properties like `padding`. A value of `1` is automatically converted to a theme spacing unit (`theme.spacing(1)`).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_6

LANGUAGE: js
CODE:
```
import Button from '@mui/material/Button';

const MyStyledButton = (props) => (
  <Button
    sx={{
      padding: 1, // means "theme.spacing(1)", NOT "1px"
    }}
  >
    {props.children}
  </Button>
);
```

----------------------------------------

TITLE: Setting order property for MUI Box children in JSX
DESCRIPTION: Demonstrates how to change the visual order of individual flex items (MUI `Box` children) within a flex container using the `order` property in the `sx` prop. A lower `order` value places the item earlier in the layout.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_6

LANGUAGE: jsx
CODE:
```
<Box sx={{ order: 2 }}>Item 1</Box>
<Box sx={{ order: 3 }}>Item 2</Box>
<Box sx={{ order: 1 }}>Item 3</Box>
```

----------------------------------------

TITLE: Creating Dynamic Styles with styled() (TSX)
DESCRIPTION: Shows how to use the `styled()` utility to create a reusable component with dynamic styles based on custom props (e.g., `success`). Includes type definition for the custom prop for TypeScript.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/how-to-customize/how-to-customize.md#_snippet_5

LANGUAGE: tsx
CODE:
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Slider, { SliderProps } from '@mui/material/Slider';

interface StyledSliderProps extends SliderProps {
  success?: boolean;
}

const StyledSlider = styled(Slider, {
  shouldForwardProp: (prop) => prop !== 'success',
})<StyledSliderProps>(({ success, theme }) => ({
  ...(success &&
    {
      // the overrides added when the new prop is used
    }),
}));
```

----------------------------------------

TITLE: Enabling MUI CSS Variables with ThemeProvider - JSX
DESCRIPTION: Explains how to create a theme with `cssVariables: true` and wrap the application with `ThemeProvider` to enable CSS theme variables in Material UI.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_0

LANGUAGE: JSX
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme({ cssVariables: true });

function App() {
  return <ThemeProvider theme={theme}>{/* ...your app */}</ThemeProvider>;
}
```

LANGUAGE: CSS
CODE:
```
:root {
  --mui-palette-primary-main: #1976d2;
  --mui-palette-primary-light: #42a5f5;
  --mui-palette-primary-dark: #1565c0;
  --mui-palette-primary-contrastText: #fff;
  /* ...other variables */
}
```

----------------------------------------

TITLE: Setting align-self property for MUI Box children in JSX
DESCRIPTION: Illustrates how to override the `align-items` property set on the flex container for a specific flex item (MUI `Box` child) using the `alignSelf` property in the `sx` prop. This allows individual items to have different cross-axis alignment.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_9

LANGUAGE: jsx
CODE:
```
<Box>Item 1</Box>
<Box sx={{ alignSelf: 'flex-end' }}>Item 2</Box>
<Box>Item 3</Box>
```

----------------------------------------

TITLE: Fixing Stack nowrap issue with minWidth
DESCRIPTION: Provides a solution to the layout issue with `direction='row'` and `noWrap` by setting `minWidth: 0` on the Stack or the child item to allow the item to shrink within the container.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/stack/stack.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Stack direction="row" sx={{ minWidth: 0 }}>
  <Typography noWrap>

```

----------------------------------------

TITLE: Enforcing value set in ToggleButtonGroup (JSX)
DESCRIPTION: Demonstrates how to modify the change handler functions for ToggleButtonGroup to ensure that at least one button remains selected. The `handleAlignment` function prevents deselecting the last item in exclusive selection, while `handleDevices` ensures the array is not empty in multiple selection.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/toggle-button/toggle-button.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const handleAlignment = (event, newAlignment) => {
  if (newAlignment !== null) {
    setAlignment(newAlignment);
  }
};

const handleDevices = (event, newDevices) => {
  if (newDevices.length) {
    setDevices(newDevices);
  }
};
```

----------------------------------------

TITLE: Wrap App with CssVarsProvider
DESCRIPTION: To enable the use of CSS variables in your Joy UI application, you must wrap the root component or the part of your application where you want to use CSS variables with the <CssVarsProvider /> utility component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import { CssVarsProvider } from '@mui/joy/styles';

function App() {
  return <CssVarsProvider>...</CssVarsProvider>;
}
```

----------------------------------------

TITLE: Adding Prop to Root Slot using slotProps (Joy UI Autocomplete, React)
DESCRIPTION: This snippet shows how to explicitly add a prop, such as `id`, to the root slot of a Joy UI component using the `slotProps.root` object. This achieves the same result as placing the prop directly on the component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/overriding-component-structure/overriding-component-structure.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Autocomplete slotProps={{ root: { id: 'badge1' } }}>
```

----------------------------------------

TITLE: Preventing MUI SSR Flickering with applyStyles - React/Diff
DESCRIPTION: Demonstrates how to refactor component styling in Material UI to prevent SSR flickering by replacing direct checks of `theme.palette.mode === 'dark'` within the `sx` prop with the `theme.applyStyles('dark', ...)` function.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_8

LANGUAGE: diff
CODE:
```
 import Card from '@mui/material/Card';

 function App() {
   return (
     <Card
-      sx={(theme) => ({
-        backgroundColor: theme.palette.mode === 'dark' ? '#000' : '#fff',
-        '&:hover': {
-          backgroundColor: theme.palette.mode === 'dark' ? '#333' : '#f5f5f5',
-        },
-      })}
+      sx={[
+        {
+          backgroundColor: '#fff',
+          '&:hover': {
+            backgroundColor: '#f5f5f5',
+          },
+        },
+        (theme) =>
+          theme.applyStyles('dark', {
+            backgroundColor: '#000',
+            '&:hover': {
+              backgroundColor: '#333',
+            },
+          }),
+      ]}
     />
   );
 }
```

----------------------------------------

TITLE: Skipping Breakpoints in MUI sx Prop (Array Syntax)
DESCRIPTION: Demonstrates how to define responsive width using the array syntax for breakpoints in the `sx` prop, specifically showing how to skip breakpoints using `null` values. This applies the width of 300 units starting from the third defined breakpoint.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Box sx={{ width: [null, null, 300] }}>This box has a responsive width.</Box>
```

----------------------------------------

TITLE: Setting flex-direction property with MUI Box in JSX
DESCRIPTION: Shows how to apply different `flex-direction` values (`row`, `row-reverse`, `column`, `column-reverse`) to a MUI `Box` component using the `sx` prop to control the direction of flex items. Examples cover row, row-reverse, column, and column-reverse layouts.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box sx={{ flexDirection: 'row' }}>…
<Box sx={{ flexDirection: 'row-reverse' }}>…
<Box sx={{ flexDirection: 'column' }}>…
<Box sx={{ flexDirection: 'column-reverse' }}>…
```

----------------------------------------

TITLE: Inferring Dimensions from Children (JSX)
DESCRIPTION: Shows how to wrap a child component (like Avatar) with Skeleton to allow the Skeleton to infer its dimensions from the child, useful when explicit width/height are not desired.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/skeleton/skeleton.md#_snippet_2

LANGUAGE: jsx
CODE:
```
loading ? (
  <Skeleton variant="circular">
    <Avatar />
  </Skeleton>
) : (
  <Avatar src={data.avatar} />
);
```

----------------------------------------

TITLE: Creating Components with styled-components (JSX)
DESCRIPTION: This snippet demonstrates how to create styled React components using the `styled` API from styled-components. It defines separate styled components for different parts of a UI element, accessing theme values for styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const StatWrapper = styled('div')(
  ({ theme }) => `
  background-color: ${theme.palette.background.paper};
  box-shadow: ${theme.shadows[1]};
  border-radius: ${theme.shape.borderRadius}px;
  padding: ${theme.spacing(2)};
  min-width: 300px;
`,
);

const StatHeader = styled('div')(
  ({ theme }) => `
  color: ${theme.palette.text.secondary};
`,
);

const StyledTrend = styled(TrendingUpIcon)(
  ({ theme }) => `
  color: ${theme.palette.success.dark};
  font-size: 16px;
  vertical-alignment: sub;
`,
);

const StatValue = styled('div')(
  ({ theme }) => `
  color: ${theme.palette.text.primary};
  font-size: 34px;
  font-weight: ${theme.typography.fontWeightMedium};
`,
);

const StatDiff = styled('div')(
  ({ theme }) => `
  color: ${theme.palette.success.dark};
  display: inline;
  font-weight: ${theme.typography.fontWeightMedium};
  margin-left: ${theme.spacing(0.5)};
  margin-right: ${theme.spacing(0.5)};
`,
);

const StatPrevious = styled('div')(
  ({ theme }) => `
  color: ${theme.palette.text.secondary};
  display: inline;
  font-size: 12px;
`,
);

return (
  <StatWrapper>
    <StatHeader>Sessions</StatHeader>
    <StatValue>98.3 K</StatValue>
    <StyledTrend />
    <StatDiff>18.77%</StatDiff>
    <StatPrevious>vs last week</StatPrevious>
  </StatWrapper>
);
```

----------------------------------------

TITLE: Configuring MUI Dark Mode with Class Selector - JavaScript
DESCRIPTION: Configures Material UI's color schemes and CSS variables to use a 'class' selector on the `<html>` element for toggling between light and dark modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_2

LANGUAGE: js
CODE:
```
createTheme({
  colorSchemes: { light: true, dark: true },
  cssVariables: {
    colorSchemeSelector: 'class'
  }
});

// CSS Result
.light { ... }
.dark { ... }
```

----------------------------------------

TITLE: Enabling CSS Variables in Material UI v6 Theme (JavaScript)
DESCRIPTION: Shows how to enable the `cssVariables` flag when creating a Material UI theme using `createTheme` to generate CSS variables from serializable theme values like palette, spacing, and typography.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const theme = createTheme({ cssVariables: true, ... });
```

----------------------------------------

TITLE: Importing a Specific MUI Color Shade
DESCRIPTION: Illustrates how to import a specific color hue (e.g., `red`) from `@mui/material/colors` and access a particular shade (e.g., `500`) to get its hex value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/color/color.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import { red } from '@mui/material/colors';

const color = red[500];
```

----------------------------------------

TITLE: Augmenting MUI Theme Types for Custom Variables (TSX)
DESCRIPTION: Provides the TypeScript code necessary to augment the `@mui/material/styles` module, adding type definitions for custom theme properties (like `status.danger`) to both the `Theme` and `ThemeOptions` interfaces. This enables type safety when using custom theme variables.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_2

LANGUAGE: TSX
CODE:
```
declare module '@mui/material/styles' {
  interface Theme {
    status: {
      danger: string;
    };
  }
  // allow configuration using `createTheme()`
  interface ThemeOptions {
    status?: {
      danger?: string;
    };
  }
}
```

----------------------------------------

TITLE: Enabling Color Schemes in Material UI v6 Theme (JavaScript)
DESCRIPTION: Shows how to configure color schemes by adding the `colorSchemes` node to the theme object when using `createTheme`. This enables built-in support for light and dark modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
const theme = createTheme({ colorSchemes: { dark: true } });
// light is generated by default.

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

----------------------------------------

TITLE: Applying Background Color with MUI sx Prop (JSX)
DESCRIPTION: Demonstrates how to apply background color to a Box component using the `sx` prop and theme palette values like `primary.main`, `text.primary`, etc. This utilizes the `bgcolor` system prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/palette/palette.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box sx={{ bgcolor: 'primary.main' }}>…
<Box sx={{ bgcolor: 'secondary.main' }}>…
<Box sx={{ bgcolor: 'error.main' }}>…
<Box sx={{ bgcolor: 'warning.main' }}>…
<Box sx={{ bgcolor: 'info.main' }}>…
<Box sx={{ bgcolor: 'success.main' }}>…
<Box sx={{ bgcolor: 'text.primary' }}>…
<Box sx={{ bgcolor: 'text.secondary' }}>…
<Box sx={{ bgcolor: 'text.disabled' }}>…
```

----------------------------------------

TITLE: Remove Tailwind CSS Preflight
DESCRIPTION: Configure Tailwind CSS to disable its preflight styles, allowing Material UI's CssBaseline to manage base styles instead.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_23

LANGUAGE: diff
CODE:
```
 module.exports = {
+  corePlugins: {
+    preflight: false,
+  },
 };
```

----------------------------------------

TITLE: Extending Theme to Change Default Palette Colors (Joy UI)
DESCRIPTION: Shows how to use `extendTheme` to override the default HEX values for specific color tokens within a palette (e.g., `primary`) for a particular color scheme (e.g., `dark`).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_1

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  colorSchemes: {
    dark: {
      palette: {
        primary: {
          50: '#C0CCD9',
          100: '#A5B8CF',
          200: '#6A96CA',
          300: '#4886D0',
          400: '#2178DD',
          500: '#096BDE',
          600: '#1B62B5',
          700: '#265995',
          800: '#2F4968',
          900: '#2F3C4C'
        }
      }
    }
  }
});

// Then, pass it to `<CssVarsProvider theme={theme}>`.
```

----------------------------------------

TITLE: Passing Props to DataGrid Slot (JSX)
DESCRIPTION: Shows how to use the componentsProps prop on the MUI DataGrid to pass specific props directly to a designated internal component slot, such as setting the columnsSort prop for the filterPanel.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/making-customizable-components.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<DataGrid
  componentsProps={{
    filterPanel: {
      columnsSort: 'asc',
    },
  }}
/>
```

----------------------------------------

TITLE: Defining Autocomplete Options Structure (JS)
DESCRIPTION: Shows the two default structures accepted by the `options` prop: an array of objects with a `label` property or a simple array of strings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/autocomplete/autocomplete.md#_snippet_1

LANGUAGE: js
CODE:
```
const options = [
  { label: 'The Godfather', id: 1 },
  { label: 'Pulp Fiction', id: 2 },
];
// or
const options = ['The Godfather', 'Pulp Fiction'];
```

----------------------------------------

TITLE: Basic Usage with Conditional Rendering (JSX)
DESCRIPTION: Shows how to conditionally render a Skeleton component as a placeholder while data is loading, replacing it with the actual content once available. Uses `variant="rectangular"` and explicit `width`/`height`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/skeleton/skeleton.md#_snippet_0

LANGUAGE: jsx
CODE:
```
{
  item ? (
    <img
      style={{
        width: 210,
        height: 118,
      }}
      alt={item.title}
      src={item.src}
    />
  ) : (
    <Skeleton variant="rectangular" width={210} height={118} />
  );
}
```

----------------------------------------

TITLE: Toggling Color Schemes with useColorScheme Hook (JSX)
DESCRIPTION: Provides an example of using the `useColorScheme` hook from `@mui/material/styles` to read the current color mode and update it based on user interaction, such as selecting an option from a dropdown.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_4

LANGUAGE: JSX
CODE:
```
import { useColorScheme } from '@mui/material/styles';

function ModeSwitcher() {
  const { mode, setMode } = useColorScheme();
  if (!mode) return null;
  return (
    <select onChange={(event) => setMode(event.target.value)}>
      <option value="system">System</option>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
    </select>
  );
}
```

----------------------------------------

TITLE: Basic Popper Usage - MUI React
DESCRIPTION: This snippet demonstrates the basic usage of the MUI Popper component, showing how to toggle its visibility using a button as the anchor element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/popper/popper.md#_snippet_0

LANGUAGE: JSX
CODE:
```
import * as React from 'react';
import Popper from '@mui/material/Popper';
import Button from '@mui/material/Button';

export default function SimplePopper() {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popper' : undefined;

  return (
    <div>
      <Button aria-describedby={id} type="button" onClick={handleClick}>
        Toggle Popper
      </Button>
      <Popper id={id} open={open} anchorEl={anchorEl}>
        <div style={{ border: '1px solid grey', padding: 16, backgroundColor: 'white' }}>
          The content of the Popper.
        </div>
      </Popper>
    </div>
  );
}
```

----------------------------------------

TITLE: Using Icon Component with Font Ligature (JSX)
DESCRIPTION: Shows how to import and use the `Icon` component from `@mui/material` to display an icon using a font ligature (like 'star'). Requires a font that supports ligatures (e.g., Material Icons font) to be loaded in the project.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import Icon from '@mui/material/Icon';

<Icon>star</Icon>;
```

----------------------------------------

TITLE: Setting Max Height and Overflow for MUI Select Listbox (JSX)
DESCRIPTION: This snippet shows how to apply CSS styles directly to the select's listbox dropdown using the `slotProps` API. It demonstrates setting a maximum height and enabling scrolling (`overflow: 'auto'`) to handle long lists of options, which is crucial for maintaining correct keyboard navigation.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/select/select.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Select
  slotProps={{
    listbox: {
      sx: {
        maxHeight: 300,
        overflow: 'auto', // required for scrolling
      }
    }
  }}
>
```

----------------------------------------

TITLE: Setting Default Color Scheme Mode - MUI ThemeProvider - JavaScript
DESCRIPTION: Shows how to set the initial default color scheme mode (e.g., 'dark', 'light', 'system') for users visiting the site for the first time using the `defaultMode` prop on the ThemeProvider.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_9

LANGUAGE: JavaScript
CODE:
```
<ThemeProvider theme={theme} defaultMode="dark">
```

----------------------------------------

TITLE: Setting Default Skeleton Animation via Theme (JavaScript)
DESCRIPTION: Demonstrates how to globally configure the default animation for all Joy UI Skeleton components using the `extendTheme` and `CssVarsProvider` from `@mui/joy/styles`. It sets the `animation` prop to `'wave'` within the `defaultProps` for `JoySkeleton`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/skeleton/skeleton.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  components: {
    JoySkeleton: {
      defaultProps: {
        animation: 'wave',
      },
    },
  },
});

function App() {
  return (
    <CssVarsProvider theme={theme}>
      <Skeleton />{' '}
      {/* The Skeleton component will have the wave animation by default */}
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Customizing Accordion Heading Element (JSX)
DESCRIPTION: Demonstrates how to change the default heading element wrapping the Accordion Summary using the `slotProps.heading.component` prop to avoid conflicts with existing page structure or adjust semantics.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Accordion slotProps={{ heading: { component: 'h4' } }}>
  <AccordionSummary
    expandIcon={<ExpandMoreIcon />}
    aria-controls="panel1-content"
    id="panel1-header"
  >
    Accordion
  </AccordionSummary>
  <AccordionDetails>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada
    lacus ex, sit amet blandit leo lobortis eget.
  </AccordionDetails>
</Accordion>
```

----------------------------------------

TITLE: Using Material UI Fade Transition with a Custom Component (JSX)
DESCRIPTION: Demonstrates how a custom React component used as a child of a Material UI transition component (like Fade) must forward the `ref` and apply the `style` prop received from the transition component for animations to work correctly. It also shows the requirement for the transition component to have only a single child element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/transitions/transitions.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// The `props` object contains a `style` prop.
// You need to provide it to the `div` element as shown here.
const MyComponent = React.forwardRef(function (props, ref) {
  return (
    <div ref={ref} {...props}>
      Fade
    </div>
  );
});

export default function Main() {
  return (
    <Fade>
      {/* MyComponent must be the only child */}
      <MyComponent />
    </Fade>
  );
}
```

----------------------------------------

TITLE: Customizing Joy UI List Component with CSS Variables - JavaScript
DESCRIPTION: Demonstrates how to customize the appearance of the Joy UI List component by overriding built-in CSS variables to match a specific design style, such as the Gatsby documentation side navigation.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/overview/overview.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
../../components/list/ExampleCollapsibleList.js
```

----------------------------------------

TITLE: Customizing Table Borders with extendTheme (JavaScript)
DESCRIPTION: Provides an example of extending the Joy UI theme to add custom border styles to the Table component based on the `borderAxis` prop, specifically targeting the border between the header and body.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/table/table.md#_snippet_4

LANGUAGE: js
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  components: {
    JoyTable: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.borderAxis === 'header' && {
            // this example applies borders between <thead> and <tbody>
            '& thead th:not([colspan])': {
              borderBottom: '2px solid var(--TableCell-borderColor)',
            },
          }),
        })
      }
    }
  }
})

<CssVarsProvider theme={theme}>…</CssVarsProvider>
```

----------------------------------------

TITLE: Applying Local RTL to Components (JSX)
DESCRIPTION: Apply the `dir="rtl"` attribute to specific HTML elements or React components to limit the scope of the text direction. Note that components using React portals (like Dialog) require the `dir` attribute to be applied directly to them if not set globally.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/right-to-left/right-to-left.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box dir="rtl">\n  <Dialog /> // ❌ this Dialog will still be left-to-right (the default)\n</Box>\n<Box dir="rtl">\n  <Dialog dir="rtl" /> // ✅ this Dialog will be right-to-left as intended\n</Box>
```

----------------------------------------

TITLE: Updating Alert Component Import (v5)
DESCRIPTION: Demonstrates the change in import path for the Alert and AlertTitle components in v5, moving them from `@mui/lab` to `@mui/material` as they are now stable core components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_4

LANGUAGE: diff
CODE:
```
-import Alert from '@mui/lab/Alert';
-import AlertTitle from '@mui/lab/AlertTitle';
+import Alert from '@mui/material/Alert';
+import AlertTitle from '@mui/material/AlertTitle';
```

----------------------------------------

TITLE: Wrapping RootLayout with AppRouterCacheProvider
DESCRIPTION: Import and wrap the content within the `<body>` tag of your `app/layout.tsx` file with `AppRouterCacheProvider` to handle server-side CSS collection for Material UI when using the Next.js App Router.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_1

LANGUAGE: diff
CODE:
```
+import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
 // or `v1X-appRouter` if you are using Next.js v1X

 export default function RootLayout(props) {
   return (
     <html lang="en">
       <body>
+        <AppRouterCacheProvider>
           {props.children}
+        </AppRouterCacheProvider>
       </body>
     </html>
   );
 }
```

----------------------------------------

TITLE: Unmounting Fade Transition Component in JSX
DESCRIPTION: Demonstrates how to use the `unmountOnExit` prop with the Material UI `Fade` component. Setting `unmountOnExit` to `true` ensures that the content inside the transition component is unmounted from the DOM when the `in` prop is `false`, which can improve performance by avoiding rendering expensive component trees.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/transitions/transitions.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Fade in={false} unmountOnExit />
```

----------------------------------------

TITLE: Using theme.vars with styled Components - JavaScript
DESCRIPTION: This example shows the recommended way to access CSS variables directly within styled components using `theme.vars` for styling properties like color and background.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v7/upgrade-to-v7.md#_snippet_11

LANGUAGE: javascript
CODE:
```
const Custom = styled('div')(({ theme }) => ({
  color: theme.vars.palette.text.primary,
  background: theme.vars.palette.primary.main,
}));
```

----------------------------------------

TITLE: Enabling Accessible DOM Structure for MUI Date/DatePicker (JSX)
DESCRIPTION: Shows how to use the `enableAccessibleFieldDOMStructure` prop on MUI X DateField and DatePicker components to improve accessibility for screen readers by enhancing the DOM structure.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v7.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<DateField enableAccessibleFieldDOMStructure />
<DatePicker enableAccessibleFieldDOMStructure />
```

----------------------------------------

TITLE: Importing ListItemButton for Interactive Lists (JSX)
DESCRIPTION: Imports the `ListItemButton` component from `@mui/joy` to make individual list items interactive and clickable.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/list/list.md#_snippet_8

LANGUAGE: jsx
CODE:
```
import ListItemButton from '@mui/joy/ListItemButton';
```

----------------------------------------

TITLE: Hiding Elements Responsively in MUI
DESCRIPTION: Shows how to hide or show elements based on screen size using responsive display values within the sx prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/display/display.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Box sx={{ display: { xs: 'block', md: 'none' }}}> hide on screens wider than md
</Box>
<Box sx={{ display: { xs: 'none', md: 'block' }}}> hide on screens smaller than md
</Box>
```

----------------------------------------

TITLE: Disabling Portal Feature in MUI Modal (JSX)
DESCRIPTION: This snippet illustrates how to disable the portal feature of the MUI Modal using the `disablePortal` prop. React's `createPortal` API is not supported on the server. Disabling the portal is necessary when rendering the modal server-side to ensure it can be displayed correctly.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/modal/modal.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Modal disablePortal />
```

----------------------------------------

TITLE: Define Custom Breakpoint Overrides in Material UI Theme (TypeScript)
DESCRIPTION: Augments the `@mui/system` module's `BreakpointOverrides` interface to define custom breakpoints (like laptop, tablet, mobile, desktop) and optionally remove default ones (xs, sm, md, lg, xl). This allows using custom breakpoint names as responsive props on Material UI components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/components/grid/grid.md#_snippet_0

LANGUAGE: TypeScript
CODE:
```
declare module '@mui/system' {
  interface BreakpointOverrides {
    // Your custom breakpoints
    laptop: true;
    tablet: true;
    mobile: true;
    desktop: true;
    // Remove default breakpoints
    xs: false;
    sm: false;
    md: false;
    lg: false;
    xl: false;
  }
}
```

----------------------------------------

TITLE: Initializing MUI Color Scheme in Next.js Pages Router (JSX)
DESCRIPTION: Integrates the `InitColorSchemeScript` component into the custom `_document.js` file for a Next.js Pages Router application. It ensures the script runs before the main content is rendered, initializing the color scheme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_11

LANGUAGE: jsx
CODE:
```
import Document, { Html, Head, Main, NextScript } from 'next/document';
import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';

export default class MyDocument extends Document {
  render() {
    return (
      <Html>
        <Head>...</Head>
        <body>
          {/* must come before the <Main> element */}
          <InitColorSchemeScript attribute="class" />
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
```

----------------------------------------

TITLE: Applying Props via Spread Behavior (JSX)
DESCRIPTION: Demonstrates how props not explicitly documented on a component, like `disableRipple` on `MenuItem`, are spread to the root element and can be consumed by underlying components in the composition chain.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/api/api.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<MenuItem disableRipple />
```

----------------------------------------

TITLE: Extend Button Sizes with Theme & Augmentation - JS/TS/JSX
DESCRIPTION: Shows how to add custom size values ('xs', 'xl') to the Joy UI Button component by extending the theme's `components.JoyButton.styleOverrides.root` in JavaScript and providing type safety with TypeScript module augmentation via `ButtonPropsSizeOverrides`. Includes examples of using the new sizes in JSX.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_8

LANGUAGE: javascript
CODE:
```
extendTheme({
  components: {
    JoyButton: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          ...(ownerState.size === 'xs' && {
            '--Icon-fontSize': '1rem',
            '--Button-gap': '0.25rem',
            minHeight: 'var(--Button-minHeight, 1.75rem)',
            fontSize: theme.vars.fontSize.xs,
            paddingBlock: '2px',
            paddingInline: '0.5rem',
          }),
          ...(ownerState.size === 'xl' && {
            '--Icon-fontSize': '2rem',
            '--Button-gap': '1rem',
            minHeight: 'var(--Button-minHeight, 4rem)',
            fontSize: theme.vars.fontSize.xl,
            paddingBlock: '0.5rem',
            paddingInline: '2rem',
          }),
        }),
      },
    },
  },
});
```

LANGUAGE: jsx
CODE:
```
<Button size="xs">Extra small</Button>
<Button size="xl">Extra large</Button>
```

LANGUAGE: typescript
CODE:
```
// This part could be declared in your theme file
declare module '@mui/joy/Button' {
  interface ButtonPropsSizeOverrides {
    xs: true;
    xl: true;
  }
}

// typed-safe
<Button size="xs" />
<Button size="xl" />
```

----------------------------------------

TITLE: Importing MUI Components (Preferred) - JavaScript
DESCRIPTION: Demonstrates the preferred way to import components from '@mui/material' using path-based imports. This approach improves development performance by avoiding the overhead of barrel imports.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/minimizing-bundle-size/minimizing-bundle-size.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// ✅ Preferred
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
```

----------------------------------------

TITLE: Importing Basic Dialog Components - MUI React
DESCRIPTION: Imports the main Dialog and DialogTitle components from the Material UI library for use in a React application to create basic dialog structures.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/dialogs/dialogs.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
```

----------------------------------------

TITLE: Controlling CSS Injection Order with StyledEngineProvider
DESCRIPTION: Shows how to use Material UI's `StyledEngineProvider` with the `injectFirst` prop to ensure custom styles take precedence over Material UI's default styles, avoiding the need for `!important`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { StyledEngineProvider } from '@mui/material/styles';

export default function GlobalCssPriority() {
  return (
    <StyledEngineProvider injectFirst>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </StyledEngineProvider>
  );
}
```

----------------------------------------

TITLE: Apply Responsive Font Sizes Helper (JS)
DESCRIPTION: This snippet shows how to use the `responsiveFontSizes` helper function from `@mui/material/styles` to automatically apply responsive font size behavior to all typography variants defined in the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_5

LANGUAGE: js
CODE:
```
import { createTheme, responsiveFontSizes } from '@mui/material/styles';

let theme = createTheme();
theme = responsiveFontSizes(theme);
```

----------------------------------------

TITLE: Creating Accessible DOM Structure for Form Fields (HTML)
DESCRIPTION: Demonstrates the required underlying HTML structure for accessible form controls, linking the label, input, and helper text using `for`, `id`, and `aria-describedby` attributes to ensure screen readers can properly associate them.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/text-fields/text-fields.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<div class="form-control">
  <label for="my-input">Email address</label>
  <input id="my-input" aria-describedby="my-helper-text" />
  <span id="my-helper-text">We'll never share your email.</span>
</div>
```

----------------------------------------

TITLE: Accessing MUI Primary and Accent Color Shades
DESCRIPTION: Demonstrates importing multiple color hues (`purple`, `red`) and accessing specific shades, including standard shades (e.g., `red[500]`) and accent shades (e.g., `purple['A200']` or `purple.A200`).
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/color/color.md#_snippet_3

LANGUAGE: js
CODE:
```
import { purple, red } from '@mui/material/colors';

const primary = red[500]; // #f44336
const accent = purple['A200']; // #e040fb
const accent = purple.A200; // #e040fb (alternative method)
```

----------------------------------------

TITLE: Overriding DataGrid Internal Component (JSX)
DESCRIPTION: Illustrates how to use the components prop on the MUI DataGrid to replace a default internal component, specifically replacing the FilterPanelDeleteIcon with a custom DeleteIcon.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/making-customizable-components.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<DataGrid {...data} components={{ FilterPanelDeleteIcon: DeleteIcon }} />
```

----------------------------------------

TITLE: Applying Border Radius with sx prop in MUI
DESCRIPTION: Illustrates setting the `borderRadius` using the `sx` prop. The provided value is multiplied by the `theme.shape.borderRadius` value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Box sx={{ borderRadius: 2 }} />
// equivalent to borderRadius: theme => 2 * theme.shape.borderRadius
```

----------------------------------------

TITLE: Increasing Specificity for Material UI Pseudo-classes (CSS)
DESCRIPTION: Demonstrates how to increase CSS specificity by combining the component's class with Material UI's state classes (e.g., .Mui-selected) to correctly style components based on their state.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v4-is-out.md#_snippet_1

LANGUAGE: css
CODE:
```
.MenuItem {
  color: black;
}
/* We increase the specificity */
.MenuItem.Mui-selected {
  color: blue;
}
```

----------------------------------------

TITLE: Running SX Prop v6 Codemod Bash
DESCRIPTION: Provides the bash command to execute the `sx-prop` codemod for v6.0.0. This codemod updates the usage of the `sx` prop for compatibility with `@pigment-css/react`. Requires specifying the target file path.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_146

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v6.0.0/sx-prop <path>
```

----------------------------------------

TITLE: Installing Material UI Icons Package (npm, pnpm, yarn)
DESCRIPTION: Commands to install the `@mui/icons-material` package along with its required dependencies (`@mui/material`, `@emotion/styled`, `@emotion/react`) using different package managers (npm, pnpm, yarn). These packages are necessary to use the Material Icons components within a Material UI project.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/material-icons/material-icons.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mui/icons-material @mui/material @emotion/styled @emotion/react
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/icons-material @mui/material @emotion/styled @emotion/react
```

LANGUAGE: bash
CODE:
```
yarn add @mui/icons-material @mui/material @emotion/styled @emotion/react
```

----------------------------------------

TITLE: Augmenting MUI Palette Interface for Custom Colors (TS)
DESCRIPTION: Provides the TypeScript code necessary to add a custom color property (e.g., 'custom') to the `Palette` and `PaletteOptions` interfaces in `@mui/material/styles`. This allows TypeScript to recognize the custom color key when accessing the theme palette.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_6

LANGUAGE: TS
CODE:
```
declare module '@mui/material/styles' {
  interface Palette {
    custom: Palette['primary'];
  }

  interface PaletteOptions {
    custom?: PaletteOptions['primary'];
  }
}
```

----------------------------------------

TITLE: Fix CSS Injection Order with StyledEngineProvider
DESCRIPTION: Wrap the application's component tree with `StyledEngineProvider` and set `injectFirst` to ensure Material UI's styles are injected before other styles (like Tailwind's), helping to resolve style conflicts.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_25

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { StyledEngineProvider } from '@mui/material/styles';

export default function GlobalCssPriority() {
  return (
    <StyledEngineProvider injectFirst>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </StyledEngineProvider>
  );
}
```

----------------------------------------

TITLE: Using Divider with role='presentation' for Accessibility
DESCRIPTION: Shows how to use the `role="presentation"` attribute on a Divider component when it's used for visual decoration, ensuring screen readers correctly interpret its content.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/divider/divider.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Divider component="div" role="presentation">
  {/* any elements nested inside the role="presentation" preserve their semantics. */}
</Divider>
```

----------------------------------------

TITLE: Passing DOM Node to Portal via State and Ref Callback (JSX)
DESCRIPTION: Demonstrates the recommended pattern for providing a DOM node to components like `Portal` or `Popper`. It uses a state variable updated via a ref callback function to ensure the component re-renders when the DOM node becomes available.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_7

LANGUAGE: jsx
CODE:
```
function App() {
  const [container, setContainer] = React.useState(null);
  const handleRef = React.useCallback(
    (instance) => setContainer(instance),
    [setContainer],
  );

  return (
    <div className="App">
      <Portal container={container}>
        <span>Portaled</span>
      </Portal>
      <div ref={handleRef} />
    </div>
  );
}
```

----------------------------------------

TITLE: Styling Slider with Theme and styled-components (JSX)
DESCRIPTION: Shows how to access and use the Material UI theme within styled-components definitions. It applies theme colors (`theme.palette.primary.main`) to style the Slider, including hover effects using `darken` from `@mui/material/styles`. Requires `@mui/material/styles` and `@mui/material/Slider`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_13

LANGUAGE: jsx
CODE:
```
const CustomizedSlider = styled(Slider)(
  ({ theme }) => `
  color: ${theme.palette.primary.main};

  :hover {
    color: ${darken(theme.palette.primary.main, 0.2)};
  }
`,
);
```

----------------------------------------

TITLE: Accessing DOM Element via Ref (JSX)
DESCRIPTION: Demonstrates how to access the underlying DOM element of a Material UI component by attaching a React ref to it. The ref's `current` property will hold the DOM node after the component mounts.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_4

LANGUAGE: jsx
CODE:
```
// or a ref setter function
const ref = React.createRef();
// render
<Button ref={ref} />;
// usage
const element = ref.current;
```

----------------------------------------

TITLE: Using styled() API in JSX
DESCRIPTION: This snippet demonstrates two common syntaxes for utilizing the `styled()` API: the object syntax and the tagged template literal syntax. Both methods achieve the same result of creating a styled component, illustrating the flexibility of the API which is now the lowest-level primitive for adding styles in Material UI.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const StyledDiv = styled('div')({
  color: 'red'
});

// or
const StyledDiv = styled.div`
  color: red;
`;
```

----------------------------------------

TITLE: Applying sx Prop Directly to JSX Elements - MUI/React
DESCRIPTION: Illustrates how Pigment CSS integration allows applying the `sx` prop directly to standard JSX elements like `<img>`, eliminating the need for wrapper components like `Box` for styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_11

LANGUAGE: diff
CODE:
```
-import Box from '@mui/material/Box';

-<Box component="img" sx={{ padding: 2 }} />
+<img sx={{ padding: 2 }} />
```

----------------------------------------

TITLE: Manually Linking Label to MUI Select Button (JSX)
DESCRIPTION: This example illustrates how to manually associate a standard HTML label with the MUI Select component for improved accessibility, as an alternative to using `FormControl`. It involves setting `htmlFor` and `id` on the label and the Select button (via `slotProps`), and using `aria-labelledby` on the button to reference both elements.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/select/select.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<label htmlFor="select-button" id="select-label">Label</label>
<Select
  slotProps={{
    button: {
      id: 'select-button',
      'aria-labelledby': 'select-label select-button',
    }
  }}
>
  <Option value="option1">Option I</Option>
  <Option value="option2">Option II</Option>
</Select>
```

----------------------------------------

TITLE: Applying Z-Index with sx prop in MUI
DESCRIPTION: Shows how to set the `zIndex` property using the `sx` prop by mapping the value to a named z-index value from `theme.zIndex`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Box sx={{ zIndex: 'tooltip' }} />
// equivalent to zIndex: theme => theme.zIndex.tooltip
```

----------------------------------------

TITLE: Configuring Theme for CSS Variables in Material UI
DESCRIPTION: This snippet demonstrates how to configure your Material UI theme to enable CSS variables and specify the selector for the color scheme attribute by setting `cssVariables.colorSchemeSelector` to 'data'. It also shows wrapping the application with the configured `ThemeProvider`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/init-color-scheme-script/init-color-scheme-script.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme({
  cssVariables: {
    colorSchemeSelector: 'data',
  },
});

function App() {
  return <ThemeProvider theme={theme}>{/* Your app */}</ThemeProvider>;
}
```

----------------------------------------

TITLE: Basic Joy UI Tooltip Usage (JSX)
DESCRIPTION: Demonstrates the basic import and usage of the Joy UI Tooltip component in a simple React functional component. This is the minimal setup required to render a Tooltip.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/tooltip/tooltip.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Tooltip from '@mui/joy/Tooltip';

export default function MyApp() {
  return <Tooltip />;
}
```

----------------------------------------

TITLE: Defining Custom Breakpoints for MUI Grid (JS)
DESCRIPTION: Demonstrates how to define custom breakpoints using MUI's ThemeProvider and createTheme, and then apply these custom breakpoint names (e.g., 'mobile', 'tablet', 'laptop') to the Grid component's responsive props like `spacing` and item `size`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/grid/grid.md#_snippet_1

LANGUAGE: js
CODE:
```
import { ThemeProvider, createTheme } from '@mui/material/styles';

function Demo() {
  return (
    <ThemeProvider
      theme={createTheme({
        breakpoints: {
          values: {
            laptop: 1024,
            tablet: 640,
            mobile: 0,
            desktop: 1280,
          },
        },
      })}
    >
      <Grid container spacing={{ mobile: 1, tablet: 2, laptop: 3 }}>
        {Array.from(Array(4)).map((_, index) => (
          <Grid key={index} size={{ mobile: 6, tablet: 4, laptop: 3 }}>
            <div>{index + 1}</div>
          </Grid>
        ))}
      </Grid>
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Adding prepend Option to Emotion createCache
DESCRIPTION: Illustrates how to add the `prepend: true` option to `createCache` from `@emotion/cache` when using a custom cache. This ensures the correct CSS injection order, allowing Material UI's styles to be overridden.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-style-changes.md#_snippet_9

LANGUAGE: diff
CODE:
```
 import * as React from 'react';
 import { CacheProvider } from '@emotion/react';
 import createCache from '@emotion/cache';

 const cache = createCache({
   key: 'css',
+  prepend: true,
 });

 export default function PlainCssPriority() {
   return (
     <CacheProvider value={cache}>
       {/* Your component tree. Now you can override Material UI's styles. */}
     </CacheProvider>
   );
 }
```

----------------------------------------

TITLE: Replace Box Render Prop with sx on MUI Button
DESCRIPTION: Shows how to replace the use of a Box component with a render prop wrapping a Material UI Button by applying the `sx` prop directly to the Button component itself. This simplifies the structure.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_21

LANGUAGE: diff
CODE:
```
diff
-<Box sx={{ border: '1px dashed grey' }}>
-  {(props) => <Button {...props}>Save</Button>}
-</Box>
+<Button sx={{ border: '1px dashed grey' }}>Save</Button>
```

----------------------------------------

TITLE: Handling Non-Standard Ranges with Progress Components (JSX)
DESCRIPTION: Provides a function to normalize a value from any range (MIN to MAX) to the 0-100 range required by Material UI progress components. Shows an example React component using this function to display both Circular and Linear determinate progress based on a potentially out-of-range input value.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/progress/progress.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// MIN = Minimum expected value
// MAX = Maximum expected value
// Function to normalise the values (MIN / MAX could be integrated)
const normalise = (value) => ((value - MIN) * 100) / (MAX - MIN);

// Example component that utilizes the `normalise` function at the point of render.
function Progress(props) {
  return (
    <React.Fragment>
      <CircularProgress variant="determinate" value={normalise(props.value)} />
      <LinearProgress variant="determinate" value={normalise(props.value)} />
    </React.Fragment>
  );
}
```

----------------------------------------

TITLE: Setting Font Size in MUI JSX
DESCRIPTION: Shows different methods for controlling the font size of text using the `fontSize` CSS property via the `sx` prop on a `Box` component. Examples include using theme values, specific variant sizes, and pixel values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Box sx={{ fontSize: 'default' }}>…  // theme.typography.fontSize
<Box sx={{ fontSize: 'h6.fontSize' }}>…
<Box sx={{ fontSize: 16 }}>…
```

----------------------------------------

TITLE: Wrap Disabled ButtonBase for not-allowed Cursor (JSX)
DESCRIPTION: Wrap a disabled Material UI Button (or any element using ButtonBase) in a `<span>` with `style={{ cursor: 'not-allowed' }}`. This DOM-based approach ensures the `not-allowed` cursor appears, supporting various underlying elements like links.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/buttons/buttons.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<span style={{ cursor: 'not-allowed' }}>
  <Button component={Link} disabled>
    disabled
  </Button>
</span>
```

----------------------------------------

TITLE: Rendering Serializable Joy UI Components in Next.js Server Component (TSX)
DESCRIPTION: Shows an example of a Next.js server component (`page.tsx`) that successfully renders Joy UI components (`Sheet`, `Typography`) with serializable props. This demonstrates that components with only serializable props can be rendered directly within server components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/next-js-app-router/next-js-app-router.md#_snippet_2

LANGUAGE: tsx
CODE:
```
// app/page.tsx
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';

export default function Page() {
  return (
    <Sheet>
      <Typography fontSize="sm">Hello World</Typography>
    </Sheet>
  );
}
```

----------------------------------------

TITLE: Applying Styles Between Two Breakpoints (between) - MUI Breakpoints - JavaScript
DESCRIPTION: This example shows how to use `theme.breakpoints.between('sm', 'md')` in a JSS style object. It applies styles (changing background color to red) when the screen width is between the 'sm' breakpoint (inclusive) and the 'md' breakpoint (exclusive), typically matching widths from 600px to 900px.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/breakpoints/breakpoints.md#_snippet_8

LANGUAGE: JavaScript
CODE:
```
const styles = (theme) => ({
  root: {
    backgroundColor: 'blue',
    // Match [sm, md)
    //       [600px, 900px)
    [theme.breakpoints.between('sm', 'md')]: {
      backgroundColor: 'red',
    },
  },
});
```

----------------------------------------

TITLE: Integrating Mode Toggle into App (Joy UI) - JSX
DESCRIPTION: Integrates the `ModeToggle` component into the main `App` function, placing it inside the `CssVarsProvider` and before the `Sheet` component to enable the theme switching functionality for the application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_10

LANGUAGE: jsx
CODE:
```
 export default function App() {
   return (
     <CssVarsProvider>
       <ModeToggle />
       <Sheet>...</Sheet>
     </CssVarsProvider>
   );
 }
```

----------------------------------------

TITLE: Configuring Emotion for RTL (JSX)
DESCRIPTION: If using Emotion for styling, wrap your application tree with `CacheProvider` and provide a new cache instance configured with `prefixer` and `rtlPlugin` from `stylis-plugin-rtl`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/right-to-left/right-to-left.md#_snippet_4

LANGUAGE: jsx
CODE:
```
import { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nimport { prefixer } from 'stylis';\nimport rtlPlugin from 'stylis-plugin-rtl';\n\n// Create rtl cache\nconst rtlCache = createCache({\n  key: 'muirtl',\n  stylisPlugins: [prefixer, rtlPlugin],\n});\n\nfunction Rtl(props) {\n  return <CacheProvider value={rtlCache}>{props.children}</CacheProvider>;\n}
```

----------------------------------------

TITLE: Basic Spacing Usage in MUI
DESCRIPTION: Provides simple examples of applying padding (`p`) and margin (`m`) using the spacing shorthand props within the `sx` prop of a MUI Box component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/spacing/spacing.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Box sx={{ p: 1 }}>…
<Box sx={{ m: 1 }}>…
<Box sx={{ p: 2 }}>…
```

----------------------------------------

TITLE: Importing MUI Components (Avoid) - JavaScript
DESCRIPTION: Shows the less performant way to import components from '@mui/material' using barrel imports. This pattern can lead to significantly slower startup and rebuild times during development.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/minimizing-bundle-size/minimizing-bundle-size.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
// ❌ Slower in dev
import { Button, TextField } from '@mui/material';
```

----------------------------------------

TITLE: Initializing Global CSS Baseline with Joy UI
DESCRIPTION: Demonstrates how to apply a global CSS reset using Joy UI's `CssBaseline` component. It shows wrapping the application within `CssVarsProvider` and including `CssBaseline` as a child to establish a consistent style baseline across the entire application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/css-baseline/css-baseline.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { CssVarsProvider } from '@mui/joy/styles';
import CssBaseline from '@mui/joy/CssBaseline';

export default function MyApp() {
  return (
    <CssVarsProvider>
      {/* must be used under CssVarsProvider */}
      <CssBaseline />

      {/* The rest of your application */}
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Setting Letter Spacing in MUI JSX
DESCRIPTION: Shows how to adjust the spacing between characters using the `letterSpacing` CSS property via the `sx` prop on a `Box` component. Values are typically in pixels or other length units.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Box sx={{ letterSpacing: 6 }}>…
<Box sx={{ letterSpacing: 10 }}>…
```

----------------------------------------

TITLE: Using Custom Secondary Palette in Joy UI Components - JavaScript
DESCRIPTION: This snippet shows how to apply the custom `secondary` color palette, added to the theme, to various Joy UI components like Button, IconButton, and Chip using the `color` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_5

LANGUAGE: js
CODE:
```
<Button color="secondary">
<IconButton variant="outlined" color="secondary">
<Chip variant="soft" color="secondary">
```

----------------------------------------

TITLE: Basic Material UI Link Usage - JavaScript
DESCRIPTION: Demonstrates the basic usage of the Material UI Link component, highlighting its default props like color="primary" and variant="inherit" when used within typography.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/links/links.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import * as React from 'react';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';

export default function Links() {
  const preventDefault = (event) => event.preventDefault();

  return (
    <Typography>
      <Link href="#" onClick={preventDefault}>
        Link
      </Link>
      <Link href="#" onClick={preventDefault} color="inherit">
        {'color="inherit"'}
      </Link>
      <Link href="#" onClick={preventDefault} variant="body2">
        {'variant="body2"'}
      </Link>
    </Typography>
  );
}
```

----------------------------------------

TITLE: Set Theme Direction (JavaScript)
DESCRIPTION: Use the `extendTheme` API from `@mui/joy/styles` to configure the theme's direction property to `'rtl'`. This ensures theme-specific styles and components respect the RTL layout.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/right-to-left/right-to-left.md#_snippet_3

LANGUAGE: js
CODE:
```
import { extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  direction: 'rtl',
});
```

----------------------------------------

TITLE: Setting flex-grow property for MUI Box children in JSX
DESCRIPTION: Explains how to control the ability of a flex item (MUI `Box` child) to grow and fill available space within the flex container using the `flexGrow` property in the `sx` prop. A value greater than 0 allows the item to grow.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/flexbox/flexbox.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Box sx={{ flexGrow: 1 }}>Item 1</Box>
<Box>Item 2</Box>
<Box>Item 3</Box>
```

----------------------------------------

TITLE: Replacing experimentalStyled with styled - Diff
DESCRIPTION: Shows the migration path from the deprecated `experimentalStyled` function to the standard `styled` function for creating styled components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v7/upgrade-to-v7.md#_snippet_17

LANGUAGE: diff
CODE:
```
-import { experimentalStyled as styled } from '@mui/material/styles';
+import { styled } from '@mui/material/styles';
```

----------------------------------------

TITLE: Augmenting MUI Component Props for Type Safety TS
DESCRIPTION: This TypeScript snippet demonstrates how to use module augmentation to extend the props interface of a Material UI component, specifically adding a custom variant ('dashed') to `ButtonPropsVariantOverrides`. This augmentation ensures that the custom prop is recognized by TypeScript and correctly typed when accessed via the `ownerState` object in style override callbacks.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/callback-support-in-style-overrides.md#_snippet_2

LANGUAGE: ts
CODE:
```
declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    dashed: true;
  }
}
```

----------------------------------------

TITLE: Using Fixed Container
DESCRIPTION: Shows how to create a fixed-width container using the 'fixed' prop. When 'fixed' is true, the container's max-width matches the min-width of the current breakpoint, providing a consistent size.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/components/container/container.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Container fixed>
```

----------------------------------------

TITLE: Use TypeScript Interfaces in MUI Styled Component and Custom Component (TypeScript/React/MUI)
DESCRIPTION: This snippet demonstrates how to apply the previously defined TypeScript interfaces (`StatOwnerState`, `StatProps`) to the styled component (`StatRoot`) and the custom component (`Stat`). It shows typing the `ownerState` prop in the styled component and typing the component's props and ref.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_8

LANGUAGE: js
CODE:
```
const StatRoot = styled('div', {
  name: 'MuiStat',
  slot: 'root',
})<{ ownerState: StatOwnerState }>(({ theme, ownerState }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  padding: theme.spacing(3, 4),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
  letterSpacing: '-0.025em',
  fontWeight: 600,
  // typed-safe access to the `variant` prop
  ...(ownerState.variant === 'outlined' && {
    border: `2px solid ${theme.palette.divider}`,
    boxShadow: 'none',
  }),
}));

// …do the same for other slots

const Stat = React.forwardRef<HTMLDivElement, StatProps>(function Stat(inProps, ref) {
  const props = useThemeProps({ props: inProps, name: 'MuiStat' });
  const { value, unit, variant, ...other } = props;

  const ownerState = { ...props, variant };

  return (
    <StatRoot ref={ref} ownerState={ownerState} {...other}>
      <StatValue ownerState={ownerState}>{value}</StatValue>
      <StatUnit ownerState={ownerState}>{unit}</StatUnit>
    </StatRoot>
  );
});
```

----------------------------------------

TITLE: Using Joy UI Input with Automatic Adjustments and Customization (JSX)
DESCRIPTION: Demonstrates how Joy UI's Input component automatically adjusts the border radius of its children (like IconButton) and how to customize the input's border radius using the sx prop and the '--Input-radius' CSS variable.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/first-look-at-joy.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Input
  placeholder="password"
  endDecorator={<IconButton size="sm"><Visibility /></IconButton>}
/>

<Input
  size="lg"
  placeholder="password"
  endDecorator={<IconButton><Visibility /></IconButton>}
  sx={{ '--Input-radius': '24px' }}
/>
```

----------------------------------------

TITLE: Applying Media Queries with MUI System sx Prop (JSX)
DESCRIPTION: Shows how to define styles that apply only under specific media query conditions, such as `@media print`, within the `sx` prop. This allows for responsive or context-specific styling directly within the component's style definition.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_5

LANGUAGE: JSX
CODE:
```
<Box
  sx={{    // some styles
    '@media print': {
      width: 300,
    },
  }}
>

```

----------------------------------------

TITLE: Applying Custom Joy UI Theme with CssVarsProvider in JavaScript
DESCRIPTION: This snippet shows how to wrap your application's root component with `CssVarsProvider` and pass your custom or extended Joy UI `theme` object to it. This makes the custom theme available throughout your application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_3

LANGUAGE: js
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';

const theme = extendTheme({ … });

function App() {
  return <CssVarsProvider theme={theme}>…</CssVarsProvider>;
}
```

----------------------------------------

TITLE: Migrating makeStyles to sx prop in React
DESCRIPTION: This diff shows how to replace the usage of `makeStyles` for simple styling with the `sx` prop directly on MUI components. This approach is recommended for creating responsive styles or overriding minor CSS without needing a separate hook or class names.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migrating-from-jss.md#_snippet_2

LANGUAGE: diff
CODE:
```
 import Chip from '@mui/material/Chip';
-import makeStyles from '@mui/styles/makeStyles';
+import Box from '@mui/material/Box';

-const useStyles = makeStyles((theme) => ({
-  wrapper: {
-    display: 'flex',
-  },
-  chip: {
-    padding: theme.spacing(1, 1.5),
-    boxShadow: theme.shadows[1],
-  }
-}));

 function App() {
-  const classes = useStyles();
   return (
-    <div className={classes.wrapper}>
-      <Chip className={classes.chip} label="Chip" />
-    </div>
+    <Box sx={{ display: 'flex' }}>
+      <Chip label="Chip" sx={{ py: 1, px: 1.5, boxShadow: 1 }} />
+    </Box>
   );
 }
```

----------------------------------------

TITLE: Integrating InitColorSchemeScript in Next.js App Router Layout
DESCRIPTION: This example shows the correct placement of the `InitColorSchemeScript` component within the root `layout.tsx` file for a Next.js application using the App Router. It should be the first child element inside the `<body>` tag to run before React hydration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/init-color-scheme-script/init-color-scheme-script.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <InitColorSchemeScript />
        {props.children}
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Setting Line Height in MUI JSX
DESCRIPTION: Illustrates how to control the vertical spacing between lines of text using the `lineHeight` CSS property via the `sx` prop on a `Box` component. Examples include using 'normal' or numerical values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<Box sx={{ lineHeight: 'normal' }}>…
<Box sx={{ lineHeight: 10 }}>…
```

----------------------------------------

TITLE: Using Custom and Disabled Variants (JSX)
DESCRIPTION: Demonstrates how to use the newly added custom variant and shows that attempting to use the disabled variant will result in a TypeScript error.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_12

LANGUAGE: jsx
CODE:
```
<Typography variant="poster">poster</Typography>;

/* This variant is no longer supported. If you are using TypeScript it will give an error */
<Typography variant="h3">h3</Typography>;
```

----------------------------------------

TITLE: Update Grid Size Props (Multiple Breakpoints)
DESCRIPTION: For Material UI v6 and v7, replace breakpoint-specific size props (like `xs`, `sm`) with a single `size` prop using an object mapping breakpoints to values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-grid-v2/upgrade-to-grid-v2.md#_snippet_2

LANGUAGE: diff
CODE:
```
 <Grid
-  xs={12}
-  sm={6}
+  size={{ xs: 12, sm: 6 }}
 >
```

----------------------------------------

TITLE: Use theme.vars with useTheme hook
DESCRIPTION: Shows how to use the useTheme hook to get the runtime theme object and access its CSS variables via theme.vars.* within a functional component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_5

LANGUAGE: jsx
CODE:
```
import { useTheme } from '@mui/joy/styles';

const SomeComponent = () => {
  const theme = useTheme(); // The runtime theme.

  return (
    <div>
      <p style={{ color: {theme.vars.palette.primary[500]} }}>Some text here.</p>
    </div>
  );
};

```

----------------------------------------

TITLE: Applying Global Variants to Joy UI Button (JSX)
DESCRIPTION: This snippet demonstrates how to apply the four global variants (`solid`, `soft`, `outlined`, `plain`) to a Joy UI Button component. Joy UI defines variants globally, allowing consistent styling across different components using the same variant names.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/first-look-at-joy.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Button variant="solid">
<Button variant="soft">
<Button variant="outlined">
<Button variant="plain">
```

----------------------------------------

TITLE: Running MUI v6 Codemods
DESCRIPTION: Provides command-line instructions using `npx` to execute automated codemods for migrating styled components, sx prop usage, and theme configurations to be compatible with Material UI v6.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_25

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v6.0.0/styled <path/to/folder-or-file>
npx @mui/codemod@latest v6.0.0/sx-prop <path/to/folder-or-file>
npx @mui/codemod@latest v6.0.0/theme-v6 <path/to/theme-file>
```

----------------------------------------

TITLE: MUI Box Width Percentage Examples - JSX
DESCRIPTION: Provides examples of setting the `width` property using percentage values and the 'auto' keyword within the MUI `Box` component's `sx` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/sizing/sizing.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Box sx={{ width: '25%' }}>…
<Box sx={{ width: '50%' }}>…
<Box sx={{ width: '75%' }}>…
<Box sx={{ width: '100%' }}>…
<Box sx={{ width: 'auto' }}>…
```

----------------------------------------

TITLE: Using AspectRatio with Next.js Image - Joy UI
DESCRIPTION: Demonstrates how to wrap a Next.js Image component with the Joy UI AspectRatio component. It highlights the necessity of using `layout="fill"` on the Image component when used in this context to ensure it conforms to the AspectRatio container.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/aspect-ratio/aspect-ratio.md#_snippet_1

LANGUAGE: js
CODE:
```
import Image from 'next/image';
import AspectRatio from '@mui/joy/AspectRatio';
import mountains from '../public/mountains.jpg';

function App() {
  return (
    <AspectRatio variant="outlined" ratio="1" objectFit="cover">
      {/* only layout="fill" makes sense for using with AspectRatio */}
      <Image alt="Mountains" src={mountains} layout="fill" placeholder="blur" />
    </AspectRatio>
  );
}
```

----------------------------------------

TITLE: Applying Custom Material UI Theme in TSX
DESCRIPTION: This TSX snippet shows how to create a custom Material UI theme using `createTheme` and apply it to a React application by wrapping the root component with `ThemeProvider`. It includes example customizations for shape and the Switch component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/design-resources/material-ui-sync/material-ui-sync.md#_snippet_3

LANGUAGE: tsx
CODE:
```
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  cssVariables: true,
  shape: {
    borderRadiusRound: 999,
  },
  components: {
    MuiSwitch: {
      styleOverrides: {
        root: {
          '&.MuiSwitch-sizeMedium:has(.MuiSwitch-colorPrimary)': {
            '&:has(.Mui-checked):not(:has(.Mui-disabled)):not(:has(.Mui-focusVisible))':
              {
                width: '40px',
                height: '21px',
                padding: '0',
                '& .MuiSwitch-switchBase': {
                  transform: 'translateX(19px) translateY(2px)',
                  padding: '0',
                  '& .MuiSwitch-thumb': {
                    width: '17px',
                    height: '17px',
                    background: '#FAFAFA',
                  },
                  '& + .MuiSwitch-track': {
                    width: '38px',
                    height: '21px',
                    background: 'var(--mui-palette-success-light)',
                    borderRadius: 'var(--mui-shape-borderRadiusRound)',
                    opacity: '1',
                  },
                },
              },
          },
        },
      },
    },
  },
});

export default function MyApp(props) {
  const { Component, pageProps } = props;

  return (
    <ThemeProvider theme={theme}>
      <Component {...pageProps} />
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Setting Border Color with MUI sx Prop (JSX)
DESCRIPTION: Provides examples for setting the border color of a MUI Box component using the `sx` prop. It demonstrates using theme palette values like `primary.main`, `secondary.main`, `error.main`, `grey.500`, and `text.primary`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/borders/borders.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Box sx={{ borderColor: 'primary.main' }}>…
<Box sx={{ borderColor: 'secondary.main' }}>…
<Box sx={{ borderColor: 'error.main' }}>…
<Box sx={{ borderColor: 'grey.500' }}>…
<Box sx={{ borderColor: 'text.primary' }}>…
```

----------------------------------------

TITLE: MUI Grid Custom Columns (JSX)
DESCRIPTION: Illustrates setting a custom number of columns (other than the default 12) for the MUI Grid container using the `columns` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_9

LANGUAGE: jsx
CODE:
```
<Grid container columns={16}>
```

----------------------------------------

TITLE: Styling Styled Component Slot with ownerState
DESCRIPTION: Updates a styled component definition to accept `ownerState` as a prop in the styling function. This enables applying styles conditionally based on properties like `variant` received through the `ownerState` object from the parent component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_4

LANGUAGE: diff
CODE:
```
  const StatRoot = styled('div', {
    name: 'JoyStat',
    slot: 'root',
-  })(({ theme }) => ({
+  })(({ theme, ownerState }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(0.5),
    padding: theme.spacing(3, 4),
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[2],
    letterSpacing: '-0.025em',
    fontWeight: 600,
+   ...ownerState.variant === 'outlined' && {
+    border: `2px solid ${theme.palette.divider}`,
+   },
  }));
```

----------------------------------------

TITLE: Adjusting MUI Grid Width with sx Prop
DESCRIPTION: Demonstrates how to make the new MUI Grid component occupy the full width of its container using the `sx` prop, either by setting `width: '100%'` or `flexGrow: 1` when the parent is a flex container.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-grid-v2/upgrade-to-grid-v2.md#_snippet_7

LANGUAGE: diff
CODE:
```
-<GridLegacy container>
+<Grid container sx={{ width: '100%' }}>

// alternatively, if the Grid's parent is a flex container:
-<GridLegacy container>
+<Grid container sx={{ flexGrow: 1 }}>
```

----------------------------------------

TITLE: Running jss-to-styled Codemod - Bash
DESCRIPTION: Command to run the `@mui/codemod` tool with the `v5.0.0/jss-to-styled` transformation on a specified file or directory path.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_205

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v5.0.0/jss-to-styled <path>
```

----------------------------------------

TITLE: Importing ListItemContent component (Joy UI) - JSX
DESCRIPTION: Imports the `ListItemContent` component from the `@mui/joy` package, used as a flexible container for the main text content within a list item, particularly useful for handling text overflow with ellipsis.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/list/list.md#_snippet_4

LANGUAGE: jsx
CODE:
```
import ListItemContent from '@mui/joy/ListItemContent';
```

----------------------------------------

TITLE: Implement CSS Grid Layout with Box (JSX)
DESCRIPTION: Provides an alternative grid implementation using the Material UI `Box` component and native CSS Grid properties (`display`, `gridTemplateColumns`, `gap`, `gridColumn`) to create a layout.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/2021-q2-update.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Box display="grid" gridTemplateColumns="repeat(12, 1fr)" gap={2}>
  <Box gridColumn="span 8">
    <Item>xs=8</Item>
  </Box>
  <Box gridColumn="span 4">
    <Item>xs=4</Item>
  </Box>
  <Box gridColumn="span 4">
    <Item>xs=4</Item>
  </Box>
  <Box gridColumn="span 8">
    <Item>xs=8</Item>
  </Box>
</Box>
```

----------------------------------------

TITLE: Syncing Color Mode between Joy UI and Material UI (JS)
DESCRIPTION: Shows how to synchronize the color mode (light/dark) between Joy UI and Material UI. It uses the `useColorScheme` hook from both libraries and calls their respective `setMode` functions simultaneously. Includes a check for component mounting to prevent server-side rendering mismatches.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/material-ui/material-ui.md#_snippet_1

LANGUAGE: js
CODE:
```
import { useColorScheme as useJoyColorScheme } from '@mui/joy/styles';
import { useColorScheme as useMaterialColorScheme } from '@mui/material/styles';

const ModeToggle = () => {
  const { mode, setMode: setMaterialMode } = useMaterialColorScheme();
  const { setMode: setJoyMode } = useJoyColorScheme();
  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => {
    setMounted(true);
  }, []);
  if (!mounted) {
    // prevent server-side rendering mismatch
    // because `mode` is undefined on the server.
    return null;
  }
  return (
    <IconButton
      onClick={() => {
        setMaterialMode(mode === 'dark' ? 'light' : 'dark');
        setJoyMode(mode === 'dark' ? 'light' : 'dark');
      }}
    >
      {/** You can use `mode` from Joy UI or Material UI since they are synced **/
      mode === 'dark' ? <DarkMode /> : <LightMode />}
    </IconButton>
  );
};
```

----------------------------------------

TITLE: Disabling Global Transitions and Animations via CssBaseline (JS)
DESCRIPTION: This snippet shows how to disable all transitions and animations across the application by applying global CSS overrides using `MuiCssBaseline` within the theme. This requires the `CssBaseline` component to be used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/faq/faq.md#_snippet_2

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material';

const theme = createTheme({
  components: {
    // Name of the component ⚛️
    MuiCssBaseline: {
      styleOverrides: {
        '*, *::before, *::after': {
          transition: 'none !important',
          animation: 'none !important',
        },
      },
    },
  },
});
```

----------------------------------------

TITLE: Accessing Theme with sx prop (Function)
DESCRIPTION: Demonstrates accessing the theme object within the `sx` prop by providing a function as the value for a style property. This function receives the theme object as an argument, allowing complex theme-based calculations.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_10

LANGUAGE: js
CODE:
```
import Button from '@mui/material/Button';
import { lighten } from 'polished';

const MyStyledButton = (props) => (
  <Button
    sx={{ backgroundColor: (theme) => lighten(0.2, theme.palette.primary.main) }}
  >
    {props.children}
  </Button>
);
```

----------------------------------------

TITLE: Configure CSS Layer Order in Next.js App Router (CSS)
DESCRIPTION: Defines the CSS layer order in the global stylesheet for a Next.js App Router project using the `@layer` directive. The order `theme, base, mui, components, utilities` ensures that Tailwind CSS utility classes (`utilities`) have higher specificity than Material UI styles (`mui`), allowing them to override MUI defaults.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/tailwindcss/tailwindcss-v4.md#_snippet_1

LANGUAGE: css
CODE:
```
@layer theme, base, mui, components, utilities;
@import 'tailwindcss';
```

----------------------------------------

TITLE: Creating Theme for Next.js Font Optimization
DESCRIPTION: Define a Material UI theme in a client component file (e.g., `src/theme.ts`) that uses a CSS variable (`var(--font-roboto)`) for the typography font family, integrating with Next.js font optimization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_3

LANGUAGE: javascript
CODE:
```
'use client';
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  typography: {
    fontFamily: 'var(--font-roboto)',
  },
});

export default theme;
```

----------------------------------------

TITLE: Enabling Native Color Scheme with CssBaseline/ScopedCssBaseline (JSX)
DESCRIPTION: Explains how to enable the native `color-scheme` CSS property on `<html>` by setting the `enableColorScheme` prop on either `CssBaseline` or `ScopedCssBaseline`. This allows native components like scrollbars to adapt to the theme's mode.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/css-baseline/css-baseline.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<CssBaseline enableColorScheme />

// or

<ScopedCssBaseline enableColorScheme >
  {/* The rest of your application using color-scheme*/}
</ScopedCssBaseline>
```

----------------------------------------

TITLE: Configure CSS Layer Order in Next.js Pages Router _app (TSX)
DESCRIPTION: Configures the CSS layer order in the `_app.tsx` file for a Next.js Pages Router project by adding the `GlobalStyles` component as the first child of `AppCacheProvider`. The `styles` prop uses the `@layer` directive to define the order `theme, base, mui, components, utilities`, ensuring Tailwind utilities override Material UI styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/tailwindcss/tailwindcss-v4.md#_snippet_3

LANGUAGE: tsx
CODE:
```
import { AppCacheProvider } from '@mui/material-nextjs/v15-pagesRouter';
import GlobalStyles from '@mui/material/GlobalStyles';

export default function MyApp(props: AppProps) {
  const { Component, pageProps } = props;
  return (
    <AppCacheProvider {...props}>
      <GlobalStyles styles="@layer theme, base, mui, components, utilities;" />
      {/* Your app */}
    </AppCacheProvider>
  );
}
```

----------------------------------------

TITLE: Replacing theme.palette.mode Check with theme.applyStyles - MUI v6
DESCRIPTION: Demonstrates using the new `theme.applyStyles` utility within styled components to apply styles specifically for different color modes (like 'dark'), replacing manual checks of `theme.palette.mode`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_24

LANGUAGE: javascript
CODE:
```
({ theme }) => ({
  padding: '0.5rem 1rem',
  border: '1px solid,',
  borderColor: '#000',
  ...theme.applyStyles('dark', {
    borderColor: '#fff',
  })
})
```

----------------------------------------

TITLE: Install Material UI with styled-components
DESCRIPTION: Installs Material UI and configures it to use styled-components instead of the default Emotion engine. This requires installing @mui/styled-engine-sc and styled-components using npm, pnpm, or yarn.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/installation/installation.md#_snippet_2

LANGUAGE: bash
CODE:
```
npm install @mui/material @mui/styled-engine-sc styled-components
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/material @mui/styled-engine-sc styled-components
```

LANGUAGE: bash
CODE:
```
yarn add @mui/material @mui/styled-engine-sc styled-components
```

----------------------------------------

TITLE: Passing ownerState to Slots in React
DESCRIPTION: Modifies a React component to include a `variant` prop and pass it down to child slots using the `ownerState` object for styling purposes. This allows slots to be styled based on the component's internal state or props without spreading them to the DOM.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_3

LANGUAGE: diff
CODE:
```
  const Stat = React.forwardRef(function Stat(props, ref) {
+   const { value, unit, variant, ...other } = props;
+
+   const ownerState = { ...props, variant };

    return (
-      <StatRoot ref={ref} {...other}>
-        <StatValue>{value}</StatValue>
-        <StatUnit>{unit}</StatUnit>
-      </StatRoot>
+      <StatRoot ref={ref} ownerState={ownerState} {...other}>
+        <StatValue ownerState={ownerState}>{value}</StatValue>
+        <StatUnit ownerState={ownerState}>{unit}</StatUnit>
+      </StatRoot>
    );
  });
```

----------------------------------------

TITLE: Rendering RichTreeView with Dataset (JSX)
DESCRIPTION: Demonstrates how to render the MUI X RichTreeView component, passing the defined dataset via the `items` prop and enabling multi-selection.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v7-beta.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<RichTreeView items={myDataSet} multiSelect />
```

----------------------------------------

TITLE: Augmenting MUI Palette Interfaces (TypeScript)
DESCRIPTION: Demonstrates how to use TypeScript module augmentation to add a custom color token like 'darker' to the `PaletteColor` and `SimplePaletteColorOptions` interfaces in the `@mui/material/styles` module. This allows the custom token to be used within the theme's palette type definitions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_9

LANGUAGE: TypeScript
CODE:
```
declare module '@mui/material/styles' {
  interface PaletteColor {
    darker?: string;
  }

  interface SimplePaletteColorOptions {
    darker?: string;
  }
}
```

----------------------------------------

TITLE: Customize Font Family with System Font (JS)
DESCRIPTION: This snippet shows how to override the default Roboto font family in a Material UI theme by setting the `typography.fontFamily` property to a list of system fonts using `createTheme`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_0

LANGUAGE: js
CODE:
```
const theme = createTheme({
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
  },
});
```

----------------------------------------

TITLE: Loading Material Icons Font (HTML)
DESCRIPTION: Provides the HTML `<link>` tag necessary to load the default Material Icons font from Google Web Fonts, which is a prerequisite for using the `Icon` component with Material Icons ligatures.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_2

LANGUAGE: html
CODE:
```
<link
  rel="stylesheet"
  href="https://fonts.googleapis.com/icon?family=Material+Icons"
/>
```

----------------------------------------

TITLE: Optimizing Accordion Performance with UnmountOnExit - JSX
DESCRIPTION: Shows how to improve performance, especially with large content trees or many accordions, by unmounting the Accordion Details content when not expanded using the `unmountOnExit` prop within `slotProps.transition`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/accordion/accordion.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Accordion slotProps={{ transition: { unmountOnExit: true } }} />
```

----------------------------------------

TITLE: JSS to tss-react Migration with $, Params, classes Prop, and Naming (Diff)
DESCRIPTION: This comprehensive diff demonstrates migrating JSS styles that use the '$' syntax, accept parameters via `useStyles()`, merge external classes via a `classes` prop, and explicitly name the stylesheet. It shows the updated `makeStyles` signature, parameter access, class merging syntax, and the use of `cx`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migrating-from-jss.md#_snippet_9

LANGUAGE: diff
CODE:
```
-import clsx from 'clsx';
-import { makeStyles, createStyles } from '@material-ui/core/styles';
+import { makeStyles } from 'tss-react/mui';

-const useStyles = makeStyles((theme) => createStyles<
-  'root' | 'small' | 'child', {color: 'primary' | 'secondary', padding: number}
->
-({
-  root: ({color, padding}) => ({
+const useStyles = makeStyles<{color: 'primary' | 'secondary', padding: number}, 'child' | 'small'>({name: 'App'})((theme, { color, padding }, classes) => ({
+  root: {
     padding: padding,
-    '&:hover $child': {
+    [`&:hover .${classes.child}`]: {
       backgroundColor: theme.palette[color].main,
     }
-  }),
+  },
   small: {},
   child: {
     border: '1px solid black',
     height: 50,
-    '&$small': {
+    [`&.${classes.small}`]: {
       height: 30
     }
   }
-}), {name: 'App'});
+}));

 function App({classes: classesProp}: {classes?: any}) {
-  const classes = useStyles({color: 'primary', padding: 30, classes: classesProp});
+  const { classes, cx } = useStyles({
+    color: 'primary',
+    padding: 30
+  }, {
+    props: {
+      classes: classesProp
+    }
+  });

   return (
     <div className={classes.root}>
       <div className={classes.child}>
         The Background take the primary theme color when the mouse hovers the parent.
       </div>
-      <div className={clsx(classes.child, classes.small)}>
+      <div className={cx(classes.child, classes.small)}>
         The Background take the primary theme color when the mouse hovers the parent.
         I am smaller than the other child.
       </div>
     </div>
   );
 }

 export default App;
```

----------------------------------------

TITLE: Defining Autocomplete Option Structure (TypeScript)
DESCRIPTION: Illustrates the default TypeScript interface and type alias for defining options accepted by the MUI Autocomplete component. Options can be objects with a 'label' property or simple strings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_0

LANGUAGE: ts
CODE:
```
interface AutocompleteOption {
  label: string;
}
// or
type AutocompleteOption = string;
```

----------------------------------------

TITLE: TypeScript Augmentation for Adding New Typography Levels (Joy UI)
DESCRIPTION: Provides the TypeScript declaration needed to inform the type system about a newly added typography level in the Joy UI theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-typography/theme-typography.md#_snippet_6

LANGUAGE: ts
CODE:
```
// You can put this to any file that's included in your tsconfig
declare module '@mui/joy/styles' {
  interface TypographySystemOverrides {
    kbd: true;
  }
}
```

----------------------------------------

TITLE: Enabling Theme Variable Typings (TS)
DESCRIPTION: Shows the necessary TypeScript import (@mui/material/themeCssVarsAugmentation) required to enable type-safe access to the theme variables via theme.vars.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_9

LANGUAGE: ts
CODE:
```
// The import can be in any file that is included in your `tsconfig.json`
import type {} from '@mui/material/themeCssVarsAugmentation';
import { styled } from '@mui/material/styles';

const StyledComponent = styled('button')(({ theme }) => ({
  // ✅ typed-safe
  color: theme.vars.palette.primary.main,
}));
```

----------------------------------------

TITLE: Running Codemod for sx Prop Migration (Bash)
DESCRIPTION: Provides the bash command to execute the MUI codemod tool. This specific codemod helps automatically migrate code using the deprecated callback syntax within the `sx` prop to the new recommended format.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_17

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v6.0.0/sx-prop path/to/file-or-folder
```

----------------------------------------

TITLE: Scoping CSS Baseline to Children with ScopedCssBaseline (JSX)
DESCRIPTION: Shows how to use `ScopedCssBaseline` to apply baseline styles only to the component's children, useful for progressive migration. Ensure `ScopedCssBaseline` is imported and used as a wrapper around the content that needs the baseline styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/css-baseline/css-baseline.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import ScopedCssBaseline from '@mui/material/ScopedCssBaseline';
import MyApp from './MyApp';

export default function MyApp() {
  return (
    <ScopedCssBaseline>
      {/* The rest of your application */}
      <MyApp />
    </ScopedCssBaseline>
  );
}
```

----------------------------------------

TITLE: Using Extended Component Colors in JSX - JSX
DESCRIPTION: Demonstrates how to apply the custom color values defined in the theme's style overrides directly as props on the component instances in JSX, enabling the use of colors beyond the default palette.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_6

LANGUAGE: JSX
CODE:
```
<Button color="secondary">Secondary color</Button>
<Button color="tertiary">Tertiary color</Button>
```

----------------------------------------

TITLE: Converting MUI Submodule Imports to Root Module
DESCRIPTION: This codemod converts imports from specific `@mui/material` submodules (like `@mui/material/List`) to top-level imports from the root `@mui/material` module. This can help with bundle size optimization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_229

LANGUAGE: diff
CODE:
```
-import List from '@mui/material/List';
-import Grid from '@mui/material/Grid';
+import { List, Grid } from '@mui/material';
```

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v5.0.0/top-level-imports <path>
```

----------------------------------------

TITLE: Setting Text Alignment in MUI JSX
DESCRIPTION: Shows how to control the horizontal alignment of text within a `Box` component using the `textAlign` CSS property via the `sx` prop. Examples include left, center, and right alignment.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Box sx={{ textAlign: 'left' }}>…
<Box sx={{ textAlign: 'center' }}>…
<Box sx={{ textAlign: 'right' }}>…
```

----------------------------------------

TITLE: Customizing Joy UI CssBaseline Styles
DESCRIPTION: Explains how to add custom global styles alongside `CssBaseline` using the `GlobalStyles` component. It shows placing `GlobalStyles` after `CssBaseline` and providing CSS object styles to target specific elements like `html` and `body`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/css-baseline/css-baseline.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import { CssVarsProvider } from '@mui/joy/styles';
import CssBaseline from '@mui/joy/CssBaseline';
import GlobalStyles from '@mui/joy/GlobalStyles';

function App() {
  return (
    <CssVarsProvider>
      <CssBaseline /> {/* CssBaseline must come first */}
      <GlobalStyles
        styles={{
          // CSS object styles
          html: {
            // ...
          },
          body: {
            // ...
          },
        }}
      />
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Controlling Autocomplete Value with Referential Stability (React/TSX)
DESCRIPTION: Demonstrates the correct way to manage the `value` prop for a controlled MUI Autocomplete component, particularly with multiple selections. It highlights the importance of using `React.useMemo` to ensure the value array reference remains stable between renders, preventing unnecessary re-renders.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_2

LANGUAGE: tsx
CODE:
```
// ⚠️ BAD
return <Autocomplete multiple value={allValues.filter((v) => v.selected)} />;

// 👍 GOOD
const selectedValues = React.useMemo(
  () => allValues.filter((v) => v.selected),
  [allValues],
);
return <Autocomplete multiple value={selectedValues} />;
```

----------------------------------------

TITLE: Using useScrollTrigger Hook to Hide AppBar - React/MUI
DESCRIPTION: Illustrates how to use the useScrollTrigger hook to detect scroll events and conditionally hide or show an element, such as an AppBar, using a Slide transition.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/app-bar/app-bar.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import useScrollTrigger from '@mui/material/useScrollTrigger';

function HideOnScroll(props) {
  const trigger = useScrollTrigger();
  return (
    <Slide in={!trigger}>
      <div>Hello</div>
    </Slide>
  );
}
```

----------------------------------------

TITLE: Augmenting DefaultTheme in MUI Styles (TypeScript)
DESCRIPTION: Fixes the "Property 'palette', 'spacing' does not exist on type 'DefaultTheme'" error when using `makeStyles` from `@mui/styles` by merging the core MUI `Theme` type into the `@mui/styles` `DefaultTheme`. Add this snippet to a theme or configuration file included in your `tsconfig.json`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/troubleshooting.md#_snippet_8

LANGUAGE: ts
CODE:
```
import { Theme } from '@mui/material/styles';

declare module '@mui/styles/defaultTheme' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface (remove this line if you don't have the rule enabled)
  interface DefaultTheme extends Theme {}
}
```

----------------------------------------

TITLE: Augmenting Joy UI Theme Interfaces for Custom Palette - TypeScript
DESCRIPTION: When working with TypeScript, this snippet demonstrates how to augment the Joy UI theme's interfaces (`ColorPalettePropOverrides`, `Palette`) to include the new `secondary` palette, ensuring type safety when using the `color` prop and configuring the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_6

LANGUAGE: ts
CODE:
```
// You can put this to any file that's included in your tsconfig
import type { PaletteRange } from '@mui/joy/styles';

declare module '@mui/joy/styles' {
  interface ColorPalettePropOverrides {
    // apply to all Joy UI components that support `color` prop
    secondary: true;
  }

  interface Palette {
    // this will make the node `secondary` configurable in `extendTheme`
    // and add `secondary` to the theme's palette.
    secondary: PaletteRange;
  }
}
```

----------------------------------------

TITLE: Configuring Data Source for Server-Side Editing in MUI X Data Grid
DESCRIPTION: This snippet demonstrates how to define a `GridDataSource` object for the MUI X Data Grid that supports server-side data fetching and updating. It includes the required `getRows` method for fetching data and the optional `updateRow` method for handling row updates on the server. The `updateRow` method should return a Promise that resolves upon successful update.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v8.md#_snippet_1

LANGUAGE: js
CODE:
```
const dataSource: GridDataSource = {
  getRows: async (params: GridGetRowsParams) => {
    // Fetch rows from the server
  },
  updateRow: async (params: GridUpdateRowParams) => {
    // Update row on the server
  }
}
```

----------------------------------------

TITLE: Update ThemeProvider Import - MUI Styles - diff
DESCRIPTION: Shows that when using `@mui/styles` utilities with `@mui/material`, the `ThemeProvider` should now be imported from `@mui/material/styles` instead of `@mui/styles` to ensure theme context is available to both.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-style-changes.md#_snippet_20

LANGUAGE: diff
CODE:
```
-import { ThemeProvider } from '@mui/styles';
+import { ThemeProvider } from '@mui/material/styles';

```

----------------------------------------

TITLE: Importing Joy UI RadioGroup Component
DESCRIPTION: Imports the RadioGroup component from the `@mui/joy/RadioGroup` module. This component is used to group multiple Radio components for better accessibility and control.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/radio-button/radio-button.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import RadioGroup from '@mui/joy/RadioGroup';
```

----------------------------------------

TITLE: Replacing MUI Box with Native HTML Elements (Diff)
DESCRIPTION: Illustrates how Pigment CSS allows using native HTML elements (like `div`, `img`) instead of the `Box` component while still supporting the `sx` prop. This involves removing the `Box` import and replacing `Box` instances.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/migrating-to-pigment-css.md#_snippet_18

LANGUAGE: diff
CODE:
```
-import Box from '@mui/material/Box';

 function CustomCard() {
   return (
-    <Box sx={{ display: 'flex' }}>
-      <Box component="img" src="..." sx={{ width: 24, height: 24 }}>
-      ...
-    </Box>
+    <div sx={{ display: 'flex' }}>
+      <img src="..." sx={{ width: 24, height: 24 }}>
+      ...
+    </div>
   );
 }
```

----------------------------------------

TITLE: Customizing Font Family in Joy UI Theme (JS)
DESCRIPTION: This JavaScript snippet demonstrates how to use `extendTheme` to override the default font family in a Joy UI theme, setting the 'display' and 'body' font families to "Public Sans" with a fallback.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_13

LANGUAGE: js
CODE:
```
extendTheme({
  fontFamily: {
    display: '"Public Sans", var(--joy-fontFamily-fallback)',
    body: '"Public Sans", var(--joy-fontFamily-fallback)',
  },
});
```

----------------------------------------

TITLE: Using useColorScheme Hook and CssVarsProvider in App - JavaScript
DESCRIPTION: This snippet illustrates how to use the `useColorScheme` hook within a component (`App`) to get and toggle the current color mode. It also shows how to wrap the root of the application with the custom `CssVarsProvider` to make the theme and hook available throughout the component tree.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/experimental-api/css-theme-variables/css-theme-variables.md#_snippet_3

LANGUAGE: js
CODE:
```
// App.js
function App() {
  const { setMode, mode } = useColorScheme();
  const toggleMode = () => {
    setMode(mode === 'dark' ? 'light' : 'dark');
  };

  return (
    <div>
      <h1>Current Mode: {mode}</h1>
      <Button onClick={toggleMode}>Toggle Mode</Button>
    </div>
  );
}

// main.js
import * as React from 'react';
import * as ReactDOM from 'react-dom/client';
import App from './App';
import { CssVarsProvider } from './CssVarsProvider';

ReactDOM.createRoot(document.getElementById('root')).render(
  <CssVarsProvider>
    <App />
  </CssVarsProvider>,
);
```

----------------------------------------

TITLE: Applying Theme Default Props with useThemeProps in React
DESCRIPTION: Integrates the `useThemeProps` hook from `@mui/joy/styles` into a React component. This hook merges the component's incoming props with default props defined in the theme configuration under the component's name (`JoyStat`), allowing for centralized default prop customization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/creating-themed-components/creating-themed-components.md#_snippet_5

LANGUAGE: diff
CODE:
```
+ import { useThemeProps } from '@mui/joy/styles';

- const Stat = React.forwardRef(function Stat(props, ref) {
+ const Stat = React.forwardRef(function Stat(inProps, ref) {
+   const props = useThemeProps({ props: inProps, name: 'JoyStat' });
    const { value, unit, ...other } = props;

    return (
      <StatRoot ref={ref} {...other}>
        <StatValue>{value}</StatValue>
        <StatUnit>{unit}</StatUnit>
      </StatRoot>
    );
  });
```

----------------------------------------

TITLE: Defining and Using Custom Breakpoints in MUI Theme
DESCRIPTION: Illustrates how to create a custom theme with specific breakpoint values using `createTheme`. It defines 'mobile', 'tablet', 'laptop', and 'desktop' breakpoints and then applies responsive styles using these custom keys within the `sx` prop of a `Box` component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_8

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import Box from '@mui/material/Box';
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  breakpoints: {
    values: {
      mobile: 0,
      tablet: 640,
      laptop: 1024,
      desktop: 1280,
    },
  },
});

export default function CustomBreakpoints() {
  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={{
          width: {
            mobile: 100,
            laptop: 300,
          },
        }}
      >
        This box has a responsive width
      </Box>
    </ThemeProvider>
  );
}
```

----------------------------------------

TITLE: Extend MUI Joy Theme with Success Palette
DESCRIPTION: Defines and applies a custom 'success' color palette to the MUI Joy theme for both light and dark color schemes. It includes various states like plain, outlined, soft, and solid, mapping them to specific color values.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_10

LANGUAGE: javascript
CODE:
```
const success = {
  50: '#F3FEF5',
  100: '#D7F5DD',
  200: '#77EC95',
  300: '#4CC76E',
  400: '#2CA24D',
  500: '#1A7D36',
  600: '#0F5D26',
  700: '#034318',
  800: '#002F0F',
  900: '#001D09'
};

extendTheme({
  colorSchemes: {
    light: {
      palette: {
        success: {
          ...success,
          plainColor: `var(--joy-palette-success-600)`,
          plainHoverBg: `var(--joy-palette-success-100)`,
          plainActiveBg: `var(--joy-palette-success-200)`,
          plainDisabledColor: `var(--joy-palette-success-200)`,

          outlinedColor: `var(--joy-palette-success-500)`,
          outlinedBorder: `var(--joy-palette-success-200)`,
          outlinedHoverBg: `var(--joy-palette-success-100)`,
          outlinedHoverBorder: `var(--joy-palette-success-300)`,
          outlinedActiveBg: `var(--joy-palette-success-200)`,
          outlinedDisabledColor: `var(--joy-palette-success-100)`,
          outlinedDisabledBorder: `var(--joy-palette-success-100)`,

          softColor: `var(--joy-palette-success-600)`,
          softBg: `var(--joy-palette-success-100)`,
          softHoverBg: `var(--joy-palette-success-200)`,
          softActiveBg: `var(--joy-palette-success-300)`,
          softDisabledColor: `var(--joy-palette-success-300)`,
          softDisabledBg: `var(--joy-palette-success}-)50`,

          solidColor: '#fff',
          solidBg: `var(--joy-palette-success-500)`,
          solidHoverBg: `var(--joy-palette-success-600)`,
          solidActiveBg: `var(--joy-palette-success-700)`,
          solidDisabledColor: '#fff',
          solidDisabledBg: `var(--joy-palette-success-200)`
        }
      }
    },
    dark: {
      palette: {
        success: {
          ...success,
          plainColor: `var(--joy-palette-success-300)`,
          plainHoverBg: `var(--joy-palette-success-800)`,
          plainActiveBg: `var(--joy-palette-success-700)`,
          plainDisabledColor: `var(--joy-palette-success-800)`,

          outlinedColor: `var(--joy-palette-success-200)`,
          outlinedBorder: `var(--joy-palette-success-700)`,
          outlinedHoverBg: `var(--joy-palette-success-800)`,
          outlinedHoverBorder: `var(--joy-palette-success-600)`,
          outlinedActiveBg: `var(--joy-palette-success-900)`,
          outlinedDisabledColor: `var(--joy-palette-success-800)`,
          outlinedDisabledBorder: `var(--joy-palette-success-800)`,

          softColor: `var(--joy-palette-success-200)`,
          softBg: `var(--joy-palette-success-900)`,
          softHoverBg: `var(--joy-palette-success-800)`,
          softActiveBg: `var(--joy-palette-success-700)`,
          softDisabledColor: `var(--joy-palette-success-800)`,
          softDisabledBg: `var(--joy-palette-success-900)`,

          solidColor: '#fff',
          solidBg: `var(--joy-palette-success-600)`,
          solidHoverBg: `var(--joy-palette-success-700)`,
          solidActiveBg: `var(--joy-palette-success-800)`,
          solidDisabledColor: `var(--joy-palette-success-700)`,
          solidDisabledBg: `var(--joy-palette-success-900)`
        }
      }
    }
  }
});
```

----------------------------------------

TITLE: Creating Emotion Cache Instance (JavaScript)
DESCRIPTION: Defines a function to create a new Emotion cache instance with a specific key. This cache is used for both server-side rendering and client-side hydration to ensure consistent style extraction and injection.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_2

LANGUAGE: js
CODE:
```
import createCache from '@emotion/cache';

export default function createEmotionCache() {
  return createCache({ key: 'css' });
}
```

----------------------------------------

TITLE: Migrating TextField Input Props in React
DESCRIPTION: Demonstrates the deprecation of specific props like `InputProps`, `inputProps`, `SelectProps`, etc., on the `<TextField>` component, showing how to consolidate them under the new `slotProps`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_120

LANGUAGE: diff
CODE:
```
 <TextField
-  InputProps={CustomInputProps}
-  inputProps={CustomHtmlInputProps}
-  SelectProps={CustomSelectProps}
-  InputLabelProps={CustomInputLabelProps}
-  FormHelperTextProps={CustomFormHelperProps}
+  slotProps={{
+    input: CustomInputProps,
+    htmlInput: CustomHtmlInputProps,
+    select: CustomSelectProps,
+    inputLabel: CustomInputLabelProps,
+    formHelper: CustomFormHelperProps,
+  }}
 />
```

----------------------------------------

TITLE: Customizing Joy UI Theme Tokens with extendTheme
DESCRIPTION: Demonstrates how to use the `extendTheme` function to customize global theme tokens like color palettes and font families for Joy UI components. This ensures consistency across components that use these tokens.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/approaches/approaches.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';

const theme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        // affects all Joy components that has `color="primary"` prop.
        primary: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          // 300, 400, ..., 800,
          900: '#78350f',
        },
      },
    },
  },
  fontFamily: {
    display: 'Inter, var(--joy-fontFamily-fallback)',
    body: 'Inter, var(--joy-fontFamily-fallback)',
  },
});

function App() {
  return <CssVarsProvider theme={theme}>...</CssVarsProvider>;
}
```

----------------------------------------

TITLE: Minimum tsconfig.json Configuration for Material UI Types
DESCRIPTION: This JSON snippet shows the recommended minimum compiler options for your `tsconfig.json` file when using Material UI with TypeScript. These options ensure proper type checking and compatibility with Material UI's type definitions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/typescript/typescript.md#_snippet_0

LANGUAGE: json
CODE:
```
{
  "compilerOptions": {
    "lib": ["es6", "dom"],
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "allowSyntheticDefaultImports": true
  }
}
```

----------------------------------------

TITLE: Material UI Link as Button for Accessibility - JavaScript
DESCRIPTION: Shows how to render a Material UI Link component as a `<button>` element using the `component` prop when the link does not have a meaningful `href`, improving accessibility.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/links/links.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
import * as React from 'react';
import Link from '@mui/material/Link';

export default function ButtonLink() {
  return (
    <Link
      component="button"
      variant="body2"
      onClick={() => {
        alert("Button clicked!");
      }}
    >
      Button Link
    </Link>
  );
}
```

----------------------------------------

TITLE: Setting Font Family in MUI JSX
DESCRIPTION: Demonstrates how to specify the font family for text using the `fontFamily` CSS property via the `sx` prop on a `Box` component. Examples show using the default theme font and a specific font like Monospace.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_6

LANGUAGE: jsx
CODE:
```
<Box sx={{ fontFamily: 'default' }}>…
<Box sx={{ fontFamily: 'Monospace' }}>…
```

----------------------------------------

TITLE: Customizing MUI X Date Picker Layout (TSX)
DESCRIPTION: Shows how to create a custom layout component for MUI X Date Pickers by accessing layout parts via `usePickerLayout` and rendering them within `PickersLayoutRoot`. It then demonstrates applying this custom layout to a `StaticDatePicker` using the `slots` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v6.md#_snippet_3

LANGUAGE: tsx
CODE:
```
function MyCustomLayout(props) {
  const { toolbar, tabs, content, actionBar } = usePickerLayout(props);

  return (
    <PickersLayoutRoot className={pickersLayout.root} ownerState={props}>
      {toolbar}
      {actionBar}
      <PickersLayoutContentWrapper className={pickersLayout.contentWrapper}>
        {tabs}
        {content}
      </PickersLayoutContentWrapper>
    </PickersLayoutRoot>
  );
}

export default function CustomStaticDatePicker() {
  return <StaticDatePicker slots={{ layout: MyCustomLayout }} />;
}
```

----------------------------------------

TITLE: Augmenting MUI Theme Breakpoint Overrides for TypeScript
DESCRIPTION: Provides the TypeScript module augmentation necessary to inform the MUI theme's type definitions about custom breakpoints ('tablet', 'laptop', 'desktop') and to remove the default ones ('xs', 'sm', 'md', 'lg', 'xl'). This ensures type safety when using custom breakpoints in theme configuration and `sx` props.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/usage/usage.md#_snippet_9

LANGUAGE: ts
CODE:
```
declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xs: false; // removes the `xs` breakpoint
    sm: false;
    md: false;
    lg: false;
    xl: false;
    tablet: true; // adds the `tablet` breakpoint
    laptop: true;
    desktop: true;
  }
}
```

----------------------------------------

TITLE: Using Portal with container prop callback for SSR
DESCRIPTION: This example demonstrates how to use the 'container' prop with a callback function. This pattern is necessary for server-side rendering (SSR) environments where the DOM is not available during the initial render pass, allowing the target node to be resolved during a layout effect.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/portal/portal.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Portal container={() => document.getElementById('filter-panel')!}>
  <Child />
</Portal>
```

----------------------------------------

TITLE: Enabling CSS Theme Variables
DESCRIPTION: Modify your theme configuration in `src/theme.ts` to enable CSS theme variables by setting the `cssVariables` flag to `true`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_5

LANGUAGE: diff
CODE:
```
'use client';
 const theme = createTheme({
+  cssVariables: true,
 });
```

----------------------------------------

TITLE: Using Material UI v6 Container Queries with styled
DESCRIPTION: Shows how to define styles based on container width using the `theme.containerQueries` utility within a styled component, including examples for breakpoints and named containers, along with the simplified CSS output.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v6-is-out.md#_snippet_6

LANGUAGE: JSX
CODE:
```
const Component = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  [theme.containerQueries.up('sm')]: {
    flexDirection: 'row',
  },
  [theme.containerQueries('sidebar').up('400px')]: {
    // @container sidebar (min-width: 400px)
    flexDirection: 'row',
  },
}));
```

LANGUAGE: CSS
CODE:
```
/* Simplified CSS Output */

.Component-ad83f {
  display: flex;
  flex-direction: column;
  gap: 16px;
  @container (min-width: 600px) {
    flexDirection: 'row';
  }
  @container sidebar (min-width: 400px) {
    flexDirection: 'row';
  }
}
```

----------------------------------------

TITLE: Configuring CSS Layer for Other Styling Solutions
DESCRIPTION: Set the `enableCssLayer` option to `true` in `AppRouterCacheProvider` options when using styling solutions other than Emotion (like CSS Modules or Tailwind CSS) to ensure Material UI styles are wrapped in a `@layer mui` rule for proper cascading.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_6

LANGUAGE: javascript
CODE:
```
<AppRouterCacheProvider options={{ enableCssLayer: true }}>
```

----------------------------------------

TITLE: Install Roboto font via npm
DESCRIPTION: Install the Roboto font package using npm as a project dependency.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/typography/typography.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @fontsource/roboto
```

----------------------------------------

TITLE: Import Roboto font weights (TSX)
DESCRIPTION: Imports the required weights (300, 400, 500, 700) of the Roboto font installed via Fontsource into your application's entry point (e.g., index.tsx). This makes the font available for use by Material UI components.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/installation/installation.md#_snippet_4

LANGUAGE: tsx
CODE:
```
import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';
```

----------------------------------------

TITLE: Adding Accessibility Attributes to Radio - Material UI - JSX
DESCRIPTION: Demonstrates how to add ARIA attributes like `aria-label` directly to the underlying input element of a standalone `Radio` component using the `inputProps` prop, which is necessary when a separate `<label>` element cannot be used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/radio-buttons/radio-buttons.md#_snippet_1

LANGUAGE: JSX
CODE:
```
<Radio
  value="radioA"
  inputProps={{
    'aria-label': 'Radio A',
  }}
/>
```

----------------------------------------

TITLE: Fixing Type Widening with as const (TS)
DESCRIPTION: This snippet provides a solution to the TypeScript type widening problem by using `as const` to narrow the type of the style object. This ensures the type is specific enough to be compatible with the Material UI `sx` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_22

LANGUAGE: ts
CODE:
```
const style = {
  flexDirection: 'column',
} as const;

export default function App() {
  return <Button sx={style}>Example</Button>;
}
```

----------------------------------------

TITLE: Implementing Fuzzy Filtering - match-sorter - MUI Autocomplete - JSX
DESCRIPTION: Demonstrates how to use the `match-sorter` library for advanced filtering, such as fuzzy matching. It imports `matchSorter` and creates a filter function that passes options and the input value to it. This custom filter is then provided to the Autocomplete component's `filterOptions` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/autocomplete/autocomplete.md#_snippet_6

LANGUAGE: jsx
CODE:
```
import { matchSorter } from 'match-sorter';

const filterOptions = (options, { inputValue }) => matchSorter(options, inputValue);

<Autocomplete filterOptions={filterOptions} />;
```

----------------------------------------

TITLE: Use theme.vars with sx prop (shorthand syntax)
DESCRIPTION: Illustrates a shorthand syntax for the sx prop where string values for certain properties (like color, shadow, fontSize) are automatically resolved by looking up the corresponding value from theme.vars.*.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<Chip
  sx={{
    border: '1px solid',

    // For color properties, lookup from `theme.vars.palette`
    color: 'neutral.800', // 'var(--joy-palette-neutral-800)'
    borderColor: 'neutral.400', // 'var(--joy-palette-neutral-400)'

    // lookup from `theme.vars.shadow`
    shadow: 'sm', // 'var(--joy-shadow-sm)'

    // lookup from `theme.vars.fontSize`
    fontSize: 'sm', // 'var(--joy-fontSize-sm)'
  }}
/>
```

----------------------------------------

TITLE: Set Global HTML Direction (HTML)
DESCRIPTION: Set the global text direction for the entire application by adding the `dir="rtl"` attribute directly to the root `<html>` tag.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/right-to-left/right-to-left.md#_snippet_0

LANGUAGE: html
CODE:
```
<html dir="rtl"></html>
```

----------------------------------------

TITLE: Customize TablePagination rowsPerPageOptions with objects
DESCRIPTION: Demonstrates how to provide an array of objects with `value` and `label` keys to the `rowsPerPageOptions` prop of `TablePagination` for custom labels, such as 'All'. This is useful for providing descriptive text for options.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/table/table.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<TablePagination rowsPerPageOptions={[10, 50, { value: -1, label: 'All' }]} />
```

----------------------------------------

TITLE: Merging Theme Options with deepmerge before createTheme (JS)
DESCRIPTION: When you need to combine options from multiple sources before creating a theme, use a utility like `@mui/utils`'s `deepmerge` to merge the option objects first, as `createTheme` only processes its first argument for the initial theme structure.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theming/theming.md#_snippet_7

LANGUAGE: js
CODE:
```
import { deepmerge } from '@mui/utils';
import { createTheme } from '@mui/material/styles';

const theme = createTheme(deepmerge(options1, options2));
```

----------------------------------------

TITLE: TypeScript Augmentation for Custom Palette Tokens (Joy UI)
DESCRIPTION: Provides the TypeScript code snippet required to augment the `Palette` interface, allowing the type system to recognize newly added custom color tokens like `gradient.primary`, improving type safety when accessing them from the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_3

LANGUAGE: ts
CODE:
```
// You can put this to any file that's included in your tsconfig
declare module '@mui/joy/styles' {
  interface Palette {
    gradient: {
      primary: string;
    };
  }
}
```

----------------------------------------

TITLE: Casting Styled Component Result with TypeScript
DESCRIPTION: This TypeScript snippet demonstrates how to use the `styled()` utility from `@mui/material/styles` to create a custom component based on a Material UI component (like `Button`) and cast the result using `as typeof Button` to maintain correct type information, addressing TypeScript limitations with the `component` prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/typescript/typescript.md#_snippet_1

LANGUAGE: tsx
CODE:
```
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';

const CustomButton = styled(Button)({
  // your custom styles go here
}) as typeof Button;
```

----------------------------------------

TITLE: Popper with Transitions - MUI React
DESCRIPTION: This example shows how to integrate a transition component, such as one from react-transition-group, with the Popper component to animate its open/close state. The transition component must handle the `onEnter` and `onExited` callbacks.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/popper/popper.md#_snippet_1

LANGUAGE: JSX
CODE:
```
import * as React from 'react';
import Popper from '@mui/material/Popper';
import Button from '@mui/material/Button';
import Fade from '@mui/material/Fade'; // Example transition component

export default function TransitionsPopper() {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [open, setOpen] = React.useState(false);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    setOpen((previousOpen) => !previousOpen);
  };

  const id = open ? 'transitions-popper' : undefined;

  return (
    <div>
      <Button aria-describedby={id} type="button" onClick={handleClick}>
        Toggle Popper with Transition
      </Button>
      <Popper id={id} open={open} anchorEl={anchorEl} transition>
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <div style={{ border: '1px solid grey', padding: 16, backgroundColor: 'white' }}>
              The content of the Popper with a fade transition.
            </div>
          </Fade>
        )}
      </Popper>
    </div>
  );
}
```

----------------------------------------

TITLE: Using Custom Tree Item with RichTreeView (JSX)
DESCRIPTION: This JSX snippet shows how to integrate a custom Tree Item component, like the `CustomTreeItem` created with `useTreeItem2`, into the `RichTreeView` component. The custom component is passed via the `slots` prop, specifically targeting the `item` slot.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v7.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<RichTreeView items={myDataSet} slots={{ item: CustomTreeItem }} />
```

----------------------------------------

TITLE: Using Typography Component Level Prop (Joy UI)
DESCRIPTION: Demonstrates how to apply theme typography styles using the `level` prop on the Joy UI Typography component. This example applies the styles defined for the `body-sm` level.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-typography/theme-typography.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// use the `theme.typography['body-sm']` styles
<Typography level="body-sm">Secondary info</Typography>
```

----------------------------------------

TITLE: Update Popper Props - MUI - Component
DESCRIPTION: This diff shows the change from the deprecated `components` and `componentsProps` props to the new `slots` and `slotProps` props when using the Popper component directly. These props are used for customizing internal elements and their props.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_87

LANGUAGE: diff
CODE:
```
 <Popper
-  components={{ Root: CustomRoot }}
-  componentsProps={{ root: { testid: 'test-id' } }}
+  slots={{ root: CustomRoot }}
+  slotProps={{ root: { testid: 'test-id' } }}
 />

```

----------------------------------------

TITLE: Generating Custom Color Using augmentColor Utility (JSX)
DESCRIPTION: Illustrates how to create a custom color ('salmon') by providing only the `main` token and using the `augmentColor` utility in a two-step theme creation process. This utility automatically generates `light`, `dark`, and `contrastText` based on theme settings like `tonalOffset`. Requires `createTheme` from `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_4

LANGUAGE: JSX
CODE:
```
import { createTheme } from '@mui/material/styles';

let theme = createTheme({
  // Theme customization goes here as usual, including tonalOffset and/or
  // contrastThreshold as the augmentColor() function relies on these
});

theme = createTheme(theme, {
  // Custom colors created with augmentColor go here
  palette: {
    salmon: theme.palette.augmentColor({
      color: {
        main: '#FF5733',
      },
      name: 'salmon',
    }),
  },
});
```

----------------------------------------

TITLE: Configuring MUI Dark Mode with Data Attribute Selector - JavaScript
DESCRIPTION: Configures Material UI's color schemes and CSS variables to use a 'data' attribute selector on the `<html>` element for toggling between light and dark modes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_3

LANGUAGE: js
CODE:
```
createTheme({
  colorSchemes: { light: true, dark: true },
  cssVariables: {
    colorSchemeSelector: 'data'
  }
});

// CSS Result
[data-light] { ... }
[data-dark] { ... }
```

----------------------------------------

TITLE: Run styled codemod (v6)
DESCRIPTION: Execute the styled codemod to update usage from `@mui/system@v5` to be compatible with `@pigment-css/react`. This command applies the transformation to files specified by `<path>`. Requires `@mui/codemod@latest`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_152

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v6.0.0/styled <path>
```

----------------------------------------

TITLE: Configuring styled-components for RTL (JSX)
DESCRIPTION: If using styled-components, wrap your application tree with `StyleSheetManager` and provide `rtlPlugin` from `stylis-plugin-rtl` to the `stylisPlugins` property.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/right-to-left/right-to-left.md#_snippet_5

LANGUAGE: jsx
CODE:
```
import { StyleSheetManager } from 'styled-components';\nimport rtlPlugin from 'stylis-plugin-rtl';\n\nfunction Rtl(props) {\n  return (\n    <StyleSheetManager stylisPlugins={[rtlPlugin]}>\n      {props.children}\n    </StyleSheetManager>\n  );\n}
```

----------------------------------------

TITLE: Importing useFormControl Hook
DESCRIPTION: Imports the `useFormControl` hook from `@mui/material/FormControl`. This hook provides access to the context value of the parent `FormControl` component for advanced customization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/text-fields/text-fields.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import { useFormControl } from '@mui/material/FormControl';
```

----------------------------------------

TITLE: Running Theme v6 Codemod Bash
DESCRIPTION: Provides the bash command to execute the `theme-v6` codemod. This codemod updates theme creation from `@mui/system@v5` for compatibility with `@pigment-css/react`. Requires specifying the target file path.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_150

LANGUAGE: bash
CODE:
```
npx @mui/codemod@latest v6.0.0/theme-v6 <path>
```

----------------------------------------

TITLE: Applying Border Width with sx prop in MUI
DESCRIPTION: Demonstrates using the `border` property within the `sx` prop to set a border width in pixels. A value of 1 results in a 1px solid black border.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ border: 1 }} />
// equivalent to border: '1px solid black'
```

----------------------------------------

TITLE: Customizing Joy UI ModalDialog Layout (JS)
DESCRIPTION: Demonstrates how to extend the Joy UI theme using `extendTheme` to add a custom `layout` value ('top') for the `JoyModalDialog` component. It defines `styleOverrides` for the root element to position the dialog at the top of the viewport when the 'top' layout is applied.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/modal/modal.md#_snippet_1

LANGUAGE: js
CODE:
```
const theme = extendTheme({
  components: {
    JoyModalDialog: {
      defaultProps: { layout: 'top' },
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.layout === 'top' && {
            top: '12vh',
            left: '50%',
            transform: 'translateX(-50%)',
          }),
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Include Inter Font via Google Fonts CDN (HTML)
DESCRIPTION: HTML link tags to include the Inter font from the Google Fonts CDN in your project's head section as an alternative to using Fontsource.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/installation/installation.md#_snippet_4

LANGUAGE: html
CODE:
```
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  rel="stylesheet"
  href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
/>
```

----------------------------------------

TITLE: Install React Type Definitions with Package Manager
DESCRIPTION: Update the @types/react and @types/react-dom packages to match the major version of your installed react package using npm, pnpm, or yarn. Replace <version> with your current React major version.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v7/upgrade-to-v7.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @types/react@<version> @types/react-dom@<version>
```

LANGUAGE: bash
CODE:
```
pnpm add @types/react@<version> @types/react-dom@<version>
```

LANGUAGE: bash
CODE:
```
yarn add @types/react@<version> @types/react-dom@<version>
```

----------------------------------------

TITLE: Avoiding Conditional Logic for Mode-Specific Styles in Joy UI
DESCRIPTION: Illustrates an anti-pattern for applying mode-specific styles in Joy UI. Using conditional operators (`theme.palette.mode === 'dark' ? ... : ...`) within style overrides is discouraged because it creates styles for both modes, impacting performance. This snippet serves as a warning against this approach.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_11

LANGUAGE: js
CODE:
```
// 🚫 Don't do this
extendTheme({
  components: {
    JoyChip: {
      styleOverrides: {
        root: ({ ownerState, theme }) => ({
          // styles will be created for both color schemes which is not performant
          boxShadow: theme.palette.mode === 'dark' ? 'none' : theme.vars.shadow.sm,
        }),
      },
    },
  },
});
```

----------------------------------------

TITLE: MUI v5: Replace Core Import (Alert)
DESCRIPTION: Shows the diff for updating an import from `@material-ui/core/Alert` to the new `@mui/material/Alert` package as part of the v5 package rename migration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_236

LANGUAGE: diff
CODE:
```
diff
-import Alert from '@material-ui/core/Alert';
+import Alert from '@mui/material/Alert';
```

----------------------------------------

TITLE: Add Row and Column Spacing to Grid (JSX)
DESCRIPTION: Demonstrates how to apply separate spacing values for rows and columns using the `rowSpacing` and `columnSpacing` props on the Material UI Grid container component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/2021-q2-update.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Grid container rowSpacing={1} columnSpacing={2} />
```

----------------------------------------

TITLE: Using InitColorSchemeScript in Next.js Document
DESCRIPTION: This code snippet demonstrates how to integrate the InitColorSchemeScript component into a Next.js custom `_document.js` file. Placing this script before the main application render ensures that the correct color scheme (light or dark) is applied early in the page load process, preventing the 'flash' of the default mode in SSR/SSG applications.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/main-features/dark-mode-optimization/dark-mode-optimization.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Document, { Html, Head, Main, NextScript } from 'next/document';
import InitColorSchemeScript from '@mui/joy/InitColorSchemeScript';

export default class MyDocument extends Document {
  render() {
    return (
      <Html data-color-scheme="light">
        <Head>...</Head>
        <body>
          <InitColorSchemeScript />
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
```

----------------------------------------

TITLE: Using MUI Base useSwitch Hook (TSX)
DESCRIPTION: Demonstrates how to use the `useSwitch` hook from MUI Base to build a custom switch component. It shows how to get input props and state (checked, disabled) from the hook and apply them to DOM elements.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/introducing-base-ui.md#_snippet_0

LANGUAGE: tsx
CODE:
```
function MySwitch(props: UseSwitchParameters) {
  const { getInputProps, checked, disabled } = useSwitch(props);

  return (
    <span className={clsx('root', { checked, disabled })}>
      <span className="thumb" />
      <input className="input" {...getInputProps()} />
    </span>
  );
}
```

----------------------------------------

TITLE: Adding Theme Mixin Toolbar Offset for Fixed AppBar - React/MUI
DESCRIPTION: Shows how to use the theme's mixins.toolbar CSS property to create an offset element that pushes content below a fixed AppBar.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/app-bar/app-bar.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const Offset = styled('div')(({ theme }) => theme.mixins.toolbar);

function App() {
  return (
    <React.Fragment>
      <AppBar position="fixed">
        <Toolbar>{/* content */}</Toolbar>
      </AppBar>
      <Offset />
    </React.Fragment>
  );
}
```

----------------------------------------

TITLE: Setting Autocomplete Attribute on Input Slot in MUI Autocomplete (JSX)
DESCRIPTION: Illustrates how to apply the `autoComplete` attribute directly to the underlying input element of the Autocomplete component using the `slotProps` API. This is presented as a potential workaround for browser autofill issues.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/autocomplete/autocomplete.md#_snippet_8

LANGUAGE: jsx
CODE:
```
<Autocomplete
  slotProps={{
    input: {
      autoComplete: 'new-password',
    },
  }}
/>
```

----------------------------------------

TITLE: Creating a Custom Aggregation Function (TypeScript)
DESCRIPTION: This TypeScript code defines a custom aggregation function named `firstAlphabeticalAggregation` for use with the MUI X Data Grid. It implements the `apply` method to find the first value alphabetically within a set of values and specifies the `label` for the UI and `columnTypes` to restrict its use to string columns.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/aggregation-functions.md#_snippet_0

LANGUAGE: ts
CODE:
```
const firstAlphabeticalAggregation: GridAggregationFunction<
  string,
  string | null
> = {
  apply: (params) => {
    if (params.values.length === 0) {
      return null;
    }
    const sortedValue = params.values.sort((a = '', b = '') =>
      a.localeCompare(b),
    );
    return sortedValue[0];
  },
  // The `label` defines what's displayed in the column header when this
  // aggregation is active.
  label: 'First Alphabetical',
  // The `types` property defines which type of columns can use this
  // aggregation function. Here, we only want to propose this aggregation
  // function for `string` columns. If not defined, aggregation will be
  // available for all column types.
  columnTypes: ['string'],
};
```

----------------------------------------

TITLE: Defining Custom Theme Tokens with createTheme (JS)
DESCRIPTION: Demonstrates how to add custom key-value pairs like 'gradient' and 'border.subtle' to the theme's colorSchemes, which are generated as CSS variables. Shows how to use existing variables within the definition.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_6

LANGUAGE: js
CODE:
```
const theme = createTheme({
  cssVariables: true,
  colorSchemes: {
    light: {
      palette: {
        // The best part is that you can refer to the variables wherever you like 🤩
        gradient:
          'linear-gradient(to left, var(--mui-palette-primary-main), var(--mui-palette-primary-dark))',
        border: {
          subtle: 'var(--mui-palette-neutral-200)',
        },
      },
    },
    dark: {
      palette: {
        gradient:
          'linear-gradient(to left, var(--mui-palette-primary-light), var(--mui-palette-primary-main))',
        border: {
          subtle: 'var(--mui-palette-neutral-600)',
        },
      },
    },
  },
});

function App() {
  return <ThemeProvider theme={theme}>...</ThemeProvider>;
}
```

----------------------------------------

TITLE: Extending MUI Joy Theme with Old Shadow Scale (JavaScript)
DESCRIPTION: Demonstrates how to use `extendTheme` to override the default shadow scale in MUI Joy, restoring the previous shadow values for `xs`, `sm`, `md`, `lg`, and `xl`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_20

LANGUAGE: js
CODE:
```
extendTheme({
  shadow: {
    xs: `var(--joy-shadowRing, 0 0 #000),
        0 1px 2px 0 rgba(var(--joy-shadowChannel, ***********) / 0.12)`,
    sm: `var(--joy-shadowRing, 0 0 #000),
        0.3px 0.8px 1.1px rgba(var(--joy-shadowChannel, ***********) / 0.11),
        0.5px 1.3px 1.8px -0.6px rgba(var(--joy-shadowChannel, ***********) / 0.18),
        1.1px 2.7px 3.8px -1.2px rgba(var(--joy-shadowChannel, ***********) / 0.26)`,
    md: `var(--joy-shadowRing, 0 0 #000),
        0.3px 0.8px 1.1px rgba(var(--joy-shadowChannel, ***********) / 0.12),
        1.1px 2.8px 3.9px -0.4px rgba(var(--joy-shadowChannel, ***********) / 0.17),
        2.4px 6.1px 8.6px -0.8px rgba(var(--joy-shadowChannel, ***********) / 0.23),
        5.3px 13.3px 18.8px -1.2px rgba(var(--joy-shadowChannel, ***********) / 0.29)`,
    lg: `var(--joy-shadowRing, 0 0 #000),
        0.3px 0.8px 1.1px rgba(var(--joy-shadowChannel, ***********) / 0.11),
        1.8px 4.5px 6.4px -0.2px rgba(var(--joy-shadowChannel, ***********) / 0.13),
        3.2px 7.9px 11.2px -0.4px rgba(var(--joy-shadowChannel, ***********) / 0.16),
        4.8px 12px 17px -0.5px rgba(var(--joy-shadowChannel, ***********) / 0.19),
        7px 17.5px 24.7px -0.7px rgba(var(--joy-shadowChannel, ***********) / 0.21)`,
    xl: `var(--joy-shadowRing, 0 0 #000),
        0.3px 0.8px 1.1px rgba(var(--joy-shadowChannel, ***********) / 0.11), 
        1.8px 4.5px 6.4px -0.2px rgba(var(--joy-shadowChannel, ***********) / 0.13), 
        3.2px 7.9px 11.2px -0.4px rgba(var(--joy-shadowChannel, ***********) / 0.16), 
        4.8px 12px 17px -0.5px rgba(var(--joy-shadowChannel, ***********) / 0.19), 
        7px 17.5px 24.7px -0.7px rgba(var(--joy-shadowChannel, ***********) / 0.21), 
        10.2px 25.5px 36px -0.9px rgba(var(--joy-shadowChannel, ***********) / 0.24), 
        14.8px 36.8px 52.1px -1.1px rgba(var(--joy-shadowChannel, ***********) / 0.27), 21px 52.3px 74px -1.2px rgba(var(--joy-shadowChannel, ***********) / 0.29)`
  }
})
```

----------------------------------------

TITLE: Destructured Imports with Tree Shaking in Material UI v4 (JavaScript)
DESCRIPTION: Demonstrates how to use destructured imports for Material UI components in v4. This syntax is enabled by native tree shaking support with ES modules, which helps reduce bundle size by only including the components actually used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/material-ui-v4-is-out.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import {
  Table
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
```

----------------------------------------

TITLE: Creating Basic Styled Component with Pigment CSS
DESCRIPTION: Demonstrates how to create a simple styled component (`Heading`) using the `styled` API from `@pigment-css/react`. It applies basic CSS properties like font size, color, weight, and margin to a `div` element.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/experimental-api/pigment-css/pigment-css.md#_snippet_7

LANGUAGE: js
CODE:
```
import { styled } from '@pigment-css/react';

const Heading = styled('div')({
  fontSize: '2rem',
  color: '#9FADBC',
  fontWeight: 'bold',
  margin: '1rem'
});
```

----------------------------------------

TITLE: Defining Conditional React RefObject Type (TS)
DESCRIPTION: Creates a custom `RefObject` type that conditionally resolves to `React.MutableRefObject<T>` for React versions prior to 19 and `React.RefObject<T>` for React 19 and later. This leverages the change in `useRef()` signature in React 19 to provide type compatibility across different React versions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/react-19-update.md#_snippet_4

LANGUAGE: TypeScript
CODE:
```
// in React 19 useRef requires a parameter, so `() => any` will not match anymore
export type RefObject<T> = typeof React.useRef extends () => any
  ? React.MutableRefObject<T>
  : React.RefObject<T>;
```

----------------------------------------

TITLE: Replacing Hidden CSS implementation with sx (xlUp diff)
DESCRIPTION: Demonstrates how to replace the Hidden component with implementation="css" for the xlUp breakpoint by using the sx prop to control display based on the breakpoint.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_50

LANGUAGE: Diff
CODE:
```
-<Hidden implementation="css" xlUp><Paper /></Hidden>
-<Hidden implementation="css" xlUp><button /></Hidden>
+<Paper sx={{ display: { xl: 'none', xs: 'block' } }} />
+<Box component="button" sx={{ display: { xl: 'none', xs: 'block' } }} />
```

----------------------------------------

TITLE: Integrating Material UI Theme with Chakra UI (JS)
DESCRIPTION: Demonstrates how to integrate Material UI with Chakra UI by placing Material UI's ThemeProvider within Chakra UI's provider and using THEME_ID for the Material UI theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/theme-scoping/theme-scoping.md#_snippet_2

LANGUAGE: js
CODE:
```
import { ChakraProvider, extendTheme as chakraExtendTheme } from '@chakra-ui/react';
import {
  ThemeProvider as MaterialThemeProvider,
  createTheme as muiCreateTheme,
  THEME_ID,
} from '@mui/material/styles';

const chakraTheme = chakraExtendTheme();
const materialTheme = muiCreateTheme();

function App() {
  return (
    <ChakraProvider theme={chakraTheme} resetCSS>
      <MaterialThemeProvider theme={{ [THEME_ID]: materialTheme }}>
        Chakra UI components and Material UI components
      </MaterialThemeProvider>
    </ChakraProvider>
  );
}
```

----------------------------------------

TITLE: Using MUI theme.vars in Styled Components - JS
DESCRIPTION: Shows how to access and use CSS theme variables via the `theme.vars` object within a styled component definition in Material UI.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_2

LANGUAGE: JS
CODE:
```
const Button = styled('button')(({ theme }) => ({
  backgroundColor: theme.vars.palette.primary.main, // var(--mui-palette-primary-main)
  color: theme.vars.palette.primary.contrastText, // var(--mui-palette-primary-contrastText)
}));
```

----------------------------------------

TITLE: Replacing withMobileDialog HOC with Hook (JSX/JS)
DESCRIPTION: The `withMobileDialog` higher-order component is removed. Achieve the same responsive full-screen behavior by using the `useTheme` and `useMediaQuery` hooks to determine if the screen size is 'sm' or below.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_35

LANGUAGE: diff
CODE:
```
-import withMobileDialog from '@mui/material/withMobileDialog';
+import { useTheme, useMediaQuery } from '@mui/material';

 function ResponsiveDialog(props) {
-  const { fullScreen } = props;
+  const theme = useTheme();
+  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
   const [open, setOpen] = React.useState(false);

 // ...

-export default withMobileDialog()(ResponsiveDialog);
+export default ResponsiveDialog;

```

----------------------------------------

TITLE: Adding and Disabling Typography Variants (JS)
DESCRIPTION: Shows how to add a new custom typography variant ('poster') and disable an existing default variant ('h3') in the Material UI theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_9

LANGUAGE: js
CODE:
```
const theme = createTheme({
  typography: {
    poster: {
      fontSize: '4rem',
      color: 'red',
    },
    // Disable h3 variant
    h3: undefined,
  },
});
```

----------------------------------------

TITLE: Update Box borderRadius prop value type (JSX)
DESCRIPTION: The behavior of the `borderRadius` system prop on the Box component has changed. A number value is now multiplied by `theme.shape.borderRadius`, while an explicit pixel value should be provided as a string (e.g., '16px').
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_17

LANGUAGE: diff
CODE:
```
-<Box borderRadius="borderRadius">
+<Box borderRadius={1}>
```

LANGUAGE: diff
CODE:
```
-<Box borderRadius={16}>
+<Box borderRadius="16px">
```

----------------------------------------

TITLE: Using MUI X DatePicker Component (JSX)
DESCRIPTION: Shows how to import and use the DatePicker component in MUI X v6. In this version, the DatePicker internally uses the new Fields components, eliminating the need to explicitly declare a separate text field.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/v6-beta-pickers.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

<DatePicker label="My first v6 picker" />;
```

----------------------------------------

TITLE: Incorrectly Nesting FormControl and TextField (JSX)
DESCRIPTION: Shows an example of incorrectly nesting a TextField component inside a FormControl, highlighting the redundancy and potential issues because TextField already includes its own FormControl internally.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/making-customizable-components.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<FormControl>
  <TextField>
</FormControl>

// Equivalent to

<FormControl>
  <FormControl>
    <InputLabel />
    <Input />
    <FormHelperText />
  </FormControl>
</FormControl>
```

----------------------------------------

TITLE: Setting Overflow Property in MUI
DESCRIPTION: Demonstrates how to control the overflow behavior of a container using the overflow property within the sx prop, showing 'hidden' and 'auto' examples.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/display/display.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Box component="div" sx={{ overflow: 'hidden' }}> Not scrollable, overflow is hidden
</Box>
<Box component="div" sx={{ overflow: 'auto' }}> Try scrolling this overflow auto box
</Box>
```

----------------------------------------

TITLE: Demonstrating TypeScript Type Widening Error with sx Prop (TS)
DESCRIPTION: This snippet illustrates the TypeScript type widening issue encountered when assigning a style object variable to the Material UI `sx` prop. The inferred `string` type for `flexDirection` is too broad, leading to a type incompatibility error.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_21

LANGUAGE: ts
CODE:
```
const style = {
  flexDirection: 'column',
};

export default function App() {
  return <Button sx={style}>Example</Button>;
}

// Type '{ flexDirection: string; }' is not assignable to type 'SxProps<Theme> | undefined'
// Type '{ flexDirection: string; }' is not assignable to type 'CSSSelectorObject<Theme>'
//   Property 'flexDirection' is incompatible with index signature
//     Type 'string' is not assignable to type 'SystemStyleObject<Theme>'
```

----------------------------------------

TITLE: Configuring useMediaQuery noSsr Globally via Theme - JavaScript
DESCRIPTION: Demonstrates how to set the `noSsr: true` option globally for all `useMediaQuery` instances by configuring the `MuiUseMediaQuery` component default props within the Material UI theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/use-media-query/use-media-query.md#_snippet_4

LANGUAGE: js
CODE:
```
const theme = createTheme({
  components: {
    MuiUseMediaQuery: {
      defaultProps: {
        noSsr: true,
      },
    },
  },
});
```

----------------------------------------

TITLE: Customizing Default Typography Variants (JS)
DESCRIPTION: Demonstrates how to modify the styles of existing default typography variants within the Material UI theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_8

LANGUAGE: js
CODE:
```
const theme = createTheme({
  typography: {
    subtitle1: {
      fontSize: 12,
    },
    body1: {
      fontWeight: 500,
    },
    button: {
      fontStyle: 'italic',
    },
  },
});
```

----------------------------------------

TITLE: Adding Custom Prop to Interior Slot using slotProps (Joy UI Autocomplete, React)
DESCRIPTION: This snippet shows how to pass custom props to an interior slot of a Joy UI component. It uses the `slotProps` prop to target the `listbox` slot of the Autocomplete component and add a `data-testid` attribute.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/overriding-component-structure/overriding-component-structure.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Autocomplete slotProps={{ listbox: { 'data-testid': 'my-listbox' } }} />
```

----------------------------------------

TITLE: Setting Default Props for Joy UI Component (JoyIconButton) via Theme - JavaScript
DESCRIPTION: Illustrates how defining `defaultProps` for a component like `JoyIconButton` within the theme's `components` node applies those props globally to all instances of that component, making the explicit prop unnecessary in JSX.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/themed-components/themed-components.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
extendTheme({
  components: {
    JoyIconButton: {
      defaultProps: {
        variant: 'outlined',
        color: 'neutral',
      },
    },
  },
});

// This is the same as:
// <IconButton variant="outlined" color="neutral">
<IconButton>...</IconButton>;
```

----------------------------------------

TITLE: Applying Grid Spacing with CSS Variables (v2 Item) - JavaScript
DESCRIPTION: Illustrates how a Grid v2 item component applies padding by referencing the CSS variables (`--Grid-rowSpacing`, `--Grid-columnSpacing`) defined by its container. This decouples spacing logic between parent and child.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/build-layouts-faster-with-grid-v2.md#_snippet_4

LANGUAGE: js
CODE:
```
{
  padding: `calc(var(--Grid-rowSpacing) / 2) calc(var(--Grid-columnSpacing) / 2)`,
}
```

----------------------------------------

TITLE: Configure Emotion Babel Plugin for MUI
DESCRIPTION: Shows the configuration required in `babel.config.js` to inform `@emotion/babel-plugin` about the MUI `styled` utilities (`@mui/system`, `@mui/material`, `@mui/material/styles`) so it can correctly process component selectors.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/styled/styled.md#_snippet_14

LANGUAGE: js
CODE:
```
module.exports = {
  ...
  plugins: [
    [
      "@emotion",
      {
        importMap: {
          "@mui/system": {
            styled: {
              canonicalImport: ["@emotion/styled", "default"],
              styledBaseImport: ["@mui/system", "styled"]
            }
          },
          "@mui/material": {
            styled: {
              canonicalImport: ["@emotion/styled", "default"],
              styledBaseImport: ["@mui/material", "styled"]
            }
          },
          "@mui/material/styles": {
            styled: {
              canonicalImport: ["@emotion/styled", "default"],
              styledBaseImport: ["@mui/material/styles", "styled"]
            }
          }
        }
      }
    ]
  ]
};
```

----------------------------------------

TITLE: Creating Styled Slots for Themed MUI Component
DESCRIPTION: Defines three styled components (`StatRoot`, `StatValue`, `StatUnit`) using MUI's `styled` API. Each component is associated with a specific slot (`root`, `value`, `unit`) and a common component name (`MuiStat`) to enable theme customization via `styleOverrides` and `variants`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_0

LANGUAGE: js
CODE:
```
import * as React from 'react';
import { styled } from '@mui/material/styles';

const StatRoot = styled('div', {
  name: 'MuiStat', // The component name
  slot: 'root', // The slot name
})(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  padding: theme.spacing(3, 4),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
  letterSpacing: '-0.025em',
  fontWeight: 600,
}));

const StatValue = styled('div', {
  name: 'MuiStat',
  slot: 'value',
})(({ theme }) => ({
  ...theme.typography.h3,
}));

const StatUnit = styled('div', {
  name: 'MuiStat',
  slot: 'unit',
})(({ theme }) => ({
  ...theme.typography.body2,
  color: theme.palette.text.secondary,
}));
```

----------------------------------------

TITLE: Importing Box component (Joy UI)
DESCRIPTION: This snippet shows the standard way to import the Box component from the @mui/joy/Box module for use in a React application using Joy UI. It is the first step before using the component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/box/box.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Box from '@mui/joy/Box';
```

----------------------------------------

TITLE: Enabling Cascade Layers in Next.js Pages Router
DESCRIPTION: Shows how to configure both `pages/_document.tsx` and `pages/_app.tsx` to enable CSS cascade layers by creating and passing an Emotion cache with `enableCssLayer: true`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_11

LANGUAGE: diff
CODE:
```
+import { createEmotionCache } from '@mui/material-nextjs/v15-pagesRouter';
 ...

 MyDocument.getInitialProps = async (ctx) => {
   const finalProps = await documentGetInitialProps(ctx, {
+    emotionCache: createEmotionCache({ enableCssLayer: true }),
   });
   return finalProps;
 };
```

LANGUAGE: diff
CODE:
```
+import { createEmotionCache } from '@mui/material-nextjs/v15-pagesRouter';
  ...

const clientCache = createEmotionCache({ enableCssLayer: true });

+ export default function MyApp({ emotionCache = clientCache }) {
    return (
+     <AppCacheProvider emotionCache={emotionCache}>
        <Head>
          ...
        </Head>
        ...
      </AppCacheProvider>
    );
  }
```

----------------------------------------

TITLE: Use theme.vars with sx prop (function syntax)
DESCRIPTION: Shows how to use the theme.vars.* notation within the sx prop when providing a function that receives the theme object. This is useful for applying styles directly to components based on theme variables.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_2

LANGUAGE: jsx
CODE:
```
// Outputs 'var(--joy-shadow-sm)'
<Chip sx={(theme) => ({ boxShadow: theme.vars.shadow.sm })} />
```

----------------------------------------

TITLE: Setting Background Color with sx Prop - Material UI - JSX
DESCRIPTION: Demonstrates how to use the `sx` prop to apply custom styles, such as setting the background color, to the Alert component. This is particularly useful for outlined Alerts within components like Snackbar to prevent background bleed-through.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/alert/alert.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Alert sx={{ bgcolor: 'background.paper' }} />
```

----------------------------------------

TITLE: MUI Grid Row/Column Spacing (JSX)
DESCRIPTION: Demonstrates how to apply row and column spacing to the MUI Grid container using the `rowSpacing` and `columnSpacing` props.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Grid container rowSpacing={1} columnSpacing={2} />
```

----------------------------------------

TITLE: Adding New Color Tokens to the Theme Palette (Joy UI)
DESCRIPTION: Illustrates how to add entirely new color tokens, such as a `gradient`, to a specific color scheme's palette using `extendTheme`, making them accessible via the theme object for use in styling props like `sx`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-colors/theme-colors.md#_snippet_2

LANGUAGE: js
CODE:
```
extendTheme({
  colorSchemes: {
    light: {
      palette: {
        // `gradient` is a new color token
        gradient: {
          primary: 'linear-gradient(to top, var(--joy-palette-primary-main), #000)'
        }
      }
    }
  }
});

// `sx` prop usage example:
<Button sx={{ background: (theme) => theme.vars.palette.gradient.primary }} />;
```

----------------------------------------

TITLE: Adding ARIA Label to Switch Input (JSX)
DESCRIPTION: Illustrates how to provide an accessible label to the Switch's input element using the `aria-label` attribute within the `slotProps` when a standard `<label>` element cannot be used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/switch/switch.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Switch value="checkedA" slotProps={{ 'aria-label': 'Switch A' }} />
```

----------------------------------------

TITLE: Applying Custom Class and Selected State to MenuItem
DESCRIPTION: Shows how to apply a custom class name (`"MenuItem"`) and the `selected` prop to a Material UI `MenuItem` component, corresponding to the CSS example using the `.Mui-selected` class.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/how-to-customize/how-to-customize.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<MenuItem selected className="MenuItem">
```

----------------------------------------

TITLE: Fix CSS Injection Order with Emotion Cache
DESCRIPTION: When using Emotion with a custom cache, create the cache with the `prepend: true` option and wrap the application with `CacheProvider` to ensure Emotion's styles are injected correctly for proper style prioritization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_26

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';

const cache = createCache({
  key: 'css',
  prepend: true,
});

export default function PlainCssPriority() {
  return (
    <CacheProvider value={cache}>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </CacheProvider>
  );
}
```

----------------------------------------

TITLE: Overriding Theme Typography Variables (JS)
DESCRIPTION: Shows how to override global theme configuration variables, specifically modifying the `fontSize` for the `button` typography variant across all instances.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theme-components/theme-components.md#_snippet_2

LANGUAGE: js
CODE:
```
const theme = createTheme({
  typography: {
    button: {
      fontSize: '1rem',
    },
  },
});
```

----------------------------------------

TITLE: Using Joy UI Link as a Button (JSX)
DESCRIPTION: Demonstrates how to render the Joy UI Link component as a native HTML button element using the `component="button"` prop. This is useful for interactive elements that perform an action via an `onClick` handler rather than navigating to a URL.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/link/link.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Link
  component="button"
  onClick={() => {
    // ...process something
  }}
>
  Do something
</Link>
```

----------------------------------------

TITLE: Install Emotion Peer Dependencies for Material UI v5
DESCRIPTION: Commands to install the required Emotion packages (`@emotion/react`, `@emotion/styled`) which are peer dependencies for Material UI v5's default styling engine, using npm or yarn.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migration-v4.md#_snippet_5

LANGUAGE: bash npm
CODE:
```
npm install @emotion/react @emotion/styled
```

LANGUAGE: bash yarn
CODE:
```
yarn add @emotion/react @emotion/styled
```

----------------------------------------

TITLE: Changing Switch ARIA Role to 'switch' (JSX)
DESCRIPTION: Shows how to use the `slotProps` prop to explicitly set the ARIA role of the underlying input element to 'switch' for accessibility, although 'checkbox' is the default due to wider support.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/switch/switch.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Switch slotProps={{ input: { role: 'switch' } }}>
```

----------------------------------------

TITLE: Replace Box Render Prop with component Prop for Native Elements
DESCRIPTION: Demonstrates how to replace the use of a Box component with a render prop wrapping a native HTML element (like `<button>`) by using the `component` prop on the Box itself and applying `sx` directly.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/v5-component-changes.md#_snippet_22

LANGUAGE: diff
CODE:
```
diff
-<Box sx={{ border: '1px dashed grey' }}>
-  {(props) => <button {...props}>Save</button>}
-</Box>
+<Box component="button" sx={{ border: '1px dashed grey' }}>Save</Box>
```

----------------------------------------

TITLE: Comparing Flat Prop vs. sx Prop for Styling in MUI
DESCRIPTION: Illustrates the equivalence between using a component's dedicated flat prop (like `color` on Typography) and achieving the same style using the generic `sx` prop. This shows how `sx` can replicate the functionality of specific utility props.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-core-v5.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Typography color="grey.600">
```

LANGUAGE: jsx
CODE:
```
<Typography sx={{ color: 'grey.600' }}>
```

----------------------------------------

TITLE: Applying MUI Dark Mode Styles with applyStyles - JS/JSX
DESCRIPTION: Demonstrates how to use the `theme.applyStyles()` function within the `sx` prop to define specific styles for dark mode for a Material UI component like Card.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/usage.md#_snippet_1

LANGUAGE: JS
CODE:
```
import Card from '@mui/material/Card';

<Card
  sx={[
    (theme) => ({
      backgroundColor: theme.vars.palette.background.default,
    }),
    (theme) =>
      theme.applyStyles('dark', {
        backgroundColor: theme.vars.palette.grey[900],
      }),
  ]}
/>;
```

----------------------------------------

TITLE: Changing Default Font Family (Joy UI JS)
DESCRIPTION: Illustrates how to override the default font families (`display` and `body`) in the Joy UI theme using the `fontFamily` property when extending the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-typography/theme-typography.md#_snippet_7

LANGUAGE: js
CODE:
```
extendTheme({
  fontFamily: {
    display: 'Noto Sans', // applies to `h1`–`h4`
    body: 'Noto Sans', // applies to `title-*` and `body-*`
  },
});
```

----------------------------------------

TITLE: Setting HTML Font Size in MUI Theme (JS)
DESCRIPTION: Configures the Material UI theme to recognize a different base font size on the HTML element, which is used to adjust rem values for correct scaling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_6

LANGUAGE: js
CODE:
```
const theme = createTheme({
  typography: {
    // Tell Material UI what the font-size on the html element is.
    htmlFontSize: 10,
  },
});
```

----------------------------------------

TITLE: Solution for white-space: nowrap Limitation in Stack
DESCRIPTION: Provides the recommended solution to the `white-space: nowrap` limitation by setting `min-width: 0` on the Stack component using the `sx` prop when `direction="row"` is used.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/components/stack/stack.md#_snippet_4

LANGUAGE: jsx
CODE:
```
<Stack direction="row" sx={{ minWidth: 0 }}>
  <span style={{ whiteSpace: 'nowrap' }}>
```

----------------------------------------

TITLE: Basic Usage - MUI Joy UI LinearProgress - JSX
DESCRIPTION: Demonstrates the basic usage of the MUI Joy UI LinearProgress component. It shows how to import the component and render it within a simple React functional component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/linear-progress/linear-progress.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import LinearProgress from '@mui/joy/LinearProgress';

export default function MyApp() {
  return <LinearProgress />;
}
```

----------------------------------------

TITLE: Setting up ThemeProvider in Material UI v5
DESCRIPTION: Demonstrates the recommended structure for wrapping your application with Material UI's ThemeProvider at the root, ensuring theme access for components and styles, particularly when using `@mui/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/migration-v4.md#_snippet_1

LANGUAGE: js
CODE:
```
import { ThemeProvider, createMuiTheme, makeStyles } from '@material-ui/core/styles';

const theme = createMuiTheme();

const useStyles = makeStyles((theme) => ({
  root: {
    // some CSS that accesses the theme
  }
}));

function App() {
  const classes = useStyles(); // ❌ If you have this, consider moving it
  // inside of a component wrapped with <ThemeProvider />
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
}
```

----------------------------------------

TITLE: Customizing Autocomplete Listbox Variant with slotProps (JavaScript)
DESCRIPTION: Illustrates how to use the `slotProps` API on the `Autocomplete` component to apply a different variant ('plain') specifically to the `listbox` slot, overriding the main component's variant.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_22

LANGUAGE: js
CODE:
```
<Autocomplete
  variant="plain"
  slotProps={{
    listbox: {
      variant: 'plain'
    }
  }}
>
```

----------------------------------------

TITLE: Customize DataGrid Toolbar Styling (v5 - Styled)
DESCRIPTION: Shows the simplified approach to customizing DataGrid toolbar styles in MUI X v5 using `styled` components. Due to reduced CSS specificity, a direct style object can be applied without needing complex selectors.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v5.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const GridToolbarContainerStyled = styled(GridToolbarContainer)({
  padding: 40,
});

function MyCustomToolbar() {
  return (
    <GridToolbarContainerStyled>
      My custom toolbar
    </GridToolbarContainerStyled>
  );
};

export default function App() {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid components={{ Toolbar: MyCustomToolbar }} />
    </div>
  );
}
```

----------------------------------------

TITLE: Globally Setting Icon baseClassName in Theme (JavaScript)
DESCRIPTION: Shows how to use `createTheme` to configure the `MuiIcon` component's default props, specifically setting `baseClassName` to `material-icons-two-tone`. This allows using two-tone icons without specifying the prop on each `Icon` instance. Requires `@mui/material/styles`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/icons/icons.md#_snippet_4

LANGUAGE: javascript
CODE:
```
const theme = createTheme({
  components: {
    MuiIcon: {
      defaultProps: {
        // Replace the `material-icons` default value.
        baseClassName: 'material-icons-two-tone',
      },
    },
  },
});
```

----------------------------------------

TITLE: Enforcing Import Restrictions with ESLint - JSON
DESCRIPTION: Provides an ESLint configuration snippet using the 'no-restricted-imports' rule to prevent accidental barrel imports from '@mui' packages, helping maintain preferred import patterns.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/minimizing-bundle-size/minimizing-bundle-size.md#_snippet_3

LANGUAGE: JSON
CODE:
```
// .eslintrc
{
  "rules": {
    "no-restricted-imports": [
      "error",
      {
        "patterns": [{ "regex": "^@mui/[^/]+$" }]
      }
    ]
  }
}
```

----------------------------------------

TITLE: Accessing MUI System Color Mode - JavaScript
DESCRIPTION: Shows how to use the `useColorScheme` hook to access both the current `mode` and the underlying `systemMode` when the current mode is set to 'system'.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_6

LANGUAGE: js
CODE:
```
const { mode, systemMode } = useColorScheme();

console.log(mode); // 'system'
console.log(systemMode); // 'light' | 'dark'
```

----------------------------------------

TITLE: Using Inherited CSS Variables in Child Styles (CSS)
DESCRIPTION: Shows how child elements consume the CSS variables inherited from a parent component with color inversion enabled. These variables provide the inverted color values for properties like `color` and `background`, allowing children to automatically adapt to the parent's inverted color scheme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/main-features/color-inversion/color-inversion.md#_snippet_4

LANGUAGE: css
CODE:
```
// The children style sheet
// The values of these variables are inherited from the parent.
{
  color: var(--joy-palette-text-primary);
  background: var(--joy-palette-background-surface);
  …
}
```

----------------------------------------

TITLE: Use theme.vars with styled function
DESCRIPTION: Demonstrates how to access CSS variables defined in the theme using the theme.vars.* notation within a styled component definition. This allows dynamic styling based on the current theme's CSS variables.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/using-css-variables/using-css-variables.md#_snippet_1

LANGUAGE: js
CODE:
```
const Div = styled('div')(({ theme }) => ({
  // Outputs 'var(--joy-palette-primary-500)'
  color: theme.vars.palette.primary[500],
}));
```

----------------------------------------

TITLE: Merging Slot Props with mergeSlotProps (TSX)
DESCRIPTION: Shows how to use the `mergeSlotProps` utility function to combine default slot props with custom props. This example customizes the `popper` slot of a `Tooltip`, demonstrating how `className` values are concatenated.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/composition/composition.md#_snippet_1

LANGUAGE: tsx
CODE:
```
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import { mergeSlotProps } from '@mui/material/utils';

export const CustomTooltip = (props: TooltipProps) => {
  const { children, title, sx: sxProps } = props;

  return (
    <Tooltip
      {...props}
      title={<Box sx={{ p: 4 }}>{title}</Box>}
      slotProps={{
        ...props.slotProps,
        popper: mergeSlotProps(props.slotProps?.popper, {
          className: 'custom-tooltip-popper',
          disablePortal: true,
          placement: 'top',
        }),
      }}
    >
      {children}
    </Tooltip>
  );
};
```

----------------------------------------

TITLE: Disabling Autocomplete Built-in Filtering
DESCRIPTION: Provides an example of how to override the `filterOptions` prop on the `Autocomplete` component to disable its default client-side filtering logic, typically used when implementing server-side search.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/autocomplete/autocomplete.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Autocomplete filterOptions={(x) => x} />
```

----------------------------------------

TITLE: Correcting Fade/Grow/Slide/Zoom Usage with DOM Nodes (Correct)
DESCRIPTION: To correctly use Material UI transition components, ensure the child is a DOM element (like a `div`) or a component that properly forwards its ref to an underlying DOM node.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/troubleshooting.md#_snippet_5

LANGUAGE: jsx
CODE:
```
// Ex. 1-2 ✅ Add a DOM node such as this div:
<Fade in>
  <div>
    <CustomComponent />
  </div>
</Fade>

```

----------------------------------------

TITLE: Adding Login Button and Sign Up Link (Joy UI) - JSX
DESCRIPTION: Adds a Joy UI Button for logging in and a Typography component with an appended Link in the `endDecorator` prop for a 'Sign up' action, placed after the input fields within the Sheet.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_7

LANGUAGE: jsx
CODE:
```
<Button sx={{ mt: 1 /* margin top */ }}>
  Log in
</Button>
<Typography
  endDecorator={<Link href="/sign-up">Sign up</Link>}
  fontSize="sm"
  sx={{ alignSelf: 'center' }}
>
  Don't have an account?
</Typography>
```

----------------------------------------

TITLE: Setting Default Mode - MUI InitColorSchemeScript - JavaScript
DESCRIPTION: Demonstrates how to set the default mode for the InitColorSchemeScript component, which is necessary for preventing SSR flicker and should match the `defaultMode` set on the ThemeProvider.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_10

LANGUAGE: JavaScript
CODE:
```
<InitColorSchemeScript defaultMode="dark">
```

----------------------------------------

TITLE: Setting Timezone in MUI X TimePicker (TSX)
DESCRIPTION: This snippet demonstrates how to set a specific timezone for the MUI X TimePicker component using the `timezone` prop. This allows the picker to display and handle time values according to the specified time zone, simplifying internationalization and backend consistency.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-mid-v6-features.md#_snippet_0

LANGUAGE: tsx
CODE:
```
<TimePicker
  value={value}
  onChange={setValue}
  timezone="Pacific/Honolulu" // Can be in any timezone of your choice
  label={'Rendered in "Pacific/Honolulu"'}
/>
```

----------------------------------------

TITLE: Applying Typography Variants in MUI JSX
DESCRIPTION: Demonstrates how to apply predefined typography variants from the MUI theme using the `sx` prop on a `Box` component. This allows consistent styling based on the theme's typography scale.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/typography/typography.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ typography: 'subtitle2' }}>… // theme.typography.subtitle2
<Box sx={{ typography: 'body1' }}>…
<Box sx={{ typography: 'body2' }}>…
```

----------------------------------------

TITLE: Integrating Next.js Font Optimization with Material UI Theme (TS)
DESCRIPTION: Shows how to integrate Next.js font optimization (specifically using `next/font/google`) with Material UI's theming system. It defines a Roboto font variable and uses it as the `fontFamily` in a Material UI theme, then applies the theme and the font variable class in the `_app.tsx` component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_14

LANGUAGE: TypeScript
CODE:
```
import * as React from 'react';
import Head from 'next/head';
import { AppProps } from 'next/app';
import { AppCacheProvider } from '@mui/material-nextjs/v15-pagesRouter';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

const theme = createTheme({
  typography: {
    fontFamily: 'var(--font-roboto)',
  },
});

export default function MyApp(props: AppProps) {
 const { Component, pageProps } = props;
 return (
   <AppCacheProvider {...props}>
     <Head>...</Head>
     <ThemeProvider theme={theme}>
       <main className={roboto.variable}>
         <Component {...pageProps} />
       </main>
     </ThemeProvider>
   </AppCacheProvider>
 );
}
```

----------------------------------------

TITLE: TypeScript Module Augmentation for Custom Table BorderAxis (TypeScript)
DESCRIPTION: Shows how to use TypeScript module augmentation to add new valid string values (like 'header') to the `TablePropsBorderAxisOverrides` interface, allowing custom `borderAxis` values in the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/table/table.md#_snippet_5

LANGUAGE: ts
CODE:
```
// this could be any file that's included in your tsconfig.json
declare module '@mui/joy/Table' {
  interface TablePropsBorderAxisOverrides {
    header: true;
  }
}
```

----------------------------------------

TITLE: Add InitColorSchemeScript to Next.js Pages Router _document.js (JSX)
DESCRIPTION: Provides the code for a custom `_document.js` file in a Next.js Pages Router application. It imports `InitColorSchemeScript` from `@mui/joy` and includes it within the `<body>` tag before `<Main />` to prevent screen flickering on the initial load by initializing the color scheme script early.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/dark-mode/dark-mode.md#_snippet_3

LANGUAGE: jsx
CODE:
```
import Document, { Html, Head, Main, NextScript } from 'next/document';
import InitColorSchemeScript from '@mui/joy/InitColorSchemeScript';

export default class MyDocument extends Document {
  render() {
    return (
      <Html data-color-scheme="light">
        <Head>...</Head>
        <body>
          <InitColorSchemeScript />
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
```

----------------------------------------

TITLE: Basic Material UI Slider Component (JSX)
DESCRIPTION: A simple React component using the Material UI Slider, demonstrating basic usage with default value and applying a custom CSS class.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/experiments/docs/codeblock.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import Slider from '@mui/material/Slider';
import './PlainCssSliderDeep1.css';

export default function PlainCssSliderDeep1() {
  return (
    <div>
      <Slider defaultValue={30} />
      <Slider defaultValue={30} className="slider" />
    </div>
  );
}
```

----------------------------------------

TITLE: Importing Stabilized CssVarsProvider and extendTheme - MUI v6
DESCRIPTION: Shows how to update imports for `extendTheme` and `CssVarsProvider` by removing the `experimental_` prefix, as these APIs are now stable in v6.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_23

LANGUAGE: javascript
CODE:
```
import { extendTheme, CssVarsProvider } from '@mui/material/styles';
```

----------------------------------------

TITLE: Importing Button and Link (Joy UI) - JSX
DESCRIPTION: Imports the Button and Link components from '@mui/joy', which replace standard HTML button and anchor tags with enhanced Joy UI versions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_6

LANGUAGE: jsx
CODE:
```
import Button from '@mui/joy/Button';
import Link from '@mui/joy/Link';
```

----------------------------------------

TITLE: Using sx Prop in MuiChip styleOverrides (TSX)
DESCRIPTION: Demonstrates how to use the experimental `theme.unstable_sx` helper within the `styleOverrides` key of a component (MuiChip) to apply styles using the sx prop's shorthand syntax directly in the theme.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/theme-components/theme-components.md#_snippet_1

LANGUAGE: tsx
CODE:
```
const finalTheme = createTheme({
  components: {
    MuiChip: {
      styleOverrides: {
        root: ({ theme }) =>
          theme.unstable_sx({
            px: 1,
            py: 0.25,
            borderRadius: 1,
          }),
        label: {
          padding: 'initial',
        },
        icon: ({ theme }) =>
          theme.unstable_sx({
            mr: 0.5,
            ml: '-2px',
          }),
      },
    },
  },
});
```

----------------------------------------

TITLE: Generated Theme Styles for Fully Customized MuiSwitch (Multiple States)
DESCRIPTION: This code snippet demonstrates the theme object generated by the Sync plugin for a fully customized Material UI Switch component with multiple variants defined in Figma. It includes style overrides for different states such as checked, unchecked, disabled, and focusVisible, applying specific styles based on the component's props and state.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/design-resources/material-ui-sync/material-ui-sync.md#_snippet_1

LANGUAGE: js
CODE:
```
{
  components: {
    MuiSwitch: {
      styleOverrides: {
        root: {
          '&.MuiSwitch-sizeMedium:has(.MuiSwitch-colorPrimary)': {
            width: '40px',
            height: '21px',
            padding: '0',
            '& .MuiSwitch-switchBase': {
              padding: '0',
              '& .MuiSwitch-thumb': {
                width: '17px',
                height: '17px',
                background: '#FAFAFA',
              },
              '& + .MuiSwitch-track': {
                width: '38px',
                height: '21px',
                borderRadius: '100px',
                opacity: '1'
              }
            },
            '&:not(:has(.Mui-checked)):not(:has(.Mui-disabled)):not(:has(.Mui-focusVisible))': {
              '& .MuiSwitch-switchBase': {
                transform: 'translateX(3px) translateY(2px)',
                '& + .MuiSwitch-track': {
                  background: '#BDBDBD'
                }
              }
            },
            '&:not(:has(.Mui-checked)):has(.Mui-disabled):not(:has(.Mui-focusVisible))': {
              '& .MuiSwitch-switchBase': {
                transform: 'translateX(3px) translateY(2px)',
                '& + .MuiSwitch-track': {
                  background: 'rgba(229, 229, 229, 0.99)'
                }
              }
            },
            '&:not(:has(.Mui-checked)):not(:has(.Mui-disabled)):has(.Mui-focusVisible)': {
              '& .MuiSwitch-switchBase': {
                transform: 'translateX(3px) translateY(2px)',
                '& + .MuiSwitch-track': {
                  border: '1px solid #000',
                  background: '#BDBDBD'
                }
              }
            }
          }
        }
      }
    }
  }
}
```

----------------------------------------

TITLE: Incorrect useStyles Usage Outside ThemeProvider (JavaScript)
DESCRIPTION: Illustrates the `TypeError: Cannot read property 'drawer' of undefined` when `useStyles` (or `withStyles`) is called outside the `<ThemeProvider>` component tree. The theme object is not available in the context where `useStyles` is invoked.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/migration-v4/troubleshooting.md#_snippet_12

LANGUAGE: js
CODE:
```
import * as React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import Card from '@mui/material/Card';
import CssBaseline from '@mui/material/CssBaseline';

const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.common.white,
  },
}));

const theme = createTheme();

function App() {
  const classes = useStyles(); // ❌ called outside of ThemeProvider
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Card className={classes.root}>...</Card>
    </ThemeProvider>
  );
}

export default App;
```

----------------------------------------

TITLE: Enabling CSS Theme Variables in Material UI Theme (TS)
DESCRIPTION: Demonstrates how to enable the CSS theme variables feature in Material UI by setting the `cssVariables` flag to `true` when creating a theme using `createTheme`. This allows theme values to be exposed as CSS custom properties.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/nextjs/nextjs.md#_snippet_15

LANGUAGE: TypeScript
CODE:
```
'use client';
const theme = createTheme({
  cssVariables: true,
});
```

----------------------------------------

TITLE: Updating Theme Configuration for v6 Compatibility JavaScript
DESCRIPTION: Shows how the `theme-v6` codemod updates theme configuration, replacing palette mode conditionals with `theme.applyStyles()`, replacing `ownerState` with `variants`, and moving theme variants to the root slot for `@pigment-css/react` compatibility.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_151

LANGUAGE: diff
CODE:
```
 createTheme({
   components: {
     MuiButton: {
-       variants: [
-         {
-           props: { color: 'primary' },
-           style: {
-             color: 'red',
-           },
-         },
-       ],
       styleOverrides: {
-          root: ({ theme, ownerState }) => ({
+          root: ({ theme }) => ({
           ...ownerState.variant === 'contained' && {
             backgroundColor: alpha(theme.palette.primary.main, 0.8),
             ...theme.palette.mode === 'dark' && {
               backgroundColor: alpha(theme.palette.primary.light, 0.9),
             }
           },
+           variants: [
+             {
+               prop: { variant: 'contained' },
+               style: {
+                 backgroundColor: alpha(theme.palette.primary.main, 0.8),
+               },
+             },
+             {
+               prop: { variant: 'contained' },
+               style: {
+                 ...theme.applyStyles('dark', {
+                   backgroundColor: alpha(theme.palette.primary.light, 0.9),
+                 })
+               },
+             },
+             {
+               prop: { color: 'primary' },
+               style: {
+                 color: 'red',
+               },
+             },
+           ],
         })
       }
     }
   }
 })
```

----------------------------------------

TITLE: Preventing Default Click Away - Material UI React Snackbar
DESCRIPTION: Demonstrates how to prevent the Snackbar from closing when a click occurs outside of it. This is achieved by setting the `defaultMuiPrevented` property to `true` within the `onClickAway` handler provided via the `slotProps`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/snackbars/snackbars.md#_snippet_2

LANGUAGE: jsx
CODE:
```
<Snackbar
  slotProps={{
    clickAwayListener: {
      onClickAway: (event) => {
        // Prevent's default 'onClickAway' behavior.
        event.defaultMuiPrevented = true;
      },
    },
  }}
/>
```

----------------------------------------

TITLE: Defining Dynamic Variants with MUI Theme (TSX)
DESCRIPTION: This snippet demonstrates how to use the new dynamic variant API in Material UI to define custom component variants directly within the theme configuration. It shows how to apply specific styles based on a combination of props (variant='dashed', color='secondary') and how to optionally add type safety for the new variant using module augmentation.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/2020-q3-update.md#_snippet_0

LANGUAGE: tsx
CODE:
```
// Define the style that should be applied for specific props.
const theme = createMuiTheme({
  components: {
    MuiButton: {
      variants: [
        {
          props: { variant: 'dashed', color: 'secondary' },
          styles: {
            border: `4px dashed ${red[500]}`,
          },
        },
      ],
    },
  },
});

// Optionally retain type safety:
declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    dashed: true;
  }
}

// Enjoy!
<Button variant="dashed" color="secondary" />;
```

----------------------------------------

TITLE: Configure Joy UI IconButton Theme Defaults (JS)
DESCRIPTION: Configures the Joy UI theme to revert the default variant of the IconButton component to 'soft' and the default color to 'primary', overriding the new defaults.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/migration/migrating-default-theme.md#_snippet_25

LANGUAGE: js
CODE:
```
extendTheme({
  components: {
    JoyIconButton: {
      defaultProps: {
        variant: 'soft',
        color: 'primary',
      },
    },
  },
});
```

----------------------------------------

TITLE: Forcing MUI Color Scheme with CSS Class (JS/JSX)
DESCRIPTION: Demonstrates how to force a specific color scheme (dark mode in this example) for a section of the application by applying a CSS class (`.mode-dark`) to a container element. This assumes the color scheme selector is configured to use classes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/css-theme-variables/configuration.md#_snippet_13

LANGUAGE: js
CODE:
```
// if the selector is '.mode-%s'
<div className=".mode-dark">
  <Paper sx={{ p: 2 }}>
    <TextField label="Email" type="email" margin="normal" />
    <TextField label="Password" type="password" margin="normal" />
    <Button>Sign in</Button>
  </Paper>
  {/* other components */}
</div>
```

----------------------------------------

TITLE: Updating MUI Slider Props in Theme
DESCRIPTION: Illustrates the migration from the deprecated `components` and `componentsProps` in the theme's `defaultProps` to the new `slots` and `slotProps` for customizing subcomponents of the MUI Slider.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/packages/mui-codemod/README.md#_snippet_101

LANGUAGE: diff
CODE:
```
MuiSlider: {
  defaultProps: {
-    components: { Track: CustomTrack }
-    componentsProps: { track: { testid: 'test-id' }}
+    slots: { track: CustomTrack },
+    slotProps: { track: { testid: 'test-id' } },
 },
},
```

----------------------------------------

TITLE: Applying Typography Styles via sx Prop (Joy UI)
DESCRIPTION: Illustrates how to use the `sx` prop on a component like Box to apply a specific theme typography level, such as `body-sm`.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/customization/theme-typography/theme-typography.md#_snippet_2

LANGUAGE: jsx
CODE:
```
// to apply the `theme.typography['body-sm']` styles:
<Box sx={{ typography: 'body-sm' }}>Small text</Box>
```

----------------------------------------

TITLE: Setting CSP Header with Nonce (JavaScript)
DESCRIPTION: Sets the 'Content-Security-Policy' HTTP header. It allows resources from the same origin ('self') and specifically allows styles with the generated nonce, enabling Emotion's injected styles while maintaining a strict policy.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/content-security-policy/content-security-policy.md#_snippet_2

LANGUAGE: js
CODE:
```
header('Content-Security-Policy').set(
  `default-src 'self'; style-src 'self' 'nonce-${nonce}';`,
);
```

----------------------------------------

TITLE: Basic Breadcrumbs - Material UI - JavaScript
DESCRIPTION: Demonstrates a basic implementation of the Material UI Breadcrumbs component using default settings.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/breadcrumbs/breadcrumbs.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
BasicBreadcrumbs.js
```

----------------------------------------

TITLE: Customize DataGrid Toolbar Styling (v5 - sx prop)
DESCRIPTION: Illustrates how to customize the DataGrid toolbar component's styles in MUI X v5 using the `sx` prop, leveraging the MUI System styling capabilities for a concise approach.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/mui-x-v5.md#_snippet_2

LANGUAGE: jsx
CODE:
```
function MyCustomToolbar() {
  // means "padding: theme.spacing(5)", NOT "5px"
  return (
    <GridToolbarContainer sx={{ p: 5 }}>My custom toolbar</GridToolbarContainer>
  );
}

export default function App() {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid components={{ Toolbar: MyCustomToolbar }} />
    </div>
  );
}
```

----------------------------------------

TITLE: Configuring Pigment CSS for RTL Support (Diff)
DESCRIPTION: Demonstrates how to enable right-to-left (RTL) support in the Pigment CSS configuration file by adding the `css` property with `defaultDirection` and `generateForBothDir` options.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/migrating-to-pigment-css.md#_snippet_21

LANGUAGE: diff
CODE:
```
 const pigmentConfig = {
   theme: createTheme(),
+  css: {
+    // Specify your default CSS authoring direction
+    defaultDirection: 'ltr',
+    // Generate CSS for the opposite of the `defaultDirection`
+    // This is set to `false` by default
+    generateForBothDir: true,
+  },
 }
```

----------------------------------------

TITLE: Define Responsive Font Size for Variant (JS)
DESCRIPTION: This snippet demonstrates how to make a specific typography variant (`h3`) responsive by defining different `fontSize` values for various breakpoints using CSS media queries and the theme's breakpoint helper within the `typography` configuration.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/typography/typography.md#_snippet_4

LANGUAGE: js
CODE:
```
const theme = createTheme();

theme.typography.h3 = {
  fontSize: '1.2rem',
  '@media (min-width:600px)': {
    fontSize: '1.5rem',
  },
  [theme.breakpoints.up('md')]: {
    fontSize: '2.4rem',
  },
};
```

----------------------------------------

TITLE: Integrate next/font Variable in Next.js Layout
DESCRIPTION: Demonstrates how to use the `variable` property of `next/font` and apply the resulting CSS variable to the `body` className in `app/layout.tsx` for font optimization.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/migrating-to-pigment-css.md#_snippet_2

LANGUAGE: TSX
CODE:
```
 import { Roboto } from 'next/font/google';

 const roboto = Roboto({
   weight: ['300', '400', '500', '700'],
   subsets: ['latin'],
   display: 'swap',
+  variable: '--my-font-family',
 });

export default function RootLayout(props) {
   const { children } = props;
   return (
     <html lang="en">
+      <body className={roboto.variable}>
          {children}
       </body>
     </html>
   );
 }
```

----------------------------------------

TITLE: Adding Borders with MUI sx Prop (JSX)
DESCRIPTION: Demonstrates how to add borders to a MUI Box component using the `sx` prop. Examples show adding all borders or specific sides (top, right, bottom, left) by setting the border width to 1.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/borders/borders.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ border: 1 }}>…
<Box sx={{ borderTop: 1 }}>…
<Box sx={{ borderRight: 1 }}>…
<Box sx={{ borderBottom: 1 }}>…
<Box sx={{ borderLeft: 1 }}>…
```

----------------------------------------

TITLE: Applying Background Color with sx prop in MUI
DESCRIPTION: Demonstrates setting the `backgroundColor` using the `sx` prop via its alias `bgcolor`, referencing a color path within the MUI theme's palette.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/the-sx-prop/the-sx-prop.md#_snippet_6

LANGUAGE: jsx
CODE:
```
<Box sx={{ bgcolor: 'primary.main' }} />
// equivalent to backgroundColor: theme => theme.palette.primary.main
```

----------------------------------------

TITLE: Applying Text Color with MUI sx Prop (JSX)
DESCRIPTION: Demonstrates how to apply text color to a Box component using the `sx` prop and theme palette values like `primary.main`, `text.primary`, etc. This utilizes the `color` system prop.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/palette/palette.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Box sx={{ color: 'primary.main' }}>…
<Box sx={{ color: 'secondary.main' }}>…
<Box sx={{ color: 'error.main' }}>…
<Box sx={{ color: 'warning.main' }}>…
<Box sx={{ color: 'info.main' }}>…
<Box sx={{ color: 'success.main' }}>…
<Box sx={{ color: 'text.primary' }}>…
<Box sx={{ color: 'text.secondary' }}>…
<Box sx={{ color: 'text.disabled' }}>…
```

----------------------------------------

TITLE: Inferring Dimensions in Typography (JSX)
DESCRIPTION: Illustrates how the Skeleton component can infer its dimensions automatically when used within a Typography component, matching the text height.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/skeleton/skeleton.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Typography variant="h1">{loading ? <Skeleton /> : 'h1'}</Typography>
```

----------------------------------------

TITLE: Update LoadingButton Import from Lab to Material
DESCRIPTION: Migrate the import path for the LoadingButton component from the Material UI Lab package to the standard Material UI package, as loading functionality is now integrated into the Button component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/migration/upgrade-to-v6/upgrade-to-v6.md#_snippet_16

LANGUAGE: diff
CODE:
```
-import { LoadingButton } from '@mui/lab';
+import { Button } from '@mui/material';
```

----------------------------------------

TITLE: Customizing Option Label with getOptionLabel
DESCRIPTION: Illustrates how to use the `getOptionLabel` prop to specify which property of an option object should be used as the displayed label when the options array contains objects with different property names.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/autocomplete/autocomplete.md#_snippet_2

LANGUAGE: js
CODE:
```
const options = [
  { title: 'Pulp Fiction', id: 2 },
  // ...
];

<Autocomplete getOptionLabel={option => option.title}>
```

----------------------------------------

TITLE: Enabling Default MUI Light/Dark Color Schemes (JavaScript)
DESCRIPTION: Illustrates how to enable the default light and dark color schemes in a Material UI theme by setting `colorSchemes: { light: true, dark: true }` within the `createTheme` configuration. This provides built-in support for switching between standard light and dark palettes.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_11

LANGUAGE: JavaScript
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  colorSchemes: {
    light: true,
    dark: true,
  },
});
```

----------------------------------------

TITLE: Migrating from System Props to sx Prop in MUI Stack
DESCRIPTION: Demonstrates the deprecated usage of system props like `mt` directly on the Stack component and the recommended alternative using the `sx` prop for applying styling.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/components/stack/stack.md#_snippet_1

LANGUAGE: jsx
CODE:
```
<Stack mt={2} />
```

LANGUAGE: jsx
CODE:
```
<Stack sx={{ mt: 2 }} />
```

----------------------------------------

TITLE: Setting up Express Server Middleware (JavaScript)
DESCRIPTION: Outlines the basic structure for an Express server handling server-side rendering requests. It imports Express, defines placeholder functions for rendering, creates an Express app instance, and sets up middleware to process incoming requests on port 3000.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/guides/server-rendering/server-rendering.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import express from 'express';

// We are going to fill these out in the sections to follow.
function renderFullPage(html, css) {
  /* ... */
}

function handleRender(req, res) {
  /* ... */
}

const app = express();

// This is fired every time the server-side receives a request.
app.use(handleRender);

const port = 3000;
app.listen(port);
```

----------------------------------------

TITLE: Augmenting MUI Component Color Props for Custom Colors (TS)
DESCRIPTION: Shows how to extend a specific component's color prop interface (e.g., `ButtonPropsColorOverrides`) in TypeScript to include a custom color key (e.g., 'custom'). This enables using the custom color string value for the component's `color` prop without TypeScript errors.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/palette/palette.md#_snippet_7

LANGUAGE: TS
CODE:
```
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    custom: true;
  }
}
```

----------------------------------------

TITLE: Install Roboto font via Fontsource
DESCRIPTION: Installs the Roboto font package using Fontsource via npm, pnpm, or yarn. This allows importing specific font weights directly into your application code.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/getting-started/installation/installation.md#_snippet_3

LANGUAGE: bash
CODE:
```
npm install @fontsource/roboto
```

LANGUAGE: bash
CODE:
```
pnpm add @fontsource/roboto
```

LANGUAGE: bash
CODE:
```
yarn add @fontsource/roboto
```

----------------------------------------

TITLE: Enabling CSS Variables in Shadow DOM (tsx)
DESCRIPTION: This snippet demonstrates how to configure Material UI's CSS theme variables to work within a Shadow DOM. It adds the `cssVariables` property to the theme configuration, setting `rootSelector` to `:host` to target the Shadow DOM host element and `colorSchemeSelector` to `'class'`. This allows CSS variables to be applied correctly within the encapsulated styles.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/shadow-dom/shadow-dom.md#_snippet_2

LANGUAGE: tsx
CODE:
```
const theme = createTheme({
  cssVariables: {
    rootSelector: ':host',
    colorSchemeSelector: 'class',
  },
  components: {
    // ...same as above steps
  }
});
```

----------------------------------------

TITLE: Importing and Using Joy UI Sheet Component
DESCRIPTION: Imports the necessary React and Joy UI components (CssVarsProvider, Sheet) and sets up a basic React application structure using the Sheet component with an 'outlined' variant.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { CssVarsProvider } from '@mui/joy/styles';
import Sheet from '@mui/joy/Sheet';

export default function App() {
  return (
    <CssVarsProvider>
      <Sheet variant="outlined">Welcome!</Sheet>
    </CssVarsProvider>
  );
}
```

----------------------------------------

TITLE: Applying CSS Modules Custom Class Styles to MUI Slider via slotProps (JSX)
DESCRIPTION: Demonstrates how to apply CSS Modules styles to a Material UI Slider and its nested thumb element using custom class names. The custom thumb class is applied using the slotProps API on the Slider component.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_22

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
// webpack, Parcel or else will inject the CSS into the page
import styles from './CssModulesSliderDeep2.module.css';
import Slider from '@mui/material/Slider';

export default function CssModulesSliderDeep2() {
  return (
    <div>
      <Slider defaultValue={30} />
      <Slider
        defaultValue={30}
        className={styles.slider}
        slotProps={{ thumb: { className: styles.thumb } }}
      />
    </div>
  );
}
```

----------------------------------------

TITLE: Disabling SSR Rerendering - MUI ThemeProvider - JSX
DESCRIPTION: Illustrates how to prevent the ThemeProvider from rerendering on the client for SSR hydration mismatches by using the `noSsr` prop, useful for SPAs or SSR with Suspense.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/dark-mode/dark-mode.md#_snippet_8

LANGUAGE: JSX
CODE:
```
<ThemeProvider theme={theme} noSsr>
```

----------------------------------------

TITLE: Manually Linking Label to Textarea for Accessibility (JSX)
DESCRIPTION: Provides an example of manually linking an HTML `<label>` element to the underlying `<textarea>` element using the `htmlFor` and `slotProps.textarea.id` attributes for improved accessibility.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/textarea/textarea.md#_snippet_3

LANGUAGE: jsx
CODE:
```
<label htmlFor="unique-id">Label</label>
<Textarea
  slotProps={{
    textarea: {
      id: 'unique-id',
    }
  }}
/>
```

----------------------------------------

TITLE: Styling Caveat: Shared Class Names (JS)
DESCRIPTION: Illustrates a potential styling conflict due to both libraries using the same CSS class name prefix (e.g., `.MuiTypography-root`). Applying styles directly to these shared classes can unintentionally affect components from both libraries, as shown with the Typography example.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/integrations/material-ui/material-ui.md#_snippet_2

LANGUAGE: js
CODE:
```
import MaterialTypography, {
  typographyClasses as materialTypographyClasses,
} from '@mui/material/Typography';
import JoyTypography, {
  typographyClasses as joyTyographyClasses,
} from '@mui/joy/Typography';
import Stack from '@mui/material/Stack';

<Stack
  sx={{
    // similar to `& .${joyTyographyClasses.root}`
    [`& .${materialTypographyClasses.root}`]: {
      color: 'red',
    },
  }}
>
  {/* Both components are red. */}
  <MaterialTypography>Red</MaterialTypography>
  <JoyTypography>Red</JoyTypography>
</Stack>;
```

----------------------------------------

TITLE: Importing the Joy UI Button Component (JSX)
DESCRIPTION: This snippet shows the standard way to import the Button component from the `@mui/joy/Button` module for use in a React application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/button/button.md#_snippet_0

LANGUAGE: jsx
CODE:
```
import Button from '@mui/joy/Button';
```

----------------------------------------

TITLE: Matching Theme colorSchemeSelector with InitColorSchemeScript Attribute
DESCRIPTION: This caveat highlights the necessity of ensuring that the `colorSchemeSelector` property within your Material UI theme configuration matches the exact value provided to the `attribute` prop of the `InitColorSchemeScript` component when customizing the attribute.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/init-color-scheme-script/init-color-scheme-script.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
const theme = createTheme({
  cssVariables: {
    colorSchemeSelector: 'same value as the `attribute` prop',
  },
});
```

----------------------------------------

TITLE: Handling Clicks with MUI Button (JSX)
DESCRIPTION: Demonstrates how to attach an `onClick` event handler to a Material UI Button component to execute a function when the button is clicked. The example shows a simple alert triggered on click.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/buttons/buttons.md#_snippet_0

LANGUAGE: jsx
CODE:
```
<Button
  onClick={() => {
    alert('clicked');
  }}
>
  Click me
</Button>
```

----------------------------------------

TITLE: Setting Default Color Mode in InitColorSchemeScript
DESCRIPTION: This snippet shows how to use the `defaultMode` prop to specify the initial color scheme that users will see when they first visit the page, before their system preference is detected. In this example, the default is set to "dark".
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/init-color-scheme-script/init-color-scheme-script.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
<InitColorSchemeScript defaultMode="dark" />
```

----------------------------------------

TITLE: Using useMediaQuery with noSsr Option - JavaScript
DESCRIPTION: Shows how to use the `noSsr: true` option with `useMediaQuery` to disable server-side rendering for the hook's value, useful when the value is only needed client-side to avoid a double render pass.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/components/use-media-query/use-media-query.md#_snippet_3

LANGUAGE: js
CODE:
```
const matches = useMediaQuery('(min-width:600px)', { noSsr: true });
```

----------------------------------------

TITLE: Configuring Emotion Cache for Injection Order (JSX)
DESCRIPTION: Shows how to configure a custom Emotion cache with the prepend: true option when using Material UI. This ensures that Emotion styles are injected before Material UI styles, maintaining the correct CSS injection order.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/integrations/interoperability/interoperability.md#_snippet_18

LANGUAGE: jsx
CODE:
```
import * as React from 'react';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';

const cache = createCache({
  key: 'css',
  prepend: true,
});

export default function CssModulesPriority() {
  return (
    <CacheProvider value={cache}>
      {/* Your component tree. Now you can override Material UI's styles. */}
    </CacheProvider>
  );
}
```

----------------------------------------

TITLE: Configure Default Props for Custom Component in MUI Theme (JS/MUI)
DESCRIPTION: This snippet provides an example of how to configure default props for a custom component (`MuiStat`) within a MUI theme object using `createTheme`. This allows setting default values for props like `variant` globally for the application.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/creating-themed-components/creating-themed-components.md#_snippet_6

LANGUAGE: js
CODE:
```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  components: {
    MuiStat: {
      defaultProps: {
        variant: 'outlined',
      },
    },
  },
});
```

----------------------------------------

TITLE: Install MUI X Premium with yarn
DESCRIPTION: Installs the `@mui/x-data-grid-premium` package using the yarn package manager. This provides an alternative method to npm for installing the required package for the MUI X Data Grid Premium version.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/premium-plan-release.md#_snippet_1

LANGUAGE: Shell
CODE:
```
yarn add @mui/x-data-grid-premium
```

----------------------------------------

TITLE: Adding Email and Password Fields (Joy UI) - JSX
DESCRIPTION: Adds two FormControl components, one for email and one for password, using FormLabel and Input components to create standard text input fields within a Sheet container.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/getting-started/tutorial/tutorial.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<FormControl>
  <FormLabel>Email</FormLabel>
  <Input
    // html input attribute
    name="email"
    type="email"
    placeholder="<EMAIL>"
  />
</FormControl>
<FormControl>
  <FormLabel>Password</FormLabel>
  <Input
    name="password"
    type="password"
    placeholder="password"
  />
</FormControl>
```

----------------------------------------

TITLE: Installing MUI Base Package (Bash)
DESCRIPTION: Provides commands to install the `@mui/base` package using different Node.js package managers: npm, pnpm, and yarn.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/pages/blog/introducing-base-ui.md#_snippet_1

LANGUAGE: bash
CODE:
```
npm install @mui/base
```

LANGUAGE: bash
CODE:
```
pnpm add @mui/base
```

LANGUAGE: bash
CODE:
```
yarn add @mui/base
```

----------------------------------------

TITLE: Combining standalone MUI System style functions in React
DESCRIPTION: Illustrates how to import and compose individual style functions (like `spacing`, `borders`, `display`) from `@mui/system` to create a custom style function. This approach is useful for optimizing bundle size by only including necessary utilities.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/system/getting-started/custom-components/custom-components.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import * as React from 'react';
import { styled, compose, spacing, borders, display } from '@mui/system';

// Compose specific style functions
const customStyleFunction = compose(spacing, borders, display);

// Apply the composed function to a custom component
const CustomStyledDiv = styled('div')(customStyleFunction);

export default function CombiningStyleFunctionsDemo() {
  return (
    <CustomStyledDiv
      // Use properties from the composed functions
      m={2} // margin from spacing
      p={1} // padding from spacing
      border={1} // border from borders
      borderColor="grey.500" // border from borders
      display="flex" // display from display
      justifyContent="center"
      alignItems="center"
      sx={{
        width: 200,
        height: 100,
      }}
    >
      Styled with composed functions
    </CustomStyledDiv>
  );
}
```

----------------------------------------

TITLE: Defining Custom Breakpoint Names in MUI Theme
DESCRIPTION: Illustrates how to define entirely new breakpoint names and their corresponding pixel values within the Material UI theme configuration, allowing for project-specific naming conventions.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/material/customization/breakpoints/breakpoints.md#_snippet_2

LANGUAGE: js
CODE:
```
const theme = createTheme({
  breakpoints: {
    values: {
      mobile: 0,
      tablet: 640,
      laptop: 1024,
      desktop: 1200,
    },
  },
});
```

----------------------------------------

TITLE: Ignoring Clickaway Close Reason - Joy UI - JS
DESCRIPTION: Shows how to prevent the Snackbar from closing when the user clicks outside of it by checking the `reason` argument in the `onClose` callback and returning early if the reason is 'clickaway'.
SOURCE: https://github.com/mui/material-ui-docs/blob/latest/docs/data/joy/components/snackbar/snackbar.md#_snippet_2

LANGUAGE: js
CODE:
```
<Snackbar
  onClose={(event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
  }}
>

```