{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": true, "alwaysAllow": []}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "******************************************************************************************************************************************************************************************"}, "disabled": true, "alwaysAllow": []}}}