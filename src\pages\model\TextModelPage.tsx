import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  type SortingState
} from '@tanstack/react-table';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import TextField from '@mui/material/TextField';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Table from '@mui/material/Table';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import TablePagination from '@mui/material/TablePagination';
import Paper from '@mui/material/Paper';
import useSystemDictionary from '@/hooks/useSystemDictionary';
import { formatDate } from '@/utils/formatDate';
import {
  getTextModels,
  createTextModel,
  updateTextModel,
  deleteTextModel,
  updateTextModelStatus,
  deployTextModel
} from '@/controllers/model/textModel';
import type { Model, ModelFormData, ModelStatusUpdateData } from '@/types/model';

const TextModelPage: React.FC = () => {
  const { modelTypes, modelStatuses } = useSystemDictionary(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [formData, setFormData] = useState<ModelFormData>({
    name: '',
    description: '',
    type: '',
    status: 'online',
    supportsFunctionCalling: false,
  });

  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const queryClient = useQueryClient();

  // Column helper for type-safe column definitions
  const columnHelper = createColumnHelper<Model>();

  // Define columns
  const columns = useMemo(() => [
    columnHelper.accessor('name', {
      header: '模型名称',
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('description', {
      header: '模型描述',
      cell: info => (
        <div className="max-w-xs truncate">{info.getValue()}</div>
      ),
    }),
    columnHelper.accessor('type', {
      header: '模型类型',
      cell: info => modelTypes.find(type => type.value === info.getValue())?.label || info.getValue(),
    }),
    columnHelper.accessor('status', {
      header: '模型状态',
      cell: info => (
        <div className="flex items-center space-x-2">
          <Switch
            checked={info.getValue() === 'online'}
            onChange={(e) => handleStatusToggle(info.row.original, e.target.checked)}
            size="small"
          />
          <span style={{ fontSize: '12px' }} className="text-black">
            {modelStatuses.find(status => status.value === info.getValue())?.label || info.getValue()}
          </span>
        </div>
      ),
    }),
    columnHelper.accessor('createdAt', {
      header: '添加时间',
      cell: info => formatDate(info.getValue()),
    }),
    columnHelper.display({
      id: 'actions',
      header: '操作',
      cell: info => (
        <div className="flex space-x-2">
          <Button
            variant="outlined"
            size="small"
            onClick={() => handleEditModel(info.row.original)}
          >
            编辑
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={() => handleDeleteModel(info.row.original.id)}
          >
            删除
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={() => handleDeployModel(info.row.original.id)}
          >
            部署
          </Button>
        </div>
      ),
    }),
  ], [modelTypes, modelStatuses]);

  // Fetch models
  const { data: models, isLoading, error } = useQuery({
    queryKey: ['textModels'],
    queryFn: async () => {
      const response = await getTextModels();
      if (response.code === '0') {
        return response.data;
      }
      throw new Error(response.msg);
    },
  });

  // Create table instance
  const table = useReactTable({
    data: models || [],
    columns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Create model mutation
  const createModelMutation = useMutation({
    mutationFn: createTextModel,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['textModels'] });
      setIsDialogOpen(false);
      resetForm();
    },
  });

  // Update model mutation
  const updateModelMutation = useMutation({
    mutationFn: (data: { id: string; formData: ModelFormData }) =>
      updateTextModel(data.id, data.formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['textModels'] });
      setIsDialogOpen(false);
      resetForm();
    },
  });

  // Delete model mutation
  const deleteModelMutation = useMutation({
    mutationFn: deleteTextModel,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['textModels'] });
    },
  });

  // Update model status mutation
  const updateStatusMutation = useMutation({
    mutationFn: updateTextModelStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['textModels'] });
    },
  });

  // Deploy model mutation
  const deployModelMutation = useMutation({
    mutationFn: deployTextModel,
    onSuccess: () => {
      // Optionally refresh data after deployment
      queryClient.invalidateQueries({ queryKey: ['textModels'] });
    },
  });

  // Reset form data
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: '',
      status: 'online',
      supportsFunctionCalling: false,
    });
    setSelectedModel(null);
    setIsEditMode(false);
  };

  // Handle dialog open for adding a new model
  const handleAddModel = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  // Handle dialog open for editing a model
  const handleEditModel = (model: Model) => {
    setSelectedModel(model);
    setFormData({
      name: model.name,
      description: model.description,
      type: model.type,
      status: model.status,
      supportsFunctionCalling: model.supportsFunctionCalling,
    });
    setIsEditMode(true);
    setIsDialogOpen(true);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (isEditMode && selectedModel) {
      updateModelMutation.mutate({ id: selectedModel.id, formData });
    } else {
      createModelMutation.mutate(formData);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle model deletion
  const handleDeleteModel = (id: string) => {
    if (window.confirm('确定要删除此模型吗？')) {
      deleteModelMutation.mutate(id);
    }
  };

  // Handle model status toggle
  const handleStatusToggle = (model: Model, checked: boolean) => {
    const newStatus = checked ? 'online' : 'offline';
    const updateData: ModelStatusUpdateData = {
      id: model.id,
      status: newStatus,
    };
    updateStatusMutation.mutate(updateData);
  };

  // Handle model deployment
  const handleDeployModel = (id: string) => {
    if (window.confirm('确定要部署此模型吗？')) {
      deployModelMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div className="p-6">加载中...</div>;
  }

  if (error) {
    return <div className="p-6 text-red-500">加载失败: {error.toString()}</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">文本模型</h1>
        <Button
          variant="contained"
          color="primary"
          onClick={handleAddModel}
          startIcon={<span>+</span>}
        >
          添加模型
        </Button>
      </div>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableCell
                    key={header.id}
                    onClick={header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined}
                    style={{
                      cursor: header.column.getCanSort() ? 'pointer' : 'default',
                      backgroundColor: '#F9F9F9',
                      color: '#606266',
                      fontSize: '14px',
                      fontFamily: 'Microsoft YaHei',
                      borderColor: '#E8E8E8'
                    }}
                  >
                    <div className="flex items-center">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {{
                        asc: ' 🔼',
                        desc: ' 🔽',
                      }[header.column.getIsSorted() as string] ?? null}
                    </div>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody>
            {table.getRowModel().rows.map(row => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map(cell => (
                  <TableCell
                    key={cell.id}
                    style={{
                      fontSize: '12px',
                      fontFamily: 'Microsoft YaHei',
                      borderColor: '#E8E8E8'
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Controls */}
      <TablePagination
        component="div"
        count={models?.length || 0}
        page={table.getState().pagination.pageIndex}
        rowsPerPage={table.getState().pagination.pageSize}
        onPageChange={(_, newPage) => {
          table.setPageIndex(newPage);
          // 模拟数据请求
          setTimeout(() => {
            alert(`已加载第${newPage + 1}页数据`);
          }, 300);
        }}
        onRowsPerPageChange={(e) => {
          table.setPageSize(Number(e.target.value));
          // 模拟数据请求
          setTimeout(() => {
            alert('已更新每页显示条数，重新加载数据');
          }, 300);
        }}
        labelRowsPerPage="每页行数:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        rowsPerPageOptions={[5, 10, 25, 50]}
        style={{
          fontFamily: 'Microsoft YaHei',
          fontSize: '12px',
          color: '#606266'
        }}
      />

      <Dialog
        open={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{isEditMode ? '编辑模型' : '添加模型'}</DialogTitle>
        <DialogContent>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="name" className="text-right text-black">
                  模型名称
                </label>
                <TextField
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                  size="small"
                  fullWidth
                  variant="outlined"
                  sx={{ gridColumn: 'span 3' }}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="description" className="text-right text-black">
                  模型描述
                </label>
                <TextField
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="col-span-3"
                  multiline
                  rows={4}
                  size="small"
                  fullWidth
                  variant="outlined"
                  sx={{ gridColumn: 'span 3' }}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="type" className="text-right text-black">
                  模型类型
                </label>
                <FormControl sx={{ gridColumn: 'span 3' }} fullWidth size="small">
                  <InputLabel id="type-label">模型类型</InputLabel>
                  <Select
                    labelId="type-label"
                    id="type"
                    value={formData.type}
                    onChange={(e) => handleSelectChange('type', e.target.value)}
                    label="模型类型"
                  >
                    {modelTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="status" className="text-right text-black">
                  模型状态
                </label>
                <FormControl sx={{ gridColumn: 'span 3' }} fullWidth size="small">
                  <InputLabel id="status-label">模型状态</InputLabel>
                  <Select
                    labelId="status-label"
                    id="status"
                    value={formData.status}
                    onChange={(e) => handleSelectChange('status', e.target.value)}
                    label="模型状态"
                  >
                    {modelStatuses.map((status) => (
                      <MenuItem key={status.value} value={status.value}>
                        {status.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="supportsFunctionCalling" className="text-right text-black">
                  支持函数调用
                </label>
                <div className="col-span-3">
                  <FormControlLabel
                    control={
                      <Checkbox
                        id="supportsFunctionCalling"
                        checked={formData.supportsFunctionCalling}
                        onChange={(e) => handleCheckboxChange('supportsFunctionCalling', e.target.checked)}
                      />
                    }
                    label="是否支持函数调用"
                  />
                </div>
              </div>
            </div>
            <DialogActions>
              <Button type="button" variant="outlined" onClick={() => setIsDialogOpen(false)}>
                取消
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={createModelMutation.isPending || updateModelMutation.isPending}
              >
                确认
              </Button>
            </DialogActions>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TextModelPage;
