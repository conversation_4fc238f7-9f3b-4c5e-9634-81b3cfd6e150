Switch
======

Switches toggle the state of a single setting on or off.


Manage your marketing, customers, and checkout flow with Squarespace.

ads via Carbon



Switches are the preferred way to adjust settings on mobile.
The option that the switch controls, as well as the state it's in,
should be made clear from the corresponding inline label.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Basic switches
--------------

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Switch {...label} defaultChecked />
<Switch {...label} />
<Switch {...label} disabled defaultChecked />
<Switch {...label} disabled />  

```
\<Switch {...label} defaultChecked /\>
\<Switch {...label} /\>
\<Switch {...label} disabled defaultChecked /\>
\<Switch {...label} disabled /\>Press `Enter` to start editingLabel
-----

You can provide a label to the `Switch` thanks to the `FormControlLabel` component.


LabelRequired \*DisabledJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormGroup>
  <FormControlLabel control={<Switch defaultChecked />} label="Label" />
  <FormControlLabel required control={<Switch />} label="Required" />
  <FormControlLabel disabled control={<Switch />} label="Disabled" />
</FormGroup>  

```
\<FormGroup\>
 \<FormControlLabel control\={\<Switch defaultChecked /\>} label\="Label" /\>
 \<FormControlLabel required control\={\<Switch /\>} label\="Required" /\>
 \<FormControlLabel disabled control\={\<Switch /\>} label\="Disabled" /\>
\</FormGroup\>Press `Enter` to start editingSize
----

Use the `size` prop to change the size of the switch.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Switch {...label} defaultChecked size="small" />
<Switch {...label} defaultChecked />  

```
\<Switch {...label} defaultChecked size\="small" /\>
\<Switch {...label} defaultChecked /\>Press `Enter` to start editingColor
-----

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Switch {...label} defaultChecked />
<Switch {...label} defaultChecked color="secondary" />
<Switch {...label} defaultChecked color="warning" />
<Switch {...label} defaultChecked color="default" />
<PinkSwitch {...label} defaultChecked />  

```
\<Switch {...label} defaultChecked /\>
\<Switch {...label} defaultChecked color\="secondary" /\>
\<Switch {...label} defaultChecked color\="warning" /\>
\<Switch {...label} defaultChecked color\="default" /\>
\<PinkSwitch {...label} defaultChecked /\>Press `Enter` to start editingControlled
----------

You can control the switch with the `checked` and `onChange` props:


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Switch
  checked={checked}
  onChange={handleChange}
  inputProps={{ 'aria-label': 'controlled' }}
/>  

```
\<Switch
 checked\={checked}
 onChange\={handleChange}
 inputProps\={{ 'aria\-label': 'controlled' }}
/\>Press `Enter` to start editingSwitches with FormGroup
-----------------------

`FormGroup` is a helpful wrapper used to group selection controls components that provides an easier API.
However, you are encouraged to use Checkboxes instead if multiple related controls are required. (See: When to use).


Assign responsibilityGilad GrayJason KillianAntoine LlorcaBe careful

JSTSShow codeCustomization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


MUI switchAndroid 12iOS styleOff

On

JSTSShow code🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


Label placement
---------------

You can change the placement of the label:


Label placementBottomEndJSTSShow codeWhen to use
-----------

* Checkboxes vs. Switches


Accessibility
-------------

* It will render an element with the `checkbox` role not `switch` role since this
role isn't widely supported yet. Please test first if assistive technology of your
target audience supports this role properly. Then you can change the role with
`<Switch inputProps={{ role: 'switch' }}>`
* All form controls should have labels, and this includes radio buttons, checkboxes, and switches. In most cases, this is done by using the `<label>` element (FormControlLabel).
* When a label can't be used, it's necessary to add an attribute directly to the input component.
In this case, you can apply the additional attribute (for example `aria-label`, `aria-labelledby`, `title`) via the `inputProps` prop.



```
<Switch value="checkedA" inputProps={{ 'aria-label': 'Switch A' }} />

```
CopyCopied(or Ctrl \+ C)
Unstyled
--------

Use the Base UI Switch for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<FormControl />`
* `<FormControlLabel />`
* `<FormGroup />`
* `<FormLabel />`
* `<Switch />`



