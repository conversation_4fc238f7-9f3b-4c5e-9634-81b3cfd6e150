Button Group
============

The ButtonGroup component can be used to group related buttons.**MUI for enterprise**. Save time and reduce risk. Managed open source — backed by maintainers.ad by MUI


* Feedback
* Bundle size
* Source
* Figma
* Sketch

Basic button group
------------------

The buttons can be grouped by wrapping them with the `ButtonGroup` component.
They need to be immediate children.


OneTwoThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<ButtonGroup variant="contained" aria-label="Basic button group">
  <Button>One</Button>
  <Button>Two</Button>
  <Button>Three</Button>
</ButtonGroup>  

```
\<ButtonGroup variant\="contained" aria\-label\="Basic button group"\>
 \<Button\>One\</Button\>
 \<Button\>Two\</Button\>
 \<Button\>Three\</Button\>
\</ButtonGroup\>Press `Enter` to start editingButton variants
---------------

All the standard button variants are supported.


OneTwoThreeOneTwoThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<ButtonGroup variant="outlined" aria-label="Basic button group">
  <Button>One</Button>
  <Button>Two</Button>
  <Button>Three</Button>
</ButtonGroup>
<ButtonGroup variant="text" aria-label="Basic button group">
  <Button>One</Button>
  <Button>Two</Button>
  <Button>Three</Button>
</ButtonGroup>  

```
\<ButtonGroup variant\="outlined" aria\-label\="Basic button group"\>
 \<Button\>One\</Button\>
 \<Button\>Two\</Button\>
 \<Button\>Three\</Button\>
\</ButtonGroup\>
\<ButtonGroup variant\="text" aria\-label\="Basic button group"\>
 \<Button\>One\</Button\>
 \<Button\>Two\</Button\>
 \<Button\>Three\</Button\>
\</ButtonGroup\>Press `Enter` to start editingSizes and colors
----------------

The `size` and `color` props can be used to control the appearance of the button group.


OneTwoThreeOneTwoThreeOneTwoThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<ButtonGroup size="small" aria-label="Small button group">
  {buttons}
</ButtonGroup>
<ButtonGroup color="secondary" aria-label="Medium-sized button group">
  {buttons}
</ButtonGroup>
<ButtonGroup size="large" aria-label="Large button group">
  {buttons}
</ButtonGroup>  

```
\<ButtonGroup size\="small" aria\-label\="Small button group"\>
 {buttons}
\</ButtonGroup\>
\<ButtonGroup color\="secondary" aria\-label\="Medium\-sized button group"\>
 {buttons}
\</ButtonGroup\>
\<ButtonGroup size\="large" aria\-label\="Large button group"\>
 {buttons}
\</ButtonGroup\>Press `Enter` to start editingVertical group
--------------

The button group can be displayed vertically using the `orientation` prop.


OneTwoThreeOneTwoThreeOneTwoThreeJSTSShow codeSplit button
------------

`ButtonGroup` can also be used to create a split button. The dropdown can change the button action (as in this example) or be used to immediately trigger a related action.


Squash and mergeJSTSShow codeDisabled elevation
------------------

You can remove the elevation with the `disableElevation` prop.


OneTwoJSTSExpand codeCopy(or Ctrl \+ C)
```
<ButtonGroup
  disableElevation
  variant="contained"
  aria-label="Disabled button group"
>
  <Button>One</Button>
  <Button>Two</Button>
</ButtonGroup>  

```
\<ButtonGroup
 disableElevation
 variant\="contained"
 aria\-label\="Disabled button group"
\>
 \<Button\>One\</Button\>
 \<Button\>Two\</Button\>
\</ButtonGroup\>Press `Enter` to start editingLoading
-------

Use the `loading` prop from `Button` to set buttons in a loading state and disable interactions.


SubmitFetch dataSaveJSTSExpand codeCopy(or Ctrl \+ C)
```
<ButtonGroup variant="outlined" aria-label="Loading button group">
  <Button>Submit</Button>
  <Button>Fetch data</Button>
  <Button loading loadingPosition="start" startIcon={<SaveIcon />}>
    Save
  </Button>
</ButtonGroup>  

```
\<ButtonGroup variant\="outlined" aria\-label\="Loading button group"\>
 \<Button\>Submit\</Button\>
 \<Button\>Fetch data\</Button\>
 \<Button loading loadingPosition\="start" startIcon\={\<SaveIcon /\>}\>
 Save
 \</Button\>
\</ButtonGroup\>Press `Enter` to start editingAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Button />`
* `<ButtonGroup />`



