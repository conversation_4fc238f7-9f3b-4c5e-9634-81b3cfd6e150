Pagination
==========

The Pagination component enables the user to select a specific page from a range of pages.


Join us May 30th for world's largest hackathon for non\-devs and vibe coders. $1M\+ Prize pool.

ads via Carbon



* Feedback
* Bundle size
* Source
* Figma
* Sketch

Basic pagination
----------------

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={10} />
<Pagination count={10} color="primary" />
<Pagination count={10} color="secondary" />
<Pagination count={10} disabled />  

```
\<Pagination count\={10} /\>
\<Pagination count\={10} color\="primary" /\>
\<Pagination count\={10} color\="secondary" /\>
\<Pagination count\={10} disabled /\>Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonOutlined pagination
-------------------

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={10} variant="outlined" />
<Pagination count={10} variant="outlined" color="primary" />
<Pagination count={10} variant="outlined" color="secondary" />
<Pagination count={10} variant="outlined" disabled />  

```
\<Pagination count\={10} variant\="outlined" /\>
\<Pagination count\={10} variant\="outlined" color\="primary" /\>
\<Pagination count\={10} variant\="outlined" color\="secondary" /\>
\<Pagination count\={10} variant\="outlined" disabled /\>Press `Enter` to start editingRounded pagination
------------------

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={10} shape="rounded" />
<Pagination count={10} variant="outlined" shape="rounded" />  

```
\<Pagination count\={10} shape\="rounded" /\>
\<Pagination count\={10} variant\="outlined" shape\="rounded" /\>Press `Enter` to start editingPagination size
---------------

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={10} size="small" />
<Pagination count={10} />
<Pagination count={10} size="large" />  

```
\<Pagination count\={10} size\="small" /\>
\<Pagination count\={10} /\>
\<Pagination count\={10} size\="large" /\>Press `Enter` to start editingButtons
-------

You can optionally enable first\-page and last\-page buttons, or disable the previous\-page and next\-page buttons.


* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={10} showFirstButton showLastButton />
<Pagination count={10} hidePrevButton hideNextButton />  

```
\<Pagination count\={10} showFirstButton showLastButton /\>
\<Pagination count\={10} hidePrevButton hideNextButton /\>Press `Enter` to start editingCustom icons
------------

It's possible to customize the control icons.


* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination
  count={10}
  renderItem={(item) => (
    <PaginationItem
      slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
      {...item}
    />
  )}
/>  

```
\<Pagination
 count\={10}
 renderItem\={(item) \=\> (
 \<PaginationItem
 slots\={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
 {...item}
 /\>
 )}
/\>Press `Enter` to start editingPagination ranges
-----------------

You can specify how many digits to display either side of current page with the `siblingCount` prop, and adjacent to the start and end page number with the `boundaryCount` prop.


* 
* 1
* …
* 6
* …
* 11
* 
* 
* 1
* …
* 5
* 6
* 7
* …
* 11
* 
 * 
* 1
* 2
* …
* 6
* …
* 10
* 11
* 
* 
* 1
* 2
* …
* 5
* 6
* 7
* …
* 10
* 11
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Pagination count={11} defaultPage={6} siblingCount={0} />
<Pagination count={11} defaultPage={6} /> {/* Default ranges */}
<Pagination count={11} defaultPage={6} siblingCount={0} boundaryCount={2} />
<Pagination count={11} defaultPage={6} boundaryCount={2} />  

```
\<Pagination count\={11} defaultPage\={6} siblingCount\={0} /\>
\<Pagination count\={11} defaultPage\={6} /\> {/\* Default ranges \*/}
\<Pagination count\={11} defaultPage\={6} siblingCount\={0} boundaryCount\={2} /\>
\<Pagination count\={11} defaultPage\={6} boundaryCount\={2} /\>Press `Enter` to start editingControlled pagination
---------------------

Page: 1

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<Typography>Page: {page}</Typography>
<Pagination count={10} page={page} onChange={handleChange} />  

```
\<Typography\>Page: {page}\</Typography\>
\<Pagination count\={10} page\={page} onChange\={handleChange} /\>Press `Enter` to start editingRouter integration
------------------

* 
* 1
* 2
* 3
* 4
* 5
* …
* 10
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<MemoryRouter initialEntries={['/inbox']} initialIndex={0}>
  <Routes>
    <Route path="*" element={<Content />} />
  </Routes>
</MemoryRouter>  

```
\<MemoryRouter initialEntries\={\['/inbox']} initialIndex\={0}\>
 \<Routes\>
 \<Route path\="\*" element\={\<Content /\>} /\>
 \</Routes\>
\</MemoryRouter\>Press `Enter` to start editing`usePagination`
---------------

For advanced customization use cases, a headless `usePagination` hook is exposed.
It accepts almost the same options as the Pagination component minus all the props
related to the rendering of JSX.
The Pagination component is built on this hook.



```
import usePagination from '@mui/material/usePagination';

```
CopyCopied(or Ctrl \+ C)
* previous
* 1
* 2
* 3
* 4
* 5
* …
* 10
* next
JSTSShow codeTable pagination
----------------

The `Pagination` component was designed to paginate a list of arbitrary items when infinite loading isn't used.
It's preferred in contexts where SEO is important, for instance, a blog.


For the pagination of a large set of tabular data, you should use the `TablePagination` component.


Rows per page:

1021–30 of 100

JSTSExpand codeCopy(or Ctrl \+ C)
```
<TablePagination
  component="div"
  count={100}
  page={page}
  onPageChange={handleChangePage}
  rowsPerPage={rowsPerPage}
  onRowsPerPageChange={handleChangeRowsPerPage}
/>  

```
\<TablePagination
 component\="div"
 count\={100}
 page\={page}
 onPageChange\={handleChangePage}
 rowsPerPage\={rowsPerPage}
 onRowsPerPageChange\={handleChangeRowsPerPage}
/\>Press `Enter` to start editing
Note that the `Pagination` page prop starts at 1 to match the requirement of including the value in the URL, while the `TablePagination` page prop starts at 0 to match the requirement of zero\-based JavaScript arrays that come with rendering a lot of tabular data.


You can learn more about this use case in the table section of the documentation.


Accessibility
-------------

### ARIA

The root node has a role of "navigation" and aria\-label "pagination navigation" by default. The page items have an aria\-label that identifies the purpose of the item ("go to first page", "go to previous page", "go to page 1" etc.).
You can override these using the `getItemAriaLabel` prop.


### Keyboard

The pagination items are in tab order, with a tabindex of "0".


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Pagination />`
* `<PaginationItem />`
* `<TablePagination />`



