Data Grid \- Column menu
========================

Customize your columns menu.


Access DeepSeek\-R1 via Together.ai's fast, scalable inference API.

ads via Carbon



Column menu
-----------

Each column header comes with a column menu with quickly accessible Data Grid features like column visibility, sorting, filtering, and others.


It can be accessed by clicking on the 3\-dots icon that appears on hover on a header cell or when focusing on a column header and pressing `Ctrl+Enter` (or `⌘ Command+Enter` on macOS).


DeskCommodityTrader NameTrader EmailD\-1151OatsEdgar <PERSON>@tiwinet.moD\-4831Sugar No.11<PERSON><PERSON>@bazutmop.htD\-7452Robusta coffeeNannie <PERSON><EMAIL>\-7186<NAME_EMAIL>\-117WheatMiguel <PERSON><EMAIL>\-8661Soybean Meal<PERSON><PERSON>@bubumte.zaD\-1181Wheat<PERSON><PERSON>@owohul.myD\-2494SoybeansE<PERSON><PERSON>@hekitep.itD\-1744Cotton No.2Estelle <PERSON>dez<PERSON>@judesuk.nfRows per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function ColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function ColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- From planning to production, GitLab brings teams together. Try it our for free.ad by CarbonCustomize column menu items
---------------------------

You can customize the column menu items by passing the `slots` and `slotProps` props to the `<GridColumnMenu />` component:


* `slots` \- use this prop to override default column menu slots or add new slots.
* `slotProps` \- use this prop to override or pass `displayOrder` for column menu items. You can also use this to pass extra props to custom column menu slots.


### Adding a menu item

To add a new menu item, create a new item slot and pass it to the `slots` prop. In the example below, the new slot is called `columnMenuUserItem` but you can choose any name and it'll be added to the menu automatically.
You can also set the `displayOrder` (default `100`) or pass new props to the slots using the `slotProps` prop.



```
function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Add new item
        columnMenuUserItem: CustomUserItem,
      }}
      slotProps={{
        columnMenuUserItem: {
          // set `displayOrder` for the new item
          displayOrder: 15,
          // Additional props
          myCustomValue: 'Do custom action',
          myCustomHandler:  => alert('Custom handler fired'),
        },
      }}
    />
  );
}

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailD\-5009WheatNina <EMAIL>\-7168WheatChad <EMAIL>\-2481<NAME_EMAIL>\-4587Sugar No.11Beatrice <EMAIL>\-1611RapeseedCory <EMAIL>\-3294Cotton No.2Antonio <EMAIL>\-6274Sugar No.14Bertie <EMAIL>\-6405<NAME_EMAIL>\-7266Sugar No.14Cynthia <EMAIL>\-9691Sugar No.14Katharine <EMAIL>\-2660WheatEtta <EMAIL>\-2336CornMary <EMAIL>\-6285Sugar No.14Belle <EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import SettingsApplicationsIcon from '@mui/icons-material/SettingsApplications';
import {
  DataGrid,
  GridColumnMenu,
  GridColumnMenuProps,
  GridColumnMenuItemProps,
} from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

function CustomUserItem(props: GridColumnMenuItemProps) {
  const { myCustomHandler, myCustomValue } = props;
  return (
    <MenuItem onClick={myCustomHandler}>
      <ListItemIcon>
        <SettingsApplicationsIcon fontSize="small" />
      </ListItemIcon>
      <ListItemText>{myCustomValue}</ListItemText>
    </MenuItem>
  );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Add new item
        columnMenuUserItem: CustomUserItem,
      }}
      slotProps={{
        columnMenuUserItem: {
          // set `displayOrder` for new item
          displayOrder: 15,
          // pass additional props
          myCustomValue: 'Do custom action',
          myCustomHandler:  => alert('Custom handler fired'),
        },
      }}
    />
  );
}

export default function AddNewColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} slots={{ columnMenu: CustomColumnMenu }} />
    </div>
  );
}  

```
import \* as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import SettingsApplicationsIcon from '@mui/icons\-material/SettingsApplications';
import {
 DataGrid,
 GridColumnMenu,
 GridColumnMenuProps,
 GridColumnMenuItemProps,
} from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

function CustomUserItem(props: GridColumnMenuItemProps) {
 const { myCustomHandler, myCustomValue } \= props;
 return (
 \<MenuItem onClick\={myCustomHandler}\>
 \<ListItemIcon\>
 \<SettingsApplicationsIcon fontSize\="small" /\>
 \</ListItemIcon\>
 \<ListItemText\>{myCustomValue}\</ListItemText\>
 \</MenuItem\>
 );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
 return (
 \<GridColumnMenu
 {...props}
 slots\={{
 // Add new item
 columnMenuUserItem: CustomUserItem,
 }}
 slotProps\={{
 columnMenuUserItem: {
 // set \`displayOrder\` for new item
 displayOrder: 15,
 // pass additional props
 myCustomValue: 'Do custom action',
 myCustomHandler:  \=\> alert('Custom handler fired'),
 },
 }}
 /\>
 );
}

export default function AddNewColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} slots\={{ columnMenu: CustomColumnMenu }} /\>
 \</div\>
 );
}Press `Enter` to start editing**AG Grid** \- Visualise Data with AG Charts: Open source, high\-performance JavaScript Charts with a flexible API.ad by Carbon### Overriding default menu items

Use the `slots` prop to override default menu items.
Check this table to know the overridable slot name for each menu item.



```
function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Override `columnMenuFilterItem` component
        columnMenuFilterItem: CustomFilterItem,
      }}
    />
  );
}

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailD\-4079<NAME_EMAIL>\-9753Cotton No.2Violet <EMAIL>\-7304RapeseedRicardo <EMAIL>\-665<NAME_EMAIL>\-5996RapeseedLena <EMAIL>\-6266Sugar No.14Ellen <EMAIL>\-3759CornLeon <EMAIL>\-4616<NAME_EMAIL>\-5553OatsMelvin <EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import IconFilter from '@mui/icons-material/FilterAlt';
import {
  DataGrid,
  GridColumnMenu,
  GridColumnMenuProps,
  GridColumnMenuItemProps,
  useGridApiContext,
} from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

function CustomFilterItem(props: GridColumnMenuItemProps) {
  const { onClick, colDef } = props;
  const apiRef = useGridApiContext;
  const handleClick = React.useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      apiRef.current.showFilterPanel(colDef.field);
      onClick(event);
    },
    [apiRef, colDef.field, onClick],
  );
  return (
    <MenuItem onClick={handleClick}>
      <ListItemIcon>
        <IconFilter fontSize="small" />
      </ListItemIcon>
      <ListItemText>Show Filters</ListItemText>
    </MenuItem>
  );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Override slot for `columnMenuFilterItem`
        columnMenuFilterItem: CustomFilterItem,
      }}
    />
  );
}

export default function OverrideColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} slots={{ columnMenu: CustomColumnMenu }} />
    </div>
  );
}  

```
import \* as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import IconFilter from '@mui/icons\-material/FilterAlt';
import {
 DataGrid,
 GridColumnMenu,
 GridColumnMenuProps,
 GridColumnMenuItemProps,
 useGridApiContext,
} from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

function CustomFilterItem(props: GridColumnMenuItemProps) {
 const { onClick, colDef } \= props;
 const apiRef \= useGridApiContext;
 const handleClick \= React.useCallback(
 (event: React.MouseEvent\<HTMLElement\>) \=\> {
 apiRef.current.showFilterPanel(colDef.field);
 onClick(event);
 },
 \[apiRef, colDef.field, onClick],
 );
 return (
 \<MenuItem onClick\={handleClick}\>
 \<ListItemIcon\>
 \<IconFilter fontSize\="small" /\>
 \</ListItemIcon\>
 \<ListItemText\>Show Filters\</ListItemText\>
 \</MenuItem\>
 );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
 return (
 \<GridColumnMenu
 {...props}
 slots\={{
 // Override slot for \`columnMenuFilterItem\`
 columnMenuFilterItem: CustomFilterItem,
 }}
 /\>
 );
}

export default function OverrideColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} slots\={{ columnMenu: CustomColumnMenu }} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon### Hiding a menu item

To hide a menu item, you can set its respective slot in `slots` to `null`.
Check this table to know the slot name for each menu item.



```
function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Hide `columnMenuColumnsItem`
        columnMenuColumnsItem: null,
      }}
    />
  );
}

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailD\-5403<NAME_EMAIL>\-2133Sugar No.11James <EMAIL>\-2828SoybeansPolly <EMAIL>\-8461Cotton No.2Gussie <EMAIL>\-8148RapeseedLouis <EMAIL>\-8273<NAME_EMAIL>\-7997Cotton No.2Betty <EMAIL>\-3436<NAME_EMAIL>\-929CornWillie <EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColumnMenu, GridColumnMenuProps } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Hide `columnMenuColumnsItem`
        columnMenuColumnsItem: null,
      }}
    />
  );
}

export default function HideColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} slots={{ columnMenu: CustomColumnMenu }} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColumnMenu, GridColumnMenuProps } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

function CustomColumnMenu(props: GridColumnMenuProps) {
 return (
 \<GridColumnMenu
 {...props}
 slots\={{
 // Hide \`columnMenuColumnsItem\`
 columnMenuColumnsItem: null,
 }}
 /\>
 );
}

export default function HideColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} slots\={{ columnMenu: CustomColumnMenu }} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by Carbon### Reordering menu items

Every item is assigned a `displayOrder` based on which it is shown before or after other items. It works in ascending order; the smaller the number is, the earlier it is displayed on the list. For new items default value for `displayOrder` is **100**.


You can override `displayOrder` for the default or new items in `slotProps`.


Check this table to see default `displayOrder` for each menu item.



```
function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slotProps={{
        // Swap positions of filter and sort items
        columnMenuFilterItem: {
          displayOrder: 0, // Previously `10`
        },
        columnMenuSortItem: {
          displayOrder: 10, // Previously `0`
        },
      }}
    />
  );
}

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailD\-9865<NAME_EMAIL>\-9012<NAME_EMAIL>\-7880<NAME_EMAIL>\-8863<NAME_EMAIL>\-6262CornJerry <EMAIL>\-7247OatsHunter <EMAIL>\-1676<NAME_EMAIL>\-6276<NAME_EMAIL>\-9131MilkStella <EMAIL>\-2017<NAME_EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColumnMenu, GridColumnMenuProps } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

function CustomColumnMenu(props: GridColumnMenuProps) {
  return (
    <GridColumnMenu
      {...props}
      slotProps={{
        // Swap positions of filter and sort items
        columnMenuFilterItem: {
          displayOrder: 0, // Previously `10`
        },
        columnMenuSortItem: {
          displayOrder: 10, // Previously `0`
        },
      }}
    />
  );
}

export default function ReorderColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} slots={{ columnMenu: CustomColumnMenu }} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColumnMenu, GridColumnMenuProps } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

function CustomColumnMenu(props: GridColumnMenuProps) {
 return (
 \<GridColumnMenu
 {...props}
 slotProps\={{
 // Swap positions of filter and sort items
 columnMenuFilterItem: {
 displayOrder: 0, // Previously \`10\`
 },
 columnMenuSortItem: {
 displayOrder: 10, // Previously \`0\`
 },
 }}
 /\>
 );
}

export default function ReorderColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} slots\={{ columnMenu: CustomColumnMenu }} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by Carbon### Default column menu items

As a reference, here are the column menu `slots` along with the default item components and `displayOrder`.


SlotDefault ComponentDisplay OrdercolumnMenuSortItem

GridColumnMenuSortItem

10columnMenuFilterItem

GridColumnMenuFilterItem

20columnMenuColumnsItem

GridColumnMenuColumnsItem

30columnMenuPinningItem

GridColumnMenuPinningItem

15columnMenuAggregationItem

GridColumnMenuAggregationItem

23columnMenuGroupingItem

GridColumnMenuGroupingItem

27Custom menu component
---------------------

You can also customize and replace the column menu by passing a fully custom component to the `columnMenu` slot of the Data Grid. If you want to add some of the default menu items to your custom component, you can import and re\-use them.


DeskCommodityTrader NameTrader EmailD\-9009CornErik <EMAIL>\-6449<NAME_EMAIL>\-946CornEunice <EMAIL>\-6603SoybeansVerna <EMAIL>\-2265<NAME_EMAIL>\-1876RapeseedAaron <EMAIL>\-8708RapeseedRandy <EMAIL>\-101RapeseedMilton <EMAIL>\-850SoybeansMamie <EMAIL>\-6428<NAME_EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridColumnMenuFilterItem,
  GridColumnMenuSortItem,
  GridColumnMenuColumnsItem,
  GridColumnMenuItemProps,
  GridColumnMenuProps,
} from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';

function MenuCloseComponent(props: GridColumnMenuItemProps) {
  return (
    <Button color="primary" onClick={props.onClick}>
      Close Menu
    </Button>
  );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
  const itemProps = {
    colDef: props.colDef,
    onClick: props.hideMenu,
  };
  return (
    <React.Fragment>
      <Stack px={0.5} py={0.5}>
        <GridColumnMenuSortItem {...itemProps} />
        {/* Only provide filtering on desk */}
        {itemProps.colDef.field === 'desk' ? (
          <GridColumnMenuFilterItem {...itemProps} />
        ) : null}
      </Stack>
      <Divider />
      <Stack px={0.5} py={0.5}>
        <GridColumnMenuColumnsItem {...itemProps} />
        <MenuCloseComponent {...itemProps} />
      </Stack>
    </React.Fragment>
  );
}

export default function CustomColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        slots={{
          columnMenu: CustomColumnMenu,
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGrid,
 GridColumnMenuFilterItem,
 GridColumnMenuSortItem,
 GridColumnMenuColumnsItem,
 GridColumnMenuItemProps,
 GridColumnMenuProps,
} from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';

function MenuCloseComponent(props: GridColumnMenuItemProps) {
 return (
 \<Button color\="primary" onClick\={props.onClick}\>
 Close Menu
 \</Button\>
 );
}

function CustomColumnMenu(props: GridColumnMenuProps) {
 const itemProps \= {
 colDef: props.colDef,
 onClick: props.hideMenu,
 };
 return (
 \<React.Fragment\>
 \<Stack px\={0\.5} py\={0\.5}\>
 \<GridColumnMenuSortItem {...itemProps} /\>
 {/\* Only provide filtering on desk \*/}
 {itemProps.colDef.field \=\=\= 'desk' ? (
 \<GridColumnMenuFilterItem {...itemProps} /\>
 ) : null}
 \</Stack\>
 \<Divider /\>
 \<Stack px\={0\.5} py\={0\.5}\>
 \<GridColumnMenuColumnsItem {...itemProps} /\>
 \<MenuCloseComponent {...itemProps} /\>
 \</Stack\>
 \</React.Fragment\>
 );
}

export default function CustomColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 slots\={{
 columnMenu: CustomColumnMenu,
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon
**Tip**: The `columnMenu` component and its components slots receive the `colDef` prop corresponding to the current column; you can use this to conditionally render some items or change some logic.


Disable column menu
-------------------

By default, each column header has the column menu enabled. To disable the column menu, set the prop `disableColumnMenu={true}`.


DeskCommodityTrader NameTrader EmailD\-6267WheatMax <EMAIL>\-3858WheatJeremy <EMAIL>\-6402Frozen Concentrated <NAME_EMAIL>\-9464WheatJane <EMAIL>\-3265<NAME_EMAIL>\-5310SoybeansHulda <EMAIL>\-213CornTravis <EMAIL> per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function DisabledColumnMenuGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 5,
  });

  return (
    <div style={{ height: 300, width: '100%' }}>
      <DataGrid {...data} disableColumnMenu />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function DisabledColumnMenuGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 5,
 });

 return (
 \<div style\={{ height: 300, width: '100%' }}\>
 \<DataGrid {...data} disableColumnMenu /\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonColumn menu with Pro/Premium features
-------------------------------------

In the following demo, in addition to Data Grid MIT features, you can see commercial features like grouping, and aggregation in action. Try tweaking the values from respective column menu items.


TitleGrossavgDirectorYear20th Century Fox (3\)$2,047,744,198Disney Studios (20\)$1,457,722,026Universal Pictures (5\)$1,379,081,348Warner Bros. Pictures (4\)$1,161,476,310New Line Cinema (1\)$1,146,030,912Paramount Pictures (2\)$1,113,924,076$1,434,653,262Total Rows: 6JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGridPremium,
  GridGroupingColDefOverride,
  GridValidRowModel,
  useGridApiRef,
  useKeepGroupedColumnsHidden,
} from '@mui/x-data-grid-premium';
import { useMovieData } from '@mui/x-data-grid-generator';

const groupingColDef: GridGroupingColDefOverride<GridValidRowModel> = {
  leafField: 'title',
};

export default function ColumnMenuGridPremium {
  const apiRef = useGridApiRef;
  const data = useMovieData;

  const initialState = useKeepGroupedColumnsHidden({
    apiRef,
    initialState: {
      aggregation: {
        model: {
          gross: 'avg',
        },
      },
      columns: {
        columnVisibilityModel: {
          cinematicUniverse: false,
          title: false,
        },
      },
      rowGrouping: {
        model: ['company'],
      },
    },
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPremium
        {...data}
        apiRef={apiRef}
        groupingColDef={groupingColDef}
        initialState={initialState}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGridPremium,
 GridGroupingColDefOverride,
 GridValidRowModel,
 useGridApiRef,
 useKeepGroupedColumnsHidden,
} from '@mui/x\-data\-grid\-premium';
import { useMovieData } from '@mui/x\-data\-grid\-generator';

const groupingColDef: GridGroupingColDefOverride\<GridValidRowModel\> \= {
 leafField: 'title',
};

export default function ColumnMenuGridPremium {
 const apiRef \= useGridApiRef;
 const data \= useMovieData;

 const initialState \= useKeepGroupedColumnsHidden({
 apiRef,
 initialState: {
 aggregation: {
 model: {
 gross: 'avg',
 },
 },
 columns: {
 columnVisibilityModel: {
 cinematicUniverse: false,
 title: false,
 },
 },
 rowGrouping: {
 model: \['company'],
 },
 },
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPremium
 {...data}
 apiRef\={apiRef}
 groupingColDef\={groupingColDef}
 initialState\={initialState}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonAPI
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Column headerColumn spanning

---

•

Blog•

Store
