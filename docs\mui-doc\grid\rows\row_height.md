Data Grid \- Row height
=======================

Customize the height of your rows.


Join us May 30th for world's largest hackathon for non\-devs and vibe coders. $1M\+ Prize pool presented by Bolt.new

ads via Carbon



Static row height
-----------------

By default, the rows have a height of 52 pixels.
This matches the normal height in the Material Design guidelines.


Use the `rowHeight` prop to change this default value, as shown below:


DeskCommodityTrader NameTrader EmailQuantityD\-2686WheatGeorge Barnes<PERSON>bpaipu@lam.es97,903D\-1121Coffee CLaw<PERSON>ce <PERSON>@wadpov.ug78,859D\-9711<NAME_EMAIL>90,701D\-9544OatsJean Warnersokuseh@gadco.eg7,039D\-3961WheatLucile Hunternijeza@risvazben.iq44,402D\-7650Sugar No.14<PERSON><PERSON><PERSON>@wod.sm58,541D\-6702WheatLester Sandovalkad@dogti.pa58,870D\-1652<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@zeepaiso.nu41,084D\-5510<NAME_EMAIL>74,520D\-9143<NAME_EMAIL>10,082D\-1115OatsDorothy Meyerak@ka.nl78,099D\-6646Cotton No.2Bill Carsonamu@reke.re82,313D\-9275Sugar No.11Tom Goodwindago@mo.fj61,472D\-167<NAME_EMAIL>12,974D\-7515SoybeansSophia Garrettbifu@vitojuoc.fr27,524D\-6509<NAME_EMAIL>7,835D\-3394OatsHarriet McDanielto@rogjafok.mr67,690D\-8466Frozen Concentrated <NAME_EMAIL>45,316Rows per page:

1001–100 of 100

JSTSExpand codeCopy(or Ctrl \+ C)
```
<DataGrid rowHeight={25} {...data} loading={loading} />  

```
\<DataGrid rowHeight\={25} {...data} loading\={loading} /\>Press `Enter` to start editingVariable row height
-------------------

If you need some rows to have different row heights, this can be achieved using the `getRowHeight` prop.
This function is called for each visible row and if the return value is a `number` then that `number` will be set as that row's `rowHeight`.
If the return value is `null` or `undefined`, then the `rowHeight` prop will take effect for the given row.


idusernameage1@hiuru652@nahuf273@vijmaz364@umvav765@buncugi64Rows per page:

1001–5 of 5

JSTSExpand codeCopy(or Ctrl \+ C)
```
<DataGrid
  rows={rows}
  columns={columns}
  getRowHeight={({ id, densityFactor }: GridRowHeightParams) => {
    if ((id as number) % 2 === 0) {
      return 100 * densityFactor;
    }

    return null;
  }}
  slots={{
    toolbar: CustomToolbar,
  }}
  showToolbar
/>  

```
\<DataGrid
 rows\={rows}
 columns\={columns}
 getRowHeight\={({ id, densityFactor }: GridRowHeightParams) \=\> {
 if ((id as number) % 2 \=\=\= 0\) {
 return 100 \* densityFactor;
 }

 return null;
 }}
 slots\={{
 toolbar: CustomToolbar,
 }}
 showToolbar
/\>Press `Enter` to start editing
Changing the Data Grid density does not affect the rows with variable row height.
You can access the density factor from the params provided to the `getRowHeight` prop



Always memoize the function provided to `getRowHeight`.
The Data Grid bases on the referential value of these props to cache their values and optimize the rendering.



```
const getRowHeight = React.useCallback( => { ... }, []);

<DataGridPro getRowHeight={getRowHeight} />

```
CopyCopied(or Ctrl \+ C)
Dynamic row height
------------------

Instead of a fixed row height, you can let the Data Grid calculate the height of each row based on its content.
To do so, return `"auto"` on the function passed to the `getRowHeight` prop.



```
<DataGrid getRowHeight={ => 'auto'} />

```
CopyCopied(or Ctrl \+ C)
The following demo shows this feature in action:


idusernameagebio0@li47Phasellus et ultrices dui. Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum.1@johonec46Phasellus et ultrices dui. Phasellus et ultrices dui.2@kap18Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum. Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum.3@niwpis58Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum in massa nibh.4@eh76Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum. Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum. Vestibulum pulvinar aliquam turpis, ac faucibus risus varius a. Fusce facilisis egestas massa, et eleifend magna imperdiet et.5@fu17Vestibulum pulvinar aliquam turpis, ac faucibus risus varius a. Fusce facilisis egestas massa, et eleifend magna imperdiet et. Vestibulum commodo et odio a laoreet. Vestibulum in massa nibh.6@igufato26Nulla venenatis justo non felis vulputate, eu mollis metus ornare. Fusce facilisis egestas massa, et eleifend magna imperdiet et.Rows per page:

1001–100 of 200

JSTSShow codeThe dynamic row height implementation is based on a lazy approach, which means that the rows are measured as they are rendered.
Because of this, you may see the size of the scrollbar thumb changing during scroll.
This side effect happens because a row height estimation is used while a row is not rendered, then this value is replaced once the true measurement is obtained.
You can configure the estimated value used by passing a function to the `getEstimatedRowHeight` prop.
If not provided, the default row height of `52px` is used as estimation.
It's recommended to pass this prop if the content deviates too much from the default value.



```
<DataGrid getRowHeight={ => 'auto'} getEstimatedRowHeight={ => 200} />

```
CopyCopied(or Ctrl \+ C)
​​idusernameagebio0@raubutom39Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum. Nulla venenatis justo non felis vulputate, eu mollis metus ornare. Fusce facilisis egestas mas view more1@but50Vestibulum in massa nibh. Aliquam dapibus, lorem vel mattis aliquet, purus lorem tincidunt mauris, in blandit quam risus sed ipsum. Vestibulum commodo et odio a laoreet. Nam ullamcorper ligula id cons view more2@mug11Nam ullamcorper ligula id consectetur auctor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum commodo et odio a laoreet. Aliquam dapibus, lorem vel mattis aliquet, purus lorem tinc view more3@ecav52Nam ullamcorper ligula id consectetur auctor. Vestibulum commodo et odio a laoreet. Maecenas non felis venenatis, porta velit quis, consectetur elit. Vestibulum in massa nibh. Nulla venenatis justo no view more4@ojeci27Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus et ultrices dui. Vestibulum in massa nibh. Phasellus et ultrices dui. Lorem  view moreRows per page:

1001–10 of 10

JSTSShow code
When the height of a row is set to `"auto"`, the final height will follow exactly the content size and ignore the density.
Add padding to the cells to increase the space between the content and the cell borders.



```
<DataGrid
  sx={{
    '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': { py: '8px' },
    '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': { py: '15px' },
    '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': { py: '22px' },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
### Column virtualization

By default, the virtualization of the columns is disabled to force all columns to be rendered at the same time and calculate the row height correctly.
However, this can lead to poor performance when rendering a lot of columns.


If you need column virtualization, you can set the `virtualizeColumnsWithAutoRowHeight` prop to `true`.
With this approach, the Data Grid measures the row height based on the visible columns.
However, the row height might change during horizontal scrolling.


1M2M3M4M5M6M7M8M9M10M11M12M13M14M15M16M17M18M0, 1 0, 2 0, 3 0, 4 0, 5 0, 6 0, 7 0, 8 0, 9 0, 10 0, 11 0, 12 0, 13 0, 14 0, 15 0, 16 0, 17 0, 18 1, 1 1, 2 1, 3 1, 4 1, 5 1, 6 1, 7 1, 8 1, 9 1, 10 1, 11 1, 12 1, 13 1, 14 1, 15 1, 16 1, 17 1, 18 2, 1 2, 2 2, 3 2, 4 2, 5 2, 6 2, 7 2, 8 2, 9 2, 10 2, 11 2, 12 2, 13 2, 14 2, 15 2, 16 2, 17 2, 18 3, 1 3, 2 3, 3 3, 4 3, 5 3, 6 3, 7 3, 8 3, 9 3, 10 3, 11 3, 12 3, 13 3, 14 3, 15 3, 16 3, 17 3, 18 4, 1 4, 2 4, 3 4, 4 4, 5 4, 6 4, 7 4, 8 4, 9 4, 10 4, 11 4, 12 4, 13 4, 14 4, 15 4, 16 4, 17 4, 18 5, 1 5, 2 5, 3 5, 4 5, 5 5, 6 5, 7 5, 8 5, 9 5, 10 5, 11 5, 12 5, 13 5, 14 5, 15 5, 16 5, 17 5, 18 6, 1 6, 2 6, 3 6, 4 6, 5 6, 6 6, 7 6, 8 6, 9 6, 10 6, 11 6, 12 6, 13 6, 14 6, 15 6, 16 6, 17 6, 18 7, 1 7, 2 7, 3 7, 4 7, 5 7, 6 7, 7 7, 8 7, 9 7, 10 7, 11 7, 12 7, 13 7, 14 7, 15 7, 16 7, 17 7, 18 8, 1 8, 2 8, 3 8, 4 8, 5 8, 6 8, 7 8, 8 8, 9 8, 10 8, 11 8, 12 8, 13 8, 14 8, 15 8, 16 8, 17 8, 18 9, 1 9, 2 9, 3 9, 4 9, 5 9, 6 9, 7 9, 8 9, 9 9, 10 9, 11 9, 12 9, 13 9, 14 9, 15 9, 16 9, 17 9, 18 10, 1 10, 2 10, 3 10, 4 10, 5 10, 6 10, 7 10, 8 10, 9 10, 10 10, 11 10, 12 10, 13 10, 14 10, 15 10, 16 10, 17 10, 18 11, 1 11, 2 11, 3 11, 4 11, 5 11, 6 11, 7 11, 8 11, 9 11, 10 11, 11 11, 12 11, 13 11, 14 11, 15 11, 16 11, 17 11, 18 12, 1 12, 2 12, 3 12, 4 12, 5 12, 6 12, 7 12, 8 12, 9 12, 10 12, 11 12, 12 12, 13 12, 14 12, 15 12, 16 12, 17 12, 18 13, 1 13, 2 13, 3 13, 4 13, 5 13, 6 13, 7 13, 8 13, 9 13, 10 13, 11 13, 12 13, 13 13, 14 13, 15 13, 16 13, 17 13, 18 14, 1 14, 2 14, 3 14, 4 14, 5 14, 6 14, 7 14, 8 14, 9 14, 10 14, 11 14, 12 14, 13 14, 14 14, 15 14, 16 14, 17 14, 18 15, 1 15, 2 15, 3 15, 4 15, 5 15, 6 15, 7 15, 8 15, 9 15, 10 15, 11 15, 12 15, 13 15, 14 15, 15 15, 16 15, 17 15, 18 16, 1 16, 2 16, 3 16, 4 16, 5 16, 6 16, 7 16, 8 16, 9 16, 10 16, 11 16, 12 16, 13 16, 14 16, 15 16, 16 16, 17 16, 18 17, 1 17, 2 17, 3 17, 4 17, 5 17, 6 17, 7 17, 8 17, 9 17, 10 17, 11 17, 12 17, 13 17, 14 17, 15 17, 16 17, 17 17, 18 18, 1 18, 2 18, 3 18, 4 18, 5 18, 6 18, 7 18, 8 18, 9 18, 10 18, 11 18, 12 18, 13 18, 14 18, 15 18, 16 18, 17 18, 18 19, 1 19, 2 19, 3 19, 4 19, 5 19, 6 19, 7 19, 8 19, 9 19, 10 19, 11 19, 12 19, 13 19, 14 19, 15 19, 16 19, 17 19, 18 20, 1 20, 2 20, 3 20, 4 20, 5 20, 6 20, 7 20, 8 20, 9 20, 10 20, 11 20, 12 20, 13 20, 14 20, 15 20, 16 20, 17 20, 18 Rows per page:

1001–100 of 100

JSTSExpand codeCopy(or Ctrl \+ C)
```
<DataGrid
  {...data}
  getRowHeight={ => 'auto'}
  virtualizeColumnsWithAutoRowHeight
/>  

```
\<DataGrid
 {...data}
 getRowHeight\={ \=\> 'auto'}
 virtualizeColumnsWithAutoRowHeight
/\>Press `Enter` to start editingRow density
-----------

Give your users the option to change the default row density to match their preferences—compact, standard, or comfortable.
Density is calculated based on the `rowHeight` and/or `columnHeaderHeight` props, when present.
See Density for details.


Row spacing
-----------

You can use the `getRowSpacing` prop to increase the spacing between rows.
This prop is called with a `GridRowSpacingParams` object.



```
const getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {
  return {
    top: params.isFirstVisible ? 0 : 5,
    bottom: params.isLastVisible ? 0 : 5,
  };
}, []);

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailQuantityD\-8697OatsGavin Robertsonsi@ma.gs30,512D\-8770Cotton No.2Lucinda Olsonna@imuwuguc.cd58,105D\-4834Sugar No.14Billy Williamsonkizoh@ji.th81,171D\-9791MilkLora Websterzi@culoham.me99,512D\-3939<NAME_EMAIL>23,748D\-2357CornLandon Moorebamgu@fodo.lt96,327D\-1528<NAME_EMAIL>15,649D\-9434CornHarold Jordanfa@jacasfu.cf99,688Rows per page:

1001–100 of 200

JSTSShow codeBy default, setting `getRowSpacing` will change the `marginXXX` CSS properties of each row.
To add a border instead, set `rowSpacingType` to `"border"` and customize the color and style.



```
<DataGrid
  getRowSpacing={...}
  rowSpacingType="border"
  sx={{ '& .MuiDataGrid-row': { borderTopColor: 'yellow', borderTopStyle: 'solid' } }}
/>

```
CopyCopied(or Ctrl \+ C)

Adding a bottom margin or border to rows that also have a detail panel is not recommended because the detail panel relies on the bottom margin to work.


As an alternative, you can use the top spacing to define the space between rows.
It's easier to always increase the next row spacing no matter if the detail panel is expanded or not, but you can use `gridDetailPanelExpandedRowIdsSelector` to apply a spacing depending on the open state.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Row updatesRow spanning

---

•

Blog•

Store
