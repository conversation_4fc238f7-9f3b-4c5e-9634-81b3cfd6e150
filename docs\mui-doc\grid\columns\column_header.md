Data Grid \- Column header
==========================

Customize your columns header.


Develop and launch modern apps with MongoDB Atlas, a resilient data platform.

ads via Carbon



You can configure the headers with:


* `headerName`: The title of the column rendered in the column header cell.
* `description`: The description of the column rendered as tooltip if the column header name is not fully displayed.


UsernameAge@MUI20Rows per page:

1001–1 of 1

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';

const rows = [
  {
    id: 1,
    username: '@MUI',
    age: 20,
  },
];

export default function HeaderColumnsGrid {
  return (
    <div style={{ height: 250, width: '100%' }}>
      <DataGrid
        columns={[
          {
            field: 'username',
            headerName: 'Username',
            description:
              'The identification used by the person with access to the online service.',
          },
          { field: 'age', headerName: 'Age' },
        ]}
        rows={rows}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';

const rows \= \[
 {
 id: 1,
 username: '@MUI',
 age: 20,
 },
];

export default function HeaderColumnsGrid {
 return (
 \<div style\={{ height: 250, width: '100%' }}\>
 \<DataGrid
 columns\={\[
 {
 field: 'username',
 headerName: 'Username',
 description:
 'The identification used by the person with access to the online service.',
 },
 { field: 'age', headerName: 'Age' },
 ]}
 rows\={rows}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonCustom header renderer
----------------------

You can customize the look of each header with the `renderHeader` method.
It takes precedence over the `headerName` property.



```
const columns: GridColDef[] = [
  {
    field: 'date',
    width: 150,
    type: 'date',
    renderHeader: (params: GridColumnHeaderParams) => (
      <strong>
        {'Birthday '}
        <span role="img" aria-label="enjoy">
          🎂
        </span>
      </strong>
    ),
  },
];

```
CopyCopied(or Ctrl \+ C)
**Birthday 🎂**1979/1/11984/2/11992/3/1Rows per page:

1001–3 of 3

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';

const columns: GridColDef[] = [
  {
    field: 'date',
    width: 150,
    type: 'date',
    renderHeader:  => (
      <strong>
        {'Birthday '}
        <span role="img" aria-label="enjoy">
          🎂
        </span>
      </strong>
    ),
  },
];

const rows = [
  {
    id: 1,
    date: new Date(1979, 0, 1),
  },
  {
    id: 2,
    date: new Date(1984, 1, 1),
  },
  {
    id: 3,
    date: new Date(1992, 2, 1),
  },
];

export default function RenderHeaderGrid {
  return (
    <div style={{ height: 300, width: '100%' }}>
      <DataGrid rows={rows} columns={columns} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColDef } from '@mui/x\-data\-grid';

const columns: GridColDef\[] \= \[
 {
 field: 'date',
 width: 150,
 type: 'date',
 renderHeader:  \=\> (
 \<strong\>
 {'Birthday '}
 \<span role\="img" aria\-label\="enjoy"\>
 🎂
 \</span\>
 \</strong\>
 ),
 },
];

const rows \= \[
 {
 id: 1,
 date: new Date(1979, 0, 1\),
 },
 {
 id: 2,
 date: new Date(1984, 1, 1\),
 },
 {
 id: 3,
 date: new Date(1992, 2, 1\),
 },
];

export default function RenderHeaderGrid {
 return (
 \<div style\={{ height: 300, width: '100%' }}\>
 \<DataGrid rows\={rows} columns\={columns} /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by CarbonHeader height
-------------

By default, column headers have a height of 56 pixels. This matches the height from the Material Design guidelines.


The `columnHeaderHeight` prop can be used to override the default value.


UsernameAge@MUI20Rows per page:

1001–1 of 1

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';

const rows = [
  {
    id: 1,
    username: '@MUI',
    age: 20,
  },
];

const columns: GridColDef[] = [
  {
    field: 'username',
    headerName: 'Username',
    description:
      'The identification used by the person with access to the online service.',
  },
  { field: 'age', headerName: 'Age' },
];

export default function HeaderHeight {
  return (
    <div style={{ height: 250, width: '100%' }}>
      <DataGrid columns={columns} rows={rows} columnHeaderHeight={36} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColDef } from '@mui/x\-data\-grid';

const rows \= \[
 {
 id: 1,
 username: '@MUI',
 age: 20,
 },
];

const columns: GridColDef\[] \= \[
 {
 field: 'username',
 headerName: 'Username',
 description:
 'The identification used by the person with access to the online service.',
 },
 { field: 'age', headerName: 'Age' },
];

export default function HeaderHeight {
 return (
 \<div style\={{ height: 250, width: '100%' }}\>
 \<DataGrid columns\={columns} rows\={rows} columnHeaderHeight\={36} /\>
 \</div\>
 );
}Press `Enter` to start editing**AG Grid** \- Add JavaScript data grids to your app in minutes with AG Grid: Fast, flexible, and open source.ad by CarbonStyling header
--------------

You can check the styling header section for more information.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Custom columnsColumn menu

---

•

Blog•

Store
