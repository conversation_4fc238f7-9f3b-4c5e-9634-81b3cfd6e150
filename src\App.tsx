import React, { useEffect } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { routes } from '@/routes';
import DarkModeProvider from '@/components/common/DarkModeProvider';
import { initSystemDictionary } from '@/utils/initDictionary';
import theme from '@/theme';
import './index.css';

// Create React Query client
const queryClient = new QueryClient();

// Create router
const router = createBrowserRouter(routes);

function App() {
  // Initialize system dictionary cache on app startup
  useEffect(() => {
    initSystemDictionary();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
          <DarkModeProvider>
            <RouterProvider router={router} />
          </DarkModeProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
