1. autogen是微软agent开发框架，可以用来开发agent，agent是一种可以与用户交互的程序，可以用来完成一些任务，比如回答问题，生成代码，生成图片等。
2. autogen作为组件形式存在于项目中
3. 项目中所有的接口请求都以标准的接口请求函数形式存在,但是目前后端项目还未开始建设,所以模拟返回数据即可,如果需要修改则直接修改接口请求函数即可
3. 系统先做一个登录页面,登录页面目前只有用户名和密码两个输入框,以及一个登录按钮,登录按钮点击后会跳转到首页,首页目前先不做任何处理,直接跳转到首页即可
4. 登录接口的请求函数为src/controllers/base/user.ts中的login函数,目前返回数据为模拟数据,如果需要修改则直接修改该函数即可
5. 首页实际上由左侧的菜单树和右侧页面内容区域组成,整个页面随着用户分辨率等比缩放.组件与组件之间的间距,大小,位置等全部等比缩放
6. 菜单树的数据由src/controllers/base/menu.ts中的getMenu函数提供,目前返回数据为模拟数据,如果需要修改则直接修改该函数即可
7. 菜单树支持多级树结构,而不是仅仅两层的树结构,当前项目中目前可能只使用到两层,但是按照无限层级来进行设计
8. 菜单分为目录和页面两种类型,目录下面可以有目录和页面,页面下面不能再有目录和页面,页面点击之后右侧内容区域则加载对应的内容
9. 目录和页面目前没有图标,但是需要支持后面直接添加图标
10. 以下为项目的菜单列表:
    模型设置:
        文本模型
        文->图模型
        文->语音模型
        文->视频模型
        图->文模型
        语音->文模型
        视频->文模型
        图像模型
        召回模型
        向量模型
        专业模型
        共享设置
        模型市场
    数据管理:
        向量数据库
        Redis数据库
        图数据库
        关系数据库
        共享设置
    MCP管理:
        MCP列表
        MCP市场
        共享设置
    工具管理:
        工具列表
        工具市场
        共享设置
    Flow管理:
        langflow
        flowise
        make
        coze
        dify
        fastgpt
        autogen
        adk
        ango         
    RAG管理:
        知识库
        文档库
        知识图谱
        共享设置
    Agent管理:
        Agent团队
        Agent列表
        中止函数
        Agent市场
        共享设置
    设备管理:
        设备列表
        共享设置    
    个人中心:
        个人中心
        积分兑换
        数据分析
        接入文档
11. 先创建菜单,采用动态路由,模拟post从后端接口请求获取菜单的数据,菜单的点击内容页面暂时为空白页面
12. 与后端请求全部默认为post,响应的数据格式统一为:{'code':'0','data':{},'msg':''},其中code为0表示成功,为1表示失败,为2表示未登录,为3表示无权限,为4表示系统错误,为5表示业务错误,为6表示参数错误,为7表示数据不存在,为8表示数据已存在,为9表示数据格式错误,为10表示数据类型错误,为11表示数据长度错误,为12表示数据范围错误,其中 data 可能是json对象也可能是数组对象
13. 身份验证方式为 在post请求头上统一添加token进行校验
14. 流程组件使用 flflowgram.ai


创建一个系统字典缓存,这个缓存要模拟从接口中请求数据,字典缓存的内容为:
1. 模型类型
2. 模型状态
3. 数据库类型
4. 数据库状态
5. MCP状态
6. 工具类型
7. 工具状态
8. Flow类型
9. Flow状态
10. RAG类型
11. RAG状态
12. Agent类型
13. Agent状态
14. 设备类型
15. 设备状态

数据格式为:{label: '', value: ''},统一为这样的格式

表格规范:
1.列表有边框,内部也有边框,表格内容字体:微软雅黑,表格内容字号:12px,边框颜色:#E8E8E8,表格头部背景颜色:#F9F9F9,表格头部字体颜色:#606266,表格头部字号:14px,分页组件有: 下一页,上一页,首页,末页,每页显示条数,当前页数,总页数,总条数,每页显示条数下拉选择框,点击分页组件可以模拟发送数据请求,并加载最新数据

组件规范:
1.使用material-ui的组件库内部组件,ui中的组件使用material-ui组件,对于material-ui中不存在的组件,则使用tailwindcss进行样式编写

移除组件:
把项目中的radix-ui组件全部删除,添加material-ui组件,并且把UI目录的button,checkbox,dialog,select,switch,table,textarea组件全部移除,使用material-ui的组件进行替换

1.文本模型
页面是一个列表,列表中每一项包含模型名称,模型描述,模型类型(文本型),模型状态,模型添加时间,模型操作(编辑,删除,部署)等信息,其中模型状态是一个switch按钮,支持点击事件,点击之后会获取当前row对象,并把数据发送给后端,需要写好模拟发送代码,
页面右上角有一个添加模型的按钮,点击后会弹出一个添加模型的弹窗,弹窗中包含模型名称,模型描述,模型类型,模型状态,是否支持函数调用,这几个字段,对话框右下角有取消和确认按钮,取消则关闭对话框,确认则将数据发送给后端,需要写好模拟发送代码,





开发计划:
1. 创建一个系统字典缓存,这个缓存数据结构为:{'test_code':[{'label':'','value':''},{'label':'','value':''}],'test_code2':[{'label':'','value':''},{'label':'','value':''}]'}
1. 封装select组件,支持字典配置和接口配置,接口配置为:
