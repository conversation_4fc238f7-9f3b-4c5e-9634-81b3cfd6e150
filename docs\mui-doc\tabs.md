Tabs
====

Tabs make it easy to explore and switch between different views.


Dekáf Coffee Roasters: You won't believe it's decaf. That's the point. Use code DK25 for 25% off.

ads via Carbon



Tabs organize and allow navigation between groups of content that are related and at the same level of hierarchy.


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Introduction
------------

Tabs are implemented using a collection of related components:


* `<Tab />` \- the tab element itself. Clicking on a tab displays its corresponding panel.
* `<Tabs />` \- the container that houses the tabs. Responsible for handling focus and keyboard navigation between tabs.


Item OneItem TwoItem ThreeItem OneJSTSExpand codeCopy(or Ctrl \+ C)
```
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
  <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
    <Tab label="Item One" {...a11yProps(0)} />
    <Tab label="Item Two" {...a11yProps(1)} />
    <Tab label="Item Three" {...a11yProps(2)} />
  </Tabs>
</Box>
<CustomTabPanel value={value} index={0}>
  Item One
</CustomTabPanel>
<CustomTabPanel value={value} index={1}>
  Item Two
</CustomTabPanel>
<CustomTabPanel value={value} index={2}>
  Item Three
</CustomTabPanel>  

```
\<Box sx\={{ borderBottom: 1, borderColor: 'divider' }}\>
 \<Tabs value\={value} onChange\={handleChange} aria\-label\="basic tabs example"\>
 \<Tab label\="Item One" {...a11yProps(0\)} /\>
 \<Tab label\="Item Two" {...a11yProps(1\)} /\>
 \<Tab label\="Item Three" {...a11yProps(2\)} /\>
 \</Tabs\>
\</Box\>
\<CustomTabPanel value\={value} index\={0}\>
 Item One
\</CustomTabPanel\>
\<CustomTabPanel value\={value} index\={1}\>
 Item Two
\</CustomTabPanel\>
\<CustomTabPanel value\={value} index\={2}\>
 Item Three
\</CustomTabPanel\>Press `Enter` to start editingBasics
------


```
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

```
CopyCopied(or Ctrl \+ C)
Experimental API
----------------

`@mui/lab` offers utility components that inject props to implement accessible tabs
following WAI\-ARIA Authoring Practices:


* `<TabList />` \- the container that houses the tabs. Responsible for handling focus and keyboard navigation between tabs.
* `<TabPanel />` \- the card that hosts the content associated with a tab.
* `<TabContext />` \- the top\-level component that wraps the Tab List and Tab Panel components.


Item OneItem TwoItem ThreeItem OneJSTSExpand codeCopy(or Ctrl \+ C)
```
<TabContext value={value}>
  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
    <TabList onChange={handleChange} aria-label="lab API tabs example">
      <Tab label="Item One" value="1" />
      <Tab label="Item Two" value="2" />
      <Tab label="Item Three" value="3" />
    </TabList>
  </Box>
  <TabPanel value="1">Item One</TabPanel>
  <TabPanel value="2">Item Two</TabPanel>
  <TabPanel value="3">Item Three</TabPanel>
</TabContext>  

```
\<TabContext value\={value}\>
 \<Box sx\={{ borderBottom: 1, borderColor: 'divider' }}\>
 \<TabList onChange\={handleChange} aria\-label\="lab API tabs example"\>
 \<Tab label\="Item One" value\="1" /\>
 \<Tab label\="Item Two" value\="2" /\>
 \<Tab label\="Item Three" value\="3" /\>
 \</TabList\>
 \</Box\>
 \<TabPanel value\="1"\>Item One\</TabPanel\>
 \<TabPanel value\="2"\>Item Two\</TabPanel\>
 \<TabPanel value\="3"\>Item Three\</TabPanel\>
\</TabContext\>Press `Enter` to start editingWrapped labels
--------------

Long labels will automatically wrap on tabs.
If the label is too long for the tab, it will overflow, and the text will not be visible.


New Arrivals in the Longest Text of Nonfiction that should appear in the next lineItem TwoItem ThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  aria-label="wrapped label tabs example"
>
  <Tab
    value="one"
    label="New Arrivals in the Longest Text of Nonfiction that should appear in the next line"
    wrapped
  />
  <Tab value="two" label="Item Two" />
  <Tab value="three" label="Item Three" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 aria\-label\="wrapped label tabs example"
\>
 \<Tab
 value\="one"
 label\="New Arrivals in the Longest Text of Nonfiction that should appear in the next line"
 wrapped
 /\>
 \<Tab value\="two" label\="Item Two" /\>
 \<Tab value\="three" label\="Item Three" /\>
\</Tabs\>Press `Enter` to start editingColored tab
-----------

Item OneItem TwoItem ThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  textColor="secondary"
  indicatorColor="secondary"
  aria-label="secondary tabs example"
>
  <Tab value="one" label="Item One" />
  <Tab value="two" label="Item Two" />
  <Tab value="three" label="Item Three" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 textColor\="secondary"
 indicatorColor\="secondary"
 aria\-label\="secondary tabs example"
\>
 \<Tab value\="one" label\="Item One" /\>
 \<Tab value\="two" label\="Item Two" /\>
 \<Tab value\="three" label\="Item Three" /\>
\</Tabs\>Press `Enter` to start editingDisabled tab
------------

A tab can be disabled by setting the `disabled` prop.


ActiveDisabledActiveJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs value={value} onChange={handleChange} aria-label="disabled tabs example">
  <Tab label="Active" />
  <Tab label="Disabled" disabled />
  <Tab label="Active" />
</Tabs>  

```
\<Tabs value\={value} onChange\={handleChange} aria\-label\="disabled tabs example"\>
 \<Tab label\="Active" /\>
 \<Tab label\="Disabled" disabled /\>
 \<Tab label\="Active" /\>
\</Tabs\>Press `Enter` to start editingFixed tabs
----------

Fixed tabs should be used with a limited number of tabs, and when a consistent placement will aid muscle memory.


### Full width

The `variant="fullWidth"` prop should be used for smaller views.


Item OneItem TwoItem ThreeItem One

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { useTheme } from '@mui/material/styles';
import AppBar from '@mui/material/AppBar';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `full-width-tab-${index}`,
    'aria-controls': `full-width-tabpanel-${index}`,
  };
}

export default function FullWidthTabs {
  const theme = useTheme;
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ bgcolor: 'background.paper', width: 500 }}>
      <AppBar position="static">
        <Tabs
          value={value}
          onChange={handleChange}
          indicatorColor="secondary"
          textColor="inherit"
          variant="fullWidth"
          aria-label="full width tabs example"
        >
          <Tab label="Item One" {...a11yProps(0)} />
          <Tab label="Item Two" {...a11yProps(1)} />
          <Tab label="Item Three" {...a11yProps(2)} />
        </Tabs>
      </AppBar>
      <TabPanel value={value} index={0} dir={theme.direction}>
        Item One
      </TabPanel>
      <TabPanel value={value} index={1} dir={theme.direction}>
        Item Two
      </TabPanel>
      <TabPanel value={value} index={2} dir={theme.direction}>
        Item Three
      </TabPanel>
    </Box>
  );
}  

```
import \* as React from 'react';
import { useTheme } from '@mui/material/styles';
import AppBar from '@mui/material/AppBar';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

interface TabPanelProps {
 children?: React.ReactNode;
 dir?: string;
 index: number;
 value: number;
}

function TabPanel(props: TabPanelProps) {
 const { children, value, index, ...other } \= props;

 return (
 \<div
 role\="tabpanel"
 hidden\={value !\=\= index}
 id\={\`full\-width\-tabpanel\-${index}\`}
 aria\-labelledby\={\`full\-width\-tab\-${index}\`}
 {...other}
 \>
 {value \=\=\= index \&\& (
 \<Box sx\={{ p: 3 }}\>
 \<Typography\>{children}\</Typography\>
 \</Box\>
 )}
 \</div\>
 );
}

function a11yProps(index: number) {
 return {
 id: \`full\-width\-tab\-${index}\`,
 'aria\-controls': \`full\-width\-tabpanel\-${index}\`,
 };
}

export default function FullWidthTabs {
 const theme \= useTheme;
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box sx\={{ bgcolor: 'background.paper', width: 500 }}\>
 \<AppBar position\="static"\>
 \<Tabs
 value\={value}
 onChange\={handleChange}
 indicatorColor\="secondary"
 textColor\="inherit"
 variant\="fullWidth"
 aria\-label\="full width tabs example"
 \>
 \<Tab label\="Item One" {...a11yProps(0\)} /\>
 \<Tab label\="Item Two" {...a11yProps(1\)} /\>
 \<Tab label\="Item Three" {...a11yProps(2\)} /\>
 \</Tabs\>
 \</AppBar\>
 \<TabPanel value\={value} index\={0} dir\={theme.direction}\>
 Item One
 \</TabPanel\>
 \<TabPanel value\={value} index\={1} dir\={theme.direction}\>
 Item Two
 \</TabPanel\>
 \<TabPanel value\={value} index\={2} dir\={theme.direction}\>
 Item Three
 \</TabPanel\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by Carbon### Centered

The `centered` prop should be used for larger views.


Item OneItem TwoItem ThreeJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs value={value} onChange={handleChange} centered>
  <Tab label="Item One" />
  <Tab label="Item Two" />
  <Tab label="Item Three" />
</Tabs>  

```
\<Tabs value\={value} onChange\={handleChange} centered\>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
\</Tabs\>Press `Enter` to start editingScrollable tabs
---------------

### Automatic scroll buttons

Use the `variant="scrollable"` and `scrollButtons="auto"` props to display left and right scroll buttons on desktop that are hidden on mobile:


Item OneItem TwoItem ThreeItem FourItem FiveItem SixItem SevenJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  variant="scrollable"
  scrollButtons="auto"
  aria-label="scrollable auto tabs example"
>
  <Tab label="Item One" />
  <Tab label="Item Two" />
  <Tab label="Item Three" />
  <Tab label="Item Four" />
  <Tab label="Item Five" />
  <Tab label="Item Six" />
  <Tab label="Item Seven" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 variant\="scrollable"
 scrollButtons\="auto"
 aria\-label\="scrollable auto tabs example"
\>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \<Tab label\="Item Four" /\>
 \<Tab label\="Item Five" /\>
 \<Tab label\="Item Six" /\>
 \<Tab label\="Item Seven" /\>
\</Tabs\>Press `Enter` to start editing### Forced scroll buttons

Apply `scrollButtons={true}` and the `allowScrollButtonsMobile` prop to display the left and right scroll buttons on all viewports:


Item OneItem TwoItem ThreeItem FourItem FiveItem SixItem SevenJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  variant="scrollable"
  scrollButtons
  allowScrollButtonsMobile
  aria-label="scrollable force tabs example"
>
  <Tab label="Item One" />
  <Tab label="Item Two" />
  <Tab label="Item Three" />
  <Tab label="Item Four" />
  <Tab label="Item Five" />
  <Tab label="Item Six" />
  <Tab label="Item Seven" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 variant\="scrollable"
 scrollButtons
 allowScrollButtonsMobile
 aria\-label\="scrollable force tabs example"
\>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \<Tab label\="Item Four" /\>
 \<Tab label\="Item Five" /\>
 \<Tab label\="Item Six" /\>
 \<Tab label\="Item Seven" /\>
\</Tabs\>Press `Enter` to start editingIf you want to make sure the buttons are always visible, you should customize the opacity.



```
.MuiTabs-scrollButtons.Mui-disabled {
  opacity: 0.3;
}

```
CopyCopied(or Ctrl \+ C)
Item OneItem TwoItem ThreeItem FourItem FiveItem SixItem SevenJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

export default function ScrollableTabsButtonVisible {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{
        flexGrow: 1,
        maxWidth: { xs: 320, sm: 480 },
        bgcolor: 'background.paper',
      }}
    >
      <Tabs
        value={value}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons
        aria-label="visible arrows tabs example"
        sx={{
          [`& .${tabsClasses.scrollButtons}`]: {
            '&.Mui-disabled': { opacity: 0.3 },
          },
        }}
      >
        <Tab label="Item One" />
        <Tab label="Item Two" />
        <Tab label="Item Three" />
        <Tab label="Item Four" />
        <Tab label="Item Five" />
        <Tab label="Item Six" />
        <Tab label="Item Seven" />
      </Tabs>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

export default function ScrollableTabsButtonVisible {
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box
 sx\={{
 flexGrow: 1,
 maxWidth: { xs: 320, sm: 480 },
 bgcolor: 'background.paper',
 }}
 \>
 \<Tabs
 value\={value}
 onChange\={handleChange}
 variant\="scrollable"
 scrollButtons
 aria\-label\="visible arrows tabs example"
 sx\={{
 \[\`\& .${tabsClasses.scrollButtons}\`]: {
 '\&.Mui\-disabled': { opacity: 0\.3 },
 },
 }}
 \>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \<Tab label\="Item Four" /\>
 \<Tab label\="Item Five" /\>
 \<Tab label\="Item Six" /\>
 \<Tab label\="Item Seven" /\>
 \</Tabs\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by Carbon### Prevent scroll buttons

Left and right scroll buttons are never be presented with `scrollButtons={false}`.
All scrolling must be initiated through user agent scrolling mechanisms (for example left/right swipe, shift mouse wheel, etc.)


Item OneItem TwoItem ThreeItem FourItem FiveItem SixItem SevenJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  variant="scrollable"
  scrollButtons={false}
  aria-label="scrollable prevent tabs example"
>
  <Tab label="Item One" />
  <Tab label="Item Two" />
  <Tab label="Item Three" />
  <Tab label="Item Four" />
  <Tab label="Item Five" />
  <Tab label="Item Six" />
  <Tab label="Item Seven" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 variant\="scrollable"
 scrollButtons\={false}
 aria\-label\="scrollable prevent tabs example"
\>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \<Tab label\="Item Four" /\>
 \<Tab label\="Item Five" /\>
 \<Tab label\="Item Six" /\>
 \<Tab label\="Item Seven" /\>
\</Tabs\>Press `Enter` to start editingCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


Tab 1Tab 2Tab 3WorkflowsDatasetsConnectionsJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

const AntTabs = styled(Tabs)({
  borderBottom: '1px solid #e8e8e8',
  '& .MuiTabs-indicator': {
    backgroundColor: '#1890ff',
  },
});

const AntTab = styled((props: StyledTabProps) => <Tab disableRipple {...props} />)(
  ({ theme }) => ({
    textTransform: 'none',
    minWidth: 0,
    [theme.breakpoints.up('sm')]: {
      minWidth: 0,
    },
    fontWeight: theme.typography.fontWeightRegular,
    marginRight: theme.spacing(1),
    color: 'rgba(0, 0, 0, 0.85)',
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:hover': {
      color: '#40a9ff',
      opacity: 1,
    },
    '&.Mui-selected': {
      color: '#1890ff',
      fontWeight: theme.typography.fontWeightMedium,
    },
    '&.Mui-focusVisible': {
      backgroundColor: '#d1eaff',
    },
  }),
);

interface StyledTabsProps {
  children?: React.ReactNode;
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const StyledTabs = styled((props: StyledTabsProps) => (
  <Tabs
    {...props}
    TabIndicatorProps={{ children: <span className="MuiTabs-indicatorSpan" /> }}
  />
))({
  '& .MuiTabs-indicator': {
    display: 'flex',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  '& .MuiTabs-indicatorSpan': {
    maxWidth: 40,
    width: '100%',
    backgroundColor: '#635ee7',
  },
});

interface StyledTabProps {
  label: string;
}

const StyledTab = styled((props: StyledTabProps) => (
  <Tab disableRipple {...props} />
))(({ theme }) => ({
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightRegular,
  fontSize: theme.typography.pxToRem(15),
  marginRight: theme.spacing(1),
  color: 'rgba(255, 255, 255, 0.7)',
  '&.Mui-selected': {
    color: '#fff',
  },
  '&.Mui-focusVisible': {
    backgroundColor: 'rgba(100, 95, 228, 0.32)',
  },
}));

export default function CustomizedTabs {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ bgcolor: '#fff' }}>
        <AntTabs value={value} onChange={handleChange} aria-label="ant example">
          <AntTab label="Tab 1" />
          <AntTab label="Tab 2" />
          <AntTab label="Tab 3" />
        </AntTabs>
        <Box sx={{ p: 3 }} />
      </Box>
      <Box sx={{ bgcolor: '#2e1534' }}>
        <StyledTabs
          value={value}
          onChange={handleChange}
          aria-label="styled tabs example"
        >
          <StyledTab label="Workflows" />
          <StyledTab label="Datasets" />
          <StyledTab label="Connections" />
        </StyledTabs>
        <Box sx={{ p: 3 }} />
      </Box>
    </Box>
  );
}  

```
import \* as React from 'react';
import { styled } from '@mui/material/styles';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

const AntTabs \= styled(Tabs)({
 borderBottom: '1px solid \#e8e8e8',
 '\& .MuiTabs\-indicator': {
 backgroundColor: '\#1890ff',
 },
});

const AntTab \= styled((props: StyledTabProps) \=\> \<Tab disableRipple {...props} /\>)(
 ({ theme }) \=\> ({
 textTransform: 'none',
 minWidth: 0,
 \[theme.breakpoints.up('sm')]: {
 minWidth: 0,
 },
 fontWeight: theme.typography.fontWeightRegular,
 marginRight: theme.spacing(1\),
 color: 'rgba(0, 0, 0, 0\.85\)',
 fontFamily: \[
 '\-apple\-system',
 'BlinkMacSystemFont',
 '"Segoe UI"',
 'Roboto',
 '"Helvetica Neue"',
 'Arial',
 'sans\-serif',
 '"Apple Color Emoji"',
 '"Segoe UI Emoji"',
 '"Segoe UI Symbol"',
 ].join(','),
 '\&:hover': {
 color: '\#40a9ff',
 opacity: 1,
 },
 '\&.Mui\-selected': {
 color: '\#1890ff',
 fontWeight: theme.typography.fontWeightMedium,
 },
 '\&.Mui\-focusVisible': {
 backgroundColor: '\#d1eaff',
 },
 }),
);

interface StyledTabsProps {
 children?: React.ReactNode;
 value: number;
 onChange: (event: React.SyntheticEvent, newValue: number) \=\> void;
}

const StyledTabs \= styled((props: StyledTabsProps) \=\> (
 \<Tabs
 {...props}
 TabIndicatorProps\={{ children: \<span className\="MuiTabs\-indicatorSpan" /\> }}
 /\>
))({
 '\& .MuiTabs\-indicator': {
 display: 'flex',
 justifyContent: 'center',
 backgroundColor: 'transparent',
 },
 '\& .MuiTabs\-indicatorSpan': {
 maxWidth: 40,
 width: '100%',
 backgroundColor: '\#635ee7',
 },
});

interface StyledTabProps {
 label: string;
}

const StyledTab \= styled((props: StyledTabProps) \=\> (
 \<Tab disableRipple {...props} /\>
))(({ theme }) \=\> ({
 textTransform: 'none',
 fontWeight: theme.typography.fontWeightRegular,
 fontSize: theme.typography.pxToRem(15\),
 marginRight: theme.spacing(1\),
 color: 'rgba(255, 255, 255, 0\.7\)',
 '\&.Mui\-selected': {
 color: '\#fff',
 },
 '\&.Mui\-focusVisible': {
 backgroundColor: 'rgba(100, 95, 228, 0\.32\)',
 },
}));

export default function CustomizedTabs {
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Box sx\={{ bgcolor: '\#fff' }}\>
 \<AntTabs value\={value} onChange\={handleChange} aria\-label\="ant example"\>
 \<AntTab label\="Tab 1" /\>
 \<AntTab label\="Tab 2" /\>
 \<AntTab label\="Tab 3" /\>
 \</AntTabs\>
 \<Box sx\={{ p: 3 }} /\>
 \</Box\>
 \<Box sx\={{ bgcolor: '\#2e1534' }}\>
 \<StyledTabs
 value\={value}
 onChange\={handleChange}
 aria\-label\="styled tabs example"
 \>
 \<StyledTab label\="Workflows" /\>
 \<StyledTab label\="Datasets" /\>
 \<StyledTab label\="Connections" /\>
 \</StyledTabs\>
 \<Box sx\={{ p: 3 }} /\>
 \</Box\>
 \</Box\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


Vertical tabs
-------------

To make vertical tabs instead of default horizontal ones, there is `orientation="vertical"`:


Item OneItem TwoItem ThreeItem FourItem FiveItem SixItem SevenItem One

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `vertical-tab-${index}`,
    'aria-controls': `vertical-tabpanel-${index}`,
  };
}

export default function VerticalTabs {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{ flexGrow: 1, bgcolor: 'background.paper', display: 'flex', height: 224 }}
    >
      <Tabs
        orientation="vertical"
        variant="scrollable"
        value={value}
        onChange={handleChange}
        aria-label="Vertical tabs example"
        sx={{ borderRight: 1, borderColor: 'divider' }}
      >
        <Tab label="Item One" {...a11yProps(0)} />
        <Tab label="Item Two" {...a11yProps(1)} />
        <Tab label="Item Three" {...a11yProps(2)} />
        <Tab label="Item Four" {...a11yProps(3)} />
        <Tab label="Item Five" {...a11yProps(4)} />
        <Tab label="Item Six" {...a11yProps(5)} />
        <Tab label="Item Seven" {...a11yProps(6)} />
      </Tabs>
      <TabPanel value={value} index={0}>
        Item One
      </TabPanel>
      <TabPanel value={value} index={1}>
        Item Two
      </TabPanel>
      <TabPanel value={value} index={2}>
        Item Three
      </TabPanel>
      <TabPanel value={value} index={3}>
        Item Four
      </TabPanel>
      <TabPanel value={value} index={4}>
        Item Five
      </TabPanel>
      <TabPanel value={value} index={5}>
        Item Six
      </TabPanel>
      <TabPanel value={value} index={6}>
        Item Seven
      </TabPanel>
    </Box>
  );
}  

```
import \* as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

interface TabPanelProps {
 children?: React.ReactNode;
 index: number;
 value: number;
}

function TabPanel(props: TabPanelProps) {
 const { children, value, index, ...other } \= props;

 return (
 \<div
 role\="tabpanel"
 hidden\={value !\=\= index}
 id\={\`vertical\-tabpanel\-${index}\`}
 aria\-labelledby\={\`vertical\-tab\-${index}\`}
 {...other}
 \>
 {value \=\=\= index \&\& (
 \<Box sx\={{ p: 3 }}\>
 \<Typography\>{children}\</Typography\>
 \</Box\>
 )}
 \</div\>
 );
}

function a11yProps(index: number) {
 return {
 id: \`vertical\-tab\-${index}\`,
 'aria\-controls': \`vertical\-tabpanel\-${index}\`,
 };
}

export default function VerticalTabs {
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box
 sx\={{ flexGrow: 1, bgcolor: 'background.paper', display: 'flex', height: 224 }}
 \>
 \<Tabs
 orientation\="vertical"
 variant\="scrollable"
 value\={value}
 onChange\={handleChange}
 aria\-label\="Vertical tabs example"
 sx\={{ borderRight: 1, borderColor: 'divider' }}
 \>
 \<Tab label\="Item One" {...a11yProps(0\)} /\>
 \<Tab label\="Item Two" {...a11yProps(1\)} /\>
 \<Tab label\="Item Three" {...a11yProps(2\)} /\>
 \<Tab label\="Item Four" {...a11yProps(3\)} /\>
 \<Tab label\="Item Five" {...a11yProps(4\)} /\>
 \<Tab label\="Item Six" {...a11yProps(5\)} /\>
 \<Tab label\="Item Seven" {...a11yProps(6\)} /\>
 \</Tabs\>
 \<TabPanel value\={value} index\={0}\>
 Item One
 \</TabPanel\>
 \<TabPanel value\={value} index\={1}\>
 Item Two
 \</TabPanel\>
 \<TabPanel value\={value} index\={2}\>
 Item Three
 \</TabPanel\>
 \<TabPanel value\={value} index\={3}\>
 Item Four
 \</TabPanel\>
 \<TabPanel value\={value} index\={4}\>
 Item Five
 \</TabPanel\>
 \<TabPanel value\={value} index\={5}\>
 Item Six
 \</TabPanel\>
 \<TabPanel value\={value} index\={6}\>
 Item Seven
 \</TabPanel\>
 \</Box\>
 );
}Press `Enter` to start editing**GetStream.io** \- Build in\-app chat, video \& moderation, faster with enterprise\-grade reliabilityad by CarbonNote that you can restore the scrollbar with `visibleScrollbar`.


Nav tabs
--------

By default, tabs use a `button` element, but you can provide your custom tag or component. Here's an example of implementing tabbed navigation:


Page OnePage TwoPage ThreeJSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

function samePageLinkNavigation(
  event: React.MouseEvent<HTMLAnchorElement, MouseEvent>,
) {
  if (
    event.defaultPrevented ||
    event.button !== 0 || // ignore everything but left-click
    event.metaKey ||
    event.ctrlKey ||
    event.altKey ||
    event.shiftKey
  ) {
    return false;
  }
  return true;
}

interface LinkTabProps {
  label?: string;
  href?: string;
  selected?: boolean;
}

function LinkTab(props: LinkTabProps) {
  return (
    <Tab
      component="a"
      onClick={(event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        // Routing libraries handle this, you can remove the onClick handle when using them.
        if (samePageLinkNavigation(event)) {
          event.preventDefault;
        }
      }}
      aria-current={props.selected && 'page'}
      {...props}
    />
  );
}

export default function NavTabs {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    // event.type can be equal to focus with selectionFollowsFocus.
    if (
      event.type !== 'click' ||
      (event.type === 'click' &&
        samePageLinkNavigation(
          event as React.MouseEvent<HTMLAnchorElement, MouseEvent>,
        ))
    ) {
      setValue(newValue);
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        value={value}
        onChange={handleChange}
        aria-label="nav tabs example"
        role="navigation"
      >
        <LinkTab label="Page One" href="/drafts" />
        <LinkTab label="Page Two" href="/trash" />
        <LinkTab label="Page Three" href="/spam" />
      </Tabs>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

function samePageLinkNavigation(
 event: React.MouseEvent\<HTMLAnchorElement, MouseEvent\>,
) {
 if (
 event.defaultPrevented \|\|
 event.button !\=\= 0 \|\| // ignore everything but left\-click
 event.metaKey \|\|
 event.ctrlKey \|\|
 event.altKey \|\|
 event.shiftKey
 ) {
 return false;
 }
 return true;
}

interface LinkTabProps {
 label?: string;
 href?: string;
 selected?: boolean;
}

function LinkTab(props: LinkTabProps) {
 return (
 \<Tab
 component\="a"
 onClick\={(event: React.MouseEvent\<HTMLAnchorElement, MouseEvent\>) \=\> {
 // Routing libraries handle this, you can remove the onClick handle when using them.
 if (samePageLinkNavigation(event)) {
 event.preventDefault;
 }
 }}
 aria\-current\={props.selected \&\& 'page'}
 {...props}
 /\>
 );
}

export default function NavTabs {
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 // event.type can be equal to focus with selectionFollowsFocus.
 if (
 event.type !\=\= 'click' \|\|
 (event.type \=\=\= 'click' \&\&
 samePageLinkNavigation(
 event as React.MouseEvent\<HTMLAnchorElement, MouseEvent\>,
 ))
 ) {
 setValue(newValue);
 }
 };

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Tabs
 value\={value}
 onChange\={handleChange}
 aria\-label\="nav tabs example"
 role\="navigation"
 \>
 \<LinkTab label\="Page One" /\>
 \<LinkTab label\="Page Two" /\>
 \<LinkTab label\="Page Three" /\>
 \</Tabs\>
 \</Box\>
 );
}Press `Enter` to start editing**Gitlab** \- A single, unified DevSecOps platform is what you need to deliver software faster. Try it for free.ad by Carbon### Third\-party routing library

One frequent use case is to perform navigation on the client only, without an HTTP round\-trip to the server.
The `Tab` component provides the `component` prop to handle this use case.
Here is a more detailed guide.


Icon tabs
---------

Tab labels may be either all icons or all text.


JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import PhoneIcon from '@mui/icons-material/Phone';
import FavoriteIcon from '@mui/icons-material/Favorite';
import PersonPinIcon from '@mui/icons-material/PersonPin';

export default function IconTabs {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Tabs value={value} onChange={handleChange} aria-label="icon tabs example">
      <Tab icon={<PhoneIcon />} aria-label="phone" />
      <Tab icon={<FavoriteIcon />} aria-label="favorite" />
      <Tab icon={<PersonPinIcon />} aria-label="person" />
    </Tabs>
  );
}  

```
import \* as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import PhoneIcon from '@mui/icons\-material/Phone';
import FavoriteIcon from '@mui/icons\-material/Favorite';
import PersonPinIcon from '@mui/icons\-material/PersonPin';

export default function IconTabs {
 const \[value, setValue] \= React.useState(0\);

 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Tabs value\={value} onChange\={handleChange} aria\-label\="icon tabs example"\>
 \<Tab icon\={\<PhoneIcon /\>} aria\-label\="phone" /\>
 \<Tab icon\={\<FavoriteIcon /\>} aria\-label\="favorite" /\>
 \<Tab icon\={\<PersonPinIcon /\>} aria\-label\="person" /\>
 \</Tabs\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonRECENTSFAVORITESNEARBYJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs value={value} onChange={handleChange} aria-label="icon label tabs example">
  <Tab icon={<PhoneIcon />} label="RECENTS" />
  <Tab icon={<FavoriteIcon />} label="FAVORITES" />
  <Tab icon={<PersonPinIcon />} label="NEARBY" />
</Tabs>  

```
\<Tabs value\={value} onChange\={handleChange} aria\-label\="icon label tabs example"\>
 \<Tab icon\={\<PhoneIcon /\>} label\="RECENTS" /\>
 \<Tab icon\={\<FavoriteIcon /\>} label\="FAVORITES" /\>
 \<Tab icon\={\<PersonPinIcon /\>} label\="NEARBY" /\>
\</Tabs\>Press `Enter` to start editing**AG Grid** \- Add JavaScript data grids to your app in minutes with AG Grid: Fast, flexible, and open source.ad by CarbonIcon position
-------------

By default, the icon is positioned at the `top` of a tab. Other supported positions are `start`, `end`, `bottom`.


topstartendbottomJSTSExpand codeCopy(or Ctrl \+ C)
```
<Tabs
  value={value}
  onChange={handleChange}
  aria-label="icon position tabs example"
>
  <Tab icon={<PhoneIcon />} label="top" />
  <Tab icon={<PhoneMissedIcon />} iconPosition="start" label="start" />
  <Tab icon={<FavoriteIcon />} iconPosition="end" label="end" />
  <Tab icon={<PersonPinIcon />} iconPosition="bottom" label="bottom" />
</Tabs>  

```
\<Tabs
 value\={value}
 onChange\={handleChange}
 aria\-label\="icon position tabs example"
\>
 \<Tab icon\={\<PhoneIcon /\>} label\="top" /\>
 \<Tab icon\={\<PhoneMissedIcon /\>} iconPosition\="start" label\="start" /\>
 \<Tab icon\={\<FavoriteIcon /\>} iconPosition\="end" label\="end" /\>
 \<Tab icon\={\<PersonPinIcon /\>} iconPosition\="bottom" label\="bottom" /\>
\</Tabs\>Press `Enter` to start editingAccessibility
-------------

(WAI\-ARIA: 


The following steps are needed in order to provide necessary information for assistive technologies:


1. Label `Tabs` via `aria-label` or `aria-labelledby`.
2. `Tab`s need to be connected to their
corresponding `[role="tabpanel"]` by setting the correct `id`, `aria-controls` and `aria-labelledby`.


An example for the current implementation can be found in the demos on this page. We've also published an experimental API in `@mui/lab` that does not require
extra work.


### Keyboard navigation

The components implement keyboard navigation using the "manual activation" behavior.
If you want to switch to the "selection automatically follows focus" behavior you have to pass `selectionFollowsFocus` to the `Tabs` component.
The WAI\-ARIA authoring practices have a detailed guide on how to decide when to make selection automatically follow focus.


#### Demo

The following two demos only differ in their keyboard navigation behavior.
Focus a tab and navigate with arrow keys to notice the difference, for example `Arrow Left`.



```
/* Tabs where selection follows focus */
<Tabs selectionFollowsFocus />

```
CopyCopied(or Ctrl \+ C)
Item OneItem TwoItem ThreeJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

export default function AccessibleTabs1 {
  const [value, setValue] = React.useState(0);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        onChange={handleChange}
        value={value}
        aria-label="Tabs where selection follows focus"
        selectionFollowsFocus
      >
        <Tab label="Item One" />
        <Tab label="Item Two" />
        <Tab label="Item Three" />
      </Tabs>
    </Box>
  );
}  

```
import \* as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

export default function AccessibleTabs1 {
 const \[value, setValue] \= React.useState(0\);
 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Tabs
 onChange\={handleChange}
 value\={value}
 aria\-label\="Tabs where selection follows focus"
 selectionFollowsFocus
 \>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \</Tabs\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon
```
/* Tabs where each tab needs to be selected manually */
<Tabs />

```
CopyCopied(or Ctrl \+ C)
Item OneItem TwoItem ThreeJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

export default function AccessibleTabs2 {
  const [value, setValue] = React.useState(0);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        onChange={handleChange}
        value={value}
        aria-label="Tabs where each tab needs to be selected manually"
      >
        <Tab label="Item One" />
        <Tab label="Item Two" />
        <Tab label="Item Three" />
      </Tabs>
    </Box>
  );
}  

```
import \* as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

export default function AccessibleTabs2 {
 const \[value, setValue] \= React.useState(0\);
 const handleChange \= (event: React.SyntheticEvent, newValue: number) \=\> {
 setValue(newValue);
 };

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Tabs
 onChange\={handleChange}
 value\={value}
 aria\-label\="Tabs where each tab needs to be selected manually"
 \>
 \<Tab label\="Item One" /\>
 \<Tab label\="Item Two" /\>
 \<Tab label\="Item Three" /\>
 \</Tabs\>
 \</Box\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonUnstyled
--------

Use the Base UI Tabs for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Tab />`
* `<TabContext />`
* `<TabList />`
* `<TabPanel />`
* `<TabScrollButton />`
* `<Tabs />`



