Progress
========

Progress indicators commonly known as spinners, express an unspecified wait time or display the length of a process.


Squarespace tools make it easy to create a beautiful and unique website.

ads via Carbon



Progress indicators inform users about the status of ongoing processes, such as loading an app, submitting a form, or saving updates.


* **Determinate** indicators display how long an operation will take.
* **Indeterminate** indicators visualize an unspecified wait time.


The animations of the components rely on CSS as much as possible to work even before the JavaScript is loaded.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Circular
--------

### Circular indeterminate

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

export default function CircularIndeterminate {
  return (
    <Box sx={{ display: 'flex' }}>
      <CircularProgress />
    </Box>
  );
}  

```
import \* as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

export default function CircularIndeterminate {
 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CircularProgress /\>
 \</Box\>
 );
}Press `Enter` to start editing**CloudBees** \- Don't just wait—conquer downtime. Leverage "The Power of Three" in CloudBees HA for faster delivery.ad by Carbon### Circular color

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularColor {
  return (
    <Stack sx={{ color: 'grey.500' }} spacing={2} direction="row">
      <CircularProgress color="secondary" />
      <CircularProgress color="success" />
      <CircularProgress color="inherit" />
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularColor {
 return (
 \<Stack sx\={{ color: 'grey.500' }} spacing\={2} direction\="row"\>
 \<CircularProgress color\="secondary" /\>
 \<CircularProgress color\="success" /\>
 \<CircularProgress color\="inherit" /\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- You asked, we delivered! Our Free Plan now includes a Custom Domain, 5 Actions, and 25,000 MAUs.ad by Carbon### Circular size

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularSize {
  return (
    <Stack spacing={2} direction="row" alignItems="center">
      <CircularProgress size="30px" />
      <CircularProgress size={40} />
      <CircularProgress size="3rem" />
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularSize {
 return (
 \<Stack spacing\={2} direction\="row" alignItems\="center"\>
 \<CircularProgress size\="30px" /\>
 \<CircularProgress size\={40} /\>
 \<CircularProgress size\="3rem" /\>
 \</Stack\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon### Circular determinate

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularDeterminate {
  const [progress, setProgress] = React.useState(0);

  React.useEffect( => {
    const timer = setInterval( => {
      setProgress((prevProgress) => (prevProgress >= 100 ? 0 : prevProgress + 10));
    }, 800);

    return  => {
      clearInterval(timer);
    };
  }, []);

  return (
    <Stack spacing={2} direction="row">
      <CircularProgress variant="determinate" value={25} />
      <CircularProgress variant="determinate" value={50} />
      <CircularProgress variant="determinate" value={75} />
      <CircularProgress variant="determinate" value={100} />
      <CircularProgress variant="determinate" value={progress} />
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularDeterminate {
 const \[progress, setProgress] \= React.useState(0\);

 React.useEffect( \=\> {
 const timer \= setInterval( \=\> {
 setProgress((prevProgress) \=\> (prevProgress \>\= 100 ? 0 : prevProgress \+ 10\));
 }, 800\);

 return  \=\> {
 clearInterval(timer);
 };
 }, \[]);

 return (
 \<Stack spacing\={2} direction\="row"\>
 \<CircularProgress variant\="determinate" value\={25} /\>
 \<CircularProgress variant\="determinate" value\={50} /\>
 \<CircularProgress variant\="determinate" value\={75} /\>
 \<CircularProgress variant\="determinate" value\={100} /\>
 \<CircularProgress variant\="determinate" value\={progress} /\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- You asked, we delivered! Our Free Plan now includes a Custom Domain, 5 Actions, and 25,000 MAUs.ad by Carbon### Interactive integration

Accept termsJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { green } from '@mui/material/colors';
import Button from '@mui/material/Button';
import Fab from '@mui/material/Fab';
import CheckIcon from '@mui/icons-material/Check';
import SaveIcon from '@mui/icons-material/Save';

export default function CircularIntegration {
  const [loading, setLoading] = React.useState(false);
  const [success, setSuccess] = React.useState(false);
  const timer = React.useRef<ReturnType<typeof setTimeout>>(undefined);

  const buttonSx = {
    ...(success && {
      bgcolor: green[500],
      '&:hover': {
        bgcolor: green[700],
      },
    }),
  };

  React.useEffect( => {
    return  => {
      clearTimeout(timer.current);
    };
  }, []);

  const handleButtonClick =  => {
    if (!loading) {
      setSuccess(false);
      setLoading(true);
      timer.current = setTimeout( => {
        setSuccess(true);
        setLoading(false);
      }, 2000);
    }
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ m: 1, position: 'relative' }}>
        <Fab
          aria-label="save"
          color="primary"
          sx={buttonSx}
          onClick={handleButtonClick}
        >
          {success ? <CheckIcon /> : <SaveIcon />}
        </Fab>
        {loading && (
          <CircularProgress
            size={68}
            sx={{
              color: green[500],
              position: 'absolute',
              top: -6,
              left: -6,
              zIndex: 1,
            }}
          />
        )}
      </Box>
      <Box sx={{ m: 1, position: 'relative' }}>
        <Button
          variant="contained"
          sx={buttonSx}
          disabled={loading}
          onClick={handleButtonClick}
        >
          Accept terms
        </Button>
        {loading && (
          <CircularProgress
            size={24}
            sx={{
              color: green[500],
              position: 'absolute',
              top: '50%',
              left: '50%',
              marginTop: '-12px',
              marginLeft: '-12px',
            }}
          />
        )}
      </Box>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { green } from '@mui/material/colors';
import Button from '@mui/material/Button';
import Fab from '@mui/material/Fab';
import CheckIcon from '@mui/icons\-material/Check';
import SaveIcon from '@mui/icons\-material/Save';

export default function CircularIntegration {
 const \[loading, setLoading] \= React.useState(false);
 const \[success, setSuccess] \= React.useState(false);
 const timer \= React.useRef\<ReturnType\<typeof setTimeout\>\>(undefined);

 const buttonSx \= {
 ...(success \&\& {
 bgcolor: green\[500],
 '\&:hover': {
 bgcolor: green\[700],
 },
 }),
 };

 React.useEffect( \=\> {
 return  \=\> {
 clearTimeout(timer.current);
 };
 }, \[]);

 const handleButtonClick \=  \=\> {
 if (!loading) {
 setSuccess(false);
 setLoading(true);
 timer.current \= setTimeout( \=\> {
 setSuccess(true);
 setLoading(false);
 }, 2000\);
 }
 };

 return (
 \<Box sx\={{ display: 'flex', alignItems: 'center' }}\>
 \<Box sx\={{ m: 1, position: 'relative' }}\>
 \<Fab
 aria\-label\="save"
 color\="primary"
 sx\={buttonSx}
 onClick\={handleButtonClick}
 \>
 {success ? \<CheckIcon /\> : \<SaveIcon /\>}
 \</Fab\>
 {loading \&\& (
 \<CircularProgress
 size\={68}
 sx\={{
 color: green\[500],
 position: 'absolute',
 top: \-6,
 left: \-6,
 zIndex: 1,
 }}
 /\>
 )}
 \</Box\>
 \<Box sx\={{ m: 1, position: 'relative' }}\>
 \<Button
 variant\="contained"
 sx\={buttonSx}
 disabled\={loading}
 onClick\={handleButtonClick}
 \>
 Accept terms
 \</Button\>
 {loading \&\& (
 \<CircularProgress
 size\={24}
 sx\={{
 color: green\[500],
 position: 'absolute',
 top: '50%',
 left: '50%',
 marginTop: '\-12px',
 marginLeft: '\-12px',
 }}
 /\>
 )}
 \</Box\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by Carbon### Circular with label

100%JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import CircularProgress, {
  CircularProgressProps,
} from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function CircularProgressWithLabel(
  props: CircularProgressProps & { value: number },
) {
  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress variant="determinate" {...props} />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography
          variant="caption"
          component="div"
          sx={{ color: 'text.secondary' }}
        >{`${Math.round(props.value)}%`}</Typography>
      </Box>
    </Box>
  );
}

export default function CircularWithValueLabel {
  const [progress, setProgress] = React.useState(10);

  React.useEffect( => {
    const timer = setInterval( => {
      setProgress((prevProgress) => (prevProgress >= 100 ? 0 : prevProgress + 10));
    }, 800);
    return  => {
      clearInterval(timer);
    };
  }, []);

  return <CircularProgressWithLabel value={progress} />;
}  

```
import \* as React from 'react';
import CircularProgress, {
 CircularProgressProps,
} from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function CircularProgressWithLabel(
 props: CircularProgressProps \& { value: number },
) {
 return (
 \<Box sx\={{ position: 'relative', display: 'inline\-flex' }}\>
 \<CircularProgress variant\="determinate" {...props} /\>
 \<Box
 sx\={{
 top: 0,
 left: 0,
 bottom: 0,
 right: 0,
 position: 'absolute',
 display: 'flex',
 alignItems: 'center',
 justifyContent: 'center',
 }}
 \>
 \<Typography
 variant\="caption"
 component\="div"
 sx\={{ color: 'text.secondary' }}
 \>{\`${Math.round(props.value)}%\`}\</Typography\>
 \</Box\>
 \</Box\>
 );
}

export default function CircularWithValueLabel {
 const \[progress, setProgress] \= React.useState(10\);

 React.useEffect( \=\> {
 const timer \= setInterval( \=\> {
 setProgress((prevProgress) \=\> (prevProgress \>\= 100 ? 0 : prevProgress \+ 10\));
 }, 800\);
 return  \=\> {
 clearInterval(timer);
 };
 }, \[]);

 return \<CircularProgressWithLabel value\={progress} /\>;
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonLinear
------

### Linear indeterminate

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearIndeterminate {
  return (
    <Box sx={{ width: '100%' }}>
      <LinearProgress />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearIndeterminate {
 return (
 \<Box sx\={{ width: '100%' }}\>
 \<LinearProgress /\>
 \</Box\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon### Linear color

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearColor {
  return (
    <Stack sx={{ width: '100%', color: 'grey.500' }} spacing={2}>
      <LinearProgress color="secondary" />
      <LinearProgress color="success" />
      <LinearProgress color="inherit" />
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearColor {
 return (
 \<Stack sx\={{ width: '100%', color: 'grey.500' }} spacing\={2}\>
 \<LinearProgress color\="secondary" /\>
 \<LinearProgress color\="success" /\>
 \<LinearProgress color\="inherit" /\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by Carbon### Linear determinate

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearDeterminate {
  const [progress, setProgress] = React.useState(0);

  React.useEffect( => {
    const timer = setInterval( => {
      setProgress((oldProgress) => {
        if (oldProgress === 100) {
          return 0;
        }
        const diff = Math.random * 10;
        return Math.min(oldProgress + diff, 100);
      });
    }, 500);

    return  => {
      clearInterval(timer);
    };
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <LinearProgress variant="determinate" value={progress} />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearDeterminate {
 const \[progress, setProgress] \= React.useState(0\);

 React.useEffect( \=\> {
 const timer \= setInterval( \=\> {
 setProgress((oldProgress) \=\> {
 if (oldProgress \=\=\= 100\) {
 return 0;
 }
 const diff \= Math.random \* 10;
 return Math.min(oldProgress \+ diff, 100\);
 });
 }, 500\);

 return  \=\> {
 clearInterval(timer);
 };
 }, \[]);

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<LinearProgress variant\="determinate" value\={progress} /\>
 \</Box\>
 );
}Press `Enter` to start editing**Gitlab** \- Software. Faster. All in one platform. Give GitLab a try for free.ad by Carbon### Linear buffer

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearBuffer {
  const [progress, setProgress] = React.useState(0);
  const [buffer, setBuffer] = React.useState(10);

  const progressRef = React.useRef( => {});
  React.useEffect( => {
    progressRef.current =  => {
      if (progress === 100) {
        setProgress(0);
        setBuffer(10);
      } else {
        setProgress(progress + 1);
        if (buffer < 100 && progress % 5 === 0) {
          const newBuffer = buffer + 1 + Math.random * 10;
          setBuffer(newBuffer > 100 ? 100 : newBuffer);
        }
      }
    };
  });

  React.useEffect( => {
    const timer = setInterval( => {
      progressRef.current;
    }, 100);

    return  => {
      clearInterval(timer);
    };
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <LinearProgress variant="buffer" value={progress} valueBuffer={buffer} />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';

export default function LinearBuffer {
 const \[progress, setProgress] \= React.useState(0\);
 const \[buffer, setBuffer] \= React.useState(10\);

 const progressRef \= React.useRef( \=\> {});
 React.useEffect( \=\> {
 progressRef.current \=  \=\> {
 if (progress \=\=\= 100\) {
 setProgress(0\);
 setBuffer(10\);
 } else {
 setProgress(progress \+ 1\);
 if (buffer \< 100 \&\& progress % 5 \=\=\= 0\) {
 const newBuffer \= buffer \+ 1 \+ Math.random \* 10;
 setBuffer(newBuffer \> 100 ? 100 : newBuffer);
 }
 }
 };
 });

 React.useEffect( \=\> {
 const timer \= setInterval( \=\> {
 progressRef.current;
 }, 100\);

 return  \=\> {
 clearInterval(timer);
 };
 }, \[]);

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<LinearProgress variant\="buffer" value\={progress} valueBuffer\={buffer} /\>
 \</Box\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Linear with label

40%

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import LinearProgress, { LinearProgressProps } from '@mui/material/LinearProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function LinearProgressWithLabel(props: LinearProgressProps & { value: number }) {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate" {...props} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography
          variant="body2"
          sx={{ color: 'text.secondary' }}
        >{`${Math.round(props.value)}%`}</Typography>
      </Box>
    </Box>
  );
}

export default function LinearWithValueLabel {
  const [progress, setProgress] = React.useState(10);

  React.useEffect( => {
    const timer = setInterval( => {
      setProgress((prevProgress) => (prevProgress >= 100 ? 10 : prevProgress + 10));
    }, 800);
    return  => {
      clearInterval(timer);
    };
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <LinearProgressWithLabel value={progress} />
    </Box>
  );
}  

```
import \* as React from 'react';
import LinearProgress, { LinearProgressProps } from '@mui/material/LinearProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function LinearProgressWithLabel(props: LinearProgressProps \& { value: number }) {
 return (
 \<Box sx\={{ display: 'flex', alignItems: 'center' }}\>
 \<Box sx\={{ width: '100%', mr: 1 }}\>
 \<LinearProgress variant\="determinate" {...props} /\>
 \</Box\>
 \<Box sx\={{ minWidth: 35 }}\>
 \<Typography
 variant\="body2"
 sx\={{ color: 'text.secondary' }}
 \>{\`${Math.round(props.value)}%\`}\</Typography\>
 \</Box\>
 \</Box\>
 );
}

export default function LinearWithValueLabel {
 const \[progress, setProgress] \= React.useState(10\);

 React.useEffect( \=\> {
 const timer \= setInterval( \=\> {
 setProgress((prevProgress) \=\> (prevProgress \>\= 100 ? 10 : prevProgress \+ 10\));
 }, 800\);
 return  \=\> {
 clearInterval(timer);
 };
 }, \[]);

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<LinearProgressWithLabel value\={progress} /\>
 \</Box\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonNon\-standard ranges
--------------------

The progress components accept a value in the range 0 \- 100\. This simplifies things for screen\-reader users, where these are the default min / max values. Sometimes, however, you might be working with a data source where the values fall outside this range. Here's how you can easily transform a value in any range to a scale of 0 \- 100:



```
// MIN = Minimum expected value
// MAX = Maximum expected value
// Function to normalise the values (MIN / MAX could be integrated)
const normalise = (value) => ((value - MIN) * 100) / (MAX - MIN);

// Example component that utilizes the `normalise` function at the point of render.
function Progress(props) {
  return (
    <React.Fragment>
      <CircularProgress variant="determinate" value={normalise(props.value)} />
      <LinearProgress variant="determinate" value={normalise(props.value)} />
    </React.Fragment>
  );
}

```
CopyCopied(or Ctrl \+ C)
Customization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


  
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import CircularProgress, {
  circularProgressClasses,
  CircularProgressProps,
} from '@mui/material/CircularProgress';
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[200],
    ...theme.applyStyles('dark', {
      backgroundColor: theme.palette.grey[800],
    }),
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: '#1a90ff',
    ...theme.applyStyles('dark', {
      backgroundColor: '#308fe8',
    }),
  },
}));

// Inspired by the former Facebook spinners.
function FacebookCircularProgress(props: CircularProgressProps) {
  return (
    <Box sx={{ position: 'relative' }}>
      <CircularProgress
        variant="determinate"
        sx={(theme) => ({
          color: theme.palette.grey[200],
          ...theme.applyStyles('dark', {
            color: theme.palette.grey[800],
          }),
        })}
        size={40}
        thickness={4}
        {...props}
        value={100}
      />
      <CircularProgress
        variant="indeterminate"
        disableShrink
        sx={(theme) => ({
          color: '#1a90ff',
          animationDuration: '550ms',
          position: 'absolute',
          left: 0,
          [`& .${circularProgressClasses.circle}`]: {
            strokeLinecap: 'round',
          },
          ...theme.applyStyles('dark', {
            color: '#308fe8',
          }),
        })}
        size={40}
        thickness={4}
        {...props}
      />
    </Box>
  );
}

// From #issuecomment-959408221
function GradientCircularProgress {
  return (
    <React.Fragment>
      <svg width={0} height={0}>
        <defs>
          <linearGradient id="my_gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#e01cd5" />
            <stop offset="100%" stopColor="#1CB5E0" />
          </linearGradient>
        </defs>
      </svg>
      <CircularProgress sx={{ 'svg circle': { stroke: 'url(#my_gradient)' } }} />
    </React.Fragment>
  );
}
export default function CustomizedProgressBars {
  return (
    <Stack spacing={2} sx={{ flexGrow: 1 }}>
      <FacebookCircularProgress />
      <GradientCircularProgress />
      <br />
      <BorderLinearProgress variant="determinate" value={50} />
    </Stack>
  );
}  

```
import \* as React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import CircularProgress, {
 circularProgressClasses,
 CircularProgressProps,
} from '@mui/material/CircularProgress';
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';

const BorderLinearProgress \= styled(LinearProgress)(({ theme }) \=\> ({
 height: 10,
 borderRadius: 5,
 \[\`\&.${linearProgressClasses.colorPrimary}\`]: {
 backgroundColor: theme.palette.grey\[200],
 ...theme.applyStyles('dark', {
 backgroundColor: theme.palette.grey\[800],
 }),
 },
 \[\`\& .${linearProgressClasses.bar}\`]: {
 borderRadius: 5,
 backgroundColor: '\#1a90ff',
 ...theme.applyStyles('dark', {
 backgroundColor: '\#308fe8',
 }),
 },
}));

// Inspired by the former Facebook spinners.
function FacebookCircularProgress(props: CircularProgressProps) {
 return (
 \<Box sx\={{ position: 'relative' }}\>
 \<CircularProgress
 variant\="determinate"
 sx\={(theme) \=\> ({
 color: theme.palette.grey\[200],
 ...theme.applyStyles('dark', {
 color: theme.palette.grey\[800],
 }),
 })}
 size\={40}
 thickness\={4}
 {...props}
 value\={100}
 /\>
 \<CircularProgress
 variant\="indeterminate"
 disableShrink
 sx\={(theme) \=\> ({
 color: '\#1a90ff',
 animationDuration: '550ms',
 position: 'absolute',
 left: 0,
 \[\`\& .${circularProgressClasses.circle}\`]: {
 strokeLinecap: 'round',
 },
 ...theme.applyStyles('dark', {
 color: '\#308fe8',
 }),
 })}
 size\={40}
 thickness\={4}
 {...props}
 /\>
 \</Box\>
 );
}

// From #issuecomment\-959408221
function GradientCircularProgress {
 return (
 \<React.Fragment\>
 \<svg width\={0} height\={0}\>
 \<defs\>
 \<linearGradient id\="my\_gradient" x1\="0%" y1\="0%" x2\="0%" y2\="100%"\>
 \<stop offset\="0%" stopColor\="\#e01cd5" /\>
 \<stop offset\="100%" stopColor\="\#1CB5E0" /\>
 \</linearGradient\>
 \</defs\>
 \</svg\>
 \<CircularProgress sx\={{ 'svg circle': { stroke: 'url(\#my\_gradient)' } }} /\>
 \</React.Fragment\>
 );
}
export default function CustomizedProgressBars {
 return (
 \<Stack spacing\={2} sx\={{ flexGrow: 1 }}\>
 \<FacebookCircularProgress /\>
 \<GradientCircularProgress /\>
 \<br /\>
 \<BorderLinearProgress variant\="determinate" value\={50} /\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by CarbonDelaying appearance
-------------------

There are 3 important limits to know around response time.
The ripple effect of the `ButtonBase` component ensures that the user feels that the UI is reacting instantaneously.
Normally, no special feedback is necessary during delays of more than 0\.1 but less than 1\.0 second.
After 1\.0 second, you can display a loader to keep user's flow of thought uninterrupted.


LoadingSimulate a loadJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Fade from '@mui/material/Fade';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';

export default function DelayingAppearance {
  const [loading, setLoading] = React.useState(false);
  const [query, setQuery] = React.useState('idle');
  const timerRef = React.useRef<ReturnType<typeof setTimeout>>(undefined);

  React.useEffect(
     =>  => {
      clearTimeout(timerRef.current);
    },
    [],
  );

  const handleClickLoading =  => {
    setLoading((prevLoading) => !prevLoading);
  };

  const handleClickQuery =  => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    if (query !== 'idle') {
      setQuery('idle');
      return;
    }

    setQuery('progress');
    timerRef.current = setTimeout( => {
      setQuery('success');
    }, 2000);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <Box sx={{ height: 40 }}>
        <Fade
          in={loading}
          style={{
            transitionDelay: loading ? '800ms' : '0ms',
          }}
          unmountOnExit
        >
          <CircularProgress />
        </Fade>
      </Box>
      <Button onClick={handleClickLoading} sx={{ m: 2 }}>
        {loading ? 'Stop loading' : 'Loading'}
      </Button>
      <Box sx={{ height: 40 }}>
        {query === 'success' ? (
          <Typography>Success!</Typography>
        ) : (
          <Fade
            in={query === 'progress'}
            style={{
              transitionDelay: query === 'progress' ? '800ms' : '0ms',
            }}
            unmountOnExit
          >
            <CircularProgress />
          </Fade>
        )}
      </Box>
      <Button onClick={handleClickQuery} sx={{ m: 2 }}>
        {query !== 'idle' ? 'Reset' : 'Simulate a load'}
      </Button>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Fade from '@mui/material/Fade';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';

export default function DelayingAppearance {
 const \[loading, setLoading] \= React.useState(false);
 const \[query, setQuery] \= React.useState('idle');
 const timerRef \= React.useRef\<ReturnType\<typeof setTimeout\>\>(undefined);

 React.useEffect(
  \=\>  \=\> {
 clearTimeout(timerRef.current);
 },
 \[],
 );

 const handleClickLoading \=  \=\> {
 setLoading((prevLoading) \=\> !prevLoading);
 };

 const handleClickQuery \=  \=\> {
 if (timerRef.current) {
 clearTimeout(timerRef.current);
 }

 if (query !\=\= 'idle') {
 setQuery('idle');
 return;
 }

 setQuery('progress');
 timerRef.current \= setTimeout( \=\> {
 setQuery('success');
 }, 2000\);
 };

 return (
 \<Box sx\={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}\>
 \<Box sx\={{ height: 40 }}\>
 \<Fade
 in\={loading}
 style\={{
 transitionDelay: loading ? '800ms' : '0ms',
 }}
 unmountOnExit
 \>
 \<CircularProgress /\>
 \</Fade\>
 \</Box\>
 \<Button onClick\={handleClickLoading} sx\={{ m: 2 }}\>
 {loading ? 'Stop loading' : 'Loading'}
 \</Button\>
 \<Box sx\={{ height: 40 }}\>
 {query \=\=\= 'success' ? (
 \<Typography\>Success!\</Typography\>
 ) : (
 \<Fade
 in\={query \=\=\= 'progress'}
 style\={{
 transitionDelay: query \=\=\= 'progress' ? '800ms' : '0ms',
 }}
 unmountOnExit
 \>
 \<CircularProgress /\>
 \</Fade\>
 )}
 \</Box\>
 \<Button onClick\={handleClickQuery} sx\={{ m: 2 }}\>
 {query !\=\= 'idle' ? 'Reset' : 'Simulate a load'}
 \</Button\>
 \</Box\>
 );
}Press `Enter` to start editing**CloudBees** \- Keep operations running smoothly with CloudBees CI—zero downtime, maximum productivity.ad by CarbonLimitations
-----------

### High CPU load

Under heavy load, you might lose the stroke dash animation or see random `CircularProgress` ring widths.
You should run processor intensive operations in a web worker or by batch in order not to block the main rendering thread.





When it's not possible, you can leverage the `disableShrink` prop to mitigate the issue.
See this issue.


JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularUnderLoad {
  return <CircularProgress disableShrink />;
}  

```
import \* as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';

export default function CircularUnderLoad {
 return \<CircularProgress disableShrink /\>;
}Press `Enter` to start editing**CloudBees** \- Don't just wait—conquer downtime. Leverage "The Power of Three" in CloudBees HA for faster delivery.ad by Carbon### High frequency updates

The `LinearProgress` uses a transition on the CSS transform property to provide a smooth update between different values.
The default transition duration is 200ms.
In the event a parent component updates the `value` prop too quickly, you will at least experience a 200ms delay between the re\-render and the progress bar fully updated.


If you need to perform 30 re\-renders per second or more, we recommend disabling the transition:



```
.MuiLinearProgress-bar {
  transition: none;
}

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<CircularProgress />`
* `<LinearProgress />`



