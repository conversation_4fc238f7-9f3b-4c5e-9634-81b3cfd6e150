import axios, { type AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/authStore';

// Create axios instance
const api = axios.create({
  baseURL: '/api', // Base URL for all requests
  timeout: 10000, // Request timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const { user } = useAuthStore.getState();

    // Add token to headers if user is authenticated
    if (user && user.token) {
      config.headers['Authorization'] = `Bearer ${user.token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    const { code } = response.data;

    // Handle different response codes
    if (code === '2') {
      // Not logged in
      useAuthStore.getState().logout();
      window.location.href = '/login';
      return Promise.reject(new Error('Not logged in'));
    } else if (code === '3') {
      // No permission
      return Promise.reject(new Error('No permission'));
    } else if (code !== '0') {
      // Other errors
      return Promise.reject(new Error(response.data.msg || 'Unknown error'));
    }

    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Generic request function
export const request = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response = await api.request<T>(config);
    return response as unknown as T;
  } catch (error) {
    throw error;
  }
};

export default api;
