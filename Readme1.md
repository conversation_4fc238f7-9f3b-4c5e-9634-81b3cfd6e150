# AgentFlow 前端

## 1. 项目概述

### 1.1 项目目标与核心价值
AgentFlow 是一个基于Autogen的前端项目,旨在提供一个用户友好的界面来构建和管理AgentFlow应用程序。通过AgentFlow，用户可以通过拖放界面创建、管理和可视化复杂的AgentFlow工作流。

Autogen是微软开发的agent开发框架，用于开发可与用户交互的程序，能够完成回答问题、生成代码、生成图片等任务。在本项目中，Autogen作为组件形式存在，为系统提供智能交互能力。

### 1.2 技术栈
- **React**：版本 18.3.1
- **TypeScript**：版本 5.4.5
- **Node.js**：与现代 Node.js 版本兼容
- **Vite**：版本 5.4.19（构建工具）
- **Tailwind CSS**：版本 3.4.4
- **Zustand**：版本 4.5.2（状态管理）
- **React Router DOM**：版本 6.23.1
- **React Query**：版本 5.49.2（数据获取）
- **Axios**：版本 1.7.4（HTTP 客户端）

### 1.3 项目设置与命令
- **初始化**：项目使用 Vite 作为构建工具
- **关键命令**：
  - `npm run dev`：启动开发服务器
  - `npm run build`：构建生产版本
  - `npm run serve`：预览生产构建
  - `npm run format`：使用 Prettier 格式化代码
  - `npm run check-format`：检查代码格式
  - `npm run type-check`：运行 TypeScript 类型检查

## 2. UI 框架与设计

### 2.1 UI 框架
项目使用自定义 UI 组件和几个 UI 库的组合：
- **Radix UI**：提供可访问的、无样式的组件（v1.x 和 v2.x）
- **Tailwind CSS**：用于样式和响应式设计

这些框架因其可访问性、可定制性和性能而被选中。

### 2.2 组件组织
- 自定义 UI 组件组织在 `src/components/ui/` 中
- 组件遵循一致的模式，具有明确的关注点分离
- 项目使用预构建组件和自定义实现的混合
- UI 组件高度可重用和可组合

### 2.3 响应式设计
- 使用 Tailwind CSS 的响应式实用程序实现
- 在 `tailwind.config.mjs` 中定义的自定义断点：
  - xl: 1200px
  - 2xl: 1400px
- 响应式布局使用 Flexbox 和 Grid 系统
- 整个页面随着用户分辨率等比缩放，组件与组件之间的间距、大小、位置等全部等比缩放

## 3. 项目结构

### 3.1 核心目录结构
- **src/components/**：按功能组织的 UI 组件
  - **ui/**：基础 UI 组件（按钮、输入等）
  - **common/**：整个应用程序中使用的共享组件
  - **core/**：核心应用程序组件
  - **authorization/**：与身份验证相关的组件
- **src/pages/**：不同路由的页面级组件
  - **login/**：登录页面组件
  - **home/**：首页组件，包含左侧菜单树和右侧内容区域
- **src/stores/**：Zustand 状态管理存储
- **src/controllers/**：API 和服务控制器
  - **base/user.ts**：用户相关接口，包含登录函数
  - **base/menu.ts**：菜单相关接口，包含获取菜单数据函数
- **src/hooks/**：自定义 React 钩子
- **src/contexts/**：React 上下文提供者
- **src/utils/**：实用函数
- **src/types/**：TypeScript 类型定义
- **src/assets/**：静态资源（图像、图标）
- **src/CustomNodes/**：流编辑器的自定义节点组件
- **src/CustomEdges/**：连接的自定义边组件

### 3.2 模块组织
- 代码按功能和功能组织
- UI 组件和业务逻辑之间有明确的分离
- 模块化方法允许轻松扩展和维护

### 3.3 命名约定
- React 组件使用 PascalCase（例如，`Button.tsx`）
- 实用函数和钩子使用 camelCase
- 文件根据其主要导出命名
- 整个代码库具有一致的命名模式

## 4. 核心库与状态管理

### 4.1 状态管理
- **Zustand**：主要的状态管理解决方案
  - 轻量级和简单的 API
  - 存储是模块化的，并专注于特定领域
  - 示例：`flowStore.ts`、`darkStore.ts`、`authStore.ts`
- 使用 React 的 `useState` 进行 UI 特定的状态的本地组件状态

### 4.2 路由
- **React Router DOM**（v6.23.1）
- 在 `routes.tsx` 中定义的路由
- 通过守卫组件实现的受保护路由
- 复杂页面层次结构的嵌套路由
- 采用动态路由，通过模拟POST请求从后端接口获取菜单数据
- 菜单点击后内容页面初始为空白页面

### 4.3 API 与数据获取
- **Axios**：API 请求的 HTTP 客户端
- **React Query**：数据获取、缓存和状态管理
- 用于身份验证和错误处理的自定义 API 拦截器
- 对实时数据的流请求支持
- 所有接口请求统一使用POST方法
- 响应数据格式为`{'code':'0','data':{},'msg':''}`，其中code值表示不同状态：
  - 0：成功
  - 1：失败
  - 2：未登录
  - 3：无权限
  - 4：系统错误
  - 5：业务错误
  - 6：参数错误
  - 7：数据不存在
  - 8：数据已存在
  - 9：数据格式错误
  - 10：数据类型错误
  - 11：数据长度错误
  - 12：数据范围错误
- 目前后端项目尚未开始建设，使用模拟数据进行开发，接口请求函数可直接修改

### 4.4 表单处理
- **React Hook Form**：表单状态管理和验证
- **Zod**：表单输入的模式验证
- 基于这些库构建的自定义表单组件

### 4.5 样式解决方案
- **Tailwind CSS**：实用优先的 CSS 框架
- 自定义实用类和组件
- 通过插件扩展：
  - @tailwindcss/forms
  - @tailwindcss/typography
  - tailwindcss-animate
  - tailwindcss-dotted-background

### 4.6 其他关键依赖项
- **Moment.js**：日期/时间处理
- **flflowgram.ai**：节点连接的基于流的 UI
- **React Markdown**：Markdown 渲染
- **Framer Motion**：动画库
- **Lodash**：实用函数

## 5. 架构与设计模式

### 5.1 组件设计
- 使用 React 钩子的函数组件
- 容器和展示组件的分离
- 可重用逻辑的自定义钩子
- 共享状态的上下文提供者

### 5.2 代码重用
- `utils/` 目录中的实用函数
- 常见行为的自定义钩子
- 一致 UI 元素的共享组件

### 5.3 TypeScript 使用
- 整个应用程序中的强类型
- `types/` 目录中的自定义类型定义
- 基于接口的组件 props 设计
- 运行时类型检查的类型守卫

## 6. 身份验证与授权
- 基于令牌的身份验证，在POST请求头统一添加token进行校验
- 带有身份验证守卫的受保护路由
- 基于角色的访问控制
- 自动令牌刷新机制
- 系统包含登录页面，有用户名和密码输入框及登录按钮，登录后跳转至首页
- 登录接口的请求函数为`src/controllers/base/user.ts`中的login函数，目前返回模拟数据

## 7. 构建与开发工具
- **Vite**：快速构建工具和开发服务器
- **ESLint**：代码 linting
- **Prettier**：代码格式化
- **TypeScript**：静态类型检查
- **npm**：包管理

## 8. 性能考虑
- 通过 React.lazy 和动态导入进行代码拆分
- 使用 React.memo、useMemo 和 useCallback 进行记忆化
- 通过正确使用键进行优化渲染
- Tailwind 的 JIT 编译器用于最小 CSS

## 9. 菜单结构
系统菜单结构如下：
- **模型设置**
  - 文本模型
  - 文->图模型
  - 文->语音模型
  - 文->视频模型
  - 图->文模型
  - 语音->文模型
  - 视频->文模型
  - 图像模型
  - 召回模型
  - 向量模型
  - 专业模型
  - 共享设置
  - 模型市场
- **数据管理**
  - 向量数据库
  - Redis数据库
  - 图数据库
  - 关系数据库
  - 共享设置
- **MCP管理**
  - MCP列表
  - MCP市场
  - 共享设置
- **工具管理**
  - 工具列表
  - 工具市场
  - 共享设置
- **Flow管理**
  - langflow
  - flowise
  - make
  - coze
  - dify
  - fastgpt
  - autogen
  - adk
  - ango
- **RAG管理**
  - 知识库
  - 文档库
  - 知识图谱
  - 共享设置
- **Agent管理**
  - Agent团队
  - Agent列表
  - 中止函数
  - Agent市场
  - 共享设置
- **设备管理**
  - 设备列表
  - 共享设置
- **个人中心**
  - 个人中心
  - 积分兑换
  - 数据分析
  - 接入文档

菜单树支持多级树结构，分为目录和页面两种类型。目录下面可以有目录和页面，页面下面不能再有目录和页面。页面点击之后右侧内容区域则加载对应的内容。目录和页面目前没有图标，但需要支持后面直接添加图标。

## 10. 测试
- 使用 Jest 和 React Testing Library 设置的测试框架
- Playwright 进行端到端测试
- package.json 中的测试脚本
- 单元测试覆盖关键组件和功能