Container
=========

The container centers your content horizontally. It's the most basic layout element.


Join us May 30th for world's largest hackathon for non\-devs and vibe coders. $1M\+ Prize pool.

ads via Carbon



While containers can be nested, most layouts do not require a nested container.


* Feedback
* Bundle size
* Source

Fluid
-----

A fluid container width is bounded by the `maxWidth` prop value.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';

export default function SimpleContainer {
  return (
    <React.Fragment>
      <CssBaseline />
      <Container maxWidth="sm">
        <Box sx={{ bgcolor: '#cfe8fc', height: '100vh' }} />
      </Container>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';

export default function SimpleContainer {
 return (
 \<React.Fragment\>
 \<CssBaseline /\>
 \<Container maxWidth\="sm"\>
 \<Box sx\={{ bgcolor: '\#cfe8fc', height: '100vh' }} /\>
 \</Container\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Gitlab** \- A single, unified DevSecOps platform is what you need to deliver software faster. Try it for free.ad by Carbon
```
<Container maxWidth="sm">

```
CopyCopied(or Ctrl \+ C)
Fixed
-----

If you prefer to design for a fixed set of sizes instead of trying to accommodate a fully fluid viewport, you can set the `fixed` prop.
The max\-width matches the min\-width of the current breakpoint.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';

export default function FixedContainer {
  return (
    <React.Fragment>
      <CssBaseline />
      <Container fixed>
        <Box sx={{ bgcolor: '#cfe8fc', height: '100vh' }} />
      </Container>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';

export default function FixedContainer {
 return (
 \<React.Fragment\>
 \<CssBaseline /\>
 \<Container fixed\>
 \<Box sx\={{ bgcolor: '\#cfe8fc', height: '100vh' }} /\>
 \</Container\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon
```
<Container fixed>

```
CopyCopied(or Ctrl \+ C)
Experimental APIs \- Toolpad
----------------------------

### Page Container

The PageContainer component in `@toolpad/core` is the ideal wrapper for the content of your dashboard. It makes the Material UI Container navigation\-aware and extends it with page title, breadcrumbs, actions, and more.


1. Inbox
2. /
3. All
#### All

DownloadPrintJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, createTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import {
  PageContainer,
  PageHeader,
  PageHeaderToolbar,
} from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import PrintIcon from '@mui/icons-material/Print';
import DownloadIcon from '@mui/icons-material/Download';

const NAVIGATION: Navigation = [
  { segment: 'inbox', title: 'Inbox' },
  {
    segment: 'inbox/all',
    title: 'All',
    icon: <DashboardIcon />,
  },
];

function useDemoRouter(initialPath: string): Router {
  const [pathname, setPathname] = React.useState(initialPath);

  const router = React.useMemo( => {
    return {
      pathname,
      searchParams: new URLSearchParams,
      navigate: (path: string | URL) => setPathname(String(path)),
    };
  }, [pathname]);

  return router;
}

const Skeleton = styled('div')<{ height: number }>(({ theme, height }) => ({
  backgroundColor: (theme.vars || theme).palette.action.hover,
  borderRadius: (theme.vars || theme).shape.borderRadius,
  height,
  content: '" "',
}));

function CustomPageToolbar {
  return (
    <PageHeaderToolbar>
      <Stack direction="row" spacing={1} alignItems="center">
        <Button
          variant="outlined"
          size="small"
          color="neutral"
          startIcon={<DownloadIcon fontSize="inherit" />}
        >
          Download
        </Button>
        <Button
          variant="outlined"
          size="small"
          color="neutral"
          startIcon={<PrintIcon fontSize="inherit" />}
        >
          Print
        </Button>
      </Stack>
    </PageHeaderToolbar>
  );
}

function CustomPageHeader {
  return <PageHeader slots={{ toolbar: CustomPageToolbar }} />;
}

const demoTheme = createTheme({
  colorSchemes: { light: true, dark: true },
});

export default function PageContainerBasic(props: any) {
  const { window } = props;
  const router = useDemoRouter('/inbox/all');
  // Remove this const when copying and pasting into your project.
  const demoWindow = window ? window : undefined;

  return (
    <AppProvider
      navigation={NAVIGATION}
      router={router}
      theme={demoTheme}
      window={demoWindow}
      branding={{
        title: 'ACME Inc.',
      }}
    >
      <Paper sx={{ p: 2, width: '100%' }}>
        <PageContainer
          slots={{
            header: CustomPageHeader,
          }}
        >
          <Grid container spacing={1}>
            <Grid size={5} />
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={4}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={8}>
              <Skeleton height={100} />
            </Grid>
          </Grid>
        </PageContainer>
      </Paper>
    </AppProvider>
  );
}  

```
import \* as React from 'react';
import { styled, createTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons\-material/Dashboard';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import {
 PageContainer,
 PageHeader,
 PageHeaderToolbar,
} from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import PrintIcon from '@mui/icons\-material/Print';
import DownloadIcon from '@mui/icons\-material/Download';

const NAVIGATION: Navigation \= \[
 { segment: 'inbox', title: 'Inbox' },
 {
 segment: 'inbox/all',
 title: 'All',
 icon: \<DashboardIcon /\>,
 },
];

function useDemoRouter(initialPath: string): Router {
 const \[pathname, setPathname] \= React.useState(initialPath);

 const router \= React.useMemo( \=\> {
 return {
 pathname,
 searchParams: new URLSearchParams,
 navigate: (path: string \| URL) \=\> setPathname(String(path)),
 };
 }, \[pathname]);

 return router;
}

const Skeleton \= styled('div')\<{ height: number }\>(({ theme, height }) \=\> ({
 backgroundColor: (theme.vars \|\| theme).palette.action.hover,
 borderRadius: (theme.vars \|\| theme).shape.borderRadius,
 height,
 content: '" "',
}));

function CustomPageToolbar {
 return (
 \<PageHeaderToolbar\>
 \<Stack direction\="row" spacing\={1} alignItems\="center"\>
 \<Button
 variant\="outlined"
 size\="small"
 color\="neutral"
 startIcon\={\<DownloadIcon fontSize\="inherit" /\>}
 \>
 Download
 \</Button\>
 \<Button
 variant\="outlined"
 size\="small"
 color\="neutral"
 startIcon\={\<PrintIcon fontSize\="inherit" /\>}
 \>
 Print
 \</Button\>
 \</Stack\>
 \</PageHeaderToolbar\>
 );
}

function CustomPageHeader {
 return \<PageHeader slots\={{ toolbar: CustomPageToolbar }} /\>;
}

const demoTheme \= createTheme({
 colorSchemes: { light: true, dark: true },
});

export default function PageContainerBasic(props: any) {
 const { window } \= props;
 const router \= useDemoRouter('/inbox/all');
 // Remove this const when copying and pasting into your project.
 const demoWindow \= window ? window : undefined;

 return (
 \<AppProvider
 navigation\={NAVIGATION}
 router\={router}
 theme\={demoTheme}
 window\={demoWindow}
 branding\={{
 title: 'ACME Inc.',
 }}
 \>
 \<Paper sx\={{ p: 2, width: '100%' }}\>
 \<PageContainer
 slots\={{
 header: CustomPageHeader,
 }}
 \>
 \<Grid container spacing\={1}\>
 \<Grid size\={5} /\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={4}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={8}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \</Grid\>
 \</PageContainer\>
 \</Paper\>
 \</AppProvider\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by CarbonAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Container />`
* `<PigmentContainer />`



