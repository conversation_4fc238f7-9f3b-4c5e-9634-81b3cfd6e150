Data Grid \- Pagination
=======================

Easily paginate your rows and only fetch what you need.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



Enabling pagination
-------------------

The default pagination behavior depends on your plan:



Exported CSV and Excel files include all data and disregard pagination by default.
To apply pagination to exported files, review the available row selectors.


### Community plan

For the Community Data Grid, pagination is enabled by default and cannot be disabled.


DeskCommodityTrader NameTrader EmailQuantityD\-9340OatsMabel Romerofifo@ru.am51,454D\-3448CornAllen Lynchne@mine.ec39,728D\-9331CocoaLela <PERSON>sebgevum@iciadeci.gf44,492D\-2836<NAME_EMAIL>49,750D\-1138RapeseedLelia <PERSON>iis@lajtelale.rw21,804D\-4068Frozen Concentrated <NAME_EMAIL>48,064D\-4598Wheat<PERSON><PERSON><PERSON>@obuze.tz52,994Rows per page:

1001–100 of 500

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function PaginationCommunityNoSnap {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 500,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 300, width: '100%' }}>
      <DataGrid {...data} loading={loading} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function PaginationCommunityNoSnap {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 500,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 300, width: '100%' }}\>
 \<DataGrid {...data} loading\={loading} /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon### Pro  and Premium

For the Pro and Premium Data Grid, pagination is disabled by default; use the `pagination` prop to enable it.


Job TitleRecruitment DateContract TypeHead of Human Resources2020/9/12full timeHead of Sales2017/4/4full timeSales Person2020/12/20full timeSales Person2020/11/14part time1–4 of 15

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPremium, GridColDef, GridRowsProp } from '@mui/x-data-grid-premium';

const rows: GridRowsProp = [
  {
    jobTitle: 'Head of Human Resources',
    recruitmentDate: new Date(2020, 8, 12),
    contract: 'full time',
    id: 0,
  },
  {
    jobTitle: 'Head of Sales',
    recruitmentDate: new Date(2017, 3, 4),
    contract: 'full time',
    id: 1,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2020, 11, 20),
    contract: 'full time',
    id: 2,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2020, 10, 14),
    contract: 'part time',
    id: 3,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2017, 10, 29),
    contract: 'part time',
    id: 4,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2020, 7, 21),
    contract: 'full time',
    id: 5,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2020, 7, 20),
    contract: 'intern',
    id: 6,
  },
  {
    jobTitle: 'Sales Person',
    recruitmentDate: new Date(2019, 6, 28),
    contract: 'full time',
    id: 7,
  },
  {
    jobTitle: 'Head of Engineering',
    recruitmentDate: new Date(2016, 3, 14),
    contract: 'full time',
    id: 8,
  },
  {
    jobTitle: 'Tech lead front',
    recruitmentDate: new Date(2016, 5, 17),
    contract: 'full time',
    id: 9,
  },
  {
    jobTitle: 'Front-end developer',
    recruitmentDate: new Date(2019, 11, 7),
    contract: 'full time',
    id: 10,
  },
  {
    jobTitle: 'Tech lead devops',
    recruitmentDate: new Date(2021, 7, 1),
    contract: 'full time',
    id: 11,
  },
  {
    jobTitle: 'Tech lead back',
    recruitmentDate: new Date(2017, 0, 12),
    contract: 'full time',
    id: 12,
  },
  {
    jobTitle: 'Back-end developer',
    recruitmentDate: new Date(2019, 2, 22),
    contract: 'intern',
    id: 13,
  },
  {
    jobTitle: 'Back-end developer',
    recruitmentDate: new Date(2018, 4, 19),
    contract: 'part time',
    id: 14,
  },
];

const columns: GridColDef[] = [
  { field: 'jobTitle', headerName: 'Job Title', width: 200 },
  {
    field: 'recruitmentDate',
    headerName: 'Recruitment Date',
    type: 'date',
    width: 150,
  },
  {
    field: 'contract',
    headerName: 'Contract Type',
    type: 'singleSelect',
    valueOptions: ['full time', 'part time', 'intern'],
    width: 150,
  },
];

export default function PageSizeAutoPremium {
  return (
    <div style={{ height: 320, width: '100%' }}>
      <DataGridPremium pagination rows={rows} columns={columns} autoPageSize />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPremium, GridColDef, GridRowsProp } from '@mui/x\-data\-grid\-premium';

const rows: GridRowsProp \= \[
 {
 jobTitle: 'Head of Human Resources',
 recruitmentDate: new Date(2020, 8, 12\),
 contract: 'full time',
 id: 0,
 },
 {
 jobTitle: 'Head of Sales',
 recruitmentDate: new Date(2017, 3, 4\),
 contract: 'full time',
 id: 1,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2020, 11, 20\),
 contract: 'full time',
 id: 2,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2020, 10, 14\),
 contract: 'part time',
 id: 3,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2017, 10, 29\),
 contract: 'part time',
 id: 4,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2020, 7, 21\),
 contract: 'full time',
 id: 5,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2020, 7, 20\),
 contract: 'intern',
 id: 6,
 },
 {
 jobTitle: 'Sales Person',
 recruitmentDate: new Date(2019, 6, 28\),
 contract: 'full time',
 id: 7,
 },
 {
 jobTitle: 'Head of Engineering',
 recruitmentDate: new Date(2016, 3, 14\),
 contract: 'full time',
 id: 8,
 },
 {
 jobTitle: 'Tech lead front',
 recruitmentDate: new Date(2016, 5, 17\),
 contract: 'full time',
 id: 9,
 },
 {
 jobTitle: 'Front\-end developer',
 recruitmentDate: new Date(2019, 11, 7\),
 contract: 'full time',
 id: 10,
 },
 {
 jobTitle: 'Tech lead devops',
 recruitmentDate: new Date(2021, 7, 1\),
 contract: 'full time',
 id: 11,
 },
 {
 jobTitle: 'Tech lead back',
 recruitmentDate: new Date(2017, 0, 12\),
 contract: 'full time',
 id: 12,
 },
 {
 jobTitle: 'Back\-end developer',
 recruitmentDate: new Date(2019, 2, 22\),
 contract: 'intern',
 id: 13,
 },
 {
 jobTitle: 'Back\-end developer',
 recruitmentDate: new Date(2018, 4, 19\),
 contract: 'part time',
 id: 14,
 },
];

const columns: GridColDef\[] \= \[
 { field: 'jobTitle', headerName: 'Job Title', width: 200 },
 {
 field: 'recruitmentDate',
 headerName: 'Recruitment Date',
 type: 'date',
 width: 150,
 },
 {
 field: 'contract',
 headerName: 'Contract Type',
 type: 'singleSelect',
 valueOptions: \['full time', 'part time', 'intern'],
 width: 150,
 },
];

export default function PageSizeAutoPremium {
 return (
 \<div style\={{ height: 320, width: '100%' }}\>
 \<DataGridPremium pagination rows\={rows} columns\={columns} autoPageSize /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonSize of the page
----------------

The Data Grid (MIT license) is limited to pages of up to 100 rows.
If you want larger pages, you will need to upgrade to Pro plan or above.


By default, each page contains 100 rows. The user can change the size of the page through the selector in the footer.


### Page size options

You can customize the options shown in the "Rows per page" select using the `pageSizeOptions` prop.
You should provide an array of items, each item should be one of these types:


* **number**, each number will be used for the option's label and value.



```
<DataGrid pageSizeOptions={[5, 10, 25]}>

```
CopyCopied(or Ctrl \+ C)
* **object**, the `value` and `label` keys will be used respectively for the value and label of the option. Define `value` as `-1` to display all results.



```
<DataGrid pageSizeOptions={[10, 100, { value: 1000, label: '1,000' }, { value: -1, label: 'All' }]}>

```
CopyCopied(or Ctrl \+ C)


DeskCommodityTrader NameTrader EmailQuantityD\-6281<NAME_EMAIL>4,390D\-7188CocoaDarrell Norrisbovuw@rel.iq44,973D\-9974<NAME_EMAIL>31,645D\-7003RapeseedFrederick Welchlevuw@jozdohzoz.ir69,719D\-4404SoybeansMinerva Moodyiku@go.gf38,726Rows per page:

51–5 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function PageSizeCustomOptions {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        initialState={{
          ...data.initialState,
          pagination: { paginationModel: { pageSize: 5 } },
        }}
        pageSizeOptions={[5, 10, 25, { value: -1, label: 'All' }]}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function PageSizeCustomOptions {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 initialState\={{
 ...data.initialState,
 pagination: { paginationModel: { pageSize: 5 } },
 }}
 pageSizeOptions\={\[5, 10, 25, { value: \-1, label: 'All' }]}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by Carbon### Automatic page size

Use the `autoPageSize` prop to auto\-scale the `pageSize` to match the container height and the max number of rows that can be displayed without a vertical scroll bar.



You cannot use both the `autoPageSize` and `autoHeight` props at the same time because `autoHeight` scales the height of the Data Grid according to the `pageSize`.


Height of the container400pxMain Grouping CriteriaDeskCommodityTrader NameTrader EmailQuantityD\-3572Frozen Concentrated <NAME_EMAIL>37,666D\-591WheatKeith Coxemaonejid@uz.bi78,693D\-1630<NAME_EMAIL>74,581D\-1693RapeseedKenneth Underwooddem@jetovi.tz14,480D\-4493SoybeansEugenia Powellpa@rauncuj.rw44,8101–5 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';
import Stack from '@mui/material/Stack';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';

export default function PageSizeAuto {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 6,
  });

  const [height, setHeight] = React.useState(400);

  return (
    <Stack style={{ width: '100%' }} alignItems="flex-start" spacing={2}>
      <FormControl fullWidth>
        <InputLabel htmlFor="height-of-container" id="height-of-container-label">
          Height of the container
        </InputLabel>
        <Select
          label="Main Grouping Criteria"
          onChange={(event) => setHeight(Number(event.target.value))}
          value={height}
          id="height-of-container"
          labelId="height-of-container-label"
        >
          <MenuItem value="300">300px</MenuItem>
          <MenuItem value="400">400px</MenuItem>
          <MenuItem value="500">500px</MenuItem>
        </Select>
      </FormControl>
      <div style={{ height, width: '100%' }}>
        <DataGrid autoPageSize {...data} loading={loading} />
      </div>
    </Stack>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import Stack from '@mui/material/Stack';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';

export default function PageSizeAuto {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 6,
 });

 const \[height, setHeight] \= React.useState(400\);

 return (
 \<Stack style\={{ width: '100%' }} alignItems\="flex\-start" spacing\={2}\>
 \<FormControl fullWidth\>
 \<InputLabel htmlFor\="height\-of\-container" id\="height\-of\-container\-label"\>
 Height of the container
 \</InputLabel\>
 \<Select
 label\="Main Grouping Criteria"
 onChange\={(event) \=\> setHeight(Number(event.target.value))}
 value\={height}
 id\="height\-of\-container"
 labelId\="height\-of\-container\-label"
 \>
 \<MenuItem value\="300"\>300px\</MenuItem\>
 \<MenuItem value\="400"\>400px\</MenuItem\>
 \<MenuItem value\="500"\>500px\</MenuItem\>
 \</Select\>
 \</FormControl\>
 \<div style\={{ height, width: '100%' }}\>
 \<DataGrid autoPageSize {...data} loading\={loading} /\>
 \</div\>
 \</Stack\>
 );
}Press `Enter` to start editing**Gitlab** \- Software. Faster. All in one platform. Give GitLab a try for free.ad by CarbonPagination model
----------------

The pagination model is an object containing the current page and the size of the page. The default value is `{ page: 0, pageSize: 100 }`. To change the default value, make it controlled by `paginationModel` prop or initialize a custom value using `initialState.pagination.paginationModel`.


### Initializing the pagination model

To initialize the pagination model without controlling it, provide the `paginationModel` to the `initialState` prop. If you don't provide a value for one of the properties, the default value will be used.



```
<DataGrid
  initialState={{
    pagination: {
      paginationModel: { pageSize: 25, page: 0 },
    },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailQuantityD\-8161RapeseedLeona Colonbouw@rafeguci.km22,736D\-9922RapeseedDustin Hansendisnefak@lum.bg73,453D\-2092Sugar No.11Luella Terrywurhubteh@bahjalara.bn92,043D\-6403<NAME_EMAIL>35,431D\-8538Sugar No.14Adelaide Lloydjiwespi@wu.kg15,376D\-2315<NAME_EMAIL>7,604D\-3997<NAME_EMAIL>14,557D\-9029<NAME_EMAIL>48,074D\-3085SoybeansBernice Santosbuhjub@wifkucse.lb34,943Rows per page:

251–25 of 500

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function PaginationModelInitialState {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 500,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        initialState={{
          ...data.initialState,
          pagination: {
            ...data.initialState?.pagination,
            paginationModel: {
              pageSize: 25,
              /* page: 0 // default value will be used if not passed */
            },
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function PaginationModelInitialState {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 500,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 initialState\={{
 ...data.initialState,
 pagination: {
 ...data.initialState?.pagination,
 paginationModel: {
 pageSize: 25,
 /\* page: 0 // default value will be used if not passed \*/
 },
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**GetStream.io** \- Built by devs, for devs. Start Coding FREE. No CC requiredad by Carbon### Controlled pagination model

Pass the `paginationModel` prop to control the size and current page of the grid. You can use the `onPaginationModelChange` prop to listen to changes to the `paginationModel` and update the prop accordingly.



```
const [paginationModel, setPaginationModel] = React.useState({
  pageSize: 25,
  page: 0,
});

<DataGrid
  paginationModel={paginationModel}
  onPaginationModelChange={setPaginationModel}
/>;

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailQuantityD\-5472OatsAndrew Sanderspoha@har.bz11,469D\-5312<NAME_EMAIL>26,601D\-7188CornAlan Potterbed@ave.et94,438D\-4973SoybeansAlvin Nortonugapuf@apu.ht9,914D\-9569MilkBetty Fitzgeraldjeref@emew.so64,306D\-7284Sugar No.11Lois Masseyso@owofinco.fj94,966D\-4920Sugar No.14Stanley Hubbardvuvoz@wericu.th51,289D\-7458CocoaMadge Jacksonzezikku@bicar.hk6,080D\-9372<NAME_EMAIL>62,361D\-8117WheatMartha Rossvokopu@waavu.sz70,716D\-982<NAME_EMAIL>98,090D\-7918WheatNicholas Kellerworwoc@jaluk.aq61,374D\-3205<NAME_EMAIL>54,927Rows per page:

251–25 of 500

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function PaginationModelControlled {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 500,
    maxColumns: 6,
  });
  const [paginationModel, setPaginationModel] = React.useState({
    pageSize: 25,
    page: 0,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        {...data}
        loading={loading}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function PaginationModelControlled {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 500,
 maxColumns: 6,
 });
 const \[paginationModel, setPaginationModel] \= React.useState({
 pageSize: 25,
 page: 0,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 paginationModel\={paginationModel}
 onPaginationModelChange\={setPaginationModel}
 {...data}
 loading\={loading}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonServer\-side pagination
-----------------------

By default, the pagination is handled on the client.
This means you have to give the rows of all pages to the Data Grid.
If your dataset is too big, and you want to fetch the pages on demand, you can use server\-side pagination.



If you enable server\-side pagination with no other server\-side features, then the Data Grid will only be provided with partial data for filtering and sorting.
To be able to work with the entire dataset, you must also implement server\-side filtering and server\-side sorting.
The demo below does exactly that.


DeskCommodityTrader NameTrader EmailQuantityD\-3700Sugar No.11Myra Suttonbiwis@juivuced.mg22,772D\-5026CornViola Wilsonejrurham@ujokot.gov40,850D\-5675Frozen Concentrated <NAME_EMAIL>33,656D\-3427Sugar No.11Virgie Hollandlin@cufuebi.je3,407D\-8605SoybeansLottie Patrickpu@zig.jp43,5541–5 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridSortModel, GridFilterModel } from '@mui/x-data-grid';
import { createFakeServer } from '@mui/x-data-grid-generator';

const SERVER_OPTIONS = {
  useCursorPagination: false,
};

const { useQuery, ...data } = createFakeServer({}, SERVER_OPTIONS);

export default function ServerPaginationFilterSortGrid {
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 5,
  });
  const [sortModel, setSortModel] = React.useState<GridSortModel>([]);
  const [filterModel, setFilterModel] = React.useState<GridFilterModel>({
    items: [],
  });
  const queryOptions = React.useMemo(
     => ({ ...paginationModel, sortModel, filterModel }),
    [paginationModel, sortModel, filterModel],
  );
  const { isLoading, rows, pageInfo } = useQuery(queryOptions);

  // Some API clients return undefined while loading
  // Following lines are here to prevent `rowCount` from being undefined during the loading
  const rowCountRef = React.useRef(pageInfo?.totalRowCount || 0);

  const rowCount = React.useMemo( => {
    if (pageInfo?.totalRowCount !== undefined) {
      rowCountRef.current = pageInfo.totalRowCount;
    }
    return rowCountRef.current;
  }, [pageInfo?.totalRowCount]);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        {...data}
        rowCount={rowCount}
        loading={isLoading}
        pageSizeOptions={[5]}
        paginationModel={paginationModel}
        sortModel={sortModel}
        filterModel={filterModel}
        paginationMode="server"
        sortingMode="server"
        filterMode="server"
        onPaginationModelChange={setPaginationModel}
        onSortModelChange={setSortModel}
        onFilterModelChange={setFilterModel}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridSortModel, GridFilterModel } from '@mui/x\-data\-grid';
import { createFakeServer } from '@mui/x\-data\-grid\-generator';

const SERVER\_OPTIONS \= {
 useCursorPagination: false,
};

const { useQuery, ...data } \= createFakeServer({}, SERVER\_OPTIONS);

export default function ServerPaginationFilterSortGrid {
 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: 5,
 });
 const \[sortModel, setSortModel] \= React.useState\<GridSortModel\>(\[]);
 const \[filterModel, setFilterModel] \= React.useState\<GridFilterModel\>({
 items: \[],
 });
 const queryOptions \= React.useMemo(
  \=\> ({ ...paginationModel, sortModel, filterModel }),
 \[paginationModel, sortModel, filterModel],
 );
 const { isLoading, rows, pageInfo } \= useQuery(queryOptions);

 // Some API clients return undefined while loading
 // Following lines are here to prevent \`rowCount\` from being undefined during the loading
 const rowCountRef \= React.useRef(pageInfo?.totalRowCount \|\| 0\);

 const rowCount \= React.useMemo( \=\> {
 if (pageInfo?.totalRowCount !\=\= undefined) {
 rowCountRef.current \= pageInfo.totalRowCount;
 }
 return rowCountRef.current;
 }, \[pageInfo?.totalRowCount]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 rows\={rows}
 {...data}
 rowCount\={rowCount}
 loading\={isLoading}
 pageSizeOptions\={\[5]}
 paginationModel\={paginationModel}
 sortModel\={sortModel}
 filterModel\={filterModel}
 paginationMode\="server"
 sortingMode\="server"
 filterMode\="server"
 onPaginationModelChange\={setPaginationModel}
 onSortModelChange\={setSortModel}
 onFilterModelChange\={setFilterModel}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonIn general, the server\-side pagination could be categorized into two types:


* Index\-based pagination
* Cursor\-based pagination



Check out Selection—Usage with server\-side pagination for more details.


### Index\-based pagination

The index\-based pagination uses the `page` and `pageSize` to fetch the data from the server page by page.


To enable server\-side pagination, you need to:


* Set the `paginationMode` prop to `server`
* Use the `onPaginationModelChange` prop to react to the page changes and load the data from the server


The server\-side pagination can be further categorized into sub\-types based on the availability of the total number of rows or `rowCount`.


The Data Grid uses the `rowCount` to calculate the number of pages and to show the information about the current state of the pagination in the footer.
You can provide the `rowCount` in one of the following ways:


* **Initialize.**
Use the `initialState.pagination.rowCount` prop to initialize the `rowCount`.
* **Control.**
Use the `rowCount` prop along with `onRowCountChange` to control the `rowCount` and reflect the changes when the row count is updated.
* **Set using the API.**
Use the `apiRef.current.setRowCount` method to set the `rowCount` after the Grid is initialized.


There can be three different possibilities regarding the availability of the `rowCount` on the server\-side:


1. Row count is available (known)
2. Row count is not available (unknown)
3. Row count is available but is not accurate and may update later on (estimated)



The `rowCount` prop is used in server\-side pagination mode to inform the DataGrid about the total number of rows in your dataset.
This prop is ignored when the `paginationMode` is set to `client`, that is when the pagination is handled on the client\-side.


You can configure `rowCount`, `paginationMeta.hasNextPage`, and `estimatedRowCount` props to handle the above scenarios.




|  | `rowCount` | `paginationMeta.hasNextPage` | `estimatedRowCount` |
| --- | --- | --- | --- |
| Known row count | `number` | — | — |
| Unknown row count | `-1` | `boolean` | — |
| Estimated row count | `-1` | `boolean` | `number` |


#### Known row count

Pass the props to the Data Grid as explained in the table above to handle the case when the actual row count is known, as the following example demonstrates.


DeskCommodityTrader NameTrader EmailQuantityD\-1467SoybeansLeah Simpsonjac@ciwabpi.lr8,496D\-8312<NAME_EMAIL>31,116D\-8361Frozen Concentrated <NAME_EMAIL>26,093D\-5106<NAME_EMAIL>95,410D\-8267CocoaIsaac Pittmanomonutu@vema.vc18,9461–5 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { createFakeServer } from '@mui/x-data-grid-generator';

const SERVER_OPTIONS = {
  useCursorPagination: false,
};

const { useQuery, ...data } = createFakeServer({}, SERVER_OPTIONS);

export default function ServerPaginationGrid {
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 5,
  });

  const { isLoading, rows, pageInfo } = useQuery(paginationModel);

  // Some API clients return undefined while loading
  // Following lines are here to prevent `rowCount` from being undefined during the loading
  const rowCountRef = React.useRef(pageInfo?.totalRowCount || 0);

  const rowCount = React.useMemo( => {
    if (pageInfo?.totalRowCount !== undefined) {
      rowCountRef.current = pageInfo.totalRowCount;
    }
    return rowCountRef.current;
  }, [pageInfo?.totalRowCount]);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        {...data}
        rowCount={rowCount}
        loading={isLoading}
        pageSizeOptions={[5]}
        paginationModel={paginationModel}
        paginationMode="server"
        onPaginationModelChange={setPaginationModel}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { createFakeServer } from '@mui/x\-data\-grid\-generator';

const SERVER\_OPTIONS \= {
 useCursorPagination: false,
};

const { useQuery, ...data } \= createFakeServer({}, SERVER\_OPTIONS);

export default function ServerPaginationGrid {
 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: 5,
 });

 const { isLoading, rows, pageInfo } \= useQuery(paginationModel);

 // Some API clients return undefined while loading
 // Following lines are here to prevent \`rowCount\` from being undefined during the loading
 const rowCountRef \= React.useRef(pageInfo?.totalRowCount \|\| 0\);

 const rowCount \= React.useMemo( \=\> {
 if (pageInfo?.totalRowCount !\=\= undefined) {
 rowCountRef.current \= pageInfo.totalRowCount;
 }
 return rowCountRef.current;
 }, \[pageInfo?.totalRowCount]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 rows\={rows}
 {...data}
 rowCount\={rowCount}
 loading\={isLoading}
 pageSizeOptions\={\[5]}
 paginationModel\={paginationModel}
 paginationMode\="server"
 onPaginationModelChange\={setPaginationModel}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon
If the value `rowCount` becomes `undefined` during loading, it will reset the page to zero.
To avoid this issue, you can memoize the `rowCount` value to ensure it doesn't change during loading:



```
const rowCountRef = React.useRef(pageInfo?.totalRowCount || 0);

const rowCount = React.useMemo( => {
  if (pageInfo?.totalRowCount !== undefined) {
    rowCountRef.current = pageInfo.totalRowCount;
  }
  return rowCountRef.current;
}, [pageInfo?.totalRowCount]);

<DataGrid rowCount={rowCount} />;

```
CopyCopied(or Ctrl \+ C)
#### Unknown row count

Pass the props to the Data Grid as explained in the table above to handle the case when the actual row count is unknown, as the following example demonstrates.


DeskCommodityTrader NameTrader EmailQuantityD\-3601SoybeansDonald Nashfi@rowe.org26,257D\-475<NAME_EMAIL>75,849D\-5648Sugar No.14Jesse Reynoldsiwaumded@vu.rw47,477D\-6021Frozen Concentrated <NAME_EMAIL>7,785D\-2956<NAME_EMAIL>72,695Rows per page:

51–5 of more than 5

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridPaginationMeta, useGridApiRef } from '@mui/x-data-grid';
import { createFakeServer } from '@mui/x-data-grid-generator';

const SERVER_OPTIONS = {
  useCursorPagination: false,
};

const rowLength = 98;

const { useQuery, ...data } = createFakeServer({ rowLength }, SERVER_OPTIONS);

export default function ServerPaginationGridNoRowCount {
  const apiRef = useGridApiRef;
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 5,
  });

  const {
    isLoading,
    rows,
    pageInfo: { hasNextPage },
  } = useQuery(paginationModel);

  const paginationMetaRef = React.useRef<GridPaginationMeta>(undefined);

  // Memoize to avoid flickering when the `hasNextPage` is `undefined` during refetch
  const paginationMeta = React.useMemo( => {
    if (
      hasNextPage !== undefined &&
      paginationMetaRef.current?.hasNextPage !== hasNextPage
    ) {
      paginationMetaRef.current = { hasNextPage };
    }
    return paginationMetaRef.current;
  }, [hasNextPage]);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        apiRef={apiRef}
        rows={rows}
        {...data}
        initialState={{ ...data.initialState, pagination: { rowCount: -1 } }}
        paginationMeta={paginationMeta}
        loading={isLoading}
        pageSizeOptions={[5, 10, 25, 50]}
        paginationModel={paginationModel}
        paginationMode="server"
        onPaginationModelChange={setPaginationModel}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridPaginationMeta, useGridApiRef } from '@mui/x\-data\-grid';
import { createFakeServer } from '@mui/x\-data\-grid\-generator';

const SERVER\_OPTIONS \= {
 useCursorPagination: false,
};

const rowLength \= 98;

const { useQuery, ...data } \= createFakeServer({ rowLength }, SERVER\_OPTIONS);

export default function ServerPaginationGridNoRowCount {
 const apiRef \= useGridApiRef;
 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: 5,
 });

 const {
 isLoading,
 rows,
 pageInfo: { hasNextPage },
 } \= useQuery(paginationModel);

 const paginationMetaRef \= React.useRef\<GridPaginationMeta\>(undefined);

 // Memoize to avoid flickering when the \`hasNextPage\` is \`undefined\` during refetch
 const paginationMeta \= React.useMemo( \=\> {
 if (
 hasNextPage !\=\= undefined \&\&
 paginationMetaRef.current?.hasNextPage !\=\= hasNextPage
 ) {
 paginationMetaRef.current \= { hasNextPage };
 }
 return paginationMetaRef.current;
 }, \[hasNextPage]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 apiRef\={apiRef}
 rows\={rows}
 {...data}
 initialState\={{ ...data.initialState, pagination: { rowCount: \-1 } }}
 paginationMeta\={paginationMeta}
 loading\={isLoading}
 pageSizeOptions\={\[5, 10, 25, 50]}
 paginationModel\={paginationModel}
 paginationMode\="server"
 onPaginationModelChange\={setPaginationModel}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by Carbon
The value of the `hasNextPage` variable might become `undefined` during loading if it's handled by some external fetching hook resulting in unwanted computations, one possible solution could be to memoize the `paginationMeta`:



```
const paginationMetaRef = React.useRef<GridPaginationMeta>;

const paginationMeta = React.useMemo( => {
  if (
    hasNextPage !== undefined &&
    paginationMetaRef.current?.hasNextPage !== hasNextPage
  ) {
    paginationMetaRef.current = { hasNextPage };
  }
  return paginationMetaRef.current;
}, [hasNextPage]);

```
CopyCopied(or Ctrl \+ C)
#### Estimated row count

Estimated row count could be considered a hybrid approach that switches between the "Known row count" and "Unknown row count" use cases.


Initially, when an `estimatedRowCount` is set and `rowCount={-1}`, the Data Grid behaves as in the "Unknown row count" use case, but with the `estimatedRowCount` value shown in the pagination footer.


If the number of rows loaded exceeds the `estimatedRowCount`, the Data Grid ignores the `estimatedRowCount` and the behavior is identical to the "Unknown row count" use case.


When the `hasNextPage` returns `false` or `rowCount` is set to a positive number, the Data Grid switches to the "Known row count" behavior.


In the following example, the actual row count is `1000` but the Data Grid is initially provided with `estimatedRowCount={100}`.
You can set the `rowCount` to the actual row count by pressing the "Set Row Count" button.


Set Row CountDeskCommodityTrader NameTrader EmailQuantityD\-9838<NAME_EMAIL>12,550D\-9109CocoaEdna Hillvo@nomkiseg.as14,339D\-9726SoybeansMaggie Matthewsvi@set.sy46,440D\-8216Cotton No.2Eddie Tylersijned@tatcadu.zm5,102D\-365SoybeansMartha Farmertilo@ad.ni62,229D\-9786Cotton No.2Hester Mortonzoici@lahol.fj85,525D\-9347WheatJulian Brewermucughe@jiacedu.bb81,893D\-2706OatsWalter Webbher@mepuzafo.gi58,026D\-2497CocoaGeorgia Bowersdoju@zebriup.aw67,259Rows per page:

501–50 of around 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Button from '@mui/material/Button';
import { DataGrid, useGridApiRef } from '@mui/x-data-grid';
import type { GridPaginationMeta } from '@mui/x-data-grid';
import { createFakeServer } from '@mui/x-data-grid-generator';

const SERVER_OPTIONS = {
  useCursorPagination: false,
};

const { useQuery, ...data } = createFakeServer({ rowLength: 1000 }, SERVER_OPTIONS);

export default function ServerPaginationGridEstimated {
  const apiRef = useGridApiRef;
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 50,
  });

  const {
    isLoading,
    rows,
    pageInfo: { hasNextPage },
  } = useQuery(paginationModel);

  const paginationMetaRef = React.useRef<GridPaginationMeta>({});
  // Memoize to avoid flickering when the `hasNextPage` is `undefined` during refetch
  const paginationMeta = React.useMemo( => {
    if (
      hasNextPage !== undefined &&
      paginationMetaRef.current?.hasNextPage !== hasNextPage
    ) {
      paginationMetaRef.current = { hasNextPage };
    }
    return paginationMetaRef.current;
  }, [hasNextPage]);

  return (
    <div style={{ width: '100%' }}>
      <Button onClick={ => apiRef.current?.setRowCount(1000)}>
        Set Row Count
      </Button>
      <div style={{ height: 400 }}>
        <DataGrid
          apiRef={apiRef}
          rows={rows}
          {...data}
          initialState={{ ...data.initialState, pagination: { rowCount: -1 } }}
          estimatedRowCount={100}
          paginationMeta={paginationMeta}
          loading={isLoading}
          pageSizeOptions={[10, 25, 50, 100]}
          paginationModel={paginationModel}
          paginationMode="server"
          onPaginationModelChange={setPaginationModel}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import Button from '@mui/material/Button';
import { DataGrid, useGridApiRef } from '@mui/x\-data\-grid';
import type { GridPaginationMeta } from '@mui/x\-data\-grid';
import { createFakeServer } from '@mui/x\-data\-grid\-generator';

const SERVER\_OPTIONS \= {
 useCursorPagination: false,
};

const { useQuery, ...data } \= createFakeServer({ rowLength: 1000 }, SERVER\_OPTIONS);

export default function ServerPaginationGridEstimated {
 const apiRef \= useGridApiRef;
 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: 50,
 });

 const {
 isLoading,
 rows,
 pageInfo: { hasNextPage },
 } \= useQuery(paginationModel);

 const paginationMetaRef \= React.useRef\<GridPaginationMeta\>({});
 // Memoize to avoid flickering when the \`hasNextPage\` is \`undefined\` during refetch
 const paginationMeta \= React.useMemo( \=\> {
 if (
 hasNextPage !\=\= undefined \&\&
 paginationMetaRef.current?.hasNextPage !\=\= hasNextPage
 ) {
 paginationMetaRef.current \= { hasNextPage };
 }
 return paginationMetaRef.current;
 }, \[hasNextPage]);

 return (
 \<div style\={{ width: '100%' }}\>
 \<Button onClick\={ \=\> apiRef.current?.setRowCount(1000\)}\>
 Set Row Count
 \</Button\>
 \<div style\={{ height: 400 }}\>
 \<DataGrid
 apiRef\={apiRef}
 rows\={rows}
 {...data}
 initialState\={{ ...data.initialState, pagination: { rowCount: \-1 } }}
 estimatedRowCount\={100}
 paginationMeta\={paginationMeta}
 loading\={isLoading}
 pageSizeOptions\={\[10, 25, 50, 100]}
 paginationModel\={paginationModel}
 paginationMode\="server"
 onPaginationModelChange\={setPaginationModel}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by Carbon
The `hasNextPage` must not be set to `false` until there are *actually* no records left to fetch, because when `hasNextPage` becomes `false`, the Grid considers this as the last page and tries to set the `rowCount` value to a finite value.


If an external data fetching library sets the values to undefined during loading, you can memoize the `paginationMeta` value to ensure it doesn't change during loading as shown in the "Unknown row count" section.



🌍 **Localization of the estimated row count**


The Data Grid uses the Table Pagination component from the Material UI library which doesn't support `estimated` row count. Until this is supported natively by the Table Pagination component, a workaround to make the localization work is to provide the `labelDisplayedRows` function to the `localeText.MuiTablePagination` property as per the locale you are interested in.


The Grid injects an additional variable `estimated` to the `labelDisplayedRows` function which you can use to accommodate the estimated row count.
The following example demonstrates how to show the estimated row count in the pagination footer in the Croatian (hr\-HR) language.



```
const labelDisplayedRows = ({ from, to, count, estimated }) => {
  if (!estimated) {
    return `${from}–${to} od ${count !== -1 ? count : `više nego ${to}`}`;
  }
  const estimateLabel =
    estimated && estimated > to ? `oko ${estimated}` : `više nego ${to}`;
  return `${from}–${to} od ${count !== -1 ? count : estimateLabel}`;
};

<DataGrid
  {...data}
  localeText={{
    MuiTablePagination: {
      labelDisplayedRows,
    },
  }}
/>;

```
CopyCopied(or Ctrl \+ C)
For more information, see the Translation keys section of the localization documentation.


### Cursor\-based pagination

You can also handle servers with cursor\-based pagination.
To do so, you just have to keep track of the next cursor associated with each page you fetched.


Row countKnownUnknownEstimatedDeskCommodityTrader NameTrader EmailQuantityD\-4848<NAME_EMAIL>61,544D\-375CornRay Pittmanwifolsak@voztuj.pn69,555D\-4373CornLinnie Garciabil@iciwu.dk70,647D\-5505Frozen Concentrated <NAME_EMAIL>55,184D\-9214<NAME_EMAIL>23,9261–5 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridRowId,
  GridPaginationModel,
  GridPaginationMeta,
} from '@mui/x-data-grid';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { createFakeServer } from '@mui/x-data-grid-generator';

const PAGE_SIZE = 5;

const SERVER_OPTIONS = {
  useCursorPagination: true,
};

const { useQuery, ...data } = createFakeServer({}, SERVER_OPTIONS);

type RowCountType = 'known' | 'unknown' | 'estimated';

export default function CursorPaginationGrid {
  const [rowCountType, setRowCountType] = React.useState<RowCountType>('known');

  const mapPageToNextCursor = React.useRef<{ [page: number]: GridRowId }>({});

  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: PAGE_SIZE,
  });

  const queryOptions = React.useMemo(
     => ({
      cursor: mapPageToNextCursor.current[paginationModel.page - 1],
      pageSize: paginationModel.pageSize,
    }),
    [paginationModel],
  );
  const {
    isLoading,
    rows,
    pageInfo: { hasNextPage, nextCursor, totalRowCount },
  } = useQuery(queryOptions);

  const handlePaginationModelChange = (newPaginationModel: GridPaginationModel) => {
    // We have the cursor, we can allow the page transition.
    if (
      newPaginationModel.page === 0 ||
      mapPageToNextCursor.current[newPaginationModel.page - 1]
    ) {
      setPaginationModel(newPaginationModel);
    }
  };

  const paginationMetaRef = React.useRef<GridPaginationMeta>(undefined);

  // Memoize to avoid flickering when the `hasNextPage` is `undefined` during refetch
  const paginationMeta = React.useMemo( => {
    if (
      hasNextPage !== undefined &&
      paginationMetaRef.current?.hasNextPage !== hasNextPage
    ) {
      paginationMetaRef.current = { hasNextPage };
    }
    return paginationMetaRef.current;
  }, [hasNextPage]);

  React.useEffect( => {
    if (!isLoading && nextCursor) {
      // We add nextCursor when available
      mapPageToNextCursor.current[paginationModel.page] = nextCursor;
    }
  }, [paginationModel.page, isLoading, nextCursor]);

  // Some API clients return undefined while loading
  // Following lines are here to prevent `rowCountState` from being undefined during the loading
  const [rowCountState, setRowCountState] = React.useState(totalRowCount || 0);
  React.useEffect( => {
    if (rowCountType === 'known') {
      setRowCountState((prevRowCountState) =>
        totalRowCount !== undefined ? totalRowCount : prevRowCountState,
      );
    }
    if (
      (rowCountType === 'unknown' || rowCountType === 'estimated') &&
      paginationMeta?.hasNextPage !== false
    ) {
      setRowCountState(-1);
    }
  }, [paginationMeta?.hasNextPage, rowCountType, totalRowCount]);

  const prevEstimatedRowCount = React.useRef<number | undefined>(undefined);
  const estimatedRowCount = React.useMemo( => {
    if (rowCountType === 'estimated') {
      if (totalRowCount !== undefined) {
        if (prevEstimatedRowCount.current === undefined) {
          prevEstimatedRowCount.current = totalRowCount / 2;
        }
        return totalRowCount / 2;
      }
      return prevEstimatedRowCount.current;
    }
    return undefined;
  }, [rowCountType, totalRowCount]);

  return (
    <div style={{ width: '100%' }}>
      <FormControl>
        <FormLabel id="demo-cursor-pagination-buttons-group-label">
          Row count
        </FormLabel>
        <RadioGroup
          row
          aria-labelledby="demo-cursor-pagination-buttons-group-label"
          name="cursor-pagination-buttons-group"
          value={rowCountType}
          onChange={(event) => setRowCountType(event.target.value as RowCountType)}
        >
          <FormControlLabel value="known" control={<Radio />} label="Known" />
          <FormControlLabel value="unknown" control={<Radio />} label="Unknown" />
          <FormControlLabel
            value="estimated"
            control={<Radio />}
            label="Estimated"
          />
        </RadioGroup>
      </FormControl>
      <div style={{ height: 400 }}>
        <DataGrid
          rows={rows}
          {...data}
          pageSizeOptions={[PAGE_SIZE]}
          rowCount={rowCountState}
          onRowCountChange={(newRowCount) => setRowCountState(newRowCount)}
          estimatedRowCount={estimatedRowCount}
          paginationMeta={paginationMeta}
          paginationMode="server"
          onPaginationModelChange={handlePaginationModelChange}
          paginationModel={paginationModel}
          loading={isLoading}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGrid,
 GridRowId,
 GridPaginationModel,
 GridPaginationMeta,
} from '@mui/x\-data\-grid';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { createFakeServer } from '@mui/x\-data\-grid\-generator';

const PAGE\_SIZE \= 5;

const SERVER\_OPTIONS \= {
 useCursorPagination: true,
};

const { useQuery, ...data } \= createFakeServer({}, SERVER\_OPTIONS);

type RowCountType \= 'known' \| 'unknown' \| 'estimated';

export default function CursorPaginationGrid {
 const \[rowCountType, setRowCountType] \= React.useState\<RowCountType\>('known');

 const mapPageToNextCursor \= React.useRef\<{ \[page: number]: GridRowId }\>({});

 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: PAGE\_SIZE,
 });

 const queryOptions \= React.useMemo(
  \=\> ({
 cursor: mapPageToNextCursor.current\[paginationModel.page \- 1],
 pageSize: paginationModel.pageSize,
 }),
 \[paginationModel],
 );
 const {
 isLoading,
 rows,
 pageInfo: { hasNextPage, nextCursor, totalRowCount },
 } \= useQuery(queryOptions);

 const handlePaginationModelChange \= (newPaginationModel: GridPaginationModel) \=\> {
 // We have the cursor, we can allow the page transition.
 if (
 newPaginationModel.page \=\=\= 0 \|\|
 mapPageToNextCursor.current\[newPaginationModel.page \- 1]
 ) {
 setPaginationModel(newPaginationModel);
 }
 };

 const paginationMetaRef \= React.useRef\<GridPaginationMeta\>(undefined);

 // Memoize to avoid flickering when the \`hasNextPage\` is \`undefined\` during refetch
 const paginationMeta \= React.useMemo( \=\> {
 if (
 hasNextPage !\=\= undefined \&\&
 paginationMetaRef.current?.hasNextPage !\=\= hasNextPage
 ) {
 paginationMetaRef.current \= { hasNextPage };
 }
 return paginationMetaRef.current;
 }, \[hasNextPage]);

 React.useEffect( \=\> {
 if (!isLoading \&\& nextCursor) {
 // We add nextCursor when available
 mapPageToNextCursor.current\[paginationModel.page] \= nextCursor;
 }
 }, \[paginationModel.page, isLoading, nextCursor]);

 // Some API clients return undefined while loading
 // Following lines are here to prevent \`rowCountState\` from being undefined during the loading
 const \[rowCountState, setRowCountState] \= React.useState(totalRowCount \|\| 0\);
 React.useEffect( \=\> {
 if (rowCountType \=\=\= 'known') {
 setRowCountState((prevRowCountState) \=\>
 totalRowCount !\=\= undefined ? totalRowCount : prevRowCountState,
 );
 }
 if (
 (rowCountType \=\=\= 'unknown' \|\| rowCountType \=\=\= 'estimated') \&\&
 paginationMeta?.hasNextPage !\=\= false
 ) {
 setRowCountState(\-1\);
 }
 }, \[paginationMeta?.hasNextPage, rowCountType, totalRowCount]);

 const prevEstimatedRowCount \= React.useRef\<number \| undefined\>(undefined);
 const estimatedRowCount \= React.useMemo( \=\> {
 if (rowCountType \=\=\= 'estimated') {
 if (totalRowCount !\=\= undefined) {
 if (prevEstimatedRowCount.current \=\=\= undefined) {
 prevEstimatedRowCount.current \= totalRowCount / 2;
 }
 return totalRowCount / 2;
 }
 return prevEstimatedRowCount.current;
 }
 return undefined;
 }, \[rowCountType, totalRowCount]);

 return (
 \<div style\={{ width: '100%' }}\>
 \<FormControl\>
 \<FormLabel id\="demo\-cursor\-pagination\-buttons\-group\-label"\>
 Row count
 \</FormLabel\>
 \<RadioGroup
 row
 aria\-labelledby\="demo\-cursor\-pagination\-buttons\-group\-label"
 name\="cursor\-pagination\-buttons\-group"
 value\={rowCountType}
 onChange\={(event) \=\> setRowCountType(event.target.value as RowCountType)}
 \>
 \<FormControlLabel value\="known" control\={\<Radio /\>} label\="Known" /\>
 \<FormControlLabel value\="unknown" control\={\<Radio /\>} label\="Unknown" /\>
 \<FormControlLabel
 value\="estimated"
 control\={\<Radio /\>}
 label\="Estimated"
 /\>
 \</RadioGroup\>
 \</FormControl\>
 \<div style\={{ height: 400 }}\>
 \<DataGrid
 rows\={rows}
 {...data}
 pageSizeOptions\={\[PAGE\_SIZE]}
 rowCount\={rowCountState}
 onRowCountChange\={(newRowCount) \=\> setRowCountState(newRowCount)}
 estimatedRowCount\={estimatedRowCount}
 paginationMeta\={paginationMeta}
 paginationMode\="server"
 onPaginationModelChange\={handlePaginationModelChange}
 paginationModel\={paginationModel}
 loading\={isLoading}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- You asked, we delivered! Our Free Plan now includes a Custom Domain, 5 Actions, and 25,000 MAUs.ad by CarbonCustom pagination UI
--------------------

You can customize the rendering of the pagination in the footer following the component section of the documentation.


apiRef
------

The Data Grid exposes a set of methods via the `apiRef` object that are used internally in the implementation of the pagination feature.
The reference below describes the relevant functions.
See API object for more details.



This API should only be used as a last resort when the Data Grid's built\-in props aren't sufficient for your specific use case.


### setPageSets the displayed page to the value given by `page`.

###### Signature:

Copy(or Ctrl \+ C)
```
setPage: (page: number) => void
```
### setPageSizeSets the number of displayed rows to the value given by `pageSize`.

###### Signature:

Copy(or Ctrl \+ C)
```
setPageSize: (pageSize: number) => void
```
### setPaginationMetaSets the `paginationMeta` to a new value.

###### Signature:

Copy(or Ctrl \+ C)
```
setPaginationMeta: (paginationMeta: GridPaginationMeta) => void
```
### setPaginationModelSets the `paginationModel` to a new value.

###### Signature:

Copy(or Ctrl \+ C)
```
setPaginationModel: (model: GridPaginationModel) => void
```
### setRowCountSets the `rowCount` to a new value.

###### Signature:

Copy(or Ctrl \+ C)
```
setRowCount: (rowCount: number) => void
```
Selectors
---------

### gridPageCountSelectorGet the amount of pages needed to display all the rows if the pagination is enabled

###### Signature:

Copy(or Ctrl \+ C)
```
gridPageCountSelector: (apiRef: GridApiRef) => number
```
###### Example

Copy(or Ctrl \+ C)
```
const pageCount = gridPageCountSelector(apiRef);
```
### gridPageSelectorGet the index of the page to render if the pagination is enabled

###### Signature:

Copy(or Ctrl \+ C)
```
gridPageSelector: (apiRef: GridApiRef) => number
```
###### Example

Copy(or Ctrl \+ C)
```
const page = gridPageSelector(apiRef);
```
### gridPageSizeSelectorGet the maximum amount of rows to display on a single page if the pagination is enabled

###### Signature:

Copy(or Ctrl \+ C)
```
gridPageSizeSelector: (apiRef: GridApiRef) => number
```
###### Example

Copy(or Ctrl \+ C)
```
const pageSize = gridPageSizeSelector(apiRef);
```
### gridPaginatedVisibleSortedGridRowEntriesSelectorGet the id and the model of each row to include in the current page if the pagination is enabled.

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginatedVisibleSortedGridRowEntriesSelector: (apiRef: GridApiRef) => GridRowEntry<GridValidRowModel>[]
```
###### Example

Copy(or Ctrl \+ C)
```
const paginatedVisibleSortedGridRowEntries = gridPaginatedVisibleSortedGridRowEntriesSelector(apiRef);
```
### gridPaginatedVisibleSortedGridRowIdsSelectorGet the id of each row to include in the current page if the pagination is enabled.

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginatedVisibleSortedGridRowIdsSelector: (apiRef: GridApiRef) => GridRowId[]
```
###### Example

Copy(or Ctrl \+ C)
```
const paginatedVisibleSortedGridRowIds = gridPaginatedVisibleSortedGridRowIdsSelector(apiRef);
```
### gridPaginationMetaSelectorGet the pagination meta

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginationMetaSelector: (apiRef: GridApiRef) => GridPaginationMeta
```
###### Example

Copy(or Ctrl \+ C)
```
const paginationMeta = gridPaginationMetaSelector(apiRef);
```
### gridPaginationModelSelectorGet the pagination model

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginationModelSelector: (apiRef: GridApiRef) => GridPaginationModel
```
###### Example

Copy(or Ctrl \+ C)
```
const paginationModel = gridPaginationModelSelector(apiRef);
```
### gridPaginationRowCountSelectorGet the row count

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginationRowCountSelector: (apiRef: GridApiRef) => number
```
###### Example

Copy(or Ctrl \+ C)
```
const paginationRowCount = gridPaginationRowCountSelector(apiRef);
```
### gridPaginationRowRangeSelectorGet the index of the first and the last row to include in the current page if the pagination is enabled.

###### Signature:

Copy(or Ctrl \+ C)
```
gridPaginationRowRangeSelector: (apiRef: GridApiRef) => { firstRowIndex: number; lastRowIndex: number } | null
```
###### Example

Copy(or Ctrl \+ C)
```
const paginationRowRange = gridPaginationRowRangeSelector(apiRef);
```
### gridVisibleRowsSelectorGet the rows, range and rowIndex lookup map after filtering and sorting.
Does not contain the collapsed children.

###### Signature:

Copy(or Ctrl \+ C)
```
gridVisibleRowsSelector: (apiRef: GridApiRef) => { rows: GridRowEntry<GridValidRowModel>[]; range: { firstRowIndex: number; lastRowIndex: number } | null; rowIdToIndexMap: Map<GridRowId, number> }
```
###### Example

Copy(or Ctrl \+ C)
```
const visibleRows = gridVisibleRowsSelector(apiRef);
```
More information about the selectors and how to use them on the dedicated page


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

RecipesRow selection

---

•

Blog•

Store
