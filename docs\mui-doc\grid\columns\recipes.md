Data Grid \- Column customization recipes
=========================================

Advanced column customization recipes.


Squarespace tools make it easy to create a beautiful and unique website.

ads via Carbon



Persisting column width and order
---------------------------------

When the `columns` prop reference is updated, the column width and order is reset to the `colDef.width` and the order of the `colDef` object and any updates will be lost.
This is because the Data Grid considers update of the columns prop as a new set of columns, and the previous state is discarded.


To persist the column width and order when the `columns` prop is updated, consider persisting the state of the columns in the userland.


Update columns referenceidusernameage1@MUI20Total Rows: 1JSTSExpand codeCopy(or Ctrl \+ C)
```
<Button onClick={ => setIndex((prev) => prev + 1)}>
  Update columns reference
</Button>
<div style={{ height: 250 }}>
  <DataGridPro
    apiRef={apiRef}
    columns={columnsState.columns}
    onColumnWidthChange={columnsState.onColumnWidthChange}
    onColumnOrderChange={columnsState.onColumnOrderChange}
    rows={rows}
  />
</div>  

```
\<Button onClick\={ \=\> setIndex((prev) \=\> prev \+ 1\)}\>
 Update columns reference
\</Button\>
\<div style\={{ height: 250 }}\>
 \<DataGridPro
 apiRef\={apiRef}
 columns\={columnsState.columns}
 onColumnWidthChange\={columnsState.onColumnWidthChange}
 onColumnOrderChange\={columnsState.onColumnOrderChange}
 rows\={rows}
 /\>
\</div\>Press `Enter` to start editing
Column ordering is a Pro feature, to use it you must be on a Pro or Premium plan.


Edit this pageWas this page helpful?



---

Column pinningRow definition

---

•

Blog•

Store
