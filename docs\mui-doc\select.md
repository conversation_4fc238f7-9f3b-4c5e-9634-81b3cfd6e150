Select
======

Select components are used for collecting user provided information from a list of options.


Sell products, services, content and more with Squarespace.

ads via Carbon



* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Basic select
------------

Menus are positioned under their emitting elements, unless they are close to the bottom of the viewport.


Age​AgeJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormControl fullWidth>
  <InputLabel id="demo-simple-select-label">Age</InputLabel>
  <Select
    labelId="demo-simple-select-label"
    id="demo-simple-select"
    value={age}
    label="Age"
    onChange={handleChange}
  >
    <MenuItem value={10}>Ten</MenuItem>
    <MenuItem value={20}>Twenty</MenuItem>
    <MenuItem value={30}>Thirty</MenuItem>
  </Select>
</FormControl>  

```
\<FormControl fullWidth\>
 \<InputLabel id\="demo\-simple\-select\-label"\>Age\</InputLabel\>
 \<Select
 labelId\="demo\-simple\-select\-label"
 id\="demo\-simple\-select"
 value\={age}
 label\="Age"
 onChange\={handleChange}
 \>
 \<MenuItem value\={10}\>Ten\</MenuItem\>
 \<MenuItem value\={20}\>Twenty\</MenuItem\>
 \<MenuItem value\={30}\>Thirty\</MenuItem\>
 \</Select\>
\</FormControl\>Press `Enter` to start editingAdvanced features
-----------------

The Select component is meant to be interchangeable with a native `<select>` element.


If you are looking for more advanced features, like combobox, multiselect, autocomplete, async or creatable support, head to the `Autocomplete` component.
It's meant to be an improved version of the "react\-select" and "downshift" packages.


Props
-----

The Select component is implemented as a custom `<input>` element of the InputBase.
It extends the text field components subcomponents, either the OutlinedInput, Input, or FilledInput, depending on the variant selected.
It shares the same styles and many of the same props. Refer to the respective component's API page for details.



Unlike input components, the `placeholder` prop is not available in Select. To add a placeholder, refer to the placeholder section below.


### Filled and standard variants

Age​Age​JSTSShow code### Labels and helper text

Age​AgeWith label \+ helper text

*None*​Without label

JSTSShow code
Note that when using FormControl with the outlined variant of the Select, you need to provide a label in two places: in the InputLabel component and in the `label` prop of the Select component (see the above demo).


### Auto width

Age​AgeJSTSShow code### Small Size

Age​AgeJSTSShow code### Other props

Age​AgeDisabled

Age​AgeError

Age​AgeRead only

Age \*​Age \* \*Required

JSTSShow codeNative select
-------------

As the user experience can be improved on mobile using the native select of the platform,
we allow such pattern.


AgeTenTwentyThirtyJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormControl fullWidth>
  <InputLabel variant="standard" htmlFor="uncontrolled-native">
    Age
  </InputLabel>
  <NativeSelect
    defaultValue={30}
    inputProps={{
      name: 'age',
      id: 'uncontrolled-native',
    }}
  >
    <option value={10}>Ten</option>
    <option value={20}>Twenty</option>
    <option value={30}>Thirty</option>
  </NativeSelect>
</FormControl>  

```
\<FormControl fullWidth\>
 \<InputLabel variant\="standard" htmlFor\="uncontrolled\-native"\>
 Age
 \</InputLabel\>
 \<NativeSelect
 defaultValue\={30}
 inputProps\={{
 name: 'age',
 id: 'uncontrolled\-native',
 }}
 \>
 \<option value\={10}\>Ten\</option\>
 \<option value\={20}\>Twenty\</option\>
 \<option value\={30}\>Thirty\</option\>
 \</NativeSelect\>
\</FormControl\>Press `Enter` to start editingTextField
---------

The `TextField` wrapper component is a complete form control including a label, input and help text.
You can find an example with the select mode in this section.


Customization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


The first step is to style the `InputBase` component.
Once it's styled, you can either use it directly as a text field or provide it to the select `input` prop to have a `select` field.
Notice that the `"standard"` variant is easier to customize, since it does not wrap the contents in a `fieldset`/`legend` markup.


AgeAge​AgeTenTwentyThirtyJSTSShow code🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


Multiple select
---------------

The `Select` component can handle multiple selections.
It's enabled with the `multiple` prop.


Like with the single selection, you can pull out the new value by accessing `event.target.value` in the `onChange` callback. It's always an array.


### Default

Name​NameJSTSShow code### Checkmarks

Tag​TagJSTSShow code### Chip

Chip​ChipJSTSShow code### Placeholder

*Placeholder*​JSTSShow code### Native

NativeOliver HansenVan HenryApril TuckerRalph HubbardOmar AlexanderCarlos AbbottMiriam WagnerBradley WilkersonVirginia AndrewsKelly SnyderNativeJSTSShow codeControlling the open state
--------------------------

You can control the open state of the select with the `open` prop. Alternatively, it is also possible to set the initial (uncontrolled) open state of the component with the `defaultOpen` prop.



* A component is **controlled** when it's managed by its parent using props.
* A component is **uncontrolled** when it's managed by its own local state.


Learn more about controlled and uncontrolled components in the React documentation.


Open the selectAge​AgeJSTSShow codeWith a dialog
-------------

While it's discouraged by the Material Design guidelines, you can use a select inside a dialog.


Open select dialogJSTSShow codeGrouping
--------

Display categories with the `ListSubheader` component or the native `<optgroup>` element.


GroupingOption 1Option 2Option 3Option 4GroupingGrouping​GroupingJSTSShow code
If you wish to wrap the ListSubheader in a custom component, you'll have to annotate it so Material UI can handle it properly when determining focusable elements.


You have two options for solving this:
Option 1: Define a static boolean field called `muiSkipListHighlight` on your component function, and set it to `true`:



```
function MyListSubheader(props: ListSubheaderProps) {
  return <ListSubheader {...props} />;
}

MyListSubheader.muiSkipListHighlight = true;
export default MyListSubheader;

// elsewhere:

return (
  <Select>
    <MyListSubheader>Group 1</MyListSubheader>
    <MenuItem value={1}>Option 1</MenuItem>
    <MenuItem value={2}>Option 2</MenuItem>
    <MyListSubheader>Group 2</MyListSubheader>
    <MenuItem value={3}>Option 3</MenuItem>
    <MenuItem value={4}>Option 4</MenuItem>
    {/* ... */}
  </Select>

```
CopyCopied(or Ctrl \+ C)
Option 2: Place a `muiSkipListHighlight` prop on each instance of your component.
The prop doesn't have to be forwarded to the ListSubheader, nor present in the underlying DOM element.
It just has to be placed on a component that's used as a subheader.



```
export default function MyListSubheader(
  props: ListSubheaderProps & { muiSkipListHighlight: boolean },
) {
  const { muiSkipListHighlight, ...other } = props;
  return <ListSubheader {...other} />;
}

// elsewhere:

return (
  <Select>
    <MyListSubheader muiSkipListHighlight>Group 1</MyListSubheader>
    <MenuItem value={1}>Option 1</MenuItem>
    <MenuItem value={2}>Option 2</MenuItem>
    <MyListSubheader muiSkipListHighlight>Group 2</MyListSubheader>
    <MenuItem value={3}>Option 3</MenuItem>
    <MenuItem value={4}>Option 4</MenuItem>
    {/* ... */}
  </Select>
);

```
CopyCopied(or Ctrl \+ C)
We recommend the first option as it doesn't require updating all the usage sites of the component.


Keep in mind this is **only necessary** if you wrap the ListSubheader in a custom component.
If you use the ListSubheader directly, **no additional code is required**.


Accessibility
-------------

To properly label your `Select` input you need an extra element with an `id` that contains a label.
That `id` needs to match the `labelId` of the `Select`, for example:



```
<InputLabel id="label">Age</InputLabel>
<Select labelId="label" id="select" value="20">
  <MenuItem value="10">Ten</MenuItem>
  <MenuItem value="20">Twenty</MenuItem>
</Select>

```
CopyCopied(or Ctrl \+ C)
Alternatively a `TextField` with an `id` and `label` creates the proper markup and
ids for you:



```
<TextField id="select" label="Age" value="20" select>
  <MenuItem value="10">Ten</MenuItem>
  <MenuItem value="20">Twenty</MenuItem>
</TextField>

```
CopyCopied(or Ctrl \+ C)
For a native select, you should mention a label by giving the value of the `id` attribute of the select element to the `InputLabel`'s `htmlFor` attribute:



```
<InputLabel htmlFor="select">Age</InputLabel>
<NativeSelect id="select">
  <option value="10">Ten</option>
  <option value="20">Twenty</option>
</NativeSelect>

```
CopyCopied(or Ctrl \+ C)
Unstyled
--------

Use the Base UI Select for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<NativeSelect />`
* `<Select />`



