TITLE: Building a Custom Dropdown with Headless UI and React
DESCRIPTION: This snippet demonstrates how to create a custom dropdown menu using the `@headlessui/react` library. It leverages `Menu`, `Menu.Button`, and `Menu.Items` components to provide built-in accessibility features like keyboard navigation and ARIA attribute management. The example shows how to style the components using Tailwind CSS utility classes and includes active and disabled states for menu items, abstracting away complex accessibility logic.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-unstyled-accessible-ui-components/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { Menu } from "@headlessui/react";

function MyDropdown() {
  return (
    <Menu as="div" className="relative">
      <Menu.Button className="rounded bg-blue-600 px-4 py-2 text-white ...">Options</Menu.Button>
      <Menu.Items className="absolute right-0 mt-1">
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/account-settings">
              Account settings
            </a>
          )}
        </Menu.Item>
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/documentation">
              Documentation
            </a>
          )}
        </Menu.Item>
        <Menu.Item disabled>
          <span className="opacity-75 ...">Invite a friend (coming soon!)</span>
        </Menu.Item>
      </Menu.Items>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Applying Dark Mode Styles to HTML Elements with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to apply dark mode specific styles using Tailwind CSS's `dark` variant. It shows how to change background colors (`dark:bg-gray-800`), text colors (`dark:text-white`, `dark:text-gray-400`), and other properties when the user's system is in dark mode. The `dark:` prefix conditionally applies the utility class based on the `prefers-color-scheme` media query.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code word:dark\:bg-gray-800] -->
<!-- prettier-ignore -->
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg class="h-6 w-6 stroke-white" ...>
        <!-- ... -->
      </svg>
    </span>
  </div>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-white] -->
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-gray-400] -->
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Styling Parent Based on Checked Descendant State (Tailwind CSS)
DESCRIPTION: This HTML snippet demonstrates how to use the `has-checked` pseudo-class in Tailwind CSS to apply styles to a parent `<label>` element when its nested radio input is checked. It showcases conditional styling for background, text, and ring colors, including dark mode variations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<label
  class="has-checked:bg-indigo-50 has-checked:text-indigo-900 has-checked:ring-indigo-200 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked:ring-indigo-900 ..."
>
  <svg fill="currentColor">
    <!-- ... -->
  </svg>
  Google Pay
  <input type="radio" class="checked:border-indigo-500 ..." />
</label>
```

----------------------------------------

TITLE: Exposing Theme Values as Native CSS Variables
DESCRIPTION: This CSS output snippet shows how Tailwind CSS makes all defined theme values available as native CSS variables within the :root selector in the compiled dist/main.css file. This allows developers to directly reference these theme values in any custom CSS without needing special Tailwind functions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_8

LANGUAGE: css
CODE:
```
/* [!code filename:dist/main.css] */
:root {
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  /* ... */
  --color-green-800: #3f6212;
  --color-green-900: #365314;
  --color-green-950: #1a2e05;
}
```

----------------------------------------

TITLE: JavaScript for Three-Way Theme Toggling (Light, Dark, System)
DESCRIPTION: This JavaScript snippet provides logic for a three-way theme toggle, supporting light, dark, and system-preferred modes. It checks `localStorage` for a saved theme preference and falls back to `window.matchMedia` for system preference if no explicit theme is set. It then applies or removes the `dark` class on the `<html>` element, and includes examples for explicitly setting light, dark, or system themes via `localStorage`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#_snippet_5

LANGUAGE: javascript
CODE:
```
document.documentElement.classList.toggle(
  "dark",
  localStorage.theme === "dark" ||
    (!("theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)
);

// Whenever the user explicitly chooses light mode
localStorage.theme = "light";

// Whenever the user explicitly chooses dark mode
localStorage.theme = "dark";

// Whenever the user explicitly chooses to respect the OS preference
localStorage.removeItem("theme");
```

----------------------------------------

TITLE: Styling Radio Button Labels with Tailwind CSS and Pointer Variants (JSX)
DESCRIPTION: This JSX snippet demonstrates styling a radio button label using Tailwind CSS, including responsive adjustments for coarse pointer devices (e.g., touchscreens) via the `pointer-coarse:p-4` utility. It sets up a flexible layout, applies various background, text, ring, and focus styles, and visually hides the native radio input.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_16

LANGUAGE: JSX
CODE:
```
<label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
          <input type="radio" name="memory-option" value="128 GB" className="sr-only" />
          <span>128 GB</span>
        </label>
```

----------------------------------------

TITLE: Applying Dark Mode Styles with Tailwind CSS in HTML
DESCRIPTION: This HTML snippet illustrates the application of Tailwind CSS `dark:` variants to conditionally style elements based on the active theme. It showcases how `dark:bg-gray-800`, `dark:text-white`, and `dark:text-gray-400` are used to change background and text colors in dark mode, providing a concise example of responsive theming.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_8

LANGUAGE: html
CODE:
```
<!-- [!code word:dark\:bg-gray-800] -->
<!-- prettier-ignore -->
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg class="h-6 w-6 stroke-white" ...>
        <!-- ... -->
      </svg>
    </span>
  </div>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-white] -->
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-gray-400] -->
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Importing Tailwind CSS Base Layers
DESCRIPTION: This CSS snippet demonstrates the core imports when `tailwindcss` is included in a project. It defines the CSS layers for theme, base, components, and utilities, and then imports the respective CSS files into their designated layers, setting up the foundational structure for Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_8

LANGUAGE: css
CODE:
```
@layer theme, base, components, utilities;

@import "./theme.css" layer(theme);
@import "./preflight.css" layer(base);
@import "./utilities.css" layer(utilities);
```

----------------------------------------

TITLE: Integrating Tailwind CSS v4.0 with Vite
DESCRIPTION: This TypeScript configuration snippet demonstrates how to integrate Tailwind CSS v4.0 into a Vite project using the dedicated @tailwindcss/vite plugin. It imports defineConfig from Vite and the tailwindcss plugin, then adds the plugin to the plugins array within the Vite configuration, offering improved performance over the PostCSS plugin for Vite users.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_7

LANGUAGE: TypeScript
CODE:
```
import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
  plugins: [
    tailwindcss()
  ]
});
```

----------------------------------------

TITLE: Defining OKLCH Color Variables for Tailwind CSS
DESCRIPTION: This CSS snippet defines a comprehensive set of custom properties for various color palettes using the OKLCH color model. Each variable, named with a color and shade (e.g., `--color-cyan-500`), provides a specific color value in the perceptually uniform OKLCH format, ensuring consistent and accessible color usage across a design system. These variables serve as foundational color tokens for a framework like Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_29

LANGUAGE: CSS
CODE:
```
--color-cyan-300: oklch(0.865 0.127 207.078);
  --color-cyan-400: oklch(0.789 0.154 211.53);
  --color-cyan-500: oklch(0.715 0.143 215.221);
  --color-cyan-600: oklch(0.609 0.126 221.723);
  --color-cyan-700: oklch(0.52 0.105 223.128);
  --color-cyan-800: oklch(0.45 0.085 224.283);
  --color-cyan-900: oklch(0.398 0.07 227.392);
  --color-cyan-950: oklch(0.302 0.056 229.695);

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-indigo-50: oklch(0.962 0.018 272.314);
  --color-indigo-100: oklch(0.93 0.034 272.788);
  --color-indigo-200: oklch(0.87 0.065 274.039);
  --color-indigo-300: oklch(0.785 0.115 274.713);
  --color-indigo-400: oklch(0.673 0.182 276.935);
  --color-indigo-500: oklch(0.585 0.233 277.117);
  --color-indigo-600: oklch(0.511 0.262 276.966);
  --color-indigo-700: oklch(0.457 0.24 277.023);
  --color-indigo-800: oklch(0.398 0.195 277.366);
  --color-indigo-900: oklch(0.359 0.144 278.697);
  --color-indigo-950: oklch(0.257 0.09 281.288);

  --color-violet-50: oklch(0.969 0.016 293.756);
  --color-violet-100: oklch(0.943 0.029 294.588);
  --color-violet-200: oklch(0.894 0.057 293.283);
  --color-violet-300: oklch(0.811 0.111 293.571);
  --color-violet-400: oklch(0.702 0.183 293.541);
  --color-violet-500: oklch(0.606 0.25 292.717);
  --color-violet-600: oklch(0.541 0.281 293.009);
  --color-violet-700: oklch(0.491 0.27 292.581);
  --color-violet-800: oklch(0.432 0.232 292.759);
  --color-violet-900: oklch(0.38 0.189 293.745);
  --color-violet-950: oklch(0.283 0.141 291.089);

  --color-purple-50: oklch(0.977 0.014 308.299);
  --color-purple-100: oklch(0.946 0.033 307.174);
  --color-purple-200: oklch(0.902 0.063 306.703);
  --color-purple-300: oklch(0.827 0.119 306.383);
  --color-purple-400: oklch(0.714 0.203 305.504);
  --color-purple-500: oklch(0.627 0.265 303.9);
  --color-purple-600: oklch(0.558 0.288 302.321);
  --color-purple-700: oklch(0.496 0.265 301.924);
  --color-purple-800: oklch(0.438 0.218 303.724);
  --color-purple-900: oklch(0.381 0.176 304.987);
  --color-purple-950: oklch(0.291 0.149 302.717);

  --color-fuchsia-50: oklch(0.977 0.017 320.058);
  --color-fuchsia-100: oklch(0.952 0.037 318.852);
  --color-fuchsia-200: oklch(0.903 0.076 319.62);
  --color-fuchsia-300: oklch(0.833 0.145 321.434);
  --color-fuchsia-400: oklch(0.74 0.238 322.16);
  --color-fuchsia-500: oklch(0.667 0.295 322.15);
  --color-fuchsia-600: oklch(0.591 0.293 322.896);
  --color-fuchsia-700: oklch(0.518 0.253 323.949);
  --color-fuchsia-800: oklch(0.452 0.211 324.591);
  --color-fuchsia-900: oklch(0.401 0.17 325.612);
  --color-fuchsia-950: oklch(0.293 0.136 325.661);

  --color-pink-50: oklch(0.971 0.014 343.198);
  --color-pink-100: oklch(0.948 0.028 342.258);
  --color-pink-200: oklch(0.899 0.061 343.231);
  --color-pink-300: oklch(0.823 0.12 346.018);
  --color-pink-400: oklch(0.718 0.202 349.761);
  --color-pink-500: oklch(0.656 0.241 354.308);
  --color-pink-600: oklch(0.592 0.249 0.584);
  --color-pink-700: oklch(0.525 0.223 3.958);
  --color-pink-800: oklch(0.459 0.187 3.815);
  --color-pink-900: oklch(0.408 0.153 2.432);
  --color-pink-950: oklch(0.284 0.109 3.907);

  --color-rose-50: oklch(0.969 0.015 12.422);
  --color-rose-100: oklch(0.941 0.03 12.58);
  --color-rose-200: oklch(0.892 0.058 10.001);
  --color-rose-300: oklch(0.81 0.117 11.638);
  --color-rose-400: oklch(0.712 0.194 13.428);
  --color-rose-500: oklch(0.645 0.246 16.439);
  --color-rose-600: oklch(0.586 0.253 17.585);
  --color-rose-700: oklch(0.514 0.222 16.935);
  --color-rose-800: oklch(0.455 0.188 13.697);
  --color-rose-900: oklch(0.41 0.159 10.272);
  --color-rose-950: oklch(0.271 0.105 12.094);

  --color-slate-50: oklch(0.984 0.003 247.858);
  --color-slate-100: oklch(0.968 0.007 247.896);
  --color-slate-200: oklch(0.929 0.013 255.508);
  --color-slate-300: oklch(0.869 0.022 252.894);
  --color-slate-400: oklch(0.704 0.04 256.788);
  --color-slate-500: oklch(0.554 0.046 257.417);
  --color-slate-600: oklch(0.446 0.043 257.281);
```

----------------------------------------

TITLE: Removing Default Outlines in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet illustrates the application of the `outline-none` utility class to a `textarea` to remove its default focus outline. It also shows how to use `focus-within:outline-2` and `focus-within:outline-indigo-600` on a parent `div` to provide custom, accessible focus indicators, as recommended when removing default outlines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/outline-style.mdx#_snippet_6

LANGUAGE: html
CODE:
```
<div class="focus-within:outline-2 focus-within:outline-indigo-600 ...">
  <textarea class="outline-none ..." placeholder="Leave a comment..." />
  <button class="..." type="button">Post</button>
</div>
```

----------------------------------------

TITLE: Applying Horizontal Padding with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates the use of the `px-<number>` utility, specifically `px-8`, to apply horizontal padding to an element. This class simultaneously controls both the left and right padding, providing symmetrical spacing along the x-axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:px-8] -->\n<div class="px-8 ...">px-8</div>
```

----------------------------------------

TITLE: Importing Tailwind CSS in Main CSS File
DESCRIPTION: This CSS snippet imports the Tailwind CSS framework into your main stylesheet. It's a crucial step to make Tailwind's utilities available for use in your project after installation and configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_13

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: Applying Responsive Width Utilities in HTML
DESCRIPTION: This HTML snippet illustrates how to apply responsive width utility classes using Tailwind CSS. By default, the image has a width of w-16, which changes to md:w-32 on medium screens and lg:w-48 on large screens, showcasing conditional styling based on breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<img class="w-16 md:w-32 lg:w-48" src="..." />
```

----------------------------------------

TITLE: Installing Tailwind CSS v4.0 with PostCSS
DESCRIPTION: This shell command installs Tailwind CSS v4.0 along with its PostCSS plugin using npm. It's the first step in setting up Tailwind CSS for a project, providing the core framework and the necessary PostCSS integration for build processes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_4

LANGUAGE: Shell
CODE:
```
npm i tailwindcss @tailwindcss/postcss;
```

----------------------------------------

TITLE: Applying User-Driven Form Validation Styles with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use the new `user-valid` and `user-invalid` variants in Tailwind CSS to apply border styles (green for valid, red for invalid) to input fields only after the user has interacted with them, preventing immediate invalid state display on page load. The `required` attribute ensures validation is active.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_29

LANGUAGE: HTML
CODE:
```
<input required class="border user-valid:border-green-500" />
<input required class="border user-invalid:border-red-500" />
```

----------------------------------------

TITLE: Defining Container Query Ranges with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to combine `@min-*` and `@max-*` variants to create container query ranges. The `@min-md:@max-xl:hidden` utility hides the element only when the container's width is between the 'md' and 'xl' breakpoints, providing fine-grained control over responsiveness.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_18

LANGUAGE: HTML
CODE:
```
<div class="@container">
  <div class="flex @min-md:@max-xl:hidden">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Migrating Custom Components to Tailwind CSS v4's @utility API
DESCRIPTION: This example demonstrates how to migrate custom component-like styles from Tailwind CSS v3's `@layer components` to v4's `@utility` API. In v4, custom utilities are sorted by property count, allowing them to be easily overridden by other Tailwind utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_37

LANGUAGE: CSS
CODE:
```
@layer components {
  .btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: ButtonFace;
  }
}
@utility btn {
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: ButtonFace;
}
```

----------------------------------------

TITLE: Installing Tailwind CSS v4.1 with PostCSS via npm
DESCRIPTION: This shell command installs the latest versions of `tailwindcss` and `@tailwindcss/postcss` using npm. This setup is for projects that use PostCSS as their CSS preprocessor, enabling Tailwind CSS to be processed as a PostCSS plugin, which is a common and flexible way to integrate it into various build workflows.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_32

LANGUAGE: Shell
CODE:
```
npm install tailwindcss@latest @tailwindcss/postcss@latest
```

----------------------------------------

TITLE: Generated CSS for Dynamic Spacing Utilities - CSS
DESCRIPTION: This generated CSS snippet shows how Tailwind CSS v4.0 derives dynamic spacing utilities. It defines a `--spacing` CSS variable and then uses `calc()` to generate utility classes like `mt-8`, `w-17`, and `pr-29` based on multiples of this variable, enabling arbitrary spacing values out of the box.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_15

LANGUAGE: CSS
CODE:
```
@layer theme {
  :root {
    --spacing: 0.25rem;
  }
}

@layer utilities {
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .w-17 {
    width: calc(var(--spacing) * 17);
  }
  .pr-29 {
    padding-right: calc(var(--spacing) * 29);
  }
}
```

----------------------------------------

TITLE: Installing Tailwind CSS with CLI
DESCRIPTION: This command installs the latest versions of Tailwind CSS and its command-line interface (CLI) tool using npm. This setup is ideal for projects that use the Tailwind CLI directly for processing CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install tailwindcss@latest @tailwindcss/cli@latest
```

----------------------------------------

TITLE: Applying Focus Ring Utilities HTML
DESCRIPTION: Demonstrates the use of new `ring` utilities (`focus:ring-opacity-50`, `focus:ring-2`, `focus:ring-blue-300`) combined with the `focus:` variant to create custom outline effects when an element is focused, often used with `focus:outline-none` to replace the default browser outline.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_8

LANGUAGE: HTML
CODE:
```
<button class="focus:ring-opacity-50 focus:ring-2 focus:ring-blue-300 focus:outline-none ...">
```

----------------------------------------

TITLE: HTML Example for Class-based Dark Mode Toggling
DESCRIPTION: This HTML snippet demonstrates how the `dark` class is applied to the `<html>` element to activate dark mode utilities. When the `dark` class is present, `dark:bg-black` will override `bg-white`, changing the background color. This setup requires JavaScript to dynamically add or remove the `dark` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<html class="dark">
  <body>
    <div class="bg-white dark:bg-black">
      <!-- ... -->
    </div>
  </body>
</html>
```

----------------------------------------

TITLE: Applying Container Query Units in HTML
DESCRIPTION: This HTML snippet shows how to use container query length units, specifically `cqw` (container query width), as arbitrary values within Tailwind CSS utility classes. The `w-[50cqw]` class sets the width of the inner `div` to 50% of its `@container` parent's width, allowing for responsive sizing relative to the container rather than the viewport.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_21

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="w-[50cqw]">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Positioning Flex Items Vertically with flex-col (HTML)
DESCRIPTION: Use `flex-col` to position flex items vertically. This utility applies `flex-direction: column;` to the element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-direction.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex-col] -->
<div class="flex flex-col ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Implementing Basic Container Queries in HTML with Tailwind CSS
DESCRIPTION: This snippet demonstrates the basic syntax for applying container queries using the @tailwindcss/container-queries plugin. It shows how to define a container element with @container and then apply responsive styles within it using @lg:flex, which applies flex when the container reaches the lg breakpoint. This differentiates container queries from standard media queries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_32

LANGUAGE: HTML
CODE:
```
<div class="@container">
  <div class="block @lg:flex">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Referencing Tailwind Colors in CSS
DESCRIPTION: This snippet demonstrates how Tailwind CSS colors are exposed as CSS variables (e.g., `--color-blue-500`) and can be directly referenced in your custom CSS for styling components, ensuring consistency with your design system.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_9

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@layer components {
  .typography {
    color: var(--color-gray-950);
    a {
      color: var(--color-blue-500);
      &:hover {
        color: var(--color-blue-800);
      }
    }
  }
}
```

----------------------------------------

TITLE: Correct Mobile-First Text Alignment with Tailwind CSS - HTML
DESCRIPTION: This HTML snippet showcases the recommended mobile-first approach for responsive text alignment in Tailwind CSS. `text-center` applies centering to all screen sizes by default, while `sm:text-left` then overrides this to left-align text specifically on screens 640px and wider, demonstrating proper progressive enhancement.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<!-- This will center text on mobile, and left align it on screens 640px and wider -->
<div class="text-center sm:text-left"></div>
```

----------------------------------------

TITLE: Building Custom Radio Buttons with Headless UI in React
DESCRIPTION: This React component demonstrates how to implement a custom radio button UI using Headless UI's `RadioGroup` component. It manages the selected plan state and renders custom `RadioGroup.Option` elements, allowing for flexible styling beyond traditional radio circles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1/index.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import { useState } from "react";
import { RadioGroup } from "@headlessui/react";

function MyRadioGroup() {
  let [plan, setPlan] = useState("startup");

  return (
    <RadioGroup value={plan} onChange={setPlan}>
      <RadioGroup.Label>Plan</RadioGroup.Label>
      <RadioGroup.Option value="startup">
        {({ checked }) => <span className={checked ? "bg-blue-200" : ""}>Startup</span>}
      </RadioGroup.Option>
      <RadioGroup.Option value="business">
        {({ checked }) => <span className={checked ? "bg-blue-200" : ""}>Business</span>}
      </RadioGroup.Option>
      <RadioGroup.Option value="enterprise">
        {({ checked }) => <span className={checked ? "bg-blue-200" : ""}>Enterprise</span>}
      </RadioGroup.Option>
    </RadioGroup>
  );
}
```

----------------------------------------

TITLE: Styling Child Elements Based on Parent State (Tailwind CSS Group Variant)
DESCRIPTION: This snippet demonstrates the `group` variant for styling child elements based on the parent's state. By adding `group` to the parent `<a>` tag and `group-hover:text-white` or `group-hover:stroke-white` to children, the text and SVG icon change color when the entire card is hovered, enabling complex interactive components.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_23

LANGUAGE: jsx
CODE:
```
<a
      href="#"
      className="group mx-auto block max-w-xs space-y-3 rounded-lg bg-white p-4 shadow-lg ring-1 ring-gray-900/5 hover:bg-sky-500 hover:ring-sky-500 dark:bg-white/5 dark:ring-white/10"
    >
      <div className="flex items-center space-x-3">
        <svg className="h-6 w-6 stroke-sky-500 group-hover:stroke-white" fill="none" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M11 19H6.931A1.922 1.922 0 015 17.087V8h12.069C18.135 8 19 8.857 19 9.913V11"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M14 7.64L13.042 6c-.36-.616-1.053-1-1.806-1H7.057C5.921 5 5 5.86 5 6.92V11M17 15v4M19 17h-4"
          />
        </svg>
        {/* This is not an h3 because we're converting h3's to links in MDX files */}
        <div className="text-sm font-semibold text-gray-900 group-hover:text-white dark:text-white">New project</div>
      </div>
      <p className="text-sm text-gray-500 group-hover:text-white dark:text-gray-400">
        Create a new project from a variety of starting templates.
      </p>
    </a>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:group-hover:stroke-white] -->
<!-- [!code classes:group-hover:text-white] -->
<!-- [!code classes:group-hover:text-white] -->
<!-- [!code classes:group] -->
<a href="#" class="group ...">
  <div>
    <svg class="stroke-sky-500 group-hover:stroke-white ..." fill="none" viewBox="0 0 24 24">
      <!-- ... -->
    </svg>
    <h3 class="text-gray-900 group-hover:text-white ...">New project</h3>
  </div>
  <p class="text-gray-500 group-hover:text-white ...">Create a new project from a variety of starting templates.</p>
</a>
```

----------------------------------------

TITLE: Optimizing Scroll with will-change-scroll in HTML
DESCRIPTION: This HTML snippet demonstrates the use of the `will-change-scroll` utility to optimize an element's scroll performance. By applying `will-change-scroll` to a `div` with `overflow-auto`, the browser is instructed to prepare for upcoming scroll position changes, which can improve animation smoothness. It's crucial to apply this utility just before the change and remove it with `will-change-auto` shortly after, as overuse can negatively impact performance.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/will-change.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:will-change-scroll] -->
<div class="overflow-auto will-change-scroll">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Defining Custom Theme Variables with @theme in CSS
DESCRIPTION: This CSS snippet shows how to define custom theme values like font families, breakpoints, and colors directly within a main.css file using the @theme directive. These CSS variables replace the need for a JavaScript configuration file, making Tailwind CSS feel more CSS-native.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_4

LANGUAGE: css
CODE:
```
/* [!code filename:main.css] */
@import "tailwindcss";

@theme {
  --font-family-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 1920px;

  --color-neon-pink: oklch(71.7% 0.25 360);
  --color-neon-lime: oklch(91.5% 0.258 129);
  --color-neon-cyan: oklch(91.3% 0.139 195.8);
}
```

----------------------------------------

TITLE: Using Named Container Queries in Tailwind CSS HTML
DESCRIPTION: This HTML snippet demonstrates how to use named container queries for more complex layouts. By naming a container with `@container/main`, child elements can target specific containers using variants like `@sm/main:flex-col`, allowing styles to depend on a distant parent's size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_17

LANGUAGE: html
CODE:
```
<div class="@container/main">
  <!-- ... -->
  <div class="flex flex-row @sm/main:flex-col">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Configuring Design Tokens with @theme - CSS
DESCRIPTION: This CSS snippet illustrates the new CSS-first configuration approach in Tailwind CSS v4.0, using the `@theme` directive. It allows defining design tokens like fonts, breakpoints, colors, and easing functions directly within the CSS file, eliminating the need for a separate `tailwind.config.js`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_11

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";

@theme {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 1920px;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Installing Tailwind CSS with PostCSS
DESCRIPTION: This command installs the latest versions of Tailwind CSS and its PostCSS plugin using npm. This setup allows Tailwind CSS to be used as a PostCSS plugin within your build pipeline, compatible with various PostCSS-enabled environments.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_2

LANGUAGE: sh
CODE:
```
npm install tailwindcss@latest @tailwindcss/postcss@latest
```

----------------------------------------

TITLE: Creating a Custom Dropdown Menu with Headless UI Menu Component (React)
DESCRIPTION: This example illustrates how to build a custom dropdown menu using the Headless UI `Menu` component in React. It utilizes compound components like `Menu.Button`, `Menu.Items`, and `Menu.Item` to manage dropdown state and accessibility. The `Menu.Item` component uses a render prop to expose the `active` state, allowing for dynamic styling based on user interaction.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
import { Menu } from "@headlessui/react";

function MyDropdown() {
  return (
    <Menu as="div" className="relative">
      <Menu.Button className="rounded bg-blue-600 px-4 py-2 text-white ...">Options</Menu.Button>
      <Menu.Items className="absolute right-0 mt-1">
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/account-settings">
              Account settings
            </a>
          )}
        </Menu.Item>
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/documentation">
              Documentation
            </a>
          )}
        </Menu.Item>
        <Menu.Item disabled>
          <span className="opacity-75 ...">Invite a friend (coming soon!)</span>
        </Menu.Item>
      </Menu.Items>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Styling Sibling Elements with peer-invalid for Form Validation in HTML
DESCRIPTION: This example demonstrates how to apply styles to a sibling element based on the state of a `peer` element, specifically using `peer-invalid` for form validation. The `peer` class is added to the input field, and a subsequent sibling paragraph uses `peer-invalid:visible` to show an error message when the input is invalid. This pattern allows for dynamic UI updates without JavaScript.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_31

LANGUAGE: html
CODE:
```
<div className="mx-auto max-w-md border-x border-x-gray-200 px-6 pt-6 pb-5 dark:border-x-gray-800 dark:bg-gray-950/10">
  <form>
    <div>
      <label htmlFor="email-2" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Email
      </label>
      <div className="mt-1">
        <input
          type="email"
          name="email"
          id="email-2"
          className="peer block w-full rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none sm:text-sm"
          defaultValue="george@krugerindustrial."
          placeholder="<EMAIL>"
        />
        <p className="invisible mt-2 text-sm text-pink-600 peer-invalid:visible">
          Please provide a valid email address.
        </p>
      </div>
    </div>
  </form>
</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:peer-invalid:visible] -->
<!-- [!code classes:peer] -->
<form>
  <label class="block">
    <span class="...">Email</span>
    <input type="email" class="peer ..." />
    <p class="invisible peer-invalid:visible ...">Please provide a valid email address.</p>
  </label>
</form>
```

----------------------------------------

TITLE: Implementing Named Container Queries in HTML with Tailwind CSS
DESCRIPTION: This snippet showcases how to use named container queries in Tailwind CSS. By adding a name like @container/main to the container element, styles can be applied conditionally to descendants using @lg/main:flex, ensuring that the styles respond specifically to the named container's size rather than any parent container. This provides better control in nested layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_35

LANGUAGE: HTML
CODE:
```
<div class="@container/main">
  <!-- ... -->
  <div>
    <div class="block @lg/main:flex">
      <!-- ... -->
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Importing Tailwind CSS in Main CSS File (CLI Flow) - CSS
DESCRIPTION: This CSS snippet imports the Tailwind CSS framework into your main application stylesheet, preparing it for compilation using the Tailwind CSS CLI.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_18

LANGUAGE: css
CODE:
```
/* [!code filename:app.css] */
@import "tailwindcss";
```

----------------------------------------

TITLE: Including Tailwind Directives in CSS
DESCRIPTION: This CSS snippet demonstrates the standard Tailwind CSS directives used to inject Tailwind's base styles, components, and utility classes into a CSS file. These directives are typically placed at the top of a main CSS file to enable Tailwind's features.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#_snippet_8

LANGUAGE: css
CODE:
```
@tailwind base;
@tailwind components;
@tailwind utilities;
```

----------------------------------------

TITLE: Applying Single-Side Padding with Tailwind CSS
DESCRIPTION: This HTML snippet illustrates how to use specific Tailwind CSS utilities like `pt-<number>`, `pr-<number>`, `pb-<number>`, and `pl-<number>` to control padding on individual sides of an element. Each example targets a different side (top, right, bottom, left) to provide granular control over spacing.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:pt-6,pr-4,pb-8,pl-2] -->\n<div class="pt-6 ...">pt-6</div>\n<div class="pr-4 ...">pr-4</div>\n<div class="pb-8 ...">pb-8</div>\n<div class="pl-2 ...">pl-2</div>
```

----------------------------------------

TITLE: Implementing Container Queries with Tailwind CSS HTML
DESCRIPTION: This snippet shows how to use Tailwind CSS's container query variants to style an element based on the width of its parent container, rather than the viewport. It sets up a container (@container) and applies a flex direction change (@md:flex-row) when the container reaches a medium size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_49

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @md:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Ensuring Accessibility for Unstyled Lists with ARIA Role
DESCRIPTION: This HTML snippet addresses accessibility concerns for unstyled lists by adding `role="list"` to the `<ul>` element. This ensures that screen readers like VoiceOver correctly announce the element as a list, even when `list-style: none` is applied, maintaining semantic meaning for assistive technologies.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/preflight.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<ul role="list">
  <li>One</li>
  <li>Two</li>
  <li>Three</li>
</ul>
```

----------------------------------------

TITLE: Incorrect Mobile Styling with sm:text-center - HTML
DESCRIPTION: This HTML snippet demonstrates a common mistake in Tailwind CSS responsive design. Using `sm:text-center` will only apply text centering on screens 640px and wider, failing to center text on smaller, mobile screens. It highlights that `sm:` targets the small breakpoint and above, not just small screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<!-- This will only center text on screens 640px and wider, not on small screens -->
<div class="sm:text-center"></div>
```

----------------------------------------

TITLE: Adding Viewport Meta Tag in HTML
DESCRIPTION: This snippet demonstrates how to include the viewport meta tag in the <head> of an HTML document. This tag is crucial for proper responsive behavior across different devices, ensuring the page scales correctly.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
```

----------------------------------------

TITLE: Avoiding Dynamic Class Names in HTML with Tailwind
DESCRIPTION: This HTML snippet illustrates an incorrect way to construct class names dynamically using interpolation. Tailwind CSS scans files as plain text and cannot understand string concatenation, meaning `text-red-600` and `text-green-600` will not be detected, leading to missing styles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="text-{{ error ? 'red' : 'green' }}-600"></div>
```

----------------------------------------

TITLE: Loading JavaScript Config File in Tailwind CSS v4
DESCRIPTION: Demonstrates how to explicitly load a JavaScript configuration file in Tailwind CSS v4 using the `@config` directive. This is necessary because JS config files are no longer automatically detected.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_45

LANGUAGE: CSS
CODE:
```
@config "../../tailwind.config.js";
```

----------------------------------------

TITLE: Adding Vertical Margin with Tailwind CSS (HTML)
DESCRIPTION: This snippet demonstrates how to apply vertical margin to an element using Tailwind CSS `my-<number>` utilities. The `my-8` class adds a margin of 8 units to both the top and bottom of the element, useful for vertical spacing.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#_snippet_4

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:my-8] -->
<div class="my-8 ...">my-8</div>
```

----------------------------------------

TITLE: Selecting Even Child Elements in CSS
DESCRIPTION: This snippet demonstrates the `:nth-child(even)` pseudo-class, which selects every even-numbered child element of its parent. In Tailwind CSS, this corresponds to the `even` utility, where the `&` symbol indicates that this rule applies to the current element if it is an even-numbered child.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_118

LANGUAGE: CSS
CODE:
```
&:nth-child(even)
```

----------------------------------------

TITLE: Applying Focus Border Color with Tailwind CSS (HTML/JSX)
DESCRIPTION: This snippet demonstrates how to apply a specific border color when an element is in its focus state using Tailwind CSS. The `focus:border-pink-600` utility class changes the input field's border to pink-600 upon focus, enhancing user interaction feedback. This requires an interactive element like an input field. No specific dependencies beyond Tailwind CSS are required.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-color.mdx#_snippet_7

LANGUAGE: JSX
CODE:
```
<label className="mx-auto block max-w-xs">
  <span className="text-sm font-medium text-gray-900 dark:text-gray-200">Email address</span>
  <input
    type="text"
    placeholder="<EMAIL>"
    className="block w-full rounded-lg border-2 border-gray-700 px-3 py-2 font-sans text-sm leading-5 text-gray-500 focus:border-pink-600 focus:outline-none dark:bg-gray-900 dark:text-gray-400 dark:placeholder:text-gray-600"
  />
</label>
```

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:focus:border-pink-600] -->
<input class="border-2 border-gray-700 focus:border-pink-600 ..." />
```

----------------------------------------

TITLE: Applying Hover Background Color with Tailwind CSS (HTML)
DESCRIPTION: This snippet demonstrates how to apply a different background color on hover using Tailwind CSS utility classes. The hover:bg-fuchsia-500 class changes the button's background to fuchsia when the user hovers over it, while bg-indigo-500 sets the default background.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-color.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:hover:bg-fuchsia-500] -->
<button class="bg-indigo-500 hover:bg-fuchsia-500 ...">Save changes</button>
```

----------------------------------------

TITLE: Responsive Layout for Marketing Component in HTML
DESCRIPTION: This HTML snippet demonstrates building a responsive marketing page component using Tailwind CSS. It utilizes md:flex to switch from a stacked layout on small screens to a side-by-side layout on medium screens, adjusting image dimensions and container width accordingly.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_4

LANGUAGE: HTML
CODE:
```
<div class="mx-auto max-w-md overflow-hidden rounded-xl bg-white shadow-md md:max-w-2xl">
  <div class="md:flex">
    <div class="md:shrink-0">
      <img
        class="h-48 w-full object-cover md:h-full md:w-48"
        src="/img/building.jpg"
        alt="Modern building architecture"
      />
    </div>
    <div class="p-8">
      <div class="text-sm font-semibold tracking-wide text-indigo-500 uppercase">Company retreats</div>
      <a href="#" class="mt-1 block text-lg leading-tight font-medium text-black hover:underline">
        Incredible accommodation for your team
      </a>
      <p class="mt-2 text-gray-500">
```

----------------------------------------

TITLE: Running the Tailwind CSS Upgrade Tool (Shell)
DESCRIPTION: This snippet demonstrates how to use the official Tailwind CSS upgrade tool via `npx`. This tool automates the majority of the migration process from v3 to v4, including dependency updates and configuration file migrations. It requires Node.js 20 or higher and is recommended to be run in a new branch for careful review.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npx @tailwindcss/upgrade
```

----------------------------------------

TITLE: Styling UI Component with Tailwind CSS in JSX
DESCRIPTION: This snippet demonstrates how to apply Tailwind CSS utility classes directly to HTML elements within a JSX context (like React). It shows the structure of a card component with styling for layout, appearance, spacing, and text, including handling dark mode styles and embedding an SVG.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
<div className="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10">
  <svg className="size-12 shrink-0" viewBox="0 0 40 40">
    <defs>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="a">
        <stop stopColor="#2397B3" offset="0%"></stop>
        <stop stopColor="#13577E" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="b">
        <stop stopColor="#73DFF2" offset="0%"></stop>
        <stop stopColor="#47B1EB" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g fill="none" fillRule="evenodd">
      <path
        d="M28.872 22.096c.084.622.128 1.258.128 1.904 0 7.732-6.268 14-14 14-2.176 0-4.236-.496-6.073-1.382l-6.022 2.007c-1.564.521-3.051-.966-2.53-2.53l2.007-6.022A13.944 13.944 0 0 1 1 24c0-7.331 5.635-13.346 12.81-13.95A9.967 9.967 0 0 0 13 14c0 5.523 4.477 10 10 10a9.955 9.955 0 0 0 5.872-1.904z"
        fill="url(#a)"
        transform="translate(1 1)"
      ></path>
      <path
        d="M35.618 20.073l2.007 6.022c.521 1.564-.966 3.051-2.53 2.53l-6.022-2.007A13.944 13.944 0 0 1 23 28c-7.732 0-14-6.268-14-14S15.268 0 23 0s14 6.268 14 14c0 2.176-.496 4.236-1.382 6.073z"
        fill="url(#b)"
        transform="translate(1 1)"
      ></path>
      <path
        d="M18 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM24 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM30 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"
        fill="#FFF"
      ></path>
    </g>
  </svg>
  <div>
    <div className="text-xl font-medium text-black dark:text-white">ChitChat</div>
    <p className="text-gray-500 dark:text-gray-400">You have a new message!</p>
  </div>
</div>
```

----------------------------------------

TITLE: Adjusting Styles for Forced Colors Mode with Tailwind CSS HTML
DESCRIPTION: This snippet introduces the new `forced-colors` variant in Tailwind CSS v3.4, which enables developers to adjust styles specifically for users in forced colors mode. This is particularly useful for fine-tuning custom controls, like an input's appearance, to ensure accessibility and proper rendering in high-contrast environments.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_14

LANGUAGE: HTML
CODE:
```
<form>
  <!-- [!code word:forced-colors\:appearance-auto] -->
  <input type="checkbox" class="appearance-none forced-colors:appearance-auto ..." />
</form>
```

----------------------------------------

TITLE: Building Component with Tailwind Utilities (React)
DESCRIPTION: Demonstrates building a responsive component using Tailwind CSS utility classes within a React/JSX context. It showcases responsive variants (@sm:), spacing (space-y, gap-x), layout (flex, items-center), and state variants (hover:, active:) for styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
<div className="mx-auto max-w-sm space-y-2 rounded-xl bg-white px-8 py-8 shadow-lg ring ring-black/5 @sm:flex @sm:items-center @sm:space-y-0 @sm:gap-x-6 @sm:py-4">
  <img
    className="mx-auto block h-24 rounded-full @sm:mx-0 @sm:shrink-0"
    src={erinLindford.src}
    alt="Woman's Face"
  />
  <div className="space-y-2 text-center @sm:text-left">
    <div className="space-y-0.5">
      <p className="text-lg font-semibold text-black">Erin Lindford</p>
      <p className="font-medium text-gray-500">Product Engineer</p>
    </div>
    <button className="rounded-full border border-purple-200 px-4 py-1 text-sm font-semibold text-purple-600 hover:border-transparent hover:bg-purple-600 hover:text-white active:bg-purple-700">
      Message
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Applying Responsive Grid Layouts with Tailwind CSS HTML
DESCRIPTION: This snippet demonstrates how to create a responsive grid layout using Tailwind CSS's responsive breakpoints. It shows how to define different column counts for mobile (grid-cols-3), medium screens (md:grid-cols-4), and large screens (lg:grid-cols-6).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_48

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Integrating Theme Variables with Framer Motion in JSX
DESCRIPTION: This JSX snippet illustrates how native CSS variables, derived from the Tailwind theme, can be directly utilized within JavaScript UI libraries like Framer Motion. This avoids the need for resolveConfig(), streamlining the integration of design tokens into animation properties.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_10

LANGUAGE: jsx
CODE:
```
// [!code filename:JSX]
import { motion } from "framer-motion";

export const MyComponent = () => (
  <motion.div initial={{ y: "var(--spacing-8)" }} animate={{ y: 0 }} exit={{ y: "var(--spacing-8)" }}>
    {children}
  </motion.div>
);
```

----------------------------------------

TITLE: Defining `@xs` Container Query in CSS
DESCRIPTION: This CSS container query applies styles when the container's width is 20rem (320px) or greater. It's useful for creating flexible UI components that respond to their allocated space, enhancing reusability and adaptability.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_168

LANGUAGE: CSS
CODE:
```
@container (width >= 20rem)
```

----------------------------------------

TITLE: Implementing Basic Container Queries in Tailwind CSS HTML
DESCRIPTION: This HTML snippet illustrates the basic usage of Tailwind CSS container queries. It marks a parent element with `@container` and then applies responsive styles to its children using `@md:flex-row`, which changes the flex direction when the container reaches its medium size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_14

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @md:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Setting Percentage-Based Widths with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to apply percentage-based widths to elements using Tailwind CSS utilities like `w-1/2`, `w-2/5`, `w-1/3`, and `w-full`. These utilities are ideal for creating responsive layouts where elements occupy a specific fraction of their parent's width. The examples show various fractional widths and a full-width element, illustrating how to distribute space within a flex container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
<div className="space-y-4 font-mono text-xs font-bold text-white">
  <div className="flex gap-x-4">
    <div className="w-1/2 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/2</div>
    <div className="w-1/2 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/2</div>
  </div>
  <div className="flex gap-x-4">
    <div className="w-2/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-2/5</div>
    <div className="w-3/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-3/5</div>
  </div>
  <div className="flex gap-x-4">
    <div className="w-1/3 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/3</div>
    <div className="w-2/3 rounded-lg bg-violet-500 px-4 py-2 text-center">w-2/3</div>
  </div>
  <div className="hidden gap-x-4 sm:flex">
    <div className="w-1/4 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/4</div>
    <div className="w-3/4 rounded-lg bg-violet-500 px-4 py-2 text-center">w-3/4</div>
  </div>
  <div className="hidden gap-x-4 sm:flex">
    <div className="w-1/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/5</div>
    <div className="w-4/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-4/5</div>
  </div>
  <div className="hidden gap-x-4 sm:flex">
    <div className="w-1/6 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/6</div>
    <div className="w-5/6 rounded-lg bg-violet-500 px-4 py-2 text-center">w-5/6</div>
  </div>
  <div className="w-full rounded-lg bg-violet-500 px-4 py-2 text-center font-mono text-white">w-full</div>
</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:w-1/2,w-2/5,w-3/5,w-1/3,w-2/3,w-1/4,w-3/4,w-1/5,w-4/5,w-1/6,w-5/6,w-full] -->
<div class="flex ...">
  <div class="w-1/2 ...">w-1/2</div>
  <div class="w-1/2 ...">w-1/2</div>
</div>
<div class="flex ...">
  <div class="w-2/5 ...">w-2/5</div>
  <div class="w-3/5 ...">w-3/5</div>
</div>
<div class="flex ...">
  <div class="w-1/3 ...">w-1/3</div>
  <div class="w-2/3 ...">w-2/3</div>
</div>
<div class="flex ...">
  <div class="w-1/4 ...">w-1/4</div>
  <div class="w-3/4 ...">w-3/4</div>
</div>
<div class="flex ...">
  <div class="w-1/5 ...">w-1/5</div>
  <div class="w-4/5 ...">w-4/5</div>
</div>
<div class="flex ...">
  <div class="w-1/6 ...">w-1/6</div>
  <div class="w-5/6 ...">w-5/6</div>
</div>
<div class="w-full ...">w-full</div>
```

----------------------------------------

TITLE: Applying Absolute Positioning with Tailwind CSS Classes
DESCRIPTION: This HTML snippet illustrates the direct application of `static` and `absolute` Tailwind CSS classes to control element positioning. It demonstrates how an element with `absolute` positioning is removed from the normal document flow, allowing other elements to occupy its original space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:absolute] -->
<div class="static ...">
  <!-- Static parent -->
  <div class="static ..."><p>Static child</p></div>
  <div class="inline-block ..."><p>Static sibling</p></div>
  <!-- Static parent -->
  <div class="absolute ..."><p>Absolute child</p></div>
  <div class="inline-block ..."><p>Static sibling</p></div>
</div>
```

----------------------------------------

TITLE: Installing Tailwind CSS v3.1 via npm
DESCRIPTION: This command installs the latest version of Tailwind CSS using npm, updating an existing project or setting up a new one with the most recent features and bug fixes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install tailwindcss@latest
```

----------------------------------------

TITLE: Hiding Focus Outline in JSX with Tailwind CSS
DESCRIPTION: This JSX snippet demonstrates how to use the `focus:outline-hidden` utility class on an input field within a React component. This class hides the default browser outline when the element is focused, while ensuring the outline remains visible in forced colors mode for accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/outline-style.mdx#_snippet_3

LANGUAGE: JavaScript
CODE:
```
<input
      type="text"
      placeholder="Your full name"
      className="mx-auto block w-full max-w-xs border-b-2 border-gray-300 bg-gray-50 px-2 py-2 text-sm text-gray-800 focus:border-indigo-600 focus:outline-hidden dark:border-white/15 dark:bg-white/5 dark:text-white dark:focus:border-indigo-500"
    />
```

----------------------------------------

TITLE: Tailwind CSS Equivalent for Hover
DESCRIPTION: Shows how Tailwind CSS achieves conditional styling by using separate utility classes for default (`.bg-sky-500`) and hover states (`.hover\:bg-sky-700:hover`). Each class is responsible for a specific style or state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_2

LANGUAGE: CSS
CODE:
```
.bg-sky-500 {
  background-color: #0ea5e9;
}

.hover\:bg-sky-700:hover {
  background-color: #0369a1;
}
```

----------------------------------------

TITLE: Setting Both Width and Height (Size Utilities) - Tailwind CSS HTML
DESCRIPTION: Explains how `size-<number>` utilities (like `size-16`, `size-20`, etc.) can be used to set both the width and height of an element simultaneously based on the spacing scale.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_7

LANGUAGE: html
CODE:
```
<!-- [!code classes:size-16,size-20,size-24,size-32,size-40] -->
<div class="size-16 ...">size-16</div>
<div class="size-20 ...">size-20</div>
<div class="size-24 ...">size-24</div>
<div class="size-32 ...">size-32</div>
<div class="size-40 ...">size-40</div>
```

----------------------------------------

TITLE: Implementing Min-Width Container Queries with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use native container queries in Tailwind CSS v4.0 without the need for a plugin. It applies a `@container` class to the parent element and uses `@sm:` and `@lg:` variants to define responsive grid column changes based on the container's width.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<div class="@container">
  <div class="grid grid-cols-1 @sm:grid-cols-3 @lg:grid-cols-4">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Applying Dark Mode Styles in Tailwind CSS HTML
DESCRIPTION: This HTML snippet illustrates how to apply distinct styles for light and dark modes using Tailwind CSS. Utilities without a variant target light mode, while those prefixed with `dark:` provide specific overrides for dark mode, affecting background and text colors based on the user's `prefers-color-scheme` setting.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_50

LANGUAGE: html
CODE:
```
<!-- [!code classes:dark:bg-gray-900] -->
<!-- [!code classes:dark:text-white] -->
<!-- [!code classes:dark:text-gray-400] -->
<div class="bg-white dark:bg-gray-900 ...">
  <!-- ... -->
  <h3 class="text-gray-900 dark:text-white ...">Writes upside-down</h3>
  <p class="text-gray-500 dark:text-gray-400 ...">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Responsive Grid Layout with Tailwind CSS and Breakpoints (JSX)
DESCRIPTION: This JSX snippet demonstrates a responsive grid layout using Tailwind CSS utility classes within a React component context. The grid changes from 2 columns to 3 columns at the `@sm` breakpoint using the `@sm:grid-cols-3` class. This requires Tailwind CSS configured with container queries or standard breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_9

LANGUAGE: JSX
CODE:
```
<div className="grid grid-cols-2 gap-4 text-center font-mono font-medium text-white @sm:grid-cols-3">
  <div className="rounded-lg bg-sky-500 p-4">01</div>
  <div className="rounded-lg bg-sky-500 p-4">02</div>
  <div className="rounded-lg bg-sky-500 p-4">03</div>
  <div className="rounded-lg bg-sky-500 p-4">04</div>
  <div className="rounded-lg bg-sky-500 p-4">05</div>
  <div className="rounded-lg bg-sky-500 p-4">06</div>
</div>
```

----------------------------------------

TITLE: Building Component with Tailwind Utilities (HTML)
DESCRIPTION: Demonstrates building a responsive component using Tailwind CSS utility classes in pure HTML. It shows responsive variants (sm:), spacing (gap), layout (flex, items-center), and state variants (hover:, active:) for styling, serving as the HTML equivalent of the React example.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:sm:flex-row,sm:py-4,sm:gap-6,sm:mx-0,sm:shrink-0,sm:text-left,sm:items-center] -->
<!-- [!code classes:hover:text-white,hover:bg-purple-600,hover:border-transparent,active:bg-purple-700] -->
<div class="flex flex-col gap-2 p-8 sm:flex-row sm:items-center sm:gap-6 sm:py-4 ...">
  <img class="mx-auto block h-24 rounded-full sm:mx-0 sm:shrink-0" src="/img/erin-lindford.jpg" alt="" />
  <div class="space-y-2 text-center sm:text-left">
    <div class="space-y-0.5">
      <p class="text-lg font-semibold text-black">Erin Lindford</p>
      <p class="font-medium text-gray-500">Product Engineer</p>
    </div>
    <!-- prettier-ignore -->
    <button class="border-purple-200 text-purple-600 hover:border-transparent hover:bg-purple-600 hover:text-white active:bg-purple-700 ...">
      Message
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Styling Parent Element with :has() in Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use the `has-checked` variant on a `label` element to apply styles when its descendant `input[type="radio"]` is checked. It utilizes Tailwind CSS utility classes for layout, spacing, colors, and dark mode support, creating a visually distinct state for selected payment methods.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_12

LANGUAGE: HTML
CODE:
```
<div className="mx-auto max-w-md border-x border-x-gray-200 px-4 py-6 text-gray-700 dark:border-x-gray-800 dark:bg-gray-950/10 dark:text-gray-200">
  <fieldset>
    <legend className="text-base font-semibold text-gray-900 dark:text-white">Payment method</legend>
    <div className="mt-4 space-y-2">
      <label
        htmlFor="apple"
        className="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-indigo-800 has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked:ring-indigo-900"
      >
        <svg className="w-8" fill="currentColor" viewBox="0 0 24 13">
          <path d="M3.96299 1.735C3.22833 1.73504 2.50814 1.9393 1.88285 2.32497C1.25756 2.71063 0.751781 3.26252 0.42199 3.919C0.144511 4.47115 0 5.08054 0 5.6985C0 6.31645 0.144511 6.92584 0.42199 7.478C0.751781 8.13447 1.25756 8.68636 1.88285 9.07202C2.50814 9.45769 3.22833 9.66195 3.96299 9.662C5.03299 9.662 5.93299 9.31 6.58999 8.705C7.33799 8.015 7.76999 6.995 7.76999 5.789C7.76976 5.51882 7.74634 5.24916 7.69999 4.983H3.96399V6.509H6.10399C6.06043 6.75276 5.96798 6.98519 5.83221 7.19228C5.69644 7.39937 5.52016 7.57684 5.31399 7.714C4.95799 7.955 4.49999 8.093 3.96399 8.093C2.92999 8.093 2.05299 7.396 1.73899 6.457C1.57315 5.96493 1.57315 5.43207 1.73899 4.94C2.05299 4 2.92999 3.304 3.96399 3.304C4.52899 3.29475 5.07496 3.50811 5.48399 3.898L6.61599 2.768C5.89873 2.09384 4.94728 1.72362 3.96299 1.735ZM10.464 2.285V9.185H11.35V6.39H12.815C13.418 6.39 13.925 6.194 14.337 5.802C14.5421 5.61815 14.705 5.39214 14.8146 5.13945C14.9242 4.88676 14.9779 4.61337 14.972 4.338C14.9762 4.06405 14.9216 3.79238 14.8121 3.54125C14.7026 3.29011 14.5406 3.06533 14.337 2.882C14.1354 2.68674 13.897 2.53337 13.6358 2.43073C13.3746 2.32809 13.0956 2.27822 12.815 2.284L10.464 2.285ZM12.891 3.135C13.0456 3.13769 13.1981 3.17139 13.3395 3.23408C13.4808 3.29678 13.6082 3.3872 13.714 3.5C13.8267 3.60959 13.9162 3.74065 13.9774 3.88544C14.0385 4.03024 14.07 4.18582 14.07 4.343C14.07 4.50017 14.0385 4.65576 13.9774 4.80055C13.9162 4.94534 13.8267 5.07641 13.714 5.186C13.6007 5.30328 13.4642 5.39562 13.3132 5.45709C13.1622 5.51857 13 5.54783 12.837 5.543H11.35V3.135H12.837C12.855 3.13458 12.873 3.13458 12.891 3.135ZM17.015 4.31C16.173 4.31 15.538 4.618 15.108 5.235L15.889 5.726C16.177 5.309 16.569 5.1 17.064 5.1C17.3798 5.09612 17.6855 5.21145 17.92 5.423C18.0354 5.51846 18.1282 5.63844 18.1915 5.77423C18.2548 5.91001 18.2871 6.05818 18.286 6.208V6.41C17.946 6.217 17.512 6.121 16.986 6.121C16.369 6.121 15.876 6.266 15.507 6.555C15.137 6.843 14.953 7.232 14.953 7.72C14.949 7.9396 14.994 8.15734 15.0848 8.35733C15.1757 8.55732 15.31 8.73451 15.478 8.876C15.828 9.184 16.263 9.339 16.783 9.339C17.393 9.339 17.881 9.069 18.248 8.529H18.286V9.184H19.134V6.275C19.134 5.665 18.944 5.185 18.566 4.835C18.186 4.485 17.67 4.31 17.015 4.31ZM19.278 4.464L21.224 8.886L20.126 11.266H21.041L24 4.463H23.035L21.667 7.854H21.647L20.241 4.464H19.278ZM17.132 6.832C17.626 6.832 18.012 6.942 18.288 7.162C18.288 7.534 18.141 7.858 17.848 8.135C17.5835 8.39951 17.225 8.54839 16.851 8.549C16.6011 8.55376 16.3573 8.47178 16.161 8.317C16.0697 8.25093 15.9954 8.16402 15.9445 8.06349C15.8935 7.96295 15.8673 7.85171 15.868 7.739C15.868 7.482 15.988 7.269 16.231 7.092C16.471 6.919 16.772 6.832 17.132 6.832Z" />
        </svg>
        Google Pay
        <input
          name="payment_method"
          id="apple"
          value="google"
          type="radio"
          className="box-content h-1.5 w-1.5 appearance-none rounded-full border-[5px] border-white bg-white bg-clip-padding ring-1 ring-gray-950/20 outline-none checked:border-indigo-500 checked:ring-indigo-500"
          defaultChecked
        />
      </label>
      <label
        htmlFor="google"
        className="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-indigo-800 has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked:ring-indigo-900"
      >
        <svg className="mt-1 w-8 fill-current" fill="currentColor" viewBox="0 0 24 13">
```

----------------------------------------

TITLE: Installing Tailwind CSS v3.4
DESCRIPTION: Installs the latest version of Tailwind CSS using npm, providing access to all new features introduced in v3.4.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install tailwindcss@latest
```

----------------------------------------

TITLE: Example of Tailwind CSS Color Utilities in HTML
DESCRIPTION: This HTML snippet showcases the direct application of Tailwind CSS color utility classes to style a UI component. It demonstrates how to use classes like `bg-white`, `border-pink-300`, `text-gray-950`, and their dark mode equivalents to control background, border, text, and outline colors.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:bg-white,border-pink-300,bg-pink-100,stroke-pink-700,text-gray-950,text-gray-500,outline-black/5,text-gray-700,dark:bg-gray-800,dark:border-pink-300/10,dark:bg-pink-400/10,dark:stroke-pink-500,dark:text-gray-400,dark:text-white] -->
<div class="flex items-center gap-4 rounded-lg bg-white p-6 shadow-md outline outline-black/5 dark:bg-gray-800">
  <!-- prettier-ignore -->
  <span class="inline-flex shrink-0 rounded-full border border-pink-300 bg-pink-100 p-2 dark:border-pink-300/10 dark:bg-pink-400/10">
    <svg class="size-6 stroke-pink-700 dark:stroke-pink-500"><!-- ... --></svg>
  </span>
  <div>
    <p class="text-gray-700 dark:text-gray-400">
      <span class="font-medium text-gray-950 dark:text-white">Tom Watson</span> mentioned you in
      <span class="font-medium text-gray-950 dark:text-white">Logo redesign</span>
    </p>
    <time class="mt-1 block text-gray-500" datetime="9:37">9:37am</time>
  </div>
</div>
```

----------------------------------------

TITLE: Styling Required Field Asterisk with Tailwind CSS `::after`
DESCRIPTION: This snippet demonstrates how to use Tailwind CSS `after` variants to style the `::after` pseudo-element. It adds a red asterisk (`*`) after a label's text, commonly used to indicate a required input field. The `after:content-['*']`, `after:ml-0.5`, and `after:text-red-500` classes apply the content, margin, and color respectively.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_38

LANGUAGE: HTML
CODE:
```
<label>
  <span class="text-gray-700 after:ml-0.5 after:text-red-500 after:content-['*'] ...">Email</span>
  <input type="email" name="email" class="..." placeholder="<EMAIL>" />
</label>
```

----------------------------------------

TITLE: Overriding Default Tailwind Colors in CSS
DESCRIPTION: This snippet illustrates how to override any of Tailwind's default colors by defining new theme variables with the same name within the `@theme` directive. This allows you to customize the default color palette to match your specific design requirements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_13

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --color-gray-50: oklch(0.984 0.003 247.858);
  --color-gray-100: oklch(0.968 0.007 247.896);
  --color-gray-200: oklch(0.929 0.013 255.508);
  --color-gray-300: oklch(0.869 0.022 252.894);
  --color-gray-400: oklch(0.704 0.04 256.788);
  --color-gray-500: oklch(0.554 0.046 257.417);
  --color-gray-600: oklch(0.446 0.043 257.281);
  --color-gray-700: oklch(0.372 0.044 257.287);
  --color-gray-800: oklch(0.279 0.041 260.031);
  --color-gray-900: oklch(0.208 0.042 265.755);
  --color-gray-950: oklch(0.129 0.042 264.695);
}
```

----------------------------------------

TITLE: Avoiding Dynamic Class Names from Props in JSX with Tailwind
DESCRIPTION: This JSX code shows an incorrect method of constructing Tailwind class names using string interpolation with component props. Since Tailwind scans files as plain text, it cannot resolve `bg-${color}-600` into complete class names like `bg-blue-600`, resulting in missing styles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
function Button({ color, children }) {
  return <button className={`bg-${color}-600 hover:bg-${color}-500 ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Applying Color Utilities in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates Tailwind's recommended approach for handling color adjustments using utility classes directly in the markup. It illustrates how to apply different shades for states like hover, promoting a utility-first workflow over traditional preprocessor color functions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<button class="bg-indigo-500 hover:bg-indigo-600 ...">
  <!-- ... -->
</button>
```

----------------------------------------

TITLE: Aligning Dropdown Menu Items with Subgrid in Tailwind CSS HTML
DESCRIPTION: This example illustrates the practical application of `grid-cols-subgrid` for aligning items within a dropdown menu. By using subgrid, items with icons can be aligned with those without, ensuring consistent text alignment across all menu options, which is crucial for a clean UI.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_10

LANGUAGE: HTML
CODE:
```
<div role="menu" class="grid grid-cols-[auto_1fr]">
  <!-- [!code word:grid-cols-subgrid] -->
  <a href="#" class="col-span-2 grid-cols-subgrid">
    <svg class="mr-2">...</svg>
    <span class="col-start-2">Account</span>
  </a>
  <a href="#" class="col-span-2 grid-cols-subgrid">
    <svg class="mr-2">...</svg>
    <span class="col-start-2">Settings</span>
  </a>
  <a href="#" class="col-span-2 grid-cols-subgrid">
    <span class="col-start-2">Sign out</span>
  </a>
</div>
```

----------------------------------------

TITLE: Responsive Visibility with not-sr-only (HTML)
DESCRIPTION: This example illustrates how to use the `not-sr-only` utility class, often with a responsive prefix like `sm:`, to conditionally make an element visible. When combined with `sr-only`, `sm:not-sr-only` ensures the element is hidden on small screens but becomes visible on screens larger than the `sm` breakpoint, providing flexible control over element visibility for both sighted users and screen readers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_16

LANGUAGE: html
CODE:
```
<!-- [!code classes:sm:not-sr-only] -->
<a href="#">
  <svg><!-- ... --></svg>
  <span class="sr-only sm:not-sr-only">Settings</span>
</a>
```

----------------------------------------

TITLE: Applying Fixed Height Utilities - Tailwind CSS HTML
DESCRIPTION: Shows how to use `h-<number>` utilities (like `h-96`, `h-80`, etc.) to set the height of elements using predefined values from the Tailwind spacing scale. These classes apply a fixed height based on `calc(var(--spacing) * <number>)`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-96,h-80,h-64,h-48,h-40,h-32,h-24] -->
<div class="h-96 ...">h-96</div>
<div class="h-80 ...">h-80</div>
<div class="h-64 ...">h-64</div>
<div class="h-48 ...">h-48</div>
<div class="h-40 ...">h-40</div>
<div class="h-32 ...">h-32</div>
<div class="h-24 ...">h-24</div>
```

----------------------------------------

TITLE: Using Arbitrary Variants for Custom Selectors
DESCRIPTION: Shows how to use arbitrary variants to apply styles based on any CSS selector, useful for complex scenarios or styling HTML you don't control.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_23

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:[&>[data-active]+span]:text-blue-600] -->
<div class="[&>[data-active]+span]:text-blue-600 ...">
  <span data-active><!-- ... --></span>
  <!-- [!code highlight:2] -->
  <span>This text will be blue</span>
</div>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
div > [data-active] + span {
  color: var(--color-blue-600);
}
```

----------------------------------------

TITLE: Enabling Dark Mode Media Strategy JavaScript
DESCRIPTION: Shows how to set the `darkMode` option to `"media"` in `tailwind.config.js`. This configures Tailwind CSS to apply dark mode styles based on the user's operating system preference (`prefers-color-scheme: dark`). Requires a `tailwind.config.js` file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_2

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  darkMode: "media",
  // ...
};
```

----------------------------------------

TITLE: Styling Disabled Elements in CSS
DESCRIPTION: Applies styles to user interface elements that are in a disabled state. This pseudo-class is commonly used for form controls like buttons or input fields that are currently inactive and cannot be interacted with.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_127

LANGUAGE: css
CODE:
```
&:disabled
```

----------------------------------------

TITLE: Implementing Sticky Table Headers with Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet demonstrates a table structure styled with Tailwind CSS to achieve sticky headers. It uses `border-separate` and `border-spacing-0` on the table element, along with `sticky top-0 z-10` on the table headers, to ensure the headers and their borders remain visible at the top of the viewport during vertical scrolling. This approach is crucial for correct border behavior with sticky elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_12

LANGUAGE: html
CODE:
```
<!-- [!code word:border-separate] -->
<!-- [!code word:border-spacing-0] -->
<table class="border-separate border-spacing-0">
  <thead class="bg-gray-50">
    <tr>
      <th class="sticky top-0 z-10 border-b border-gray-300 ...">Name</th>
      <th class="sticky top-0 z-10 border-b border-gray-300 ...">Email</th>
      <th class="sticky top-0 z-10 border-b border-gray-300 ...">Role</th>
    </tr>
  </thead>
  <tbody class="bg-white">
    <tr>
      <td class="border-b border-gray-200 ...">Courtney Henry</td>
      <td class="border-b border-gray-200 ..."><EMAIL></td>
      <td class="border-b border-gray-200 ...">Admin</td>
    </tr>
    <!-- ... -->
  </tbody>
</table>
```

----------------------------------------

TITLE: Implementing Continuous CSS Animations with Negative Delay (JSX)
DESCRIPTION: This `Logo` component demonstrates a CSS-driven animation technique where logos continuously move across the screen and pause on hover. It leverages a negative `animation-delay` to offset the starting position of each logo within a shared animation keyframe, allowing them to appear at different points in their cycle. The `group-hover:[animation-play-state:running]` class dynamically controls the animation's play state based on the parent's hover, eliminating the need for JavaScript state management.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2024-09-12-radiant-a-beautiful-new-marketing-site-template/index.mdx#_snippet_5

LANGUAGE: JSX
CODE:
```
// [!code filename:logo-timeline.tsx]
function Logo({
  label,
  src,
  className,
}: {
  label: string
  src: string
  className: string
}) {
  return (
    <div
      className={clsx(
        className,
        'absolute top-2 grid grid-cols-[1rem,1fr] items-center gap-2 whitespace-nowrap px-3 py-1',
        'rounded-full bg-gradient-to-t from-gray-800 from-50% to-gray-700 ring-1 ring-inset ring-white/10',
        // [!code highlight:2]
        '[--move-x-from:-100%] [--move-x-to:calc(100%+100cqw)] [animation-iteration-count:infinite] [animation-name:move-x] [animation-play-state:paused] [animation-timing-function:linear] group-hover:[animation-play-state:running]',
      )}
    >
      <img alt="" src={src} className="size-4" />
      <span className="text-sm/6 font-medium text-white">{label}</span>
    </div>
  )
}

export function LogoTimeline() {
  return (
    /* ... */
    <Row>
      <Logo
        label="Loom"
        src="./logo-timeline/loom.svg"
        // [!code highlight:2]
        className="[animation-delay:-26s] [animation-duration:30s]"
      />
      <Logo
        label="Gmail"
        src="./logo-timeline/gmail.svg"
        // [!code highlight:2]
        className="[animation-delay:-8s] [animation-duration:30s]"
      />
    </Row>
    /* ... */
```

----------------------------------------

TITLE: Creating a User List with Headless UI Dropdown Menu in React
DESCRIPTION: This React component displays a list of 'people' data, where each person's entry includes an image, name, email, and an interactive dropdown menu. It utilizes `@headlessui/react` for the `Menu` and `Transition` components to manage dropdown state and animations, and `@heroicons/react/solid` for the vertical dots icon. The `classNames` utility (assumed to be imported or defined elsewhere) is used for conditional Tailwind CSS styling based on menu item active state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#_snippet_5

LANGUAGE: javascript
CODE:
```
import { Menu, Transition } from "@headlessui/react";
import { DotsVerticalIcon } from "@heroicons/react/solid";
import { Fragment } from "react";

const people = [
  {
    name: "Calvin Hawkins",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1491528323818-fdd1faba62cc?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
  {
    name: "Kristen Ramos",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
  {
    name: "Ted Fox",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
];

export default function Example() {
  return (
    <ul className="divide-y divide-gray-200">
      {people.map((person) => (
        <li key={person.email} className="flex py-4">
          <img className="h-10 w-10 rounded-full" src={person.image.src} alt="" />
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900">{person.name}</p>
            <p className="text-sm text-gray-500">{person.email}</p>
          </div>
          <Menu as="div" className="relative ml-3 inline-block text-left">
            {({ open }) => (
              <>
                <div>
                  <Menu.Button className="flex items-center rounded-full bg-gray-100 text-gray-400 hover:text-gray-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-100 focus:outline-none">
                    <span className="sr-only">Open options</span>
                    <DotsVerticalIcon className="h-5 w-5" aria-hidden="true" />
                  </Menu.Button>
                </div>

                <Transition
                  show={open}
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items
                    static
                    className="ring-opacity-5 absolute right-0 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black focus:outline-none"
                  >
                    <div className="py-1">
                      <Menu.Item>
                        {({ active }) => (
                          <a
                            href="#"
                            className={classNames(
                              active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                              "block px-4 py-2 text-sm",
                            )}
                          >
                            View details
                          </a>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <a
                            href="#"
                            className={classNames(
                              active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                              "block px-4 py-2 text-sm",
                            )}
                          >
                            Send message
                          </a>
                        )}
                      </Menu.Item>
                    </div>
                  </Menu.Items>
                </Transition>
              </>
            )}
          </Menu>
        </li>
      ))}
    </ul>
  );
}
```

----------------------------------------

TITLE: Using flex-1 for Basic Flex Item Growth and Shrinkage in HTML
DESCRIPTION: Use `flex-<number>` utilities like `flex-1` to allow a flex item to grow and shrink as needed, ignoring its initial size. This example demonstrates how `flex-1` makes items expand to fill available space within a flex container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code word:flex-1] -->
<div class="flex">
  <div class="w-14 flex-none ...">01</div>
  <div class="w-64 flex-1 ...">02</div>
  <div class="w-32 flex-1 ...">03</div>
</div>
```

----------------------------------------

TITLE: Using theme() for Breakpoints with CSS Variables in Tailwind CSS
DESCRIPTION: Shows how to use the `theme()` function for media queries in Tailwind CSS v4, specifically for breakpoints. It highlights the change from dot notation to CSS variable names within the `theme()` function for compatibility in contexts where CSS variables are not directly supported.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_44

LANGUAGE: CSS
CODE:
```
@media (width >= theme(screens.xl)) { 
@media (width >= theme(--breakpoint-xl)) { 
  /* ... */
}
```

----------------------------------------

TITLE: Applying `items-stretch` in Tailwind CSS (HTML/JSX)
DESCRIPTION: This snippet demonstrates how to use the `items-stretch` utility in Tailwind CSS to make flex items stretch to fill the available space along the cross axis. It includes both a React JSX example for dynamic rendering and a plain HTML example for direct usage.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
<div className="grid grid-cols-1">
  <Stripes border className="col-start-1 row-start-1 rounded-lg" />
  <div className="col-start-1 row-start-1 flex w-full items-stretch gap-4 rounded-lg text-center font-mono text-sm leading-6 font-bold text-white">
    <div className="flex flex-1 items-center justify-center rounded-lg bg-cyan-500 py-4">01</div>
    <div className="flex flex-1 items-center justify-center rounded-lg bg-cyan-500 py-12">02</div>
    <div className="flex flex-1 items-center justify-center rounded-lg bg-cyan-500 py-8">03</div>
  </div>
</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:items-stretch] -->
<div class="flex items-stretch ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Updating Vue Components to Script Setup Syntax - Vue 3
DESCRIPTION: This snippet illustrates how to refactor a Vue 3 single-file component to utilize the modern `<script setup>` syntax. It demonstrates importing reactive state (`ref`) from Vue and UI components from `@headlessui/vue` and `@heroicons/vue/solid`, making them implicitly available to the template. This approach significantly reduces boilerplate by removing the need for explicit component registration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2022-05-23-headless-ui-v1-6-tailwind-ui-team-management/index.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<template>
  <Listbox as="div" v-model="selected">
    <!-- ... -->
  </Listbox>
</template>

<script setup>
  import { ref } from "vue";
  import { Listbox, ListboxButton, ListboxLabel, ListboxOption, ListboxOptions } from "@headlessui/vue";
  import { CheckIcon, SelectorIcon } from "@heroicons/vue/solid";

  const people = [
    { id: 1, name: "Wade Cooper" },
    // ...
  ];

  const selected = ref(people[3]);
</script>
```

----------------------------------------

TITLE: Styling Inert Elements with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to apply visual styles to elements marked with the `inert` attribute using Tailwind CSS. The `inert:opacity-50` class makes the content within the `fieldset` appear semi-transparent when it's non-interactive, providing a clear visual cue to users.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_88

LANGUAGE: html
CODE:
```
<!-- [!code classes:inert:opacity-50] -->
<form>
  <legend>Notification preferences</legend>
  <fieldset>
    <input type="radio" />
    <label> Custom </label>
    <fieldset inert class="inert:opacity-50">
      <!-- ... -->
    </fieldset>
    <input type="radio" />
    <label> Everything </label>
  </fieldset>
</form>
```

----------------------------------------

TITLE: Conditional Styling with supports-[backdrop-filter] (HTML)
DESCRIPTION: This HTML snippet shows how to apply styles like `bg-black/25` and `backdrop-blur` only if the browser supports the `backdrop-filter` CSS property. It provides a fallback `bg-black/75` for unsupported environments, ensuring a consistent visual experience.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<div class="bg-black/75 supports-[backdrop-filter]:bg-black/25 supports-[backdrop-filter]:backdrop-blur ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Enabling Transitions Only for Motion-Safe Users in HTML
DESCRIPTION: This HTML snippet illustrates the `motion-safe` variant, which applies styles only when the user has *not* opted for reduced motion. The `motion-safe:transition` class ensures that a transition is applied only to users who are not sensitive to motion, allowing for explicit opt-in to animations and transitions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<div class="duration-150 ease-in-out motion-safe:transition ... ..."></div>
```

----------------------------------------

TITLE: Excluding Elements from Typography Styles with not-prose
DESCRIPTION: This HTML snippet demonstrates how to prevent the Tailwind CSS typography styles from affecting specific content. By wrapping a `div` with the `not-prose` class, any HTML inside it will be excluded from the applied prose styles, useful for embedding non-content elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-typography-v0-5/index.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<article class="prose">
  <h1>My Heading</h1>
  <p>...</p>

  <div class="not-prose">
    <!-- Some HTML that needs to be prose-free -->
  </div>

  <p>...</p>
  <!-- ... -->
</article>
```

----------------------------------------

TITLE: Positioning Dropdown with Anchor Prop (React/JSX)
DESCRIPTION: Shows how to use the new `anchor` prop on a Headless UI `MenuItems` component to control its position relative to the anchor element, leveraging integrated Floating UI capabilities. It also demonstrates using CSS variables like `--anchor-gap` and `--anchor-padding` for fine-tuning.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";

function Example() {
  return (
    <Menu>
      <MenuButton>Options</MenuButton>
      <MenuItems
        // [!code highlight:3]
        anchor="bottom start"
        className="[--anchor-gap:8px] [--anchor-padding:8px]"
      >
        <MenuItem>
          <button>Edit</button>
        </MenuItem>
        <MenuItem>
          <button>Duplicate</button>
        </MenuItem>
        <hr />
        <MenuItem>
          <button>Archive</button>
        </MenuItem>
        <MenuItem>
          <button>Delete</button>
        </MenuItem>
      </MenuItems>
    </Menu>
  );
}
```

----------------------------------------

TITLE: HTML Line-Clamp Implementation with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use the `line-clamp-3` class in Tailwind CSS to truncate text to a maximum of three lines. It includes styling for the article, time, heading, and paragraph elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_28

LANGUAGE: HTML
CODE:
```
<article>
  <div>
    <time datetime="2020-03-16" class="block text-sm/6 text-gray-600">Mar 10, 2020</time>
    <h2 class="mt-2 text-lg font-semibold text-gray-900">Boost your conversion rate</h2>
    >
    <p class="mt-4 line-clamp-3 text-sm/6 text-gray-600">
      Nulla dolor velit adipisicing duis excepteur esse in duis nostrud occaecat mollit incididunt deserunt sunt. Ut ut
      sunt laborum ex occaecat eu tempor labore enim adipisicing minim ad. Est in quis eu dolore occaecat excepteur
      fugiat dolore nisi aliqua fugiat enim ut cillum. Labore enim duis nostrud eu. Est ut eiusmod consequat irure quis
      deserunt ex. Enim laboris dolor magna pariatur. Dolor et ad sint voluptate sunt elit mollit officia ad enim sit
      consectetur enim.
    </p>
  </div>
  <div class="mt-4 flex gap-x-2.5 text-sm leading-6 font-semibold text-gray-900">
    <img src="..." class="h-6 w-6 flex-none rounded-full bg-gray-50" />
    Lindsay Walton
  </div>
</article>
```

----------------------------------------

TITLE: Applying Typography Styles with Tailwind CSS Prose Classes (HTML)
DESCRIPTION: This HTML snippet demonstrates how to apply typographic styles to a block of content using the @tailwindcss/typography plugin. By adding the 'prose' class (and responsive variants like 'lg:prose-xl') to an <article> element, all nested vanilla HTML content (like <h1> and <p> tags) will automatically receive beautiful, well-formatted typographic defaults. This eliminates the need for extensive custom CSS to style rich-text content.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-typography/index.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<article class="prose lg:prose-xl">
  <h1>Garlic bread with cheese: What the science tells us</h1>
  <p>
    For years parents have espoused the health benefits of eating garlic bread with cheese to their children, with the
    food earning such an iconic status in our culture that kids will often dress up as warm, cheesy loaf for Halloween.
  </p>
  <p>
    But a recent study shows that the celebrated appetizer may be linked to a series of rabies cases springing up around
    the country.
  </p>
  <!-- ... -->
</article>
```

----------------------------------------

TITLE: Applying Styles within a Breakpoint Range (md to xl) - HTML
DESCRIPTION: This HTML snippet demonstrates how to apply a utility class (`flex`) only within a specific breakpoint range using stacked responsive variants. The `md:max-xl:flex` class ensures that `display: flex` is active exclusively from the `md` breakpoint up to, but not including, the `xl` breakpoint, providing fine-grained control over responsive layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<div class="md:max-xl:flex">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Defining `@sm` Container Query in CSS
DESCRIPTION: This CSS container query applies styles when the container's width is 24rem (384px) or greater. It allows for more granular control over component layouts within different container sizes, improving modularity in design systems.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_169

LANGUAGE: CSS
CODE:
```
@container (width >= 24rem)
```

----------------------------------------

TITLE: Applying Sizing with `size-*` Utility in HTML
DESCRIPTION: This snippet demonstrates the new `size-*` utility in Tailwind CSS, which allows setting both width and height simultaneously. It replaces the need for separate `h-*` and `w-*` classes, offering a more concise way to define element dimensions. The example shows how `size-10` achieves the same result as `h-10 w-10`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<div>
  <img class="h-10 w-10" ...>
  <img class="h-12 w-12" ...>
  <img class="h-14 w-14" ...>
  <img class="size-10" ...>
  <img class="size-12" ...>
  <img class="size-14" ...>
</div>
```

----------------------------------------

TITLE: Implementing Responsive Columns with React/JSX and Tailwind CSS
DESCRIPTION: This snippet showcases a responsive multi-column layout using Tailwind CSS classes within a React/JSX structure. It dynamically adjusts the number of columns and gap based on the container's breakpoint (@sm), demonstrating container queries. It also includes image elements with aspect ratios and object-fit properties for visual presentation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/columns.mdx#_snippet_8

LANGUAGE: JavaScript
CODE:
```
<div className="@container relative">\n  <div className="absolute inset-0 -top-8 -bottom-8 grid grid-cols-2 gap-4 @sm:grid-cols-3 @sm:gap-8">\n    <Stripes border="x" />\n    <Stripes border="x" />\n    <Stripes border="x" className="@max-sm:hidden" />\n  </div>\n  <div className="relative columns-2 gap-4 *:mb-4 @sm:-mb-8 @sm:columns-3 @sm:gap-8 @sm:*:mb-8">\n    <img\n      className="aspect-3/2 rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1454496522488-7a8e488e8606?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2952&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1434394354979-a235cd36269d?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2902&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1491904768633-2b7e3e7fede5?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=3131&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1463288889890-a56b2853c40f?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=3132&q=80"\n    />\n    <img\n      className="aspect-3/2 rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1611605645802-c21be743c321?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2940&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1498603993951-8a027a8a8f84?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2936&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1526400473556-aac12354f3db?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2940&q=80"\n    />\n    <img\n      className="aspect-square rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1617369120004-4fc70312c5e6?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1587&q=80"\n    />\n    <img\n      className="aspect-3/2 rounded-lg bg-black/5 object-cover outline -outline-offset-1 outline-black/10 dark:outline-0"\n      src="https://images.unsplash.com/photo-1518892096458-a169843d7f7f?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2940&q=80"\n    />\n  </div>\n</div>
```

----------------------------------------

TITLE: Using CSS Variables without var() in React
DESCRIPTION: This React component demonstrates how to use CSS variables directly within Tailwind CSS classes, omitting the `var()` function. It defines CSS variables using the style prop and applies them to the background color of a div element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_32

LANGUAGE: javascript
CODE:
```
export function MyComponent({ company }) {
  return (
    <div
      style={{
        "--brand-color": company.brandColor,
        "--brand-hover-color": company.brandHoverColor,
      }}
      className="bg-[var(--brand-color)] hover:bg-[var(--brand-hover-color)]" // [!code --]
      className="bg-[--brand-color] hover:bg-[--brand-hover-color]]" // [!code ++]
    />
  );
}
```

----------------------------------------

TITLE: Setting Font Size and Line Height with Tailwind CSS (React/JSX)
DESCRIPTION: This snippet demonstrates how to apply Tailwind CSS utility classes like `text-sm/6`, `text-sm/7`, and `text-sm/8` to simultaneously set the font size (`text-sm`) and line-height (e.g., `/6`, `/7`, `/8`) of paragraph elements within a React component. It shows the visual effect of different line-height values on text readability.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<div className="flex flex-col gap-8">
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-sm/6</span>
    <p className="text-sm/6 text-gray-900 dark:text-gray-200">
      So I started to walk into the water. I won't lie to you boys, I was terrified. But I pressed on, and as I made
      my way past the breakers a strange calm came over me. I don't know if it was divine intervention or the
      kinship of all living things but I tell you Jerry at that moment, I <em>was</em> a marine biologist.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-sm/7</span>
    <p className="text-sm/7 text-gray-900 dark:text-gray-200">
      So I started to walk into the water. I won't lie to you boys, I was terrified. But I pressed on, and as I made
      my way past the breakers a strange calm came over me. I don't know if it was divine intervention or the
      kinship of all living things but I tell you Jerry at that moment, I <em>was</em> a marine biologist.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-sm/8</span>
    <p className="text-sm/8 text-gray-900 dark:text-gray-200">
      So I started to walk into the water. I won't lie to you boys, I was terrified. But I pressed on, and as I made
      my way past the breakers a strange calm came over me. I don't know if it was divine intervention or the
      kinship of all living things but I tell you Jerry at that moment, I <em>was</em> a marine biologist.
    </p>
  </div>
</div>
```

----------------------------------------

TITLE: Defining Tailwind CSS Color Palettes with OKLCH and Hex
DESCRIPTION: This snippet defines a comprehensive set of CSS custom properties for various color scales (gray, zinc, neutral, stone) using the OKLCH color model, along with standard black and white in hex. These variables are designed for use in CSS, particularly within frameworks like Tailwind CSS, allowing for consistent and reusable color definitions across a project. They can be remapped or reused under different names.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_28

LANGUAGE: css
CODE:
```
--color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  --color-zinc-50: oklch(0.985 0 0);
  --color-zinc-100: oklch(0.967 0.001 286.375);
  --color-zinc-200: oklch(0.92 0.004 286.32);
  --color-zinc-300: oklch(0.871 0.006 286.286);
  --color-zinc-400: oklch(0.705 0.015 286.067);
  --color-zinc-500: oklch(0.552 0.016 285.938);
  --color-zinc-600: oklch(0.442 0.017 285.786);
  --color-zinc-700: oklch(0.37 0.013 285.805);
  --color-zinc-800: oklch(0.274 0.006 286.033);
  --color-zinc-900: oklch(0.21 0.006 285.885);
  --color-zinc-950: oklch(0.141 0.005 285.823);

  --color-neutral-50: oklch(0.985 0 0);
  --color-neutral-100: oklch(0.97 0 0);
  --color-neutral-200: oklch(0.922 0 0);
  --color-neutral-300: oklch(0.87 0 0);
  --color-neutral-400: oklch(0.708 0 0);
  --color-neutral-500: oklch(0.556 0 0);
  --color-neutral-600: oklch(0.439 0 0);
  --color-neutral-700: oklch(0.371 0 0);
  --color-neutral-800: oklch(0.269 0 0);
  --color-neutral-900: oklch(0.205 0 0);
  --color-neutral-950: oklch(0.145 0 0);

  --color-stone-50: oklch(0.985 0.001 106.423);
  --color-stone-100: oklch(0.97 0.001 106.424);
  --color-stone-200: oklch(0.923 0.003 48.717);
  --color-stone-300: oklch(0.869 0.005 56.366);
  --color-stone-400: oklch(0.709 0.01 56.259);
  --color-stone-500: oklch(0.553 0.013 58.071);
  --color-stone-600: oklch(0.444 0.011 73.639);
  --color-stone-700: oklch(0.374 0.01 67.558);
  --color-stone-800: oklch(0.268 0.007 34.298);
  --color-stone-900: oklch(0.216 0.006 56.043);
  --color-stone-950: oklch(0.147 0.004 49.25);

  --color-black: #000;
  --color-white: #fff;
```

----------------------------------------

TITLE: Using Tailwind's `theme()` Function in CSS
DESCRIPTION: This CSS snippet illustrates the use of Tailwind's `theme()` function to retrieve values from the `tailwind.config.js` file. It applies configured border-radius, background color, and text color to a `.select2-dropdown` element, centralizing design token management.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_4

LANGUAGE: CSS
CODE:
```
.select2-dropdown {
  border-radius: theme(borderRadius.lg);
  background-color: theme(colors.gray.100);
  color: theme(colors.gray.900);
}
/* ... */
```

----------------------------------------

TITLE: Sorting HTML Classes with Prettier for Tailwind CSS
DESCRIPTION: This snippet demonstrates the automatic class sorting functionality provided by the official Prettier plugin for Tailwind CSS. It shows how utility classes in HTML markup are reordered according to the recommended class order, improving consistency and readability. The 'Before' section illustrates unsorted classes, while the 'After' section displays the same classes after being processed and sorted by Prettier.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/editor-setup.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- Before -->
<!-- prettier-ignore -->
<button class="text-white px-4 sm:px-8 py-2 sm:py-3 bg-sky-700 hover:bg-sky-800">Submit</button>

<!-- After -->
<button class="bg-sky-700 px-4 py-2 text-white hover:bg-sky-800 sm:px-8 sm:py-3">Submit</button>
```

----------------------------------------

TITLE: Filtering Combobox Results in React
DESCRIPTION: This snippet demonstrates how to implement a basic string comparison filter for the Headless UI `Combobox` component in React. It manages selected person and query states using `useState`, dynamically filtering the `people` array based on the query input. This allows users to quickly narrow down options in a large dataset.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-5/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useState } from 'react'
import { Combobox } from '@headlessui/react'

const people = [
  'Wade Cooper',
  'Arlene McCoy',
  'Devon Webb',
  'Tom Cook',
  'Tanya Fox',
  'Hellen Schmidt',
]

function MyCombobox() {
  const [selectedPerson, setSelectedPerson] = useState(people[0])
  const [query, setQuery] = useState('')

  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.toLowerCase().includes(query.toLowerCase())
        })

  return (
    <Combobox value={selectedPerson} onChange={setSelectedPerson}>
      <Combobox.Input onChange={(event) => setQuery(event.target.value)} />
      <Combobox.Options>
        {filteredPeople.map((person) => (
          <Combobox.Option key={person} value={person}>
            {person}
          </Combobox.Option>
        ))}
      </Combobox.Options>
    </Combobox>
  )
}
```

----------------------------------------

TITLE: Extending Tailwind CSS Default Theme with Custom Font
DESCRIPTION: This CSS snippet demonstrates how to extend the default Tailwind CSS theme by adding a new custom font variable, `--font-script`. By defining this variable within the `@theme` block, a new `font-script` utility class becomes available for use in HTML, allowing for custom typography.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_10

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --font-script: Great Vibes, cursive;
}
```

----------------------------------------

TITLE: Implementing Vertical Scrolling with Overflow-Y Auto in React/JSX
DESCRIPTION: This React/JSX example demonstrates how to use the `overflow-y-auto` Tailwind CSS utility within a `div` element to enable vertical scrolling for a list of items. It sets a fixed height (`h-72`) and allows content to scroll if it overflows vertically, creating a scrollable container for user profiles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#_snippet_6

LANGUAGE: JSX
CODE:
```
<div className="relative mx-auto flex h-72 max-w-sm flex-col divide-y divide-gray-200 overflow-y-auto rounded-xl bg-white shadow-lg ring-1 ring-black/5 dark:divide-gray-200/5 dark:bg-gray-800">
  <div className="flex items-center gap-4 p-4">
    <img
      className="h-12 w-12 rounded-full"
      src="https://images.unsplash.com/photo-1501196354995-cbb51c65aaea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
    />
    <div className="flex flex-col">
      <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Andrew Alfred</strong>
      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Technical advisor</span>
    </div>
  </div>
  <div className="flex items-center gap-4 p-4">
    <img
      className="h-12 w-12 rounded-full"
      src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
    />
    <div className="flex flex-col">
      <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Debra Houston</strong>
      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Analyst</span>
    </div>
  </div>
  <div className="flex items-center gap-4 p-4">
    <img
      className="h-12 w-12 rounded-full"
      src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
    />
    <div className="flex flex-col">
      <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Jane White</strong>
      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Director, Marketing</span>
    </div>
  </div>
  <div className="flex items-center gap-4 p-4">
    <img
      className="h-12 w-12 rounded-full"
      src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
    />
    <div className="flex flex-col">
      <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Ray Flint</strong>
      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Technical Advisor</span>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Tailwind CSS Display Utilities Example (JSX)
DESCRIPTION: Illustrates the application of `inline`, `inline-block`, and `block` Tailwind CSS classes within a React component to control text and element wrapping and flow.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
<div className="mx-auto max-w-xs px-4 text-sm leading-6 text-gray-500 sm:text-base sm:leading-7 dark:text-gray-300">
      When controlling the flow of text, using the CSS property{" "}
      <span className="inline rounded bg-sky-100 font-mono text-sm font-bold text-gray-900 dark:bg-white/15 dark:text-gray-200">
        display: inline
      </span>{" "}
      will cause the text inside the element to wrap normally.
      <br />
      <br />
      While using the property{" "}
      <span className="inline-block rounded bg-sky-100 font-mono text-sm font-bold text-gray-900 dark:bg-white/15 dark:text-gray-200">
        display: inline-block
      </span>{" "}
      will wrap the element to prevent the text inside from extending beyond its parent.
      <br />
      <br />
      Lastly, using the property{" "}
      <span className="block rounded bg-sky-100 font-mono text-sm font-bold text-gray-900 dark:bg-white/15 dark:text-gray-200">
        display: block
      </span>{" "}
      will put the element on its own line and fill its parent.
    </div>
```

----------------------------------------

TITLE: Implementing Sticky Headers with Tailwind CSS HTML
DESCRIPTION: This HTML snippet demonstrates how to apply `sticky` and `top-0` Tailwind CSS classes to a `div` element, making it stick to the top of its container when scrolled. This pattern is commonly used for section headers in long lists or directories, ensuring they remain visible as the user scrolls through the content. The `...` indicates additional content within the sticky header.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:sticky,top-0] -->
<div>
  <div>
    <div class="sticky top-0 ...">A</div>
    <div>
      <div>
        <img src="/img/andrew.jpg" />
        <strong>Andrew Alfred</strong>
      </div>
      <div>
        <img src="/img/aisha.jpg" />
        <strong>Aisha Houston</strong>
      </div>
      <!-- ... -->
    </div>
  </div>
  <div>
    <div class="sticky top-0">B</div>
    <div>
      <div>
        <img src="/img/bob.jpg" />
        <strong>Bob Alfred</strong>
      </div>
      <!-- ... -->
    </div>
  </div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Overriding Dark Mode Variant with Class Selector in CSS
DESCRIPTION: This CSS snippet overrides Tailwind CSS's default `dark` variant behavior. Instead of relying on `prefers-color-scheme`, it configures `dark:*` utilities to apply when an element with the `.dark` class (or any of its descendants) is present in the HTML tree. This allows for manual dark mode toggling via class manipulation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#_snippet_1

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));
```

----------------------------------------

TITLE: Importing Tailwind CSS v4.0 in CSS
DESCRIPTION: This CSS line imports the entire Tailwind CSS framework into a project's stylesheet. In v4.0, this single @import rule replaces the previous @tailwind directives, streamlining the process of including Tailwind's base styles, components, and utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_6

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: Mapping Props to Static Class Name Variants in JSX with Tailwind
DESCRIPTION: This JSX code further illustrates the best practice of mapping component props to complete, static Tailwind class names. This approach allows for flexible styling, such as applying different color shades or text colors based on a single prop, while ensuring all class names are detectable by Tailwind's plain-text scanner.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_5

LANGUAGE: JSX
CODE:
```
function Button({ color, children }) {
  const colorVariants = {
    blue: "bg-blue-600 hover:bg-blue-500 text-white",
    red: "bg-red-500 hover:bg-red-400 text-white",
    yellow: "bg-yellow-300 hover:bg-yellow-400 text-black"
  };

  return <button className={`${colorVariants[color]} ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Hiding Elements Visually with sr-only (HTML)
DESCRIPTION: This snippet demonstrates the use of the `sr-only` utility class in Tailwind CSS. Applying `sr-only` to an element, such as a `<span>` tag, visually hides it from sighted users while ensuring it remains accessible to screen readers. This is useful for providing descriptive text for icons or other non-textual elements without cluttering the visual interface.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_15

LANGUAGE: html
CODE:
```
<!-- [!code classes:sr-only] -->
<a href="#">
  <svg><!-- ... --></svg>
  <span class="sr-only">Settings</span>
</a>
```

----------------------------------------

TITLE: Installing Latest Tailwind CSS via npm (Shell)
DESCRIPTION: Command to install the latest version of the `tailwindcss` package as a dev dependency using npm. This is the standard way to upgrade or install Tailwind CSS for a project. Requires Node.js and npm.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-2/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Creating Sticky Table Headers with Tailwind CSS
DESCRIPTION: This example demonstrates how to create a table with a sticky header row using Tailwind CSS. It utilizes `border-separate` and `border-spacing-0` on the table, combined with `sticky top-0` on the table headers, to achieve a persistent bottom border under the headings while scrolling. The `overflow-auto` on the parent div enables scrolling within the table.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_11

LANGUAGE: JSX
CODE:
```
<div className="isolate h-72 overflow-auto rounded-xl">
  <table className="min-w-full border-separate border-spacing-0">
    <thead className="bg-gray-50">
      <tr>
        <th
          scope="col"
          className="bg-opacity-75 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8"
        >
          <>Name</>
        </th>
        <th
          scope="col"
          className="bg-opacity-75 sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter lg:table-cell"
        >
          <>Email</>
        </th>
        <th
          scope="col"
          className="bg-opacity-75 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter"
        >
          <>Role</>
        </th>
      </tr>
    </thead>
    <tbody className="bg-white">
      <tr>
        <td className="border-b border-gray-200 py-4 pr-3 pl-4 text-sm font-medium whitespace-nowrap text-gray-900 sm:pl-6 lg:pl-8">
          <>Courtney Henry</>
        </td>
        <td className="hidden border-b border-gray-200 px-3 py-4 text-sm whitespace-nowrap text-gray-500 lg:table-cell">
          <><EMAIL></>
        </td>
        <td className="border-b border-gray-200 px-3 py-4 text-sm whitespace-nowrap text-gray-500">Admin</td>
      </tr>
      <tr>
        <td className="border-b border-gray-200 py-4 pr-3 pl-4 text-sm font-medium whitespace-nowrap text-gray-900 sm:pl-6 lg:pl-8">
          <>Tom Cook</>
        </td>
        <td className="hidden border-b border-gray-200 px-3 py-4 text-sm whitespace-nowrap text-gray-500 lg:table-cell">
          <><EMAIL></>
        </td>
        <td className="border-b border-gray-200 px-3 py-4 text-sm whitespace-nowrap text-gray-500">Member</td>
```

----------------------------------------

TITLE: Styling Required Inputs with :required in HTML
DESCRIPTION: This snippet demonstrates how to apply a Tailwind CSS class to an input element when it is marked as required using the `required` variant. This is crucial for form validation, visually indicating mandatory fields with a `border-red-500`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_253

LANGUAGE: HTML
CODE:
```
<input required class="border required:border-red-500 ..." />
```

----------------------------------------

TITLE: Applying Dark Mode Hover Styles HTML
DESCRIPTION: Demonstrates how to combine the `dark:` variant with state variants like `hover:` (`dark:hover:bg-gray-50`) to apply specific styles when both dark mode is active and the element is being hovered over. Requires `darkMode` enabled.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_4

LANGUAGE: HTML
CODE:
```
<button class="bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-50">
```

----------------------------------------

TITLE: Simplifying Outline Utility Usage in HTML
DESCRIPTION: This snippet illustrates the updated usage of Tailwind CSS outline utilities in v4. It shows that the `outline-<number>` utilities now implicitly include `outline-style: solid`, removing the need to explicitly add the `outline` class when specifying an outline width.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_17

LANGUAGE: HTML
CODE:
```
<!-- [!code --:2] -->
<input class="outline outline-2" />
<!-- [!code ++:2] -->
<input class="outline-2" />
```

----------------------------------------

TITLE: Applying Conditional Styles with aria-checked in HTML
DESCRIPTION: This snippet demonstrates how to apply conditional styling using the `aria-checked` variant in Tailwind CSS. When the `aria-checked` attribute is set to `true`, the `bg-sky-700` class is applied, overriding the default `bg-gray-600` background. This allows for dynamic styling based on the accessibility state of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_72

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:aria-checked:bg-sky-700] -->
<div aria-checked="true" class="bg-gray-600 aria-checked:bg-sky-700">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Closing Disclosure with Internal Button in React
DESCRIPTION: This snippet demonstrates how to close a Headless UI `Disclosure` component by clicking a `Disclosure.Button` located inside its `Disclosure.Panel`. This is useful for scenarios like mobile navigation where clicking a link within the menu should close the menu. It imports `Disclosure` from `@headlessui/react` and a custom `MyLink` component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-4/index.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import { Disclosure } from '@headlessui/react'
import MyLink from './MyLink'

function MyDisclosure() {
  return (
    <Disclosure>
      <Disclosure.Button>Open mobile menu</Disclosure.Button>
      <Disclosure.Panel>
        <Disclosure.Button as={MyLink} href="/home">
          Home
        </Disclosure.Button>
        {/* ... */}
      </Disclosure.Panel>
    </Disclosure>
  )
}
```

----------------------------------------

TITLE: Updating PostCSS Configuration for Tailwind CSS v4 (JavaScript)
DESCRIPTION: This snippet shows the updated `postcss.config.mjs` for Tailwind CSS v4. In v4, the PostCSS plugin is now in `@tailwindcss/postcss`, replacing the direct `tailwindcss` package. Additionally, `postcss-import` and `autoprefixer` can be removed as their functionalities are now handled automatically by Tailwind CSS v4.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_1

LANGUAGE: js
CODE:
```
export default {
  plugins: {
    "@tailwindcss/postcss": {}
  }
};
```

----------------------------------------

TITLE: Styling Nested Groups with Named Variants in HTML
DESCRIPTION: This HTML snippet demonstrates the use of Tailwind CSS's named group variants (`group/item`, `group/edit`) to control visibility and styling of nested elements. The `group-hover/item:visible` class ensures the 'Call' button only appears when hovering over the main list item (`group/item`), while `group-hover/edit:text-gray-700` changes text color when hovering specifically over the 'Call' button itself (`group/edit`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_24

LANGUAGE: HTML
CODE:
```
<div className="px-4">
  <ul
    role="list"
    className="mx-auto max-w-md border-x border-x-gray-200 p-2 dark:border-x-gray-800 dark:bg-gray-950/10"
  >
    <li className="group/item relative flex items-center justify-between rounded-xl p-4 hover:bg-gray-100 dark:hover:bg-white/5">
      <div className="flex gap-4">
        <div className="flex-shrink-0">
          <img
            className="h-12 w-12 rounded-full"
            src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt=""
          />
        </div>
        <div className="w-full text-sm leading-6">
          <a href="#" className="font-semibold text-gray-900 dark:text-white">
            <span className="absolute inset-0 rounded-xl" aria-hidden="true"></span>Leslie Abbott
          </a>
          <div className="text-gray-500">Co-Founder / CEO</div>
        </div>
      </div>
      <a
        href="#"
        className="group/edit invisible relative flex items-center rounded-full py-1 pr-3 pl-4 text-sm whitespace-nowrap text-gray-500 transition group-hover/item:visible hover:bg-gray-200 dark:text-gray-400"
      >
        <span className="font-semibold transition group-hover/edit:text-gray-700">Call</span>
        <svg
          className="mt-px h-5 w-5 text-gray-400 transition group-hover/edit:translate-x-0.5 group-hover/edit:text-gray-500"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
            clipRule="evenodd"
          />
        </svg>
      </a>
    </li>
    <li className="group/item relative flex items-center justify-between rounded-xl p-4 hover:bg-gray-100 dark:hover:bg-white/5">
      <div className="flex gap-4">
        <div className="flex-shrink-0">
          <img
            className="h-12 w-12 rounded-full"
            src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt=""
          />
        </div>
        <div className="w-full text-sm leading-6">
          <a href="#" className="font-semibold text-gray-900 dark:text-white">
            <span className="absolute inset-0 rounded-xl" aria-hidden="true"></span>Hector Adams
          </a>
          <div className="text-gray-500">VP, Marketing</div>
        </div>
      </div>
      <a
        href="#"
        className="group/edit invisible relative flex items-center rounded-full py-1 pr-3 pl-4 text-sm whitespace-nowrap text-gray-500 transition group-hover/item:visible hover:bg-gray-200 dark:text-gray-400"
      >
        <span className="font-semibold transition group-hover/edit:text-gray-700">Call</span>
        <svg
          className="mt-px h-5 w-5 text-gray-400 transition group-hover/edit:translate-x-0.5 group-hover/edit:text-gray-500"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
            clipRule="evenodd"
          />
        </svg>
      </a>
    </li>
    <li className="group/item relative flex items-center justify-between rounded-xl p-4 hover:bg-gray-100 dark:hover:bg-white/5">
      <div className="flex gap-4">
        <div className="flex-shrink-0">
          <img
            className="h-12 w-12 rounded-full"
            src="https://images.unsplash.com/photo-*************-0a1dd7228f2d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt=""
          />
        </div>
        <div className="w-full text-sm leading-6">
          <a href="#" className="font-semibold text-gray-900 dark:text-white">
            <span className="absolute inset-0 rounded-xl" aria-hidden="true"></span>Blake Alexander
          </a>
          <div className="text-gray-500">Account Coordinator</div>
        </div>
      </div>
      <a
        href="#"
```

----------------------------------------

TITLE: Implementing Form Field with Headless UI Components (React/JSX)
DESCRIPTION: Shows how the new Headless UI form components (`Field`, `Label`, `Input`, `Description`) simplify the creation of accessible form fields in React. These components automatically handle the necessary ID and `aria-*` attribute wiring, reducing boilerplate code.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
import { Description, Field, Input, Label } from "@headlessui/react";

function Example() {
  return (
    <Field>
      <Label>Name</Label>
      <Input name="your_name" />
      <Description>Use your real name so people will recognize you.</Description>
    </Field>
  );
}
```

----------------------------------------

TITLE: Importing Tailwind CSS in main.css
DESCRIPTION: This snippet demonstrates the basic method of including Tailwind CSS into a project using a standard CSS @import statement in the main.css file. This is the initial step to integrate the framework.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_3

LANGUAGE: css
CODE:
```
/* [!code filename:main.css] */
@import "tailwindcss";
```

----------------------------------------

TITLE: Styling Hover State with Tailwind (HTML)
DESCRIPTION: Shows how to apply styles to an element's hover state using Tailwind CSS state variants in pure HTML. The `hover:` prefix is used before a utility class (e.g., `hover:bg-sky-700`) to apply that style only when the element is hovered, serving as the HTML equivalent of the React example.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code word:hover\:bg-sky-700] -->
<button class="bg-sky-500 hover:bg-sky-700 ...">Save changes</button>
```

----------------------------------------

TITLE: Applying object-contain in HTML
DESCRIPTION: This HTML snippet illustrates the use of the `object-contain` utility class on an `img` tag. The `object-contain` class scales the image down to fit within its container, preserving its aspect ratio. This ensures the entire image is visible, potentially leaving empty space around it.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#_snippet_4

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:object-contain] -->
<img class="h-48 w-96 object-contain ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Using Dynamic Grid Column Utilities - HTML
DESCRIPTION: This HTML snippet demonstrates the enhanced flexibility of Tailwind CSS v4.0, allowing dynamic values for utilities like `grid-cols-*`. Users can now specify arbitrary column counts (e.g., `grid-cols-15`) without prior configuration, simplifying grid layout creation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_13

LANGUAGE: HTML
CODE:
```
<div class="grid grid-cols-15">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Creating a Reusable React Component with Tailwind CSS
DESCRIPTION: Demonstrates how to build a reusable React component (`VacationCard`) using Tailwind CSS utility classes for styling. Shows how props can be used to make the component dynamic, allowing for easy reuse with different content.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_30

LANGUAGE: jsx
CODE:
```
export function VacationCard({ img, imgAlt, eyebrow, title, pricing, url }) {
  return (
    <div>
      <img className="rounded-lg" src={img} alt={imgAlt} />
      <div className="mt-4">
        <div className="text-xs font-bold text-sky-500">{eyebrow}</div>
        <div className="mt-1 font-bold text-gray-700">
          <a href={url} className="hover:underline">
            {title}
          </a>
        </div>
        <div className="mt-2 text-sm text-gray-600">{pricing}</div>
      </div>
    </div>
  );
}
```

----------------------------------------

TITLE: Defining `@3xs` Container Query in CSS
DESCRIPTION: This CSS container query applies styles when the container's width is 16rem (256px) or greater. Unlike media queries that target the viewport, container queries enable component-level responsiveness, allowing elements to adapt to their immediate parent's dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_166

LANGUAGE: CSS
CODE:
```
@container (width >= 16rem)
```

----------------------------------------

TITLE: Defining `@2xs` Container Query in CSS
DESCRIPTION: This CSS container query applies styles when the container's width is 18rem (288px) or greater. It provides granular control over component layouts, allowing them to respond dynamically to the space available within their parent.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_167

LANGUAGE: CSS
CODE:
```
@container (width >= 18rem)
```

----------------------------------------

TITLE: Setting Percentage Maximum Width with Tailwind CSS HTML
DESCRIPTION: This snippet illustrates how to apply percentage-based maximum widths to elements using Tailwind CSS `max-w-full` or `max-w-<fraction>` utilities. These utilities allow elements to take up a specified percentage of their parent's width, ensuring responsive behavior. Examples include `max-w-9/10`, `max-w-3/4`, `max-w-1/2`, and `max-w-1/3`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#_snippet_32

LANGUAGE: html
CODE:
```
<!-- [!code classes:max-w-3/4,max-w-9/10,max-w-1/2,max-w-1/3] -->
<div class="w-full max-w-9/10 ...">max-w-9/10</div>
<div class="w-full max-w-3/4 ...">max-w-3/4</div>
<div class="w-full max-w-1/2 ...">max-w-1/2</div>
<div class="w-full max-w-1/3 ...">max-w-1/3</div>
```

----------------------------------------

TITLE: Styling Invalid Form Fields in CSS
DESCRIPTION: Applies styles to form input elements whose content fails to validate according to their type or pattern attribute. This pseudo-class is crucial for indicating errors and guiding users to correct their input.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_135

LANGUAGE: css
CODE:
```
&:invalid
```

----------------------------------------

TITLE: Updating Default Ring Width in HTML
DESCRIPTION: This snippet shows how to update the `ring` utility in Tailwind CSS v4. Since the default `ring` width changed from `3px` to `1px`, any previous usage of `ring` that intended a `3px` ring should now be explicitly replaced with `ring-3` for consistency with borders and outlines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_19

LANGUAGE: HTML
CODE:
```
<!-- [!code --:2] -->
<input class="ring ring-blue-500" />
<!-- [!code ++:2] -->
<input class="ring-3 ring-blue-500" />
```

----------------------------------------

TITLE: Adding Horizontal Margin with Tailwind CSS (HTML)
DESCRIPTION: This snippet illustrates the use of `mx-<number>` utilities in Tailwind CSS to control the horizontal margin of an element. The `mx-8` class applies a margin of 8 units to both the left and right sides of the element, effectively centering it or providing spacing.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:mx-8] -->
<div class="mx-8 ...">mx-8</div>
```

----------------------------------------

TITLE: Allowing Flex Item Wrapping with flex-wrap in HTML
DESCRIPTION: This snippet illustrates the `flex-wrap` utility in Tailwind CSS, which allows flex items to wrap onto new lines when the container's width is insufficient to accommodate them on a single line. This is the default wrapping behavior for flex containers, enabling responsive layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-wrap.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:flex-wrap] -->
<div class="flex flex-wrap">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Centering Grid Items with justify-self-center in HTML
DESCRIPTION: This snippet demonstrates how to use the `justify-self-center` utility in Tailwind CSS to align a grid item to the center of its inline axis. This utility is effective when there is sufficient space within the grid container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-self.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="grid justify-items-stretch ...">
  <!-- ... -->
  <div class="justify-self-center ...">02</div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Menu Transitions with Headless UI and React
DESCRIPTION: This snippet demonstrates how to apply transitions to a Headless UI `MenuItems` component using the new `transition` prop and data attributes. It shows how to define different styles for closed, entering, and leaving states using Tailwind CSS classes like `data-[closed]:scale-95` and `data-[enter]:duration-200`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2024-06-21-headless-ui-v2-1/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";

function Example() {
  return (
    <Menu>
      <MenuButton>My account</MenuButton>
      <MenuItems
        // [!code highlight:7]
        transition
        className={`
          transition ease-out
          data-[closed]:scale-95 data-[closed]:opacity-0
          data-[enter]:duration-200 data-[leave]:duration-300
        `}
      >
        {/* Menu items… */}
      </MenuItems>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Centering Grid Items with Tailwind CSS `place-self-center` (JSX)
DESCRIPTION: This JSX snippet demonstrates how to use the `place-self-center` utility class in Tailwind CSS within a React component to center an individual item within a grid container along both axes. It shows a practical application in a React environment, requiring a parent grid container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-self.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
<div className="grid grid-cols-3 place-items-stretch gap-4 font-mono text-sm leading-6 font-bold text-white">
  <div className="flex items-center justify-center rounded-lg bg-sky-300 p-8 dark:bg-sky-800 dark:text-sky-500">
    01
  </div>
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 flex size-14 items-center justify-center place-self-center rounded-lg bg-sky-500">
      02
    </div>
  </div>
  <div className="flex items-center justify-center rounded-lg bg-sky-300 p-8 dark:bg-sky-800 dark:text-sky-500">
    03
  </div>
  <div className="flex items-center justify-center rounded-lg bg-sky-300 p-8 dark:bg-sky-800 dark:text-sky-500">
    04
  </div>
  <div className="flex items-center justify-center rounded-lg bg-sky-300 p-8 dark:bg-sky-800 dark:text-sky-500">
    05
  </div>
  <div className="flex items-center justify-center rounded-lg bg-sky-300 p-8 dark:bg-sky-800 dark:text-sky-500">
    06
  </div>
</div>
```

----------------------------------------

TITLE: Stretching Grid Items with place-content-stretch in Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet demonstrates the `place-content-stretch` utility class in Tailwind CSS. It configures a grid container to stretch its items along their grid areas on the block axis, making them fill the available vertical space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#_snippet_13

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-stretch gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Applying Independent Row and Column Gaps with Tailwind CSS
DESCRIPTION: This example illustrates how to control horizontal and vertical gaps independently using `gap-x-<number>` and `gap-y-<number>` utilities. `gap-x-8` sets a 32px column gap, and `gap-y-4` sets a 16px row gap, providing distinct spacing for grid items.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/gap.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:gap-x-8,gap-y-4] -->
<div class="grid grid-cols-3 gap-x-8 gap-y-4">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Leveraging Modern CSS Features in Tailwind CSS v4.0
DESCRIPTION: This snippet demonstrates how Tailwind CSS v4.0 utilizes modern CSS features like native cascade layers (@layer), color-mix() for color manipulation, and registered custom properties (@property) for advanced styling capabilities. It shows examples of utility classes defined using these features, such as margin-inline for logical properties and color-mix() for opacity adjustments.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_3

LANGUAGE: CSS
CODE:
```
@layer theme, base, components, utilities;

@layer utilities {
  .mx-6 {
    margin-inline: calc(var(--spacing) * 6);
  }
  .bg-blue-500\/50 {
    background-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
  }
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
```

----------------------------------------

TITLE: Default Theme Variables in Tailwind CSS
DESCRIPTION: This snippet provides the default CSS custom properties (variables) for Tailwind CSS's theme. It includes definitions for font families (sans, serif, mono) and an extensive range of color palettes (red, orange, amber, yellow, lime, green, emerald, teal, cyan) using the Oklch color format. These variables are automatically available for use when Tailwind CSS is imported into a project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_28

LANGUAGE: css
CODE:
```
@theme {
  /* prettier-ignore */
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  --color-red-300: oklch(0.808 0.114 19.571);
  --color-red-400: oklch(0.704 0.191 22.216);
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-red-600: oklch(0.577 0.245 27.325);
  --color-red-700: oklch(0.505 0.213 27.518);
  --color-red-800: oklch(0.444 0.177 26.899);
  --color-red-900: oklch(0.396 0.141 25.723);
  --color-red-950: oklch(0.258 0.092 26.042);

  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-400: oklch(0.75 0.183 55.934);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-orange-900: oklch(0.408 0.123 38.172);
  --color-orange-950: oklch(0.266 0.079 36.259);

  --color-amber-50: oklch(0.987 0.022 95.277);
  --color-amber-100: oklch(0.962 0.059 95.617);
  --color-amber-200: oklch(0.924 0.12 95.746);
  --color-amber-300: oklch(0.879 0.169 91.605);
  --color-amber-400: oklch(0.828 0.189 84.429);
  --color-amber-500: oklch(0.769 0.188 70.08);
  --color-amber-600: oklch(0.666 0.179 58.318);
  --color-amber-700: oklch(0.555 0.163 48.998);
  --color-amber-800: oklch(0.473 0.137 46.201);
  --color-amber-900: oklch(0.414 0.112 45.904);
  --color-amber-950: oklch(0.279 0.077 45.635);

  --color-yellow-50: oklch(0.987 0.026 102.212);
  --color-yellow-100: oklch(0.973 0.071 103.193);
  --color-yellow-200: oklch(0.945 0.129 101.54);
  --color-yellow-300: oklch(0.905 0.182 98.111);
  --color-yellow-400: oklch(0.852 0.199 91.936);
  --color-yellow-500: oklch(0.795 0.184 86.047);
  --color-yellow-600: oklch(0.681 0.162 75.834);
  --color-yellow-700: oklch(0.554 0.135 66.442);
  --color-yellow-800: oklch(0.476 0.114 61.907);
  --color-yellow-900: oklch(0.421 0.095 57.708);
  --color-yellow-950: oklch(0.286 0.066 53.813);

  --color-lime-50: oklch(0.986 0.031 120.757);
  --color-lime-100: oklch(0.967 0.067 122.328);
  --color-lime-200: oklch(0.938 0.127 124.321);
  --color-lime-300: oklch(0.897 0.196 126.665);
  --color-lime-400: oklch(0.841 0.238 128.85);
  --color-lime-500: oklch(0.768 0.233 130.85);
  --color-lime-600: oklch(0.648 0.2 131.684);
  --color-lime-700: oklch(0.532 0.157 131.589);
  --color-lime-800: oklch(0.453 0.124 130.933);
  --color-lime-900: oklch(0.405 0.101 131.063);
  --color-lime-950: oklch(0.274 0.072 132.109);

  --color-green-50: oklch(0.982 0.018 155.826);
  --color-green-100: oklch(0.962 0.044 156.743);
  --color-green-200: oklch(0.925 0.084 155.995);
  --color-green-300: oklch(0.871 0.15 154.449);
  --color-green-400: oklch(0.792 0.209 151.711);
  --color-green-500: oklch(0.723 0.219 149.579);
  --color-green-600: oklch(0.627 0.194 149.214);
  --color-green-700: oklch(0.527 0.154 150.069);
  --color-green-800: oklch(0.448 0.119 151.328);
  --color-green-900: oklch(0.393 0.095 152.535);
  --color-green-950: oklch(0.266 0.065 152.934);

  --color-emerald-50: oklch(0.979 0.021 166.113);
  --color-emerald-100: oklch(0.95 0.052 163.051);
  --color-emerald-200: oklch(0.905 0.093 164.15);
  --color-emerald-300: oklch(0.845 0.143 164.978);
  --color-emerald-400: oklch(0.765 0.177 163.223);
  --color-emerald-500: oklch(0.696 0.17 162.48);
  --color-emerald-600: oklch(0.596 0.145 163.225);
  --color-emerald-700: oklch(0.508 0.118 165.612);
  --color-emerald-800: oklch(0.432 0.095 166.913);
  --color-emerald-900: oklch(0.378 0.077 168.94);
  --color-emerald-950: oklch(0.262 0.051 172.552);

  --color-teal-50: oklch(0.984 0.014 180.72);
  --color-teal-100: oklch(0.953 0.051 180.801);
  --color-teal-200: oklch(0.91 0.096 180.426);
  --color-teal-300: oklch(0.855 0.138 181.071);
  --color-teal-400: oklch(0.777 0.152 181.912);
  --color-teal-500: oklch(0.704 0.14 182.503);
  --color-teal-600: oklch(0.6 0.118 184.704);
  --color-teal-700: oklch(0.511 0.096 186.391);
  --color-teal-800: oklch(0.437 0.078 188.216);
  --color-teal-900: oklch(0.386 0.063 188.416);
  --color-teal-950: oklch(0.277 0.046 192.524);

  --color-cyan-50: oklch(0.984 0.019 200.873);
  --color-cyan-100: oklch(0.956 0.045 203.388);
  --color-cyan-200: oklch(0.917 0.08 205.041);
```

----------------------------------------

TITLE: Configuring Tailwind CSS with CSS-based Theme
DESCRIPTION: This CSS snippet demonstrates a future concept for configuring Tailwind CSS directly within a CSS file, replacing JavaScript-based configuration. It shows how to import Tailwind CSS, include custom fonts, and define custom color and font-family variables within a `:theme` block, simplifying the setup process.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2023-07-18-tailwind-connect-2023-recap/index.mdx#_snippet_7

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@import "./fonts" layer(base);

:theme {
  --colors-neon-pink: oklch(71.7% 0.25 360);
  --colors-neon-lime: oklch(91.5% 0.258 129);
  --colors-neon-cyan: oklch(91.3% 0.139 195.8);

  --font-family-sans: "Inter", sans-serif;
  --font-family-display: "Satoshi", sans-serif;
}
```

----------------------------------------

TITLE: Applying Dynamic Viewport Height with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to apply the `h-dvh` utility class from Tailwind CSS to a `div` element. This class sets the height of the element to 100% of the dynamic viewport height, which adjusts for browser UI changes on mobile devices, providing a more reliable full-height layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code word:h-dvh] -->
<div class="h-dvh">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Conditionally Styling Specific Children with Data Attributes and * Variant in JSX
DESCRIPTION: This JSX example illustrates how to combine the `*` variant with data attribute selectors (`data-[slot]`) to conditionally apply styles to specific direct children. The `Field` component uses `data-[slot=description]:*:mt-4` to add top margin only to children with `data-slot="description"`, like the `Description` component. This enables advanced conditional styling from a parent component without complex arbitrary variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
function Field({ children }) {
  return (
    <div className="data-[slot=description]:*:mt-4 ...">
      {children}
    </div>
  )
}

function Description({ children }) {
  return (
    <p data-slot="description" ...>{children}</p>
  )
}

function Example() {
  return (
    <Field>
      <Label>First name</Label>
      <Input />
      <Description>Please tell me you know your own name.</Description>
    </Field>
  )
}
```

----------------------------------------

TITLE: Setting CSS Variables with Arbitrary Values - HTML - Tailwind CSS
DESCRIPTION: Shows how to set custom CSS variables directly using arbitrary value syntax. The `[--gutter-width:1rem]` class sets a variable, and `lg:[--gutter-width:2rem]` shows how to apply it responsively.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_19

LANGUAGE: HTML
CODE:
```
<div class="[--gutter-width:1rem] lg:[--gutter-width:2rem]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using `theme()` in Arbitrary HTML Values
DESCRIPTION: This HTML snippet demonstrates the use of the `theme()` function within arbitrary values for utility classes. It creates a linear gradient background where colors and their opacities are dynamically pulled from the Tailwind configuration, offering flexible styling without writing custom CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<div class="bg-[image:linear-gradient(to_right,theme(colors.red.500)_75%,theme(colors.red.500/25%))]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Styling with Open State TailwindCSS Variant (CSS)
DESCRIPTION: This variant targets elements that are in an 'open' state, such as `<details>` elements, popovers, or other dynamically open components. It uses `:is` to combine multiple selectors for comprehensive open state styling in TailwindCSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_225

LANGUAGE: CSS
CODE:
```
&:is([open], :popover-open, :open)
```

----------------------------------------

TITLE: Implementing Scroll Snapping with snap-end in React/JSX
DESCRIPTION: This React/JSX snippet demonstrates how to create a horizontally scrollable container where child elements snap to their end when scrolled. It utilizes Tailwind CSS classes like `snap-x`, `snap-mandatory`, and `overflow-x-auto` on the parent container, and `shrink-0`, `snap-end`, `scroll-mx-6` on the child elements to achieve the snapping effect. It includes visual indicators and image elements to illustrate the behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-align.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
<div className="relative">
  <div className="mr-6 mb-6 flex items-end justify-end pt-10">
    <div className="dark:highlight-white/10 mr-2 rounded bg-indigo-50 px-1.5 font-mono text-[0.625rem] leading-6 text-indigo-600 ring-1 ring-indigo-600 ring-inset dark:bg-indigo-500 dark:text-white dark:ring-0">
      snap point
    </div>
    <div className="absolute top-0 right-6 bottom-0 border-l border-indigo-500"></div>
  </div>
  <div className="relative flex w-full snap-x snap-mandatory gap-6 overflow-x-auto pb-14">
    <div className="shrink-0 snap-end scroll-mx-6">
      <div className="w-3 shrink-0 sm:-mr-[2px] sm:w-10"></div>
    </div>
    <div className="shrink-0 snap-end scroll-mx-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1604999565976-8913ad2ddb7c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <div className="shrink-0 snap-end scroll-mx-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1540206351-d6465b3ac5c1?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <div className="shrink-0 snap-end scroll-mx-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1622890806166-111d7f6c7c97?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <div className="shrink-0 snap-end scroll-mx-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <div className="shrink-0 snap-end scroll-mx-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1575424909138-46b05e5919ec?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <div className="shrink-0 snap-end scroll-mx-6 pr-6">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1559333086-b0a56225a93c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Styling UI Component with Tailwind CSS in HTML
DESCRIPTION: This snippet provides a standard HTML example of applying Tailwind CSS utility classes to create a card component. It mirrors the JSX example but uses standard `class` attributes, showcasing the direct application of utilities for layout, appearance, spacing, and text styling, including dark mode variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- prettier-ignore -->
<div class="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10">
  <img class="size-12 shrink-0" src="/img/logo.svg" alt="ChitChat Logo" />
  <div>
    <div class="text-xl font-medium text-black dark:text-white">ChitChat</div>
    <p class="text-gray-500 dark:text-gray-400">You have a new message!</p>
  </div>
</div>
```

----------------------------------------

TITLE: Migrating Space-Between Layouts to Flex/Grid in HTML
DESCRIPTION: This HTML snippet demonstrates migrating from `space-y-*` utilities to a flexbox layout with `gap` in Tailwind CSS v4. It shows how to replace the `space-y-4` class with `flex flex-col gap-4` for more robust and performant spacing, especially when `space-y-*` causes issues.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_21

LANGUAGE: HTML
CODE:
```
<div class="space-y-4 p-4"> <!-- [!code --] -->
<div class="flex flex-col gap-4 p-4"> <!-- [!code ++] -->
  <label for="name">Name</label>
  <input type="text" name="name" />
</div>
```

----------------------------------------

TITLE: Applying Initial Opacity Transition with Tailwind CSS starting Variant (HTML)
DESCRIPTION: This snippet shows how to use the `starting` variant in Tailwind CSS to define an initial style for an element that transitions when it first appears. It sets the `opacity-0` class with the `starting:open` variant, ensuring the popover starts invisible and transitions to visible when opened, leveraging the CSS `@starting-style` feature.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_23

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:starting:open:opacity-0,transition-discrete] -->
<div>
  <button popovertarget="my-popover">Check for updates</button>
  <div popover id="my-popover" class="transition-discrete starting:open:opacity-0 ...">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Defining Themed Colors with Opacity in Tailwind Config
DESCRIPTION: This JavaScript snippet for `tailwind.config.js` shows how to define custom colors using the `theme()` function within the `extend.colors` section, including support for adjusting opacity with the new slash syntax. This enables creating derived colors like `primary-fade` directly in the configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_6

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  content: [
    // ...
  ],
  theme: {
    extend: {
      colors: ({ theme }) => ({
        primary: theme("colors.blue.500"),
        "primary-fade": theme("colors.blue.500 / 75%"),
      }),
    },
  },
  plugins: [],
};
```

----------------------------------------

TITLE: Safely Centering Flex Items with `justify-center-safe` in HTML
DESCRIPTION: This HTML snippet illustrates the `justify-center-safe` utility, which centers flex items but intelligently switches to `start` alignment when the container overflows. This prevents content from overflowing in both directions, ensuring visibility and a better user experience on smaller screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_21

LANGUAGE: html
CODE:
```
<ul class="flex justify-center-safe gap-2 ...">
  <li>Sales</li>
  <li>Marketing</li>
  <li>SEO</li>
  <!-- ... -->
</ul>
```

----------------------------------------

TITLE: Using @config for Admin Stylesheet (CSS)
DESCRIPTION: This CSS snippet demonstrates the use of the new `@config` directive to specify a dedicated Tailwind CSS configuration file for an admin stylesheet. It imports the base, components, and utilities layers from Tailwind CSS, tailored by `tailwind.admin.config.js`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_1

LANGUAGE: css
CODE:
```
@config "./tailwind.admin.config.js";
@tailwind base;
@tailwind components;
@tailwind utilities;
```

----------------------------------------

TITLE: Using Calc() with Arbitrary Values - HTML - Tailwind CSS
DESCRIPTION: Demonstrates incorporating CSS functions like `calc()` within arbitrary values. The `max-h-[calc(100dvh-(--spacing(6)))]` class sets a dynamic maximum height based on viewport height and a CSS variable.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_18

LANGUAGE: HTML
CODE:
```
<div class="max-h-[calc(100dvh-(--spacing(6)))]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using Theme Variables in Custom CSS
DESCRIPTION: This snippet demonstrates how to use Tailwind CSS theme variables within custom CSS, specifically within an `@layer components` block. This approach ensures that custom styles, particularly for elements like paragraphs and headings, adhere to the defined design tokens, which is useful for styling HTML content from external sources.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_24

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@layer components {
  .typography {
    p {
      font-size: var(--text-base);
      color: var(--color-gray-700);
    }

    h1 {
      font-size: var(--text-2xl--line-height);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-950);
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-950);
    }
  }
}
```

----------------------------------------

TITLE: Sorting Utilities by Box Model and Impact in HTML
DESCRIPTION: This snippet shows the sorting logic for various utility classes, which is loosely based on the CSS box model. Classes affecting layout (e.g., `ml-4`, `flex`, `h-24`) are prioritized, followed by border-related classes, and then decorative classes (e.g., `text-gray-700`, `shadow-md`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/automatic-class-sorting-with-prettier/index.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<div class="text-gray-700 shadow-md p-3 border-gray-300 ml-4 h-24 flex border-2"> <!-- [!code --] -->
<div class="ml-4 flex h-24 border-2 border-gray-300 p-3 text-gray-700 shadow-md"> <!-- [!code ++] -->
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Conditionally Hiding 'Required' Text with `peer-optional`
DESCRIPTION: This HTML snippet illustrates the use of the `peer-optional` variant in Tailwind CSS to conditionally hide a 'Required' notice for form fields that are optional. By combining `peer` on the input and `peer-optional:hidden` on the sibling div, the text is only visible for required fields.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<form>
  <div>
    <label for="email" ...>Email</label>
    <div>
      <input required class="peer ..." id="email" />
      <!-- [!code word:peer-optional\:hidden] -->
      <div class="peer-optional:hidden ...">Required</div>
    </div>
  </div>
  <div>
    <label for="name" ...>Name</label>
    <div>
      <input class="peer ..." id="name" />
```

----------------------------------------

TITLE: Styling Form Elements for Pointer Devices with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use Tailwind CSS's `pointer-coarse` variant to adjust the layout and padding of a form's radio button group when the user is interacting with a low-precision input device like a touchscreen. It shows a memory option selector where the grid columns and padding change for coarse pointers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_15

LANGUAGE: HTML
CODE:
```
<fieldset aria-label="Choose a memory option" className="mx-auto max-w-md">
  <div className="flex items-center justify-between">
    <div className="text-sm/6 font-medium text-gray-900 dark:text-white">RAM</div>
    <a href="#" className="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
      See performance specs
    </a>
  </div>
  <div className="mt-4 grid grid-cols-6 gap-2 max-sm:grid-cols-3 pointer-coarse:mt-6 pointer-coarse:grid-cols-3 pointer-coarse:gap-4">
    <label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
      <input type="radio" name="memory-option" value="4 GB" className="sr-only" />
      <span>4 GB</span>
    </label>
    <label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
      <input type="radio" name="memory-option" value="8 GB" className="sr-only" defaultChecked />
      <span>8 GB</span>
    </label>
    <label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
      <input type="radio" name="memory-option" value="16 GB" className="sr-only" />
      <span>16 GB</span>
    </label>
    <label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
      <input type="radio" name="memory-option" value="32 GB" className="sr-only" />
      <span>32 GB</span>
    </label>
    <label className="flex items-center justify-center rounded-md bg-white p-2 text-sm font-semibold text-gray-900 uppercase ring-1 ring-gray-300 not-data-focus:not-has-checked:ring-inset hover:bg-gray-50 has-checked:bg-indigo-600 has-checked:text-white has-checked:ring-0 has-checked:hover:bg-indigo-500 has-focus-visible:outline-2 has-focus-visible:outline-offset-2 has-focus-visible:outline-indigo-600 data-focus:ring-2 data-focus:ring-indigo-600 data-focus:ring-offset-2 data-focus:has-checked:ring-2 sm:flex-1 dark:bg-transparent dark:text-white dark:ring-white/20 dark:hover:bg-gray-950/50 pointer-coarse:p-4">
      <input type="radio" name="memory-option" value="64 GB" className="sr-only" />
      <span>64 GB</span>
    </label>
  </div>
</fieldset>
```

----------------------------------------

TITLE: Applying Relative Positioning with Tailwind CSS (HTML/JSX)
DESCRIPTION: This example illustrates the use of the `relative` utility in Tailwind CSS, which positions an element according to the normal document flow but allows offsets to be applied relative to its original position. Crucially, relatively positioned elements establish a new positioning context, acting as a reference for absolutely positioned child elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<div className="relative text-sm leading-6 font-medium">\n  <div className="rounded-lg border border-sky-700/10 bg-sky-400/20 p-4 dark:border-0 dark:bg-blue-900/70">\n    <div className="relative h-32 border border-sky-700/10 bg-sky-400/20 p-4 dark:border-0 dark:bg-blue-400/20">\n      <p className="text-sky-700 dark:text-white">Relative parent</p>\n      <div className="absolute bottom-0 left-0 rounded-lg bg-sky-500 p-4 text-white shadow-lg dark:bg-blue-500">\n        <p>Absolute child</p>\n      </div>\n    </div>\n  </div>\n</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:relative] -->\n<div class="relative ...">\n  <p>Relative parent</p>\n  <div class="absolute bottom-0 left-0 ...">\n    <p>Absolute child</p>\n  </div>\n</div>
```

----------------------------------------

TITLE: Customizing Tailwind CSS Theme with @theme Directive
DESCRIPTION: This CSS snippet demonstrates how to customize Tailwind CSS's default theme using the `@theme` directive. It allows defining custom design tokens such as font families, breakpoints, color palettes, and easing functions, providing a centralized way to manage design system variables.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_0

LANGUAGE: CSS
CODE:
```
@theme {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 120rem;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Styling Disabled Input Wrapper with :has() in JSX
DESCRIPTION: This snippet demonstrates how to style a wrapper element based on the `:disabled` state of its child `<input>` using the CSS `:has()` pseudo-class within a Tailwind CSS class. It allows for conditional styling of the parent `<span>` without JavaScript. The `has-[:disabled]:opacity-50` class applies `opacity-50` to the `<span>` when the nested `<input>` is disabled.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
export function Input({ ... }) {
  return (
    <span className="has-[:disabled]:opacity-50 ...">
      <input ... />
    </span>
  )
}
```

----------------------------------------

TITLE: Applying Line Clamp Utility - HTML
DESCRIPTION: This HTML snippet shows how to apply the line-clamp-3 utility class to a paragraph element. This class automatically truncates the text to three lines, adding an ellipsis at the end to indicate truncation, ensuring consistent card heights.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/multi-line-truncation-with-tailwindcss-line-clamp/index.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<p class="line-clamp-3">
  Here's a block of text from a blog post that isn't conveniently three lines long like you designed for originally.
  It's probably like 6 lines on mobile or even on desktop depending on how you have things laid out. Truly a big pain in
  the derriere, and not the sort of thing you expected to be wasting your time trying to deal with at 4:45pm on a Friday
  am I right? You've got tickets to SmackDown and you heard there's gonna be a dark match with that local guy from two
  towns over that your cousin went to high school with before the show starts, and you're gonna miss it if you're not
  there early.
</p>
```

----------------------------------------

TITLE: Styling Form Elements with Tailwind CSS Variants (JSX)
DESCRIPTION: This JSX code demonstrates styling `input` elements using Tailwind CSS pseudo-class variants like `invalid`, `focus`, and `disabled` to visually indicate different states without explicit conditional logic. It shows a form with username, email, and password fields, where the username is disabled and the email has an invalid default value, allowing users to observe state-based styling changes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_10

LANGUAGE: JSX
CODE:
```
<div className="mx-auto max-w-md border-x border-x-gray-200 px-6 py-5 dark:border-x-gray-800 dark:bg-gray-950/10">
  <form>
    <div>
      <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Username
      </label>
      <div className="mt-1">
        <input
          type="text"
          name="username"
          id="username"
          className="block w-full rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none sm:text-sm dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20"
          defaultValue="tbone"
          disabled
        />
      </div>
    </div>
    <div className="mt-6">
      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Email
      </label>
      <div className="mt-1">
        <input
          type="email"
          name="email"
          id="email-1"
          className="block w-full rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none sm:text-sm dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20"
          defaultValue="george@krugerindustrial."
          placeholder="<EMAIL>"
        />
      </div>
    </div>
    <div className="mt-6">
      <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Password
      </label>
      <div className="mt-1">
        <input
          type="password"
          name="password"
          id="password"
          autoComplete="none"
          className="block w-full rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none sm:text-sm dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20"
          defaultValue="Bosco"
        />
      </div>
    </div>
    <div className="mt-6 text-right">
      <button className="rounded-md bg-sky-500 px-5 py-2.5 text-sm leading-5 font-semibold text-white hover:bg-sky-700">
        Save changes
      </button>
    </div>
  </form>
</div>
```

----------------------------------------

TITLE: Defining Container Query for @max-5xl (Width < 64rem) in CSS
DESCRIPTION: This snippet defines a container query associated with the @max-5xl breakpoint, applying styles when the container's width is less than 64 rem. It's used for responsive design based on the parent container's size rather than the viewport.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_190

LANGUAGE: CSS
CODE:
```
@container (width < 64rem)
```

----------------------------------------

TITLE: Defining Minimum Width Container Query for 4XL Breakpoint in CSS
DESCRIPTION: This CSS snippet defines a container query that applies styles when the container's width is greater than or equal to 56rem (896px). It corresponds to the `@4xl` container breakpoint in Tailwind CSS, enabling responsive design based on parent container dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_175

LANGUAGE: CSS
CODE:
```
@container (width >= 56rem)
```

----------------------------------------

TITLE: Restoring Pointer Cursor for Buttons in Tailwind CSS v4 (CSS)
DESCRIPTION: This CSS snippet shows how to restore the `cursor: pointer` behavior for buttons in Tailwind CSS v4, matching the v3 default. By adding this `@layer base` rule, `button:not(:disabled)` and `[role="button"]:not(:disabled)` will display a pointer cursor instead of the new default `cursor: default`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_31

LANGUAGE: CSS
CODE:
```
@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}
```

----------------------------------------

TITLE: Defining Custom Design Tokens with @theme Directive (CSS)
DESCRIPTION: The @theme directive allows you to define custom design tokens such as fonts, breakpoints, colors, and easing functions directly within your CSS. These tokens can then be referenced elsewhere in your styles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/functions-and-directives.mdx#_snippet_1

LANGUAGE: CSS
CODE:
```
@theme {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 120rem;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Using Arbitrary CSS Properties with Tailwind CSS HTML
DESCRIPTION: This example illustrates the use of arbitrary CSS properties within Tailwind CSS, allowing direct application of any CSS property and value. It shows how to set `mask-type` to `luminance` by default and change it to `alpha` on hover, demonstrating flexibility with modifiers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#_snippet_13

LANGUAGE: HTML
CODE:
```
<div class="[mask-type:luminance] hover:[mask-type:alpha]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Tabs with Headless UI in React
DESCRIPTION: This snippet demonstrates how to create a basic tab interface using the `Tab` component from `@headlessui/react`. It shows the declarative structure for `Tab.Group`, `Tab.List`, `Tab`, `Tab.Panels`, and `Tab.Panel` to manage tab navigation and content display, abstracting away accessibility concerns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-4/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { Tab } from '@headlessui/react'

function MyTabs() {
  return (
    <Tab.Group>
      <Tab.List>
        <Tab>Tab 1</Tab>
        <Tab>Tab 2</Tab>
        <Tab>Tab 3</Tab>
      </Tab.List>
      <Tab.Panels>
        <Tab.Panel>Content 1</Tab.Panel>
        <Tab.Panel>Content 2</Tab.Panel>
        <Tab.Panel>Content 3</Tab.Panel>
      </Tab.Panels>
    </Tab.Group>
  )
}
```

----------------------------------------

TITLE: Applying Dark Mode Responsive Styles HTML
DESCRIPTION: Shows how to combine the `dark:` variant with responsive prefixes like `lg:` (`lg:dark:bg-black`) to apply dark mode styles that are also scoped to a specific breakpoint. Requires `darkMode` enabled and the responsive variant.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<div class="lg:bg-white lg:dark:bg-black ...">
```

----------------------------------------

TITLE: Aligning Flex Items to Start with Tailwind CSS HTML
DESCRIPTION: This snippet demonstrates the `justify-start` utility in Tailwind CSS, which aligns flex items to the beginning of the container's main axis. It's applied to the flex container to position its direct children.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:justify-start] -->
<div class="flex justify-start ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Payment Method Selection UI with HTML and Tailwind CSS
DESCRIPTION: This snippet defines the HTML structure for selecting payment methods (Apple Pay, Credit Card) using radio buttons. It applies Tailwind CSS classes for layout, styling, and interactive states, particularly using `has-[:checked]` variants to change appearance when a radio button is selected.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<label
  htmlFor="apple-pay"
  className="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 text-slate-700 ring-2 ring-transparent hover:bg-slate-100 has-[:checked]:bg-indigo-50 has-[:checked]:text-indigo-900 has-[:checked]:ring-indigo-500"
>
  <svg className="w-8" viewBox="0 0 24 11" fill="none">
    <path d="M4.38526 1.86704C4.10401 2.19606 3.65392 2.45565 3.20387 2.41854C3.14762 1.97367 3.36793 1.50091 3.62579 1.20892C3.90704 0.870635 4.39932 0.62962 4.79781 0.611084C4.84468 1.07453 4.66182 1.52871 4.38526 1.86704ZM4.79312 2.50663C4.14146 2.46956 3.5836 2.87272 3.27418 2.87272C2.96012 2.87272 2.48659 2.52517 1.97092 2.53443C1.30056 2.5437 0.677025 2.91906 0.33479 3.51694C-0.368428 4.71265 0.151978 6.48308 0.831712 7.45632C1.16457 7.9383 1.56306 8.46662 2.0881 8.44809C2.58507 8.42955 2.78195 8.12834 3.38204 8.12834C3.98677 8.12834 4.1
```

----------------------------------------

TITLE: Applying Text Color on Hover in React/JSX
DESCRIPTION: This React/JSX snippet demonstrates how to change text color on hover using Tailwind CSS classes like `hover:text-blue-600` and `dark:hover:text-blue-400` applied to an anchor tag, providing interactive styling within a React component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
<p className="text-center text-xl font-medium text-gray-900 dark:text-gray-200">
  Oh I gotta get on that{" "}
  <a
    href="https://en.wikipedia.org/wiki/Internet"
    target="_blank"
    className="underline hover:text-blue-600 dark:hover:text-blue-400"
  >
    internet
  </a>
  , I'm late on everything!
</p>
```

----------------------------------------

TITLE: Targeting Container Query Ranges in Tailwind CSS HTML
DESCRIPTION: This HTML snippet shows how to target a specific range of container sizes by combining regular and max-width container query variants. The `@sm:@max-md:flex-col` class applies a style only when the container is between its small and medium breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_16

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-row @sm:@max-md:flex-col">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Setting Max Width to Full Percentage in Tailwind CSS
DESCRIPTION: Sets the maximum width to 100% of the parent's width. This is commonly used to make an element fill its available horizontal space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#_snippet_17

LANGUAGE: CSS
CODE:
```
max-width: 100%;
```

----------------------------------------

TITLE: Setting Explicit Sort Order with Tailwind CSS `order` Utility (HTML)
DESCRIPTION: This snippet demonstrates how to explicitly set the display order of flex or grid items using Tailwind CSS's `order-<number>` utilities. By applying classes like `order-1`, `order-2`, and `order-3`, items are rendered in a custom sequence, overriding their natural document flow. This is useful for visually reordering elements without changing their source HTML position.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/order.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:order-1,order-2,order-3] -->
<div class="flex justify-between ...">
  <div class="order-3 ...">01</div>
  <div class="order-1 ...">02</div>
  <div class="order-2 ...">03</div>
</div>
```

----------------------------------------

TITLE: Filtering Combobox Results in Vue
DESCRIPTION: This snippet illustrates how to implement a basic string comparison filter for the Headless UI `Combobox` component in Vue. It uses `ref` for reactive state and a `computed` property for filtering the `people` array based on the query input. This provides a dynamic and efficient way to filter options within a Vue application's template.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-5/index.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<script setup>
  import { ref, computed } from 'vue'
  import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/vue'

  const people = [
    'Wade Cooper',
    'Arlene McCoy',
    'Devon Webb',
    'Tom Cook',
    'Tanya Fox',
    'Hellen Schmidt',
  ]
  const selectedPerson = ref(people[0])
  const query = ref('')

  const filteredPeople = computed(() =>
    query.value === ''
      ? people
      : people.filter((person) => {
          return person.toLowerCase().includes(query.value.toLowerCase())
        })
  )
</script>

<template>
  <Combobox v-model="selectedPerson">
    <ComboboxInput @change="query = $event.target.value" />
    <ComboboxOptions>
      <ComboboxOption v-for="person in filteredPeople" :key="person" :value="person">
        {{ person }}
      </ComboboxOption>
    </ComboboxOptions>
  </Combobox>
</template>

```

----------------------------------------

TITLE: Supporting Reduced Motion with Tailwind CSS
DESCRIPTION: This HTML snippet shows how to use `motion-reduce` variants to conditionally disable transitions and transforms for users who prefer reduced motion. It ensures a smoother experience for accessibility-conscious users.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-property.mdx#_snippet_10

LANGUAGE: HTML
CODE:
```
<button class="transform transition hover:-translate-y-1 motion-reduce:transition-none motion-reduce:hover:transform-none ...">
  <!-- ... -->
</button>
```

----------------------------------------

TITLE: Simulating Fixed Header in React/JSX with Absolute Positioning
DESCRIPTION: This React/JSX snippet demonstrates how to create a scrollable container with a header that appears fixed, using `absolute` positioning within a `relative` parent. The `overflow-auto` class on the content area enables scrolling, while the header remains visible at the top. This pattern is useful for components that need a static header within a confined, scrollable region, rather than truly fixed to the viewport.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
<div className="px-3">
  <div className="relative mx-auto h-80 max-w-md overflow-hidden bg-white shadow-lg ring-1 ring-gray-900/5 dark:bg-gray-800">
    <div className="absolute top-0 right-0 left-0 flex items-center bg-gray-50/90 px-4 py-3 text-sm font-semibold text-gray-900 ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-700/90 dark:text-gray-200 dark:ring-black/10">
      Contacts
    </div>
    <div className="flex h-80 flex-col divide-y divide-gray-200 overflow-auto dark:divide-gray-200/5">
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1501196354995-cbb51c65aaea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Andrew Alfred</strong>
      </div>
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Debra Houston</strong>
      </div>
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Jane White</strong>
      </div>
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Ray Flint</strong>
      </div>
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Mindy Albrect</strong>
      </div>
      <div className="flex items-center gap-4 p-4">
        <img
          className="size-12 rounded-full"
          src="https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"
        />
        <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">David Arnold</strong>
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Styling Direct Children with Tailwind CSS * Variant in HTML
DESCRIPTION: This HTML snippet showcases the new `*` variant in Tailwind CSS, which allows applying utility classes directly to all immediate children of an element. Here, it's used to style `<li>` elements within a `<ul>` for rounded borders, background, and padding. This is useful when you don't control the child elements directly or need conditional tweaks based on context.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<div>
  <h2>Categories:<h2>
  <ul class="*:rounded-full *:border *:border-sky-100 *:bg-sky-50 *:px-2 *:py-0.5 dark:text-sky-300 dark:*:border-sky-500/15 dark:*:bg-sky-500/10 ...">
    <li>Sales</li>
    <li>Marketing</li>
    <li>SEO</li>
    <!-- ... -->
  </ul>
</div>
```

----------------------------------------

TITLE: Combining Multiple Tailwind Variants
DESCRIPTION: Demonstrates how to combine multiple Tailwind variants like dark mode, breakpoint, data attribute, and hover to apply styles under specific conditions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_21

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:dark:lg:data-current:hover:bg-indigo-600] -->
<button class="dark:lg:data-current:hover:bg-indigo-600 ...">
  <!-- ... -->
</button>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
@media (prefers-color-scheme: dark) and (width >= 64rem) {
  button[data-current]:hover {
    background-color: var(--color-indigo-600);
  }
}
```

----------------------------------------

TITLE: Simplified CSS Variable Color Configuration in Tailwind
DESCRIPTION: This JavaScript snippet for `tailwind.config.js` demonstrates the simplified method for defining colors using CSS variables. Instead of a function, a format string with an `<alpha-value>` placeholder is used, which Tailwind automatically replaces with the correct opacity, reducing boilerplate.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_9

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  theme: {
    colors: {
      primary: "rgb(var(--color-primary) / <alpha-value>)",
      secondary: "rgb(var(--color-secondary) / <alpha-value>)",
      // ...
    },
  },
};
```

----------------------------------------

TITLE: Adding Single-Side Margin with Tailwind CSS (HTML)
DESCRIPTION: This snippet demonstrates how to apply margin to a single side of an element using Tailwind CSS utilities. It uses `mt-<number>` for top margin, `mr-<number>` for right margin, `mb-<number>` for bottom margin, and `ml-<number>` for left margin. The number indicates the margin size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:mt-6,mr-4,mb-8,ml-2] -->
<div class="mt-6 ...">mt-6</div>
<div class="mr-4 ...">mr-4</div>
<div class="mb-8 ...">mb-8</div>
<div class="ml-2 ...">ml-2</div>
```

----------------------------------------

TITLE: Basic Element Positioning with Tailwind CSS in HTML
DESCRIPTION: This HTML snippet illustrates how to use Tailwind CSS positioning utilities such as `top-0`, `left-0`, `inset-x-0`, `inset-y-0`, `inset-0`, `right-0`, and `bottom-0` to control the placement of absolutely positioned child elements within a relative parent. It covers pinning to corners, spanning edges, and filling the parent.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/top-right-bottom-left.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:inset-x-0,inset-y-0,inset-0,right-0,left-0,top-0,bottom-0] -->
<!-- Pin to top left corner -->
<div class="relative size-32 ...">
  <div class="absolute top-0 left-0 size-16 ...">01</div>
</div>

<!-- Span top edge -->
<div class="relative size-32 ...">
  <div class="absolute inset-x-0 top-0 h-16 ...">02</div>
</div>

<!-- Pin to top right corner -->
<div class="relative size-32 ...">
  <div class="absolute top-0 right-0 size-16 ...">03</div>
</div>

<!-- Span left edge -->
<div class="relative size-32 ...">
  <div class="absolute inset-y-0 left-0 w-16 ...">04</div>
</div>

<!-- Fill entire parent -->
<div class="relative size-32 ...">
  <div class="absolute inset-0 ...">05</div>
</div>

<!-- Span right edge -->
<div class="relative size-32 ...">
  <div class="absolute inset-y-0 right-0 w-16 ...">06</div>
</div>

<!-- Pin to bottom left corner -->
<div class="relative size-32 ...">
  <div class="absolute bottom-0 left-0 size-16 ...">07</div>
</div>

<!-- Span bottom edge -->
<div class="relative size-32 ...">
  <div class="absolute inset-x-0 bottom-0 h-16 ...">08</div>
</div>

<!-- Pin to bottom right corner -->
<div class="relative size-32 ...">
  <div class="absolute right-0 bottom-0 size-16 ...">09</div>
</div>
```

----------------------------------------

TITLE: Custom Focus Styles with JSX and Tailwind CSS
DESCRIPTION: This JSX snippet demonstrates how to remove the default browser outline using `outline-none` on a `textarea` and apply custom focus styles to its parent container using `focus-within:outline-2` and `focus-within:outline-indigo-600`. It also shows a button with its own `focus:outline` styles, emphasizing the importance of accessibility when removing default outlines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/outline-style.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
<div className="mx-auto flex max-w-md flex-col rounded-lg outline-1 outline-gray-300 focus-within:outline-2 focus-within:outline-indigo-600 dark:bg-white/5 dark:outline-transparent dark:focus-within:outline-indigo-500">
  <textarea className="w-full resize-none p-2 outline-none" placeholder="Leave a comment..." />
  <button
    className="mr-2 mb-2 inline-flex items-center self-end rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus:outline-2 focus:outline-offset-2 focus:outline-indigo-600"
    type="button"
  >
    Post
  </button>
</div>
```

----------------------------------------

TITLE: Styling Direct Children with Tailwind CSS `*` Variant
DESCRIPTION: This example illustrates the use of the `*` variant in Tailwind CSS to apply styles directly to all immediate child elements. This is particularly useful when you cannot directly modify the child elements, allowing you to apply common styles like `rounded-full`, `border`, and background colors to all `<li>` items within the `<ul>`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_89

LANGUAGE: html
CODE:
```
<!-- [!code classes:*:rounded-full] -->
<!-- [!code classes:*:border] -->
<!-- [!code classes:*:border-sky-100] -->
<!-- [!code classes:*:bg-sky-50] -->
<!-- [!code classes:*:px-2] -->
<!-- [!code classes:*:py-0.5] -->
<!-- [!code classes:dark:text-sky-300] -->
<!-- [!code classes:dark:*:border-sky-500/15] -->
<!-- [!code classes:dark:*:bg-sky-500/10] -->
<div>
  <h2>Categories<h2>
  <ul class="*:rounded-full *:border *:border-sky-100 *:bg-sky-50 *:px-2 *:py-0.5 dark:text-sky-300 dark:*:border-sky-500/15 dark:*:bg-sky-500/10 ...">
    <li>Sales</li>
    <li>Marketing</li>
    <li>SEO</li>
    <!-- ... -->
  </ul>
</div>
```

----------------------------------------

TITLE: Basic Text Color Usage in HTML
DESCRIPTION: This HTML snippet illustrates the application of `text-blue-600` and `dark:text-sky-400` classes to control the text color of a paragraph element, providing a straightforward example for web development.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<p class="text-blue-600 dark:text-sky-400">The quick brown fox...</p>
```

----------------------------------------

TITLE: Spanning Rows with `row-span` in JSX
DESCRIPTION: This JSX snippet demonstrates how to use `row-span-<number>` utilities within a React component to make grid items span a specified number of rows. It shows a grid container with three items, where '01' spans 3 rows and '03' spans 2 rows, illustrating the visual effect of these utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-row.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
<div className=\"grid grid-cols-1\">\n      <Stripes border className=\"col-start-1 row-start-1 rounded-lg\" />\n      <div className=\"col-start-1 row-start-1 grid grid-flow-col grid-rows-3 gap-4 rounded-lg text-center font-mono text-sm leading-6 font-bold text-white\">\n        <div className=\"row-span-3 grid place-content-center rounded-lg bg-fuchsia-500 p-4\">01</div>\n        <div className=\"col-span-2 grid place-content-center rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400\">\n          02\n        </div>\n        <div className=\"col-span-2 row-span-2 grid place-content-center rounded-lg bg-fuchsia-500 p-4\">03</div>\n      </div>\n    </div>
```

----------------------------------------

TITLE: Applying Responsive Flex Basis in HTML
DESCRIPTION: Demonstrates how to use responsive prefixes, such as `md:basis-1/3`, to apply different `flex-basis` values at various breakpoints. This enables adaptive layouts where flex item sizes change based on screen dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-basis.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex flex-row">
  <div class="basis-1/4 md:basis-1/3">01</div>
  <div class="basis-1/4 md:basis-1/3">02</div>
  <div class="basis-1/2 md:basis-1/3">03</div>
</div>
```

----------------------------------------

TITLE: Applying Font Size Utilities in React/JSX
DESCRIPTION: This React/JSX example demonstrates how to use Tailwind CSS font size utilities within a component. It showcases various `text-` classes applied to `p` elements to visually represent different font sizes, along with descriptive labels.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<div className="flex flex-col gap-8">
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-sm</span>
    <p className="text-sm font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-base</span>
    <p className="text-base font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-lg</span>
    <p className="text-lg font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-xl</span>
    <p className="text-xl font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">text-2xl</span>
    <p className="text-2xl font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
</div>
```

----------------------------------------

TITLE: Defining Violet Oklch Color Palette in CSS
DESCRIPTION: This snippet outlines various violet color shades as CSS custom properties, leveraging the Oklch color space. These definitions enable developers to easily access and apply a consistent violet palette throughout their stylesheets, promoting design uniformity.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_21

LANGUAGE: CSS
CODE:
```
--color-violet-50: oklch(0.969 0.016 293.756);
--color-violet-100: oklch(0.943 0.029 294.588);
--color-violet-200: oklch(0.894 0.057 293.283);
--color-violet-300: oklch(0.811 0.111 293.571);
--color-violet-400: oklch(0.702 0.183 293.541);
--color-violet-500: oklch(0.606 0.25 292.717);
--color-violet-600: oklch(0.541 0.281 293.009);
--color-violet-700: oklch(0.491 0.27 292.581);
--color-violet-800: oklch(0.432 0.232 292.759);
--color-violet-900: oklch(0.38 0.189 293.745);
--color-violet-950: oklch(0.283 0.141 291.089);
```

----------------------------------------

TITLE: Applying Transition Utilities to a Button in HTML
DESCRIPTION: This HTML snippet demonstrates how to apply Tailwind CSS transition utilities to a button. It uses `transition` for default properties, `delay-150`, `duration-300`, and `ease-in-out` for timing, and hover effects for transform and background color changes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-property.mdx#_snippet_9

LANGUAGE: HTML
CODE:
```
<button class="bg-blue-500 transition delay-150 duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 hover:bg-indigo-500 ...">
  Save Changes
</button>
```

----------------------------------------

TITLE: Applying Vertical Padding with Tailwind CSS
DESCRIPTION: This HTML snippet showcases the `py-<number>` utility, such as `py-8`, for applying vertical padding to an element. This utility class simultaneously controls both the top and bottom padding, ensuring symmetrical spacing along the y-axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:py-8] -->\n<div class="py-8 ...">py-8</div>
```

----------------------------------------

TITLE: Configuring Custom Color Palette JavaScript
DESCRIPTION: Illustrates how to import and use the `tailwindcss/colors` module within `tailwind.config.js` to define a custom color palette for the theme. This allows developers to curate specific color shades from the extended palette. Requires a `tailwind.config.js` file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const colors = require("tailwindcss/colors");

module.exports = {
  theme: {
    colors: {
      gray: colors.trueGray,
      indigo: colors.indigo,
      red: colors.rose,
      yellow: colors.amber,
    },
  },
};
```

----------------------------------------

TITLE: Creating a Collapsible Disclosure with Headless UI in Vue
DESCRIPTION: This Vue component illustrates the use of Headless UI's `Disclosure` component to create show/hide inline content. It includes `DisclosureButton` for toggling and `DisclosurePanel` for the collapsible content, ideal for FAQs or "show more" features.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1/index.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<template>
  <Disclosure>
    <DisclosureButton> Is team pricing available? </DisclosureButton>
    <DisclosurePanel> Yes! You can purchase a license that you can share with your entire team. </DisclosurePanel>
  </Disclosure>
</template>

<script>
  import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";

  export default {
    components: { Disclosure, DisclosureButton, DisclosurePanel },
  };
</script>

```

----------------------------------------

TITLE: Preventing Flex Item Growth/Shrinkage with flex-none in JSX
DESCRIPTION: This JSX snippet demonstrates how to use the `flex-none` utility class within a React component to prevent specific flex items from growing or shrinking. It showcases two fixed-size `flex-none` items and one `flex-1` item that expands to fill available space, illustrating the combined effect of these Tailwind CSS flex properties.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
<div className="grid grid-cols-1">
  <Stripes border className="col-start-1 row-start-1 rounded-lg" />
  <div className="col-start-1 row-start-1 flex gap-4 rounded-lg font-mono text-sm leading-6 font-bold text-white">
    <div className="flex-none last:pr-8 sm:last:pr-0">
      <div className="flex h-14 w-14 items-center justify-center rounded-lg bg-indigo-500 p-4">01</div>
    </div>
    <div className="flex-none last:pr-8 sm:last:pr-0">
      <div className="flex w-32 items-center justify-center rounded-lg bg-indigo-500 p-4">02</div>
    </div>
    <div className="flex-1 last:pr-8 sm:last:pr-0">
      <div className="flex items-center justify-center rounded-lg bg-indigo-300 p-4 dark:bg-indigo-800 dark:text-indigo-400">
        03
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Defining Dynamic Variant with matchVariant Plugin API in JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to define a custom dynamic variant, `placement-*`, using Tailwind CSS's `matchVariant` plugin API. It creates a variant that targets elements based on their `data-placement` attribute, allowing for arbitrary values or predefined shortcuts like `placement-t` for `top`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_28

LANGUAGE: JavaScript
CODE:
```
let plugin = require("tailwindcss/plugin");

module.exports = {
  // ...
  plugins: [
    plugin(function ({ matchVariant }) {
      matchVariant(
        "placement",
        (value) => {
          return `&[data-placement=${value}]`;
        },
        {
          values: {
            t: "top",
            r: "right",
            b: "bottom",
            l: "left"
          }
        }
      );
    })
  ]
};
```

----------------------------------------

TITLE: Using flex-initial for Shrinking Flex Items in HTML
DESCRIPTION: Use `flex-initial` to allow a flex item to shrink but not grow, taking into account its initial size. This utility is useful when you want items to maintain their original width unless space is constrained, ensuring they only reduce in size when necessary.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code word:flex-initial] -->
<div class="flex">
  <div class="w-14 flex-none ...">01</div>
  <div class="w-64 flex-initial ...">02</div>
  <div class="w-32 flex-initial ...">03</div>
</div>
```

----------------------------------------

TITLE: Defining Custom Container Query for Dynamic Width in CSS
DESCRIPTION: This snippet defines a flexible container query allowing for custom width values. It applies styles when the container's width is less than a dynamically specified value, enabling highly customizable responsive design.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_193

LANGUAGE: CSS
CODE:
```
@container (width < ...)
```

----------------------------------------

TITLE: Defining Tailwind CSS Typography Size Variables
DESCRIPTION: This snippet defines CSS custom properties for a comprehensive typography scale, including font sizes (e.g., text-xs, text-base, text-9xl) and their corresponding line heights. These variables ensure consistent and scalable text rendering.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_34

LANGUAGE: CSS
CODE:
```
--text-xs: 0.75rem;
--text-xs--line-height: calc(1 / 0.75);
--text-sm: 0.875rem;
--text-sm--line-height: calc(1.25 / 0.875);
--text-base: 1rem;
--text-base--line-height: calc(1.5 / 1);
--text-lg: 1.125rem;
--text-lg--line-height: calc(1.75 / 1.125);
--text-xl: 1.25rem;
--text-xl--line-height: calc(1.75 / 1.25);
--text-2xl: 1.5rem;
--text-2xl--line-height: calc(2 / 1.5);
--text-3xl: 1.875rem;
--text-3xl--line-height: calc(2.25 / 1.875);
--text-4xl: 2.25rem;
--text-4xl--line-height: calc(2.5 / 2.25);
--text-5xl: 3rem;
--text-5xl--line-height: 1;
--text-6xl: 3.75rem;
--text-6xl--line-height: 1;
--text-7xl: 4.5rem;
--text-7xl--line-height: 1;
--text-8xl: 6rem;
--text-8xl--line-height: 1;
--text-9xl: 8rem;
--text-9xl--line-height: 1;
```

----------------------------------------

TITLE: Using Complete Class Names in HTML with Tailwind
DESCRIPTION: This HTML snippet demonstrates the correct approach for applying conditional classes in Tailwind CSS. By ensuring that the full class names (`text-red-600`, `text-green-600`) are present as complete strings, Tailwind's plain-text scanner can successfully detect and generate the corresponding CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="{{ error ? 'text-red-600' : 'text-green-600' }}"></div>
```

----------------------------------------

TITLE: Visualizing Box-Content with JSX and Tailwind CSS
DESCRIPTION: This JSX snippet provides a detailed visual demonstration of the `box-content` utility. It renders a `div` with `size-32` (8rem x 8rem) and `box-content`, along with `p-5` (padding) and `ring-4` (border), showing how these additions expand the element beyond its initial 32x32 unit size. The surrounding grid and measurement indicators highlight the actual rendered dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-sizing.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<div className="relative grid grid-cols-[1fr_8rem_1fr] grid-rows-[1fr_2fr_1fr] gap-px bg-gray-700/10 font-mono text-sm leading-6 font-bold dark:bg-gray-700">
      <div className="col-start-1 row-start-1 bg-white dark:bg-gray-900"></div>
      <div className="relative col-start-2 row-start-1 bg-white dark:bg-gray-900">
        {/* w-measure indicator */}
        <div className="absolute right-0 bottom-2 left-0 flex -translate-y-5">
          {/* Horizontal line */}
          <div className="absolute top-1/2 right-0 left-0 h-px -translate-y-px bg-blue-400"></div>
          {/* Left chip */}
          <div className="w-full">
            <div className="absolute top-1/2 left-0 h-2 w-px -translate-x-px -translate-y-1 rounded-full bg-blue-400"></div>
          </div>
          {/* Badge */}
          <div className="relative flex w-full flex-auto items-center justify-center bg-white px-1.5 font-mono text-xs leading-none font-bold text-blue-600 dark:bg-gray-900 dark:text-blue-400">
            128px
          </div>
          {/* Right chip */}
          <div className="w-full">
            <div className="absolute top-1/2 right-0 h-2 w-px translate-x-px -translate-y-1 rounded-full bg-blue-400"></div>
          </div>
        </div>
      </div>
      <div className="col-start-3 row-start-1 bg-white dark:bg-gray-900"></div>
      <div className="relative col-start-1 row-start-2 bg-white dark:bg-gray-900">
        {/* h-measure indicator */}
        <div className="absolute top-0 right-2 bottom-0 flex w-3 -translate-x-5">
          {/* Vertical line */}
          <div className="absolute top-0 bottom-0 left-1/2 w-px -translate-x-[0.5px] bg-blue-400"></div>
          {/* Top chip */}
          <div className="w-full">
            <div className="absolute top-0 left-1/2 h-px w-2 -translate-x-1 -translate-y-px rounded-full bg-blue-400"></div>
          </div>
          {/* Badge */}
          <div className="relative flex h-3 flex-auto -translate-x-[1.15rem] translate-y-14 -rotate-90 items-center justify-center bg-white px-1.5 font-mono text-xs leading-none font-bold text-blue-600 dark:bg-gray-900 dark:text-blue-400">
            128px
          </div>
          {/* Bottom chip */}
          <div className="w-full">
            <div className="absolute bottom-0 left-1/2 h-px w-2 -translate-x-1 translate-y-px rounded-full bg-blue-400"></div>
          </div>
        </div>
      </div>
      <div className="col-start-2 row-start-2 size-32 bg-white">
        <div className="relative box-content size-32 -translate-x-5 -translate-y-5 p-5 ring-4 ring-blue-300 ring-inset dark:ring-blue-500">
          <div className="h-full w-full bg-blue-500 ring-1 ring-blue-500"></div>
          <div className="absolute inset-1 z-10">
            <Stripes className="h-full" />
          </div>
        </div>
      </div>
      <div className="col-start-3 row-start-2 bg-white dark:bg-gray-900"></div>
      <div className="col-start-1 row-start-3 bg-white dark:bg-gray-900"></div>
      <div className="col-start-2 row-start-3 bg-white dark:bg-gray-900"></div>
      <div className="col-start-3 row-start-3 bg-white dark:bg-gray-900"></div>
    </div>
```

----------------------------------------

TITLE: Applying Media Query for Dark Color Scheme in CSS
DESCRIPTION: This snippet defines a media query that applies styles when the user's operating system or browser is set to prefer a dark color scheme, enabling dark mode support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_194

LANGUAGE: CSS
CODE:
```
@media (prefers-color-scheme: dark)
```

----------------------------------------

TITLE: Applying Pointer-Coarse Variants for Responsive Layout (HTML)
DESCRIPTION: This HTML snippet illustrates the use of Tailwind CSS `pointer-coarse` variants to adapt layout for touch-based devices. It adjusts margins, grid columns, and padding within a memory option selection `fieldset`, making touch targets larger and easier to interact with on phones.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_17

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:pointer-coarse:mt-6,pointer-coarse:grid-cols-3,pointer-coarse:gap-4,pointer-coarse:p-4] -->
<fieldset aria-label="Choose a memory option">
  <div class="flex items-center justify-between">
    <div>RAM</div>
    <a href="#"> See performance specs </a>
  </div>
  <div class="mt-4 grid grid-cols-6 gap-2 pointer-coarse:mt-6 pointer-coarse:grid-cols-3 pointer-coarse:gap-4">
    <label class="p-2 pointer-coarse:p-4 ...">
      <input type="radio" name="memory-option" value="4 GB" className="sr-only" />
      <span>4 GB</span>
    </label>
    <!-- ... -->
  </div>
</fieldset>
```

----------------------------------------

TITLE: Rendering Elements in a Loop with Tailwind CSS (Svelte)
DESCRIPTION: This Svelte snippet shows how to use the `#each` block to iterate over a list of contributors and render an image tag for each. The Tailwind CSS classes are applied once within the loop template, avoiding manual duplication of the class list.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_29

LANGUAGE: Svelte
CODE:
```
<div>
  <div class="flex items-center space-x-2 text-base">
    <h4 class="font-semibold text-slate-900">Contributors</h4>
    <span class="bg-slate-100 px-2 py-1 text-xs font-semibold text-slate-700 ...">204</span>
  </div>
  <div class="mt-3 flex -space-x-2 overflow-hidden">
    <!-- prettier-ignore -->
    <!-- [!code highlight:4] -->
    {#each contributors as user}
      <img class="inline-block h-12 w-12 rounded-full ring-2 ring-white" src={user.avatarUrl} alt={user.handle} />
    {/each}
  </div>
  <div class="mt-3 text-sm font-medium">
    <a href="#" class="text-blue-500">+ 198 others</a>
  </div>
</div>
```

----------------------------------------

TITLE: Aligning Element to Middle in HTML
DESCRIPTION: This HTML snippet shows the basic usage of the `align-middle` utility class to align an inline element's middle with the baseline plus half the x-height of the parent.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/vertical-align.mdx#_snippet_6

LANGUAGE: html
CODE:
```
<!-- [!code classes:align-middle] -->
<span class="inline-block align-middle">The quick brown fox...</span>
```

----------------------------------------

TITLE: Implementing Enter/Leave Animations with Headless UI Transition Component (React)
DESCRIPTION: This snippet demonstrates how to use the Headless UI `Transition` component in React to add enter and leave animations to an element. It leverages Tailwind CSS utility classes for defining animation properties like opacity and duration, making it easy to style transitions without being coupled to Tailwind. The `show` prop controls the visibility, triggering the animations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Transition
  show={isOpen}
  enter="transition-opacity duration-75"
  enterFrom="opacity-0"
  enterTo="opacity-100"
  leave="transition-opacity duration-150"
  leaveFrom="opacity-100"
  leaveTo="opacity-0"
>
  I will fade in and out
</Transition>
```

----------------------------------------

TITLE: Defining Custom Minimum Width Container Query in CSS
DESCRIPTION: This CSS snippet illustrates the syntax for defining a custom minimum width container query. It allows developers to specify any arbitrary minimum width for a container, enabling highly flexible responsive designs beyond predefined breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_179

LANGUAGE: CSS
CODE:
```
@container (width >= ...)
```

----------------------------------------

TITLE: Installing Tailwind CSS with Vite
DESCRIPTION: This command installs the latest versions of Tailwind CSS and its dedicated Vite plugin via npm. This integration is optimized for projects built with Vite, providing a seamless development experience.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_1

LANGUAGE: sh
CODE:
```
npm install tailwindcss@latest @tailwindcss/vite@latest
```

----------------------------------------

TITLE: Installing Tailwind CSS v4.1 with Vite via npm
DESCRIPTION: This shell command installs the latest versions of `tailwindcss` and `@tailwindcss/vite` using npm. This integration is specifically designed for projects using Vite as their build tool, providing optimized and seamless integration of Tailwind CSS within the Vite development server and build process.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_31

LANGUAGE: Shell
CODE:
```
npm install tailwindcss@latest @tailwindcss/vite@latest
```

----------------------------------------

TITLE: Detecting Classes in JSX with Tailwind
DESCRIPTION: This JSX code demonstrates how Tailwind CSS scans source files as plain text to identify potential utility classes. It highlights various class names and keywords that Tailwind would detect, even if they are part of a larger string or not directly applied to an element, to generate the necessary CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
export function Button({ color, children }) {
  const colors = {
    black: "bg-black text-white",
    blue: "bg-blue-500 text-white",
    white: "bg-white text-black"
  };

  return (
    <button className={`${colors[color]} rounded-full px-2 py-1.5 font-sans text-sm/6 font-medium shadow`}>
      {children}
    </button>
  );
}
```

----------------------------------------

TITLE: Updating CSS Variable Syntax in Tailwind CSS v4 Arbitrary Values
DESCRIPTION: Tailwind CSS v4 updates the syntax for using CSS variables in arbitrary values to resolve ambiguity. This HTML snippet demonstrates the migration from the old square bracket syntax (e.g., `bg-[--brand-color]`) to the new parenthesis syntax (e.g., `bg-(--brand-color)`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_39

LANGUAGE: HTML
CODE:
```
<div class="bg-[--brand-color]"></div>
<div class="bg-(--brand-color)"></div>
```

----------------------------------------

TITLE: Demonstrating CSS Variable Resolution Issues in HTML
DESCRIPTION: This HTML snippet demonstrates a common pitfall in CSS variable resolution without the `inline` option. It shows how a variable (`--font-sans`) defined on a parent element might resolve to an unexpected fallback (`sans-serif`) because a nested variable (`--font-inter`) is not yet defined at the parent's scope, leading to incorrect font application.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_19

LANGUAGE: html
CODE:
```
<div id="parent" style="--font-sans: var(--font-inter, sans-serif);">
  <div id="child" style="--font-inter: Inter; font-family: var(--font-sans);">
    This text will use the sans-serif font, not Inter.
  </div>
</div>
```

----------------------------------------

TITLE: Applying Dark Mode Styles in HTML with Tailwind CSS
DESCRIPTION: Demonstrates how to use Tailwind CSS `dark:` prefixed utility classes alongside regular classes to apply different styles based on the user's color scheme preference. Shows examples for background color and text color.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_12

LANGUAGE: html
CODE:
```
<!-- [!code word:dark\:bg-gray-800] -->
<!-- prettier-ignore -->
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg
        class="h-6 w-6 text-white"

        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <!-- ... -->
      </svg>
    </span>
  </div>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-white] -->
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-gray-400] -->
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Configuring PostCSS Plugin for Tailwind CSS v4.0
DESCRIPTION: This JavaScript snippet shows a minimal PostCSS configuration file that adds the @tailwindcss/postcss plugin. This configuration is essential for PostCSS to process Tailwind CSS directives and generate the final CSS output, simplifying the setup by reducing boilerplate.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_5

LANGUAGE: JavaScript
CODE:
```
export default {
  plugins: ["@tailwindcss/postcss"]
};
```

----------------------------------------

TITLE: Updating Tailwind CSS to the latest version
DESCRIPTION: This command updates Tailwind CSS to the latest version using npm. It installs the latest version as a development dependency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_37

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Updating Tailwind CSS Dependency via npm
DESCRIPTION: This shell command provides instructions for updating the Tailwind CSS dependency in a project. Running npm install -D tailwindcss@latest installs the latest version of Tailwind CSS as a development dependency, ensuring users can access new features and improvements like container queries without breaking changes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_36

LANGUAGE: Shell
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Defining Custom Font Theme Variable in Tailwind CSS
DESCRIPTION: This CSS snippet shows how to define a new custom font theme variable, `--font-poppins`, within `app.css` using the `@theme` directive. By defining this variable, Tailwind CSS automatically makes a `font-poppins` utility class available for use in HTML, allowing easy application of the custom font.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_4

LANGUAGE: css
CODE:
```
/* [!code filename:app.css] */
@import "tailwindcss";

@theme {
  /* [!code highlight:2] */
  --font-poppins: Poppins, sans-serif;
}
```

----------------------------------------

TITLE: Importing Tailwind CSS in Main CSS File (PostCSS Flow) - CSS
DESCRIPTION: This CSS snippet imports the Tailwind CSS framework into your main application stylesheet, making its utility classes available when processed by PostCSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_16

LANGUAGE: css
CODE:
```
/* [!code filename:app.css] */
@import "tailwindcss";
```

----------------------------------------

TITLE: Installing Tailwind CSS v3.0 with npm
DESCRIPTION: This shell command installs the latest version of Tailwind CSS (v3.0), PostCSS, and Autoprefixer as development dependencies using npm. These packages are crucial for compiling and processing Tailwind CSS in a project, enabling the use of its utility-first classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#_snippet_1

LANGUAGE: Shell
CODE:
```
npm install -D tailwindcss@latest postcss autoprefixer
```

----------------------------------------

TITLE: Supporting Negative Values for Inset Utilities in Tailwind CSS
DESCRIPTION: To enable negative values for utilities, this snippet shows how to register separate positive and negative utility declarations. It uses `calc()` and `--value()` with percentage and length types to define `inset-*` and `-inset-*` utilities, where the negative variant multiplies the value by -1.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_39

LANGUAGE: CSS
CODE:
```
@utility inset-* {
  inset: calc(var(--spacing) * --value([percentage], [length]));
}

@utility -inset-* {
  inset: calc(var(--spacing) * --value([percentage], [length]) * -1);
}
```

----------------------------------------

TITLE: Applying `focus-visible` Variant to HTML Button
DESCRIPTION: This HTML snippet demonstrates how to use the new `focus-visible` variant to apply focus styles that are only visible to keyboard users. This improves accessibility by preventing unwanted focus outlines for mouse users while maintaining them for keyboard navigation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-5/index.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<button class="focus-visible:shadow-outline focus-visible:outline-none ...">Click me</button>
```

----------------------------------------

TITLE: Centering Text with Tailwind CSS
DESCRIPTION: This snippet shows how to center text within an HTML element using the `text-center` utility class provided by Tailwind CSS. Apply this class to any block-level element to horizontally center its text content.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:text-center] -->
<p class="text-center">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Applying Responsive Grid Columns in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet shows how to apply responsive grid column classes in Tailwind CSS using breakpoint prefixes. The `grid-cols-2` class applies by default, and the `sm:grid-cols-3` class overrides it at the `sm` breakpoint and above, changing the grid to 3 columns. This requires a Tailwind CSS setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_10

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:sm:grid-cols-3] -->
<div class="grid grid-cols-2 sm:grid-cols-3">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Initializing Tailwind CSS Configuration in ES Module
DESCRIPTION: This JavaScript code initializes the Tailwind CSS configuration in an ES module. It defines the content, theme, and plugins for Tailwind CSS. The `export default` statement makes the configuration available for use in other modules.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
/** @type {import('tailwindcss').Config} */
export default {
  content: [],
  theme: {
    extend: {},
  },
  plugins: [],
};
```

----------------------------------------

TITLE: Using Arbitrary ARIA Variants for Sorting in HTML
DESCRIPTION: This HTML snippet demonstrates the use of arbitrary value variants for ARIA attributes, specifically `aria-sort`. It shows how to apply conditional styling to an SVG icon within a table header based on the `aria-sort` attribute of its parent group, using `group-aria-[sort=ascending]` and `group-aria-[sort=descending]` to control rotation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_74

LANGUAGE: HTML
CODE:
```
<div className="py-8">
  <table className="w-full border-collapse border-y border-gray-400 bg-white text-sm dark:border-white/10 dark:bg-transparent">
    <thead className="bg-gray-50 dark:bg-white/5">
      <tr>
        <th
          className="group border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-white/10 dark:text-gray-200"
          aria-sort="ascending"
        >
          <span className="flex w-full items-center justify-between gap-2">
            Invoice #
            <svg
              viewBox="0 0 20 20"
              className="h-5 w-5 fill-gray-500 group-aria-[sort=ascending]:rotate-0 group-aria-[sort=descending]:rotate-180"
            >
              <path
                fillRule="evenodd"
```

----------------------------------------

TITLE: Styling with aria-required TailwindCSS Variant (CSS)
DESCRIPTION: This variant targets elements where the `aria-required` attribute is set to `true`, useful for indicating and styling mandatory form fields in TailwindCSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_219

LANGUAGE: CSS
CODE:
```
&[aria-required="true"]
```

----------------------------------------

TITLE: Dynamically Setting SVG Stroke to Current Text Color in Tailwind CSS
DESCRIPTION: This example illustrates the use of the `stroke-current` utility to make an SVG's stroke color inherit the current text color of its parent element. This is particularly useful for creating interactive elements, such as buttons where the icon's color changes on hover, aligning with the text color for a cohesive design.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/stroke.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<div className="flex items-center justify-center">
  <button className="inline-flex gap-2 rounded-md border border-pink-100 bg-white px-3 py-2 text-sm font-semibold text-pink-600 shadow-sm transition-colors duration-150 hover:border-pink-600 hover:bg-pink-600 hover:text-white dark:border-transparent">
    <svg fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-5 stroke-current">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
      />
    </svg>
    Download file
  </button>
</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:stroke-current] -->
<button class="bg-white text-pink-600 hover:bg-pink-600 hover:text-white ...">
  <svg class="size-5 stroke-current ..." fill="none">
    <!-- ... -->
  </svg>
  Download file
</button>
```

----------------------------------------

TITLE: Using Custom Theme Utilities in HTML Markup
DESCRIPTION: This HTML snippet demonstrates how the custom theme variables defined with @theme are translated into usable utility classes. It shows examples like font-display, text-neon-cyan, and 3xl:max-w-xl, which are derived from the CSS variables, allowing for direct application in the markup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code filename:index.html] -->
<!-- [!code word:3xl\:max-w-xl] -->
<!-- [!code word:font-display] -->
<!-- [!code word:text-neon-cyan] -->
<div class="max-w-lg 3xl:max-w-xl">
  <h1 class="font-display text-4xl">
    Data to <span class="text-neon-cyan">enrich</span> your online business
  </h1>
</div>
```

----------------------------------------

TITLE: Applying flex-none and flex-1 Utilities in HTML
DESCRIPTION: This HTML snippet illustrates the direct application of Tailwind CSS `flex-none` and `flex-1` utility classes to control the sizing behavior of child elements within a flex container. `flex-none` ensures items maintain their intrinsic size, while `flex-1` allows an item to grow and shrink as needed, distributing remaining space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code word:flex-none] -->
<div class="flex ...">
  <div class="w-14 flex-none ...">01</div>
  <div class="w-32 flex-none ...">02</div>
  <div class="flex-1 ...">03</div>
</div>
```

----------------------------------------

TITLE: Has Variant CSS in Tailwind CSS
DESCRIPTION: This snippet shows the CSS for the `has-[...]` variant in Tailwind CSS. It applies styles to an element if it contains a descendant that matches the specified selector, utilizing the `:has()` pseudo-class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_108

LANGUAGE: CSS
CODE:
```
&:has(...)
```

----------------------------------------

TITLE: Styling First/Last Child Elements with Tailwind CSS (HTML/JSX)
DESCRIPTION: This snippet demonstrates how to apply specific styles to the first and last child elements within a list using Tailwind CSS's `first:` and `last:` pseudo-class variants. It removes top padding from the first list item and bottom padding from the last list item, ensuring consistent spacing. This is useful for lists where the first and last items might otherwise have extra padding due to a general padding rule.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
<div className="px-4">
  <div className="mx-auto max-w-md border-x border-x-gray-200 dark:border-x-gray-800 dark:bg-gray-950/10">
    <ul role="list" className="divide-y divide-gray-200 p-6 dark:divide-gray-800">
      <li className="flex py-4 first:pt-0 last:pb-0">
        <img
          className="h-10 w-10 rounded-full"
          src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt=""
        />
        <div className="ml-3 overflow-hidden">
          <p className="text-sm font-medium text-gray-900 dark:text-white">Kristen Ramos</p>
          <p className="truncate text-sm text-gray-500 dark:text-gray-400"><EMAIL></p>
        </div>
      </li>
      <li className="flex py-4 first:pt-0 last:pb-0">
        <img
          className="h-10 w-10 rounded-full"
          src="https://images.unsplash.com/photo-1463453091185-61582044d556?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt=""
        />
        <div className="ml-3 overflow-hidden">
          <p className="text-sm font-medium text-gray-900 dark:text-white">Floyd Miles</p>
          <p className="truncate text-sm text-gray-500 dark:text-gray-400"><EMAIL></p>
        </div>
      </li>
      <li className="flex py-4 first:pt-0 last:pb-0">
        <img
          className="h-10 w-10 rounded-full"
          src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt=""
        />
        <div className="ml-3 overflow-hidden">
          <p className="text-sm font-medium text-gray-900 dark:text-white">Courtney Henry</p>
          <p className="truncate text-sm text-gray-500 dark:text-gray-400"><EMAIL></p>
        </div>
      </li>
      <li className="flex py-4 first:pt-0 last:pb-0">
        <img
          className="h-10 w-10 rounded-full"
          src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt=""
        />
        <div className="ml-3 overflow-hidden">
          <p className="text-sm font-medium text-gray-900 dark:text-white">Ted Fox</p>
          <p className="truncate text-sm text-gray-500 dark:text-gray-400"><EMAIL></p>
        </div>
      </li>
    </ul>
  </div>
</div>
```

----------------------------------------

TITLE: Compiling Tailwind CSS with CLI Tool - Shell
DESCRIPTION: This shell command uses the @tailwindcss/cli to process app.css, which contains Tailwind imports, and outputs the compiled CSS to dist/app.css.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_19

LANGUAGE: sh
CODE:
```
npx @tailwindcss/cli@next -i app.css -o dist/app.css
```

----------------------------------------

TITLE: Applying Inert Variant to Form Elements in JSX
DESCRIPTION: This snippet demonstrates how to apply the `inert` variant in Tailwind CSS to a `fieldset` element within a React/JSX form. The `inert` attribute makes the `fieldset` and its contents non-interactive, while `inert:opacity-25` visually dims it, indicating its inactive state. It showcases a notification preferences form with custom radio and checkbox inputs.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_87

LANGUAGE: JSX
CODE:
```
<form className="mx-auto max-w-md space-y-6 border-x border-x-gray-200 p-8 dark:border-x-gray-800">
  <legend>Notification preferences</legend>
  <fieldset name="resale" defaultValue="permit">
    <div className="flex items-center gap-x-3">
      <input
        readOnly
        id="push-everything"
        name="push-notifications"
        type="radio"
        className="relative size-4 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 dark:border-gray-700 dark:bg-gray-950 forced-colors:appearance-auto forced-colors:before:hidden"
      />
      <label htmlFor="push-everything" className="block text-sm/6 font-medium text-gray-900 dark:text-white">
        Custom
      </label>
    </div>
    <fieldset className="mt-6 space-y-6 pl-8 inert:opacity-25" inert>
      <div className="flex gap-3">
        <div className="flex h-6 shrink-0 items-center">
          <div className="group grid size-4 grid-cols-1">
            <input
              defaultChecked
              id="comments"
              name="comments"
              type="checkbox"
              aria-describedby="comments-description"
              className="col-start-1 row-start-1 appearance-none rounded-sm border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 dark:border-gray-700 dark:bg-gray-950 forced-colors:appearance-auto"
            />
            <svg
              fill="none"
              viewBox="0 0 14 14"
              className="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-disabled:stroke-gray-950/25"
            >
              <path
                d="M3 8L6 11L11 3.5"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
                className="opacity-0 group-has-checked:opacity-100"
              />
              <path
                d="M3 7H11"
                strokeWidth={2}
                strokeLinecap="round"

```

----------------------------------------

TITLE: Tailwind CSS Line Height Utility Class Reference
DESCRIPTION: This table outlines the various Tailwind CSS utility classes available for controlling `line-height`, demonstrating how each class maps to its corresponding CSS property and value. It includes utilities for combined font size and line height, as well as standalone leading utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/line-height.mdx#_snippet_0

LANGUAGE: CSS
CODE:
```
text-<size>/<number> -> font-size: <size>;\nline-height: calc(var(--spacing) * <number>);
text-<size>/(<custom-property>) -> font-size: <size>;\nline-height: var(<custom-property>);
text-<size>/[<value>] -> font-size: <size>;\nline-height: <value>;
leading-none -> line-height: 1;
leading-<number> -> line-height: calc(var(--spacing) * <number>)
leading-(<custom-property>) -> line-height: var(<custom-property>);
leading-[<value>] -> line-height: <value>;
```

----------------------------------------

TITLE: Applying Responsive object-fit in HTML
DESCRIPTION: This HTML snippet demonstrates how to apply responsive `object-fit` behavior using Tailwind CSS. The `object-contain` class is applied by default, and `md:object-cover` overrides it for medium and larger screen sizes. This allows the image to behave differently based on the viewport width, adapting its resizing behavior for various devices.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#_snippet_11

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:md:object-cover] -->
<img class="object-contain md:object-cover" src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Using Arbitrary Container Query Values in HTML
DESCRIPTION: This HTML snippet demonstrates the use of arbitrary container query values, such as `@min-[475px]`, for one-off responsive styling without modifying the theme. When the `@container` element's width is at least `475px`, the child element's flex direction will change from `flex-col` to `flex-row`, providing flexible, on-the-fly breakpoint control.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_20

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @min-[475px]:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Implementing a Toggle Switch with Headless UI and React
DESCRIPTION: This snippet demonstrates how to create a customizable toggle switch component using React and Headless UI's `Switch` component. It manages the enabled state with React's `useState` hook and applies Tailwind CSS classes dynamically based on the state for visual styling. The `classNames` helper function is used to conditionally join CSS classes, ensuring a fully functional and accessible component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwind-ui-now-with-react-and-vue-support/index.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
import { useState } from "react";
import { Switch } from "@headlessui/react";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Example() {
  const [enabled, setEnabled] = useState(false);

  return (
    <Switch
      checked={enabled}
      onChange={setEnabled}
      className={classNames(
        enabled ? "bg-indigo-600" : "bg-gray-200",
        "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none",
      )}
    >
      <span className="sr-only">Use setting</span>
      <span
        aria-hidden="true"
        className={classNames(
          enabled ? "translate-x-5" : "translate-x-0",
          "pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",
        )}
      />
    </Switch>
  );
}
```

----------------------------------------

TITLE: Configuring PostCSS for Tailwind CSS v4.0 - JavaScript
DESCRIPTION: This JavaScript snippet for `postcss.config.js` demonstrates the simplified configuration for Tailwind CSS v4.0. It shows that the `postcss-import` plugin is no longer needed, as Tailwind CSS now provides built-in `@import` support, streamlining the PostCSS setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_10

LANGUAGE: JavaScript
CODE:
```
export default {
  plugins: [
    "@tailwindcss/postcss"
  ]
};
```

----------------------------------------

TITLE: Applying Logical Border Radius with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to apply border radius using Tailwind CSS logical properties like `rounded-s-lg`. These properties adapt to text direction, applying to the start (left in LTR, right in RTL) of an element. It shows examples for both left-to-right (LTR) and right-to-left (RTL) contexts using the `dir` attribute.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#_snippet_4

LANGUAGE: React
CODE:
```
<div className="grid grid-cols-2 place-items-center gap-x-4">
  <div className="flex flex-col items-start gap-y-4" dir="ltr">
    <p className="text-sm font-medium">Left-to-right</p>
    <div className="size-16 rounded-s-lg bg-blue-500 p-4"></div>
  </div>
  <div className="flex flex-col items-start gap-y-4" dir="rtl">
    <p className="text-sm font-medium">Right-to-left</p>
    <div className="size-16 rounded-s-lg bg-blue-500 p-4"></div>
  </div>
</div>
```

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:rounded-s-lg] -->
<!-- [!code word:dir="ltr"] -->
<!-- [!code word:dir="rtl"] -->
<div dir="ltr">
  <div class="rounded-s-lg ..."></div>
</div>

<div dir="rtl">
  <div class="rounded-s-lg ..."></div>
</div>
```

----------------------------------------

TITLE: Mapping Props to Static Class Names in JSX with Tailwind
DESCRIPTION: This JSX snippet demonstrates the recommended way to handle conditional styling with props in Tailwind CSS. By mapping prop values to complete, static class name strings within an object, Tailwind's plain-text scanner can easily detect all possible class names at build-time, ensuring proper CSS generation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#_snippet_4

LANGUAGE: JSX
CODE:
```
function Button({ color, children }) {
  const colorVariants = {
    blue: "bg-blue-600 hover:bg-blue-500",
    red: "bg-red-600 hover:bg-red-500"
  };

  return <button className={`${colorVariants[color]} ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Stretching to fit in HTML
DESCRIPTION: This HTML snippet demonstrates the `object-fill` utility class on an `img` tag. The `object-fill` class stretches the image to fill the entire content box, regardless of its aspect ratio. This can lead to image distortion if the container's aspect ratio differs from the image's original aspect ratio.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:object-fill] -->
<img class="h-48 w-96 object-fill ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Updating Ring Width in Tailwind CSS v4 (HTML)
DESCRIPTION: This HTML snippet shows how to update the `ring` utility usage in Tailwind CSS v4. The default ring width changed from 3px to 1px, so `ring` should be replaced with `ring-3` to maintain the previous visual appearance.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_27

LANGUAGE: HTML
CODE:
```
<button class="focus:ring ..."> <!-- [!code --] -->
<button class="focus:ring-3 ..."> <!-- [!code ++] -->
  <!-- ... -->
</button>
```

----------------------------------------

TITLE: Percentage min-width Example HTML
DESCRIPTION: Demonstrates using percentage-based min-width utilities like `min-w-3/4` to set a minimum width relative to the parent element's width.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-width.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:min-w-3/4] -->
<div class="flex ...">
  <div class="min-w-3/4 ...">min-w-3/4</div>
  <div class="w-full ...">w-full</div>
</div>
```

----------------------------------------

TITLE: Applying Arbitrary CSS Properties in HTML
DESCRIPTION: This HTML snippet demonstrates how to apply completely arbitrary CSS properties using Tailwind CSS's square bracket notation. It allows developers to use any CSS property not included out-of-the-box, such as `mask-type:luminance`, directly within their HTML.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<div class="[mask-type:luminance]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Demonstrating Automatic Class Sorting with Prettier in HTML
DESCRIPTION: This HTML example illustrates how the Prettier plugin for Tailwind CSS automatically sorts utility classes. The 'Before' section shows unsorted classes, while the 'After' section demonstrates the same classes reordered according to Tailwind's recommended class order, improving readability and consistency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/automatic-class-sorting-with-prettier/index.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- Before -->
<button class="text-white px-4 sm:px-8 py-2 sm:py-3 bg-sky-700 hover:bg-sky-800">...</button>

<!-- After -->
<button class="bg-sky-700 px-4 py-2 text-white hover:bg-sky-800 sm:px-8 sm:py-3">...</button>
```

----------------------------------------

TITLE: Defining Purple Oklch Color Palette in CSS
DESCRIPTION: This snippet defines a comprehensive set of purple color shades using CSS custom properties and the Oklch color model. These variables are essential for maintaining a unified purple color scheme across a project, facilitating easy updates and consistency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_22

LANGUAGE: CSS
CODE:
```
--color-purple-50: oklch(0.977 0.014 308.299);
--color-purple-100: oklch(0.946 0.033 307.174);
--color-purple-200: oklch(0.902 0.063 306.703);
--color-purple-300: oklch(0.827 0.119 306.383);
--color-purple-400: oklch(0.714 0.203 305.504);
--color-purple-500: oklch(0.627 0.265 303.9);
--color-purple-600: oklch(0.558 0.288 302.321);
--color-purple-700: oklch(0.496 0.265 301.924);
--color-purple-800: oklch(0.438 0.218 303.724);
--color-purple-900: oklch(0.381 0.176 304.987);
--color-purple-950: oklch(0.291 0.149 302.717);
```

----------------------------------------

TITLE: Hiding Spinner with `motion-reduce` in HTML
DESCRIPTION: This HTML snippet demonstrates the direct application of the `motion-reduce:hidden` class to an SVG element. This class ensures that the `animate-spin` utility is disabled, effectively hiding the spinner, when the user's operating system has 'prefers-reduced-motion' enabled, enhancing accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_52

LANGUAGE: html
CODE:
```
<!-- [!code classes:motion-reduce:hidden] -->
<button type="button" class="bg-indigo-500 ..." disabled>
  <svg class="animate-spin motion-reduce:hidden ..." viewBox="0 0 24 24"><!-- ... --></svg>
  Processing...
</button>
```

----------------------------------------

TITLE: Defining Arbitrary Groups in HTML
DESCRIPTION: This HTML example illustrates the creation of an arbitrary `group-*` variant using a custom selector `[.is-published]`. It demonstrates how a child element can be conditionally displayed (`block`) when its parent has both the `group` class and the `is-published` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_26

LANGUAGE: HTML
CODE:
```
<div class="group is-published">
  <div class="hidden group-[.is-published]:block">
    Published
  </div>
</div>
```

----------------------------------------

TITLE: Applying Dynamic Group Variant in HTML
DESCRIPTION: This HTML snippet demonstrates how to apply a dynamic `group-[.is-published]:block` variant. It uses the `group` class on the parent and `is-published` to control the visibility of a child element, making it visible when the parent has the `is-published` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_24

LANGUAGE: HTML
CODE:
```
<div class="group is-published">
        <div class="group-[.is-published]:block hidden">Published</div>
      </div>
```

----------------------------------------

TITLE: Applying Custom Variants in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to apply a custom variant, such as `theme-midnight:bg-black`, to an HTML element. This variant will only take effect when the corresponding custom variant conditions (defined in CSS) are met, in this case, when the `data-theme="midnight"` attribute is present on the `html` tag.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_43

LANGUAGE: HTML
CODE:
```
<html data-theme="midnight">
  <button class="theme-midnight:bg-black ..."></button>
</html>
```

----------------------------------------

TITLE: Applying Conditional Styles with ARIA Checked in HTML
DESCRIPTION: This HTML snippet demonstrates how to conditionally apply Tailwind CSS styles based on the `aria-checked` attribute. When `aria-checked` is `true`, the element's background color changes to blue-600, otherwise it remains gray-600. This allows for dynamic styling based on accessibility states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<span class="bg-gray-600 aria-checked:bg-blue-600" aria-checked="true" role="checkbox">
  <!-- ... -->
</span>
```

----------------------------------------

TITLE: Proportional Flex Item Growth with Tailwind CSS
DESCRIPTION: This example illustrates the use of `grow-<number>` utilities, such as `grow-3` and `grow-7`, to control the proportional growth of flex items. Items will expand relative to each other based on their assigned growth factors, distributing available space accordingly.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-grow.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:grow-3,grow-7] -->
<div class="flex ...">
  <div class="size-14 grow-3 ...">01</div>
  <div class="size-14 grow-7 ...">02</div>
  <div class="size-14 grow-3 ...">03</div>
</div>
```

----------------------------------------

TITLE: Tailwind CSS `sr-only` Utility CSS Definition
DESCRIPTION: Defines the CSS properties for the `sr-only` utility class, which makes an element visually hidden but still accessible to screen readers. This is crucial for accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_1

LANGUAGE: CSS
CODE:
```
position: absolute;
width: 1px;
height: 1px;
padding: 0;
margin: -1px;
overflow: hidden;
clip: rect(0, 0, 0, 0);
white-space: nowrap;
border-width: 0;
```

----------------------------------------

TITLE: Understanding Specificity with Tailwind CSS Child Selectors
DESCRIPTION: This snippet demonstrates a common pitfall when using Tailwind CSS's `*` variant: direct utility classes on child elements cannot override styles applied via the `*` variant on the parent. This is due to the higher specificity of the generated child selector, meaning `*:bg-sky-50` on the `<ul>` will take precedence over `bg-red-50` on the `<li>`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_90

LANGUAGE: html
CODE:
```
<!-- [!code classes:*:bg-sky-50] -->
<!-- [!code classes:bg-red-50] -->
<ul class="*:bg-sky-50 ...">
  <li class="bg-red-50 ...">Sales</li>
  <li>Marketing</li>
  <li>SEO</li>
  <!-- ... -->
</ul>
```

----------------------------------------

TITLE: Right Aligning Text with Tailwind CSS
DESCRIPTION: This snippet illustrates how to right align text within an HTML element using the `text-right` utility class from Tailwind CSS. Attach this class to the desired element to align its contained text to the right margin.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:text-right] -->
<p class="text-right">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Defining Container Query for @max-4xl (Width < 56rem) in CSS
DESCRIPTION: This snippet defines a container query associated with the @max-4xl breakpoint, applying styles when the container's width is less than 56 rem. It's used for responsive design based on the parent container's size rather than the viewport.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_189

LANGUAGE: CSS
CODE:
```
@container (width < 56rem)
```

----------------------------------------

TITLE: Applying Tailwind CSS Background Color Utilities in HTML
DESCRIPTION: This HTML snippet demonstrates the application of various Tailwind CSS background color utility classes ('bg-sky-50' through 'bg-sky-950') to 'div' elements. Each 'div' is styled with a different shade from the Sky color palette, illustrating the 11 steps of a default Tailwind color scale for visual representation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:bg-sky-50,bg-sky-100,bg-sky-200,bg-sky-300,bg-sky-400,bg-sky-500,bg-sky-600,bg-sky-700,bg-sky-800,bg-sky-900,bg-sky-950] -->
<div>
  <div class="bg-sky-50"></div>
  <div class="bg-sky-100"></div>
  <div class="bg-sky-200"></div>
  <div class="bg-sky-300"></div>
  <div class="bg-sky-400"></div>
  <div class="bg-sky-500"></div>
  <div class="bg-sky-600"></div>
  <div class="bg-sky-700"></div>
  <div class="bg-sky-800"></div>
  <div class="bg-sky-900"></div>
  <div class="bg-sky-950"></div>
</div>
```

----------------------------------------

TITLE: Applying Styles with Range-Based Breakpoint Variant in HTML
DESCRIPTION: This HTML snippet demonstrates how the `max-*` variant simplifies applying styles within a specific breakpoint range. The `sr-only` utility will be active from the `md` breakpoint up to (but not including) the `xl` breakpoint, avoiding the need to explicitly undo the style.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_21

LANGUAGE: HTML
CODE:
```
<div class="md:max-xl:sr-only">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Styles with Arbitrary Min/Max Breakpoint Values in HTML
DESCRIPTION: This HTML snippet showcases the use of arbitrary values with `min-*` and `max-*` variants. It allows for highly precise, dynamic breakpoints, applying the `right-16` utility only when the viewport width is between 712px and 877px.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_22

LANGUAGE: HTML
CODE:
```
<div class="min-[712px]:max-[877px]:right-16 ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Styling Invalid Inputs with :invalid in HTML
DESCRIPTION: This snippet shows how to apply a Tailwind CSS class to an input element when its content is invalid according to its validation rules, using the `invalid` variant. This is essential for guiding users, typically by applying a `border-red-500` to highlight errors.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_255

LANGUAGE: HTML
CODE:
```
<input required class="border invalid:border-red-500 ..." />
```

----------------------------------------

TITLE: Customizing Tailwind CSS Theme for Text Properties (CSS)
DESCRIPTION: This CSS snippet demonstrates how to extend the Tailwind CSS theme to define custom text properties for a specific font size. Within the `@theme` block, it sets a custom font size `--text-tiny` and associates default `line-height`, `letter-spacing`, and `font-weight` values, allowing for consistent typography across the application.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#_snippet_5

LANGUAGE: css
CODE:
```
@theme {
  --text-tiny: 0.625rem;
  --text-tiny--line-height: 1.5rem; /* [!code highlight] */
  --text-tiny--letter-spacing: 0.125rem; /* [!code highlight] */
  --text-tiny--font-weight: 500; /* [!code highlight] */
}
```

----------------------------------------

TITLE: Implementing Bidirectional Scrolling with Tailwind CSS and React
DESCRIPTION: This React component demonstrates how to create a scrollable container using Tailwind CSS's `overflow-scroll` utility. It sets up a grid layout for a calendar-like interface, ensuring both horizontal and vertical scrolling when content exceeds the container's dimensions. Sticky headers and time labels are also implemented to maintain visibility during scrolling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#_snippet_12

LANGUAGE: JSX
CODE:
```
<div className="overflow-hidden dark:bg-gray-800">
  <div className="grid max-h-90 grid-cols-[70px_repeat(7,150px)] grid-rows-[auto_repeat(16,50px)] overflow-scroll">
    {/* Calendar frame */}
    <div className="sticky top-0 z-10 col-start-1 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200"></div>
    <div className="sticky top-0 z-10 col-start-2 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Sun
    </div>
    <div className="sticky top-0 z-10 col-start-3 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Mon
    </div>
    <div className="sticky top-0 z-10 col-start-4 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Tue
    </div>
    <div className="sticky top-0 z-10 col-start-5 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Wed
    </div>
    <div className="sticky top-0 z-10 col-start-6 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Thu
    </div>
    <div className="sticky top-0 z-10 col-start-7 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Fri
    </div>
    <div className="sticky top-0 z-10 col-start-8 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Sat
    </div>
    <div className="sticky left-0 col-start-1 row-start-2 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
      5 AM
    </div>
    <div className="col-start-2 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-3 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-4 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-5 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-6 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-7 row-start-2 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-8 row-start-2 border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="sticky left-0 col-start-1 row-start-3 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
      6 AM
    </div>
    <div className="col-start-2 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-3 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-4 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-5 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-6 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-7 row-start-3 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="col-start-8 row-start-3 border-b border-gray-100 dark:border-gray-200/5"></div>
    <div className="sticky left-0 col-start-1 row-start-4 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
      7 AM
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Migrating Vite Configuration to Tailwind CSS v4 Plugin (TypeScript)
DESCRIPTION: This snippet illustrates how to configure Vite to use the new dedicated `@tailwindcss/vite` plugin for Tailwind CSS v4. This migration is recommended for Vite users to achieve improved performance and a better developer experience. It involves importing the new plugin and adding it to the `plugins` array in `vite.config.ts`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
  plugins: [
    tailwindcss()
  ]
});
```

----------------------------------------

TITLE: Using Arbitrary Dynamic Variant Value in HTML
DESCRIPTION: This HTML snippet shows how to use an arbitrary value, `top-start`, with the custom `placement-*` dynamic variant. This allows for flexible styling based on values not explicitly defined in the `matchVariant` plugin configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_29

LANGUAGE: HTML
CODE:
```
<div class="placement-[top-start]:mb-2 ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Styling on Hover with Tailwind CSS (HTML)
DESCRIPTION: This snippet demonstrates how to apply styles when an element is hovered over using the `hover` variant in Tailwind CSS. The `hover:bg-white` class changes the background color to white on hover, while `bg-black` is the default background.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_227

LANGUAGE: HTML
CODE:
```
<div class="bg-black hover:bg-white ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Building Form with Fieldset and Disabled State (React/JSX)
DESCRIPTION: Provides an example of building a shipping details form using `Fieldset`, `Legend`, `Field`, `Label`, `Input`, and `Select` components. It illustrates how to dynamically disable a `Field` based on state (`country` selection) and how the `data-disabled` attribute is exposed for styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import { Button, Description, Field, Fieldset, Input, Label, Legend, Select } from "@headlessui/react";
import { regions } from "./countries";

export function Example() {
  const [country, setCountry] = useState(null);

  return (
    <form action="/shipping">
      <Fieldset>
        <Legend>Shipping details</Legend>
        <Field>
          <Label>Street address</Label>
          <Input name="address" />
        </Field>
        <Field>
          <Label>Country</Label>
          <Description>We currently only ship to North America.</Description>
          <Select name="country" value={country} onChange={(event) => setCountry(event.target.value)}>
            <option></option>
            <option>Canada</option>
            <option>Mexico</option>
            <option>United States</option>
          </Select>
        </Field>
        // [!code highlight:4]
        <Field disabled={!country}>
          <Label className="data-[disabled]:opacity-40">State/province</Label>
          <Select name="region" className="data-[disabled]:opacity-50">
            <option></option>
            {country && regions[country].map((region) => <option>{region}</option>)}
          </Select>
        </Field>
        <Button>Submit</Button>
      </Fieldset>
    </form>
  );
}
```

----------------------------------------

TITLE: Applying bg-cover Utility for Filling Container
DESCRIPTION: This snippet demonstrates the `bg-cover` utility, which scales a background image to fill the entire background layer, potentially cropping the image. It includes both a JSX example for React components and a plain HTML example.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-size.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<div className="relative mx-auto flex w-56 items-center justify-center overflow-hidden rounded-lg sm:w-96">
      <div className="absolute inset-0">
        <Stripes border className="h-full rounded-lg" />
      </div>
      <div className="relative z-10 h-48 w-full bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=512&h=640&q=80)] bg-cover bg-center bg-no-repeat"></div>
    </div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:bg-cover] -->
<div class="bg-[url(/img/mountains.jpg)] bg-cover bg-center"></div>
```

----------------------------------------

TITLE: Extending Tailwind CSS Preflight with Custom Base Styles in CSS
DESCRIPTION: This CSS snippet shows how to add custom base styles on top of Tailwind CSS's Preflight using the `@layer base` directive. It defines default font sizes for heading elements (h1, h2, h3) and styling for anchor tags, integrating custom styles into Tailwind's layer system.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/preflight.mdx#_snippet_12

LANGUAGE: CSS
CODE:
```
@layer base {
  h1 {
    font-size: var(--text-2xl);
  }
  h2 {
    font-size: var(--text-xl);
  }
  h3 {
    font-size: var(--text-lg);
  }
  a {
    color: var(--color-blue-600);
    text-decoration-line: underline;
  }
}
```

----------------------------------------

TITLE: Styling with aria-expanded TailwindCSS Variant (CSS)
DESCRIPTION: This variant targets elements where the `aria-expanded` attribute is set to `true`, useful for styling expandable UI components like accordions or dropdowns in TailwindCSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_215

LANGUAGE: CSS
CODE:
```
&[aria-expanded="true"]
```

----------------------------------------

TITLE: Styling Headless UI Listbox Options with Tailwind CSS Data Attributes (JSX)
DESCRIPTION: This snippet demonstrates how to style Headless UI `Listbox.Option` components using the new `data-headlessui-state` attributes and the `@headlessui/tailwindcss` plugin. It applies conditional Tailwind CSS classes based on the `ui-active` and `ui-selected` states, allowing for CSS-only styling without render props. The `key` and `value` props are used for component identification and data binding, while `CheckIcon` is conditionally displayed based on selection.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2022-09-09-new-personal-website-heroicons-2-headless-ui-v17/index.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
<Listbox.Option
  key={person.id}
  value={person}
  className="ui-active:bg-blue-500 ui-active:text-white ui-not-active:bg-white ui-not-active:text-black"
>
  <CheckIcon className="ui-selected:block hidden" />
  {person.name}
</Listbox.Option>
```

----------------------------------------

TITLE: Importing Fonts and Tailwind CSS with @import
DESCRIPTION: This CSS snippet demonstrates the correct order for `@import` statements: external font imports (like Google Fonts) must precede other imports, such as `@import "tailwindcss"`. It also shows how to define a custom font variable (`--font-roboto`) within the `@theme` block after importing the font, making it available for use in Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-family.mdx#_snippet_5

LANGUAGE: CSS
CODE:
```
@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap"); /* [!code highlight] */
@import "tailwindcss";

@theme {
  --font-roboto: "Roboto", sans-serif; /* [!code highlight] */
}
```

----------------------------------------

TITLE: Configuring JIT Mode in Tailwind CSS v2.1 (JavaScript)
DESCRIPTION: This snippet demonstrates how to enable the new Just-in-Time (JIT) engine in Tailwind CSS v2.1 by setting the `mode` option to `"jit"` in your `tailwind.config.js` file. This feature is opt-in and allows for on-demand compilation of CSS utilities, improving build performance. It requires updating your `tailwind.config.js`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-1/index.mdx#_snippet_0

LANGUAGE: js
CODE:
```
module.exports = {
  mode: "jit",
  purge: [
    // ...
  ],
  // ...
};
```

----------------------------------------

TITLE: Applying Arbitrary Value for Top Position in HTML
DESCRIPTION: This HTML snippet illustrates the use of an arbitrary value to set the `top` CSS property directly within a Tailwind CSS class. The `top-[117px]` class applies a specific pixel value for positioning.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_20

LANGUAGE: HTML
CODE:
```
<div class="top-[117px]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Composing Tailwind CSS Variants in HTML
DESCRIPTION: This snippet illustrates the enhanced composability of Tailwind CSS v4 variants. It shows how `group-*` can be combined with `has-*` and `focus` to create complex, dynamic selectors like `group-has-[&:focus]:opacity-100`, demonstrating the framework's shift towards more flexible and powerful variant combinations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="group">
  <div class="group-has-[&:focus]:opacity-100">
  <div class="group-has-focus:opacity-100">
      <!-- ... -->
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Styling on Focus-Within with Tailwind CSS (HTML)
DESCRIPTION: This snippet illustrates how to apply styles to a parent element when it or any of its descendants has focus, using the `focus-within` variant. The `focus-within:shadow-lg` class adds a large shadow to the div if the nested input is focused.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_229

LANGUAGE: HTML
CODE:
```
<div class="focus-within:shadow-lg ...">
  <input type="text" />
</div>
```

----------------------------------------

TITLE: Applying `wrap-break-word` for Mid-Word Wrapping in HTML
DESCRIPTION: This snippet demonstrates how to use the `wrap-break-word` utility class in Tailwind CSS to allow long words to break and wrap onto the next line, even in the middle of a word. This is useful for preventing overflow in constrained layouts where content might otherwise exceed its container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow-wrap.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:wrap-break-word] -->
<p class="wrap-break-word">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Defining Custom Breakpoint Variant in Tailwind CSS (CSS)
DESCRIPTION: This CSS snippet demonstrates how to define a custom responsive breakpoint variant in Tailwind CSS. By setting `--breakpoint-3xl` to `120rem` within the `@theme` block, a new `3xl` variant is created, which will activate when the viewport is `120rem` or wider.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_6

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-3xl: 120rem;
}
```

----------------------------------------

TITLE: Demonstrating Logical Properties with Tailwind CSS (JSX)
DESCRIPTION: This JSX example showcases how logical properties behave in both left-to-right (LTR) and right-to-left (RTL) contexts. It uses standard `left-0` and `right-0` for demonstration, setting up two columns to visually compare positioning based on text direction, illustrating the need for logical properties like `start-0` for adaptable layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/top-right-bottom-left.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
<div className="grid grid-cols-2 place-items-center gap-x-4">
      <div className="flex flex-col items-start gap-y-4">
        <p className="text-sm font-medium">Left-to-right</p>
        <div className="relative size-18 rounded-lg sm:size-32">
          <div className="absolute inset-0">
            <Stripes border className="h-full rounded-lg" />
          </div>
          <div className="absolute top-0 left-0 flex size-14 items-center justify-center rounded-lg bg-purple-500 p-4"></div>
        </div>
      </div>
      <div className="flex flex-col items-end gap-y-4">
        <p className="text-sm font-medium">Right-to-left</p>
        <div className="relative size-18 rounded-lg sm:size-32">
          <div className="absolute inset-0">
            <Stripes border className="h-full rounded-lg" />
          </div>
          <div className="absolute top-0 right-0 flex size-14 items-center justify-center rounded-lg bg-purple-500 p-4"></div>
        </div>
      </div>
    </div>
```

----------------------------------------

TITLE: Defining Minimum Width Container Query for Extra Large Breakpoint in CSS
DESCRIPTION: This CSS snippet defines a container query that applies styles when the container's width is greater than or equal to 36rem (576px). It corresponds to the `@xl` container breakpoint in Tailwind CSS, enabling responsive design based on parent container dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_172

LANGUAGE: CSS
CODE:
```
@container (width >= 36rem)
```

----------------------------------------

TITLE: Styling Odd/Even Table Rows with Tailwind CSS in Svelte
DESCRIPTION: This Svelte snippet demonstrates how to apply `odd` and `even` background colors to table rows using Tailwind CSS classes. It iterates over a `people` array, dynamically applying `odd:bg-white`, `even:bg-gray-50`, and their dark mode equivalents to each row. This allows for visually distinguishing alternating rows in a table.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_8

LANGUAGE: Svelte
CODE:
```
<!-- [!code classes:odd:bg-white,even:bg-gray-50,dark:odd:bg-gray-900/50,dark:even:bg-gray-950] -->
<table>
  <!-- ... -->
  <tbody>
    {#each people as person}
      <!-- Use different background colors for odd and even rows -->
      <tr class="odd:bg-white even:bg-gray-50 dark:odd:bg-gray-900/50 dark:even:bg-gray-950">
        <td>{person.name}</td>
        <td>{person.title}</td>
        <td>{person.email}</td>
      </tr>
    {/each}
  </tbody>
</table>
```

----------------------------------------

TITLE: Resetting Element Margins and Paddings in Preflight CSS
DESCRIPTION: This Preflight CSS rule removes all default margins and paddings from all elements, including pseudo-elements and specific form controls. This ensures a clean slate, preventing accidental reliance on browser-default spacing and promoting consistent spacing based on Tailwind's utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/preflight.mdx#_snippet_1

LANGUAGE: CSS
CODE:
```
*,
::after,
::before,
::backdrop,
::file-selector-button {
  margin: 0;
  padding: 0;
}
```

----------------------------------------

TITLE: Applying the Hidden Utility with Tailwind CSS in HTML
DESCRIPTION: This HTML snippet shows how to use the `hidden` Tailwind CSS utility class to remove an element from the document. Applying `class="hidden"` to a `div` element effectively sets its `display` property to `none`, making it disappear from the layout and not affect other elements' positioning.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_14

LANGUAGE: html
CODE:
```
<!-- [!code classes:hidden] -->
<div class="flex ...">
  <div class="hidden ...">01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Adding Ellipsis to Overflowing Text with Tailwind CSS `text-ellipsis` Utility
DESCRIPTION: Apply the `text-ellipsis` utility to truncate overflowing text with an ellipsis (...). This utility specifically sets the `text-overflow` CSS property to `ellipsis`, requiring `overflow: hidden` to be also applied for it to take effect and display the ellipsis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-overflow.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:text-ellipsis] -->
<p class="overflow-hidden text-ellipsis">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: React Example with LTR and RTL Direction
DESCRIPTION: This React component demonstrates the use of `dir="ltr"` and `dir="rtl"` to display content in both left-to-right and right-to-left directions. It uses Tailwind CSS classes for styling, including `ms-3` for margin-inline-start, which adapts automatically in RTL layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_5

LANGUAGE: JavaScript
CODE:
```
<div className="mx-auto grid max-w-lg grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2">
  <div dir="ltr">
    <p className="mb-4 text-sm font-medium">Left-to-right</p>
    <div className="group flex items-center">
      <img
        className="h-12 w-12 shrink-0 rounded-full"
        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
        alt=""
      />
      <div className="ms-3">
        <p className="text-sm font-medium text-slate-700 group-hover:text-slate-900 dark:text-slate-300 dark:group-hover:text-white">
          <>Tom Cook</>
        </p>
        <p className="text-sm font-medium text-slate-500 group-hover:text-slate-700 dark:group-hover:text-slate-300">
          <>Director of Operations</>
        </p>
      </div>
    </div>
  </div>
  <div dir="rtl">
    <p className="mb-4 text-sm font-medium">Right-to-left</p>
    <div className="group flex items-center">
      <img
        className="h-12 w-12 shrink-0 rounded-full"
        src="https://images.unsplash.com/photo-1563833717765-00462801314e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
        alt=""
      />
      <div className="ms-3">
        <p className="text-sm font-medium text-slate-700 group-hover:text-slate-900 dark:text-slate-300 dark:group-hover:text-white">
          <>تامر كرم</>
        </p>
        <p className="text-sm font-medium text-slate-500 group-hover:text-slate-700 dark:group-hover:text-slate-300">
          <>الرئيس التنفيذي</>
        </p>
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Distributing Items with Space Between in Tailwind CSS
DESCRIPTION: This snippet showcases the `justify-between` utility in Tailwind CSS. It distributes flex items along the main axis, placing an equal amount of space between each item. The first item is aligned to the start, and the last item is aligned to the end of the container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<div class="flex justify-between ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Changing Text Color Opacity in HTML
DESCRIPTION: This HTML snippet shows how to apply text color opacity modifiers such as `/100`, `/75`, `/50`, and `/25` to Tailwind CSS color utilities, allowing fine-grained control over text transparency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<p class="text-blue-600/100 dark:text-sky-400/100">The quick brown fox...</p>
<p class="text-blue-600/75 dark:text-sky-400/75">The quick brown fox...</p>
<p class="text-blue-600/50 dark:text-sky-400/50">The quick brown fox...</p>
<p class="text-blue-600/25 dark:text-sky-400/25">The quick brown fox...</p>
```

----------------------------------------

TITLE: Tailwind CSS Basic Display Utilities Reference
DESCRIPTION: A reference table mapping basic Tailwind CSS display utility classes to their corresponding CSS `display` property values. These utilities control the fundamental box type of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_0

LANGUAGE: CSS
CODE:
```
display: inline;
```

LANGUAGE: CSS
CODE:
```
display: block;
```

LANGUAGE: CSS
CODE:
```
display: inline-block;
```

LANGUAGE: CSS
CODE:
```
display: flow-root;
```

LANGUAGE: CSS
CODE:
```
display: flex;
```

LANGUAGE: CSS
CODE:
```
display: inline-flex;
```

LANGUAGE: CSS
CODE:
```
display: grid;
```

LANGUAGE: CSS
CODE:
```
display: inline-grid;
```

LANGUAGE: CSS
CODE:
```
display: contents;
```

LANGUAGE: CSS
CODE:
```
display: table;
```

LANGUAGE: CSS
CODE:
```
display: inline-table;
```

LANGUAGE: CSS
CODE:
```
display: table-caption;
```

LANGUAGE: CSS
CODE:
```
display: table-cell;
```

LANGUAGE: CSS
CODE:
```
display: table-column;
```

LANGUAGE: CSS
CODE:
```
display: table-column-group;
```

LANGUAGE: CSS
CODE:
```
display: table-footer-group;
```

LANGUAGE: CSS
CODE:
```
display: table-header-group;
```

LANGUAGE: CSS
CODE:
```
display: table-row-group;
```

LANGUAGE: CSS
CODE:
```
display: table-row;
```

LANGUAGE: CSS
CODE:
```
display: list-item;
```

LANGUAGE: CSS
CODE:
```
display: none;
```

----------------------------------------

TITLE: Adjusting Variant Stacking Order in Tailwind CSS v4 HTML
DESCRIPTION: Tailwind CSS v4 changes variant stacking order from right-to-left to left-to-right. This HTML snippet shows how to update order-sensitive stacked variants, such as direct child (`*`) and typography plugin variants, by reversing their order to match the new left-to-right application.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_38

LANGUAGE: HTML
CODE:
```
<ul class="py-4 first:*:pt-0 last:*:pb-0">
<ul class="py-4 *:first:pt-0 *:last:pb-0">
  <li>One</li>
  <li>Two</li>
  <li>Three</li>
</ul>
```

----------------------------------------

TITLE: Applying Styles with Data Attribute Variant (Will Apply) in HTML
DESCRIPTION: This HTML snippet illustrates how Tailwind's `data-*` variant applies styles. The `p-8` utility will be applied because the `data-size` attribute on the div matches the `large` value specified in the `data-[size=large]:p-8` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_12

LANGUAGE: HTML
CODE:
```
<!-- Will apply -->
<div data-size="large" class="data-[size=large]:p-8">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Accessing Theme Variables in CSS - Generated CSS
DESCRIPTION: This snippet shows the generated CSS output from the `@theme` configuration, where all defined design tokens are automatically converted into CSS custom properties (variables) under the `:root` selector. This enables runtime access and manipulation of theme values directly in CSS or JavaScript.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_12

LANGUAGE: CSS
CODE:
```
:root {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 1920px;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Applying Arbitrary Values for Positioning in HTML
DESCRIPTION: This HTML snippet illustrates the use of Tailwind CSS's square bracket notation to apply an arbitrary `top` value. This method allows for precise, pixel-perfect positioning, similar to inline styles, while still enabling the use of Tailwind's utility class system.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="top-[117px]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using @config for Client Stylesheet (CSS)
DESCRIPTION: This CSS snippet illustrates how to use the `@config` directive to link a client-facing stylesheet to its specific Tailwind CSS configuration file. It imports Tailwind's core layers, customized by `tailwind.client.config.js`, allowing for separate styling contexts within a single project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_2

LANGUAGE: css
CODE:
```
@config "./tailwind.client.config.js";
@tailwind base;
@tailwind components;
@tailwind utilities;
```

----------------------------------------

TITLE: Applying Custom Blur Values with Tailwind CSS
DESCRIPTION: This example illustrates how to apply an arbitrary blur value to an element using Tailwind CSS's square bracket notation. This method provides precise control over the blur intensity beyond the predefined utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/filter-blur.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<img class="blur-[2px]" src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Implementing a Dialog (Modal) with Headless UI in React
DESCRIPTION: This React component demonstrates how to create a modal dialog using Headless UI's `Dialog` component. It manages the dialog's open/closed state with `useState` and includes an overlay, title, description, and action buttons. It's suitable for traditional modals or full-page take-over UIs.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useState } from "react";
import { Dialog } from "@headlessui/react";

function MyDialog() {
  let [isOpen, setIsOpen] = useState(true);

  return (
    <Dialog open={isOpen} onClose={setIsOpen}>
      <Dialog.Overlay />

      <Dialog.Title>Deactivate account</Dialog.Title>
      <Dialog.Description>This will permanently deactivate your account</Dialog.Description>

      <p>
        Are you sure you want to deactivate your account? All of your data will be permanently removed. This action
        cannot be undone.
      </p>

      <button onClick={() => setIsOpen(false)}>Deactivate</button>
      <button onClick={() => setIsOpen(false)}>Cancel</button>
    </Dialog>
  );
}
```

----------------------------------------

TITLE: Applying Basic Font Weights in JSX/React
DESCRIPTION: This JSX snippet demonstrates how to apply various Tailwind CSS font-weight utility classes (font-light, font-normal, font-medium, font-semibold, font-bold) to paragraph elements within a React component to control their visual weight. Each paragraph is accompanied by a label indicating the applied class, showcasing the effect of each utility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-weight.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
<div className="flex flex-col gap-8">
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">font-light</span>
    <p className="text-lg font-light text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">font-normal</span>
    <p className="text-lg font-normal text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">font-medium</span>
    <p className="text-lg font-medium text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">font-semibold</span>
    <p className="text-lg font-semibold text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
  <div>
    <span className="mb-3 font-mono text-xs font-medium text-gray-500 dark:text-gray-400">font-bold</span>
    <p className="text-lg font-bold text-gray-900 dark:text-gray-200">
      The quick brown fox jumps over the lazy dog.
    </p>
  </div>
</div>
```

----------------------------------------

TITLE: Preventing Flex Item Wrapping with flex-nowrap in HTML
DESCRIPTION: This snippet demonstrates the use of the `flex-nowrap` utility in Tailwind CSS to prevent flex items from wrapping within their container. When applied, items will remain on a single line, potentially overflowing if they exceed the container's width. This is useful for maintaining a strict horizontal layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-wrap.mdx#_snippet_0

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:flex-nowrap] -->
<div class="flex flex-nowrap">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Implementing Subgrid with Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet demonstrates the `grid-cols-subgrid` utility, which allows a nested grid to inherit column tracks from its parent. This is useful for maintaining consistent alignment and structure across complex grid layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-template-columns.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:grid-cols-subgrid] -->
<div class="grid grid-cols-4 gap-4">
  <div>01</div>
  <!-- ... -->
  <div>05</div>
  <div class="col-span-3 grid grid-cols-subgrid gap-4">
    <div class="col-start-2">06</div>
  </div>
</div>
```

----------------------------------------

TITLE: Centering Items with `items-center` in Tailwind CSS (HTML/JSX)
DESCRIPTION: This snippet demonstrates how to use the `items-center` utility in Tailwind CSS to vertically center flex items along the container's cross axis. It includes both a React JSX example and a plain HTML example for implementation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
<div className="grid grid-cols-1">
  <Stripes border className="col-start-1 row-start-1 rounded-lg" />
  <div className="col-start-1 row-start-1 flex w-full items-center gap-4 rounded-lg text-center font-mono text-sm leading-6 font-bold text-white">
    <div className="flex flex-1 items-center justify-center rounded-lg bg-violet-500 py-4">01</div>
    <div className="flex flex-1 items-center justify-center rounded-lg bg-violet-500 py-12">02</div>
    <div className="flex flex-1 items-center justify-center rounded-lg bg-violet-500 py-8">03</div>
  </div>
</div>
```

LANGUAGE: html
CODE:
```
<!-- [!code classes:items-center] -->
<div class="flex items-center ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Combining Reduced Motion Variants with Responsive and Pseudo-Class Variants in HTML
DESCRIPTION: This HTML snippet showcases the flexibility of combining `motion-reduce` variants with other Tailwind CSS variants like responsive (`sm:`) and pseudo-class (`hover:`). It demonstrates how to apply conditional styles based on user motion preference, screen size, and interaction states, allowing for fine-grained control over UI behavior and accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- With responsive variants -->
<div class="sm:motion-reduce:translate-y-0"></div>

<!-- With pseudo-class variants -->
<div class="motion-reduce:hover:translate-y-0"></div>

<!-- With responsive and pseudo-class variants -->
<div class="sm:motion-reduce:hover:translate-y-0"></div>
```

----------------------------------------

TITLE: Applying Arbitrary Box Shadow Value - Tailwind CSS
DESCRIPTION: Applies an arbitrary box shadow value directly using bracket notation, enabling the use of any valid CSS `box-shadow` value not predefined by Tailwind's scale.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#_snippet_20

LANGUAGE: CSS
CODE:
```
box-shadow: <value>;
```

----------------------------------------

TITLE: Applying Custom Breakpoints in Tailwind CSS HTML
DESCRIPTION: This HTML snippet illustrates how to apply the previously defined custom breakpoints (`xs` and `3xl`) to an element using Tailwind CSS utility classes. It shows how `xs:grid-cols-2` and `3xl:grid-cols-6` modify the grid layout based on the custom breakpoint sizes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_10

LANGUAGE: html
CODE:
```
<div class="grid xs:grid-cols-2 3xl:grid-cols-6">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Sticky Headers with Tailwind CSS in HTML
DESCRIPTION: This HTML snippet demonstrates how to create sticky headers within a scrollable container using Tailwind CSS's `sticky` utility. Elements with `sticky` and `top-0` will remain at the top of their parent container when scrolled past, until the parent itself scrolls out of view. This is commonly used for navigation bars or section headers in a list.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<div className="px-3">\n  <div className="relative mx-auto -my-px h-80 max-w-md overflow-auto bg-white shadow-lg ring-1 ring-gray-900/5 dark:bg-gray-800">\n    <div className="relative">\n      <div className="sticky top-0 flex items-center bg-gray-50/90 px-4 py-3 text-sm font-semibold text-gray-900 ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-700/90 dark:text-gray-200 dark:ring-black/10">\n        A\n      </div>\n      <div className="divide-y divide-gray-200 dark:divide-gray-200/5">\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1501196354995-cbb51c65aaea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Andrew Alfred</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Aisha Houston</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Anna White</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Andy Flint</strong>\n        </div>\n      </div>\n    </div>\n    <div className="relative">\n      <div className="sticky top-0 flex items-center bg-gray-50/90 px-4 py-3 text-sm font-semibold text-gray-900 ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-700/90 dark:text-gray-200 dark:ring-black/10">\n        B\n      </div>\n      <div className="divide-y divide-gray-200 dark:divide-gray-200/5">\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1501196354995-cbb51c65aaea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Bob Alfred</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Bianca Houston</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Brianna White</strong>\n        </div>\n        <div className="flex items-center gap-4 p-4">\n          <img\n            className="size-12 rounded-full"\n            src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=4&w=256&h=256&q=80"\n          />\n          <strong className="text-sm font-medium text-gray-900 dark:text-gray-200">Bert Flint</strong>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>
```

----------------------------------------

TITLE: Applying Tailwind CSS Size Utilities (HTML)
DESCRIPTION: This HTML snippet illustrates the direct application of Tailwind CSS `size` utilities (e.g., `size-16`, `size-20`) to `div` elements. These utilities set both the width and height of the element, providing a concise way to control dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#_snippet_7

LANGUAGE: html
CODE:
```
<div class="size-16 ...">size-16</div>
<div class="size-20 ...">size-20</div>
<div class="size-24 ...">size-24</div>
<div class="size-32 ...">size-32</div>
<div class="size-40 ...">size-40</div>
```

----------------------------------------

TITLE: Applying Basic Border Radius Utilities in HTML
DESCRIPTION: This HTML snippet demonstrates the application of basic Tailwind CSS border-radius utility classes such as `rounded-sm`, `rounded-md`, `rounded-lg`, and `rounded-xl` to `div` elements. Each class applies a predefined border radius size, visually showcasing the effect.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:rounded-sm,rounded-md,rounded-lg,rounded-xl] -->
<div class="rounded-sm ..."></div>
<div class="rounded-md ..."></div>
<div class="rounded-lg ..."></div>
<div class="rounded-xl ..."></div>
```

----------------------------------------

TITLE: Applying Percentage Height Utilities - Tailwind CSS HTML
DESCRIPTION: Illustrates the use of `h-full` and `h-<fraction>` utilities (like `h-9/10`, `h-3/4`, `h-1/2`, `h-1/3`) to set element heights relative to their parent container as a percentage.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-9/10,h-3/4,h-1/2,h-1/3,h-full] -->
<div class="h-full ...">h-full</div>
<div class="h-9/10 ...">h-9/10</div>
<div class="h-3/4 ...">h-3/4</div>
<div class="h-1/2 ...">h-1/2</div>
<div class="h-1/3 ...">h-1/3</div>
```

----------------------------------------

TITLE: Generating an ESM Config File
DESCRIPTION: This shell command generates an ES Module (ESM) config file for Tailwind CSS. The `--esm` flag tells the `tailwindcss init` command to create the configuration file using ES module syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_3

LANGUAGE: Shell
CODE:
```
npx tailwindcss init --esm
```

----------------------------------------

TITLE: Unstyling Heading Elements in Preflight CSS
DESCRIPTION: This Preflight CSS rule unstyles all heading elements (`h1` through `h6`), setting their `font-size` and `font-weight` to `inherit`. This approach prevents accidental deviation from the defined type scale and encourages conscious styling of headings using Tailwind's utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/preflight.mdx#_snippet_4

LANGUAGE: CSS
CODE:
```
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
```

----------------------------------------

TITLE: Sorting Responsive Modifiers by Breakpoint in HTML
DESCRIPTION: This snippet illustrates how responsive modifiers (e.g., `sm:`, `md:`, `lg:`) are sorted. They are grouped at the end of the class list and ordered according to their configured breakpoints, typically from smallest to largest (e.g., `grid-cols-2`, `sm:grid-cols-3`, `lg:grid-cols-4`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/automatic-class-sorting-with-prettier/index.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<div class="lg:grid-cols-4 grid sm:grid-cols-3 grid-cols-2"> <!-- [!code --] -->
<div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4"> <!-- [!code ++] -->
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Specifying Grid Rows with Tailwind CSS HTML
DESCRIPTION: This snippet demonstrates how to define a grid with a specific number of equally sized rows using Tailwind CSS. The `grid-rows-4` utility creates a grid with four rows, each taking up an equal amount of space. This is useful for structuring content into a fixed-row layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-template-rows.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:grid-rows-4] -->
<div class="grid grid-flow-col grid-rows-4 gap-4">
  <div>01</div>
  <!-- ... -->
  <div>09</div>
</div>
```

----------------------------------------

TITLE: Rendering Calendar Event Entries with Tailwind CSS in JSX
DESCRIPTION: This snippet defines a `div` component used to display individual calendar events. It leverages Tailwind CSS for grid placement (`col-start`, `row-span`, `row-start`), margin, flexbox layout (`flex flex-col`), rounded corners, borders, background colors, and dark mode styling. Nested `span` elements format the event time, title, and location.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#_snippet_19

LANGUAGE: JSX
CODE:
```
<div className="col-start-3 row-span-4 row-start-2 m-1 flex flex-col rounded-lg border border-blue-700/10 bg-blue-400/20 p-1 dark:border-sky-500 dark:bg-sky-600/50">\n          <span className="text-xs text-blue-600 dark:text-sky-100">5 AM</span>\n          <span className="text-xs font-medium text-blue-600 dark:text-sky-100">Flight to Vancouver</span>\n          <span className="text-xs text-blue-600 dark:text-sky-100">Toronto YYZ</span>\n        </div>
```

----------------------------------------

TITLE: Redefining All Breakpoints in Tailwind CSS
DESCRIPTION: This CSS snippet shows how to remove all default Tailwind CSS breakpoints by setting `--breakpoint-*` to `initial`, and then define an entirely new set of custom breakpoints. This allows for a complete overhaul of the responsive design system.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_12

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-*: initial;
  --breakpoint-tablet: 40rem;
  --breakpoint-laptop: 64rem;
  --breakpoint-desktop: 80rem;
}
```

----------------------------------------

TITLE: Installing Tailwind CSS v4.1 with CLI via npm
DESCRIPTION: This shell command installs the latest versions of `tailwindcss` and `@tailwindcss/cli` using npm. This setup is suitable for projects where Tailwind CSS is managed directly via its command-line interface, allowing for standalone compilation and other CLI-specific features.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#_snippet_30

LANGUAGE: Shell
CODE:
```
npm install tailwindcss@latest @tailwindcss/cli@latest
```

----------------------------------------

TITLE: Implementing Tabs with Headless UI in Vue
DESCRIPTION: This snippet illustrates how to implement a tabbed interface in Vue using Headless UI components. It includes both the template structure for `TabGroup`, `TabList`, `Tab`, `TabPanels`, and `TabPanel`, and the script section for importing and registering these components within a Vue component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-4/index.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<template>
  <TabGroup>
    <TabList>
      <Tab>Tab 1</Tab>
      <Tab>Tab 2</Tab>
      <Tab>Tab 3</Tab>
    </TabList>
    <TabPanels>
      <TabPanel>Content 1</TabPanel>
      <TabPanel>Content 2</TabPanel>
      <TabPanel>Content 3</TabPanel>
    </TabPanels>
  </TabGroup>
</template>

<script>
  import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'

  export default {
    components: {
      TabGroup,
      TabList,
      Tab,
      TabPanels,
      TabPanel,
    },
  }
</script>
```

----------------------------------------

TITLE: Applying Styles Based on Specific Data Attribute Value in HTML
DESCRIPTION: This example illustrates how to apply Tailwind CSS styles when a `data-*` attribute has a specific value. Using arbitrary values like `data-[size=large]:p-8` ensures that the padding style is applied only if the `data-size` attribute's value is exactly 'large'.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_80

LANGUAGE: HTML
CODE:
```
<!-- Will apply -->
<div data-size="large" class="data-[size=large]:p-8">
  <!-- ... -->
</div>

<!-- Will not apply -->
<div data-size="medium" class="data-[size=large]:p-8">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Customizing Breakpoints in Tailwind CSS
DESCRIPTION: This CSS snippet demonstrates how to customize default Tailwind CSS breakpoints and define new ones using `--breakpoint-*` CSS variables within the `@theme` block. It updates the `2xl` breakpoint and introduces `xs` and `3xl` breakpoints, allowing for more granular control over responsive design.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_9

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-xs: 30rem;
  --breakpoint-2xl: 100rem;
  --breakpoint-3xl: 120rem;
}
```

----------------------------------------

TITLE: Applying Tailwind CSS State Variants to Input (HTML)
DESCRIPTION: This HTML snippet showcases the direct application of Tailwind CSS utility classes with pseudo-class variants (`invalid`, `focus`, `disabled`, `dark:disabled`) to an `<input>` element. It highlights how these variants automatically apply styles based on the input's state, simplifying template logic and reducing the need for manual conditional styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_11

LANGUAGE: HTML
CODE:
```
<input
  type="text"
  value="tbone"
  disabled
  class="invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20 ..."
/>
```

----------------------------------------

TITLE: Creating Block-Level Flex Containers with Tailwind CSS
DESCRIPTION: Illustrates the `flex` utility for creating a block-level flex container. This enables the use of flexbox properties to control the layout and alignment of its direct children.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:flex] -->
<div class="flex items-center">
  <img src="path/to/image.jpg" />
  <div>
    <strong>Andrew Alfred</strong>
    <span>Technical advisor</span>
  </div>
</div>
```

----------------------------------------

TITLE: Conditional Styling with supports-[display:grid] (HTML)
DESCRIPTION: This HTML snippet demonstrates using the `supports-[...]` variant to apply `grid` display only if the browser supports CSS Grid. The `flex` class is applied by default, providing a fallback for browsers without grid support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex supports-[display:grid]:grid ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Configuring Tailwind CSS with TypeScript Types
DESCRIPTION: This JavaScript configuration file for Tailwind CSS includes a JSDoc type annotation to enable first-party TypeScript type checking. This provides IDE support for the configuration object, making it easier to define content paths, extend themes, and add plugins with autocompletion and validation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_1

LANGUAGE: js
CODE:
```
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // ...
  ],
  theme: {
    extend: {},
  },
  plugins: []
};
```

----------------------------------------

TITLE: Defining Minimum Width Container Query for 5XL Breakpoint in CSS
DESCRIPTION: This CSS snippet defines a container query that applies styles when the container's width is greater than or equal to 64rem (1024px). It corresponds to the `@5xl` container breakpoint in Tailwind CSS, enabling responsive design based on parent container dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_176

LANGUAGE: CSS
CODE:
```
@container (width >= 64rem)
```

----------------------------------------

TITLE: Default Tailwind CSS Import Structure in CSS
DESCRIPTION: This CSS snippet illustrates the default import structure for Tailwind CSS, showing how different parts of the framework are imported into specific CSS layers. It defines the `theme`, `base` (Preflight), `components`, and `utilities` layers and imports their respective CSS files, providing the full set of Tailwind's default styles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/preflight.mdx#_snippet_13

LANGUAGE: CSS
CODE:
```
@layer theme, base, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
```

----------------------------------------

TITLE: Applying Arbitrary CSS Properties with Modifiers in HTML
DESCRIPTION: This HTML snippet shows how to combine arbitrary CSS properties with interactive modifiers in Tailwind CSS. It applies a default `mask-type` and changes it on hover, highlighting the ability to control custom CSS properties based on element states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<div class="[mask-type:luminance] hover:[mask-type:alpha]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Justifying Items to the End with Tailwind CSS
DESCRIPTION: This snippet demonstrates the `justify-end` utility in Tailwind CSS, which aligns flex items to the end of the container's main axis. It ensures that content is pushed towards the right (for a row-direction flex container) or bottom (for a column-direction flex container).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex justify-end ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Installing Tailwind CSS v4 Alpha with Vite
DESCRIPTION: This command installs the alpha version of Tailwind CSS v4 and its new Vite plugin. It's the first step to integrate the new engine into a Vite-based project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_11

LANGUAGE: Shell
CODE:
```
npm install tailwindcss@next @tailwindcss/vite@next
```

----------------------------------------

TITLE: Replacing Deprecated bg-opacity-* with Tailwind CSS Opacity Modifiers
DESCRIPTION: This snippet demonstrates replacing the deprecated `bg-opacity-*` utility with the modern Tailwind CSS opacity modifier syntax, such as `bg-black/50`, for controlling background color opacity.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_5

LANGUAGE: Tailwind CSS
CODE:
```
bg-opacity-*
```

LANGUAGE: Tailwind CSS
CODE:
```
bg-black/50
```

----------------------------------------

TITLE: Applying Prefers-Contrast Variants in HTML with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use Tailwind CSS's `contrast-more` and `contrast-less` variants to apply conditional styling based on the user's `prefers-contrast` operating system setting. It shows how to modify border color, placeholder color, and opacity for improved accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_17

LANGUAGE: html
CODE:
```
<form>
  <label class="block">
    <span class="block text-sm font-medium text-slate-700">Social Security Number</span>
    <!-- [!code word:contrast-more\:border-slate-400] -->
    <!-- [!code word:contrast-more\:placeholder-slate-500] -->
    <!-- [!code word:contrast-more\:opacity-100] -->
    <input
      class="border-slate-200 placeholder-slate-400 contrast-more:border-slate-400 contrast-more:placeholder-slate-500"
    />
    <p class="mt-2 text-sm text-slate-600 opacity-10 contrast-more:opacity-100">We need this to steal your identity.</p>
  </label>
</form>
```

----------------------------------------

TITLE: Compiled CSS Output of Tailwind Theme Variables
DESCRIPTION: This CSS snippet displays the compiled output of Tailwind CSS theme variables, showing how they are transformed into standard CSS custom properties within the `:root` selector. This makes the design tokens easily accessible for use in custom CSS or inline styles throughout the project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_23

LANGUAGE: css
CODE:
```
:root {
  /* prettier-ignore */
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  /* ... */

  --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  /* ... */
}
```

----------------------------------------

TITLE: Applying `forced-colors` Variant to Theme Selector in JSX
DESCRIPTION: This JSX snippet demonstrates how to build a theme selection form that adapts to `forced-colors` mode using Tailwind CSS. It applies `forced-colors:border-0` to remove borders and `forced-colors:appearance-auto` to restore default radio button appearance, ensuring accessibility and proper rendering when forced colors are active. Hidden `p` tags become visible in forced color mode to display theme names.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_56

LANGUAGE: jsx
CODE:
```
<div className="mx-auto max-w-sm border-x border-x-gray-200 px-6 pt-6 pb-4 text-gray-900 dark:border-x-gray-800 dark:bg-gray-950/10 dark:text-white">
  <form>
    <legend> Choose a theme: </legend>
    <div className="mt-4 grid grid-flow-col">
      <label htmlFor="theme-1" className="text-sm font-medium text-gray-700 dark:text-white">
        <div className="relative grid h-16 w-16 items-center justify-center rounded-xl border border-transparent bg-transparent text-white hover:bg-gray-50 has-checked:border-cyan-500 has-checked:bg-cyan-50 has-checked:text-cyan-50 dark:text-gray-800 dark:hover:bg-gray-800 dark:has-checked:bg-cyan-950 dark:has-checked:text-cyan-950 forced-colors:border-0">
          <input
            type="radio"
            name="themes"
            id="theme-1"
            className="appearance-none forced-colors:appearance-auto"
            defaultChecked
          />
          <p className="hidden forced-colors:block">Cyan</p>
          <div className="absolute top-3 left-3 h-6 w-6 rounded-full bg-cyan-200 forced-colors:hidden"></div>
          <div className="absolute right-3 bottom-3 h-6 w-6 rounded-full bg-cyan-500 ring-2 ring-current forced-colors:hidden"></div>
        </div>
      </label>
      <label htmlFor="theme-2" className="text-sm font-medium text-gray-700 dark:text-white">
        <div className="relative grid h-16 w-16 items-center justify-center rounded-xl border border-transparent bg-transparent text-white hover:bg-gray-50 has-checked:border-blue-500 has-checked:bg-blue-50 has-checked:text-blue-50 dark:text-gray-800 dark:hover:bg-gray-800 dark:has-checked:bg-blue-950 dark:has-checked:text-blue-950 forced-colors:border-0">
          <input
            type="radio"
            name="themes"
            id="theme-2"
            className="appearance-none forced-colors:appearance-auto"
          />
          <p className="hidden forced-colors:block">Blue</p>
          <div className="absolute top-3 left-3 h-6 w-6 rounded-full bg-blue-200 forced-colors:hidden"></div>
          <div className="absolute right-3 bottom-3 h-6 w-6 rounded-full bg-blue-500 ring-2 ring-current forced-colors:hidden"></div>
        </div>
      </label>
      <label htmlFor="theme-3" className="text-sm font-medium text-gray-700 dark:text-white">
        <div className="relative grid h-16 w-16 items-center justify-center rounded-xl border border-transparent bg-transparent text-white hover:bg-gray-50 has-checked:border-indigo-500 has-checked:bg-indigo-50 has-checked:text-indigo-50 dark:text-gray-800 dark:hover:bg-gray-800 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-950 forced-colors:border-0">
          <input
            type="radio"
            name="themes"
            id="theme-3"
            className="appearance-none forced-colors:appearance-auto"
          />
          <p className="hidden forced-colors:block">Indigo</p>
          <div className="absolute top-3 left-3 h-6 w-6 rounded-full bg-indigo-200 forced-colors:hidden"></div>
          <div className="absolute right-3 bottom-3 h-6 w-6 rounded-full bg-indigo-500 ring-2 ring-current forced-colors:hidden"></div>
        </div>
      </label>
      <label htmlFor="theme-4" className="text-sm font-medium text-gray-700 dark:text-white">
        <div className="relative grid h-16 w-16 items-center justify-center rounded-xl border border-transparent bg-transparent text-white hover:bg-gray-50 has-checked:border-purple-500 has-checked:bg-purple-50 has-checked:text-purple-50 dark:text-gray-800 dark:hover:bg-gray-800 dark:has-checked:bg-purple-950 dark:has-checked:text-purple-950 forced-colors:border-0">
          <input
            type="radio"
            name="themes"
            id="theme-4"
            className="appearance-none forced-colors:appearance-auto"
          />
          <p className="hidden forced-colors:block">Purple</p>
          <div className="absolute top-3 left-3 h-6 w-6 rounded-full bg-purple-200 forced-colors:hidden"></div>
          <div className="absolute right-3 bottom-3 h-6 w-6 rounded-full bg-purple-500 ring-2 ring-current forced-colors:hidden"></div>
        </div>
      </label>
    </div>
  </form>
</div>
```

----------------------------------------

TITLE: Using Tailwind CSS Standalone CLI Commands
DESCRIPTION: This section provides examples of common commands for interacting with the standalone Tailwind CSS CLI. It covers initializing a configuration file, starting a watcher for development, and compiling/minifying CSS for production, mirroring the functionality of the npm-distributed CLI.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/standalone-cli/index.mdx#_snippet_1

LANGUAGE: sh
CODE:
```
# Create a tailwind.config.js file
./tailwindcss init

# Start a watcher
./tailwindcss -i input.css -o output.css --watch

# Compile and minify your CSS for production
./tailwindcss -i input.css -o output.css --minify
```

----------------------------------------

TITLE: Applying Base Styles to HTML Elements with Tailwind Classes
DESCRIPTION: Demonstrates how to apply default base styles, such as background color, font family, and text color, directly to the html or body elements using Tailwind CSS utility classes for consistent page-level styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_17

LANGUAGE: HTML
CODE:
```
<!doctype html>
<html lang="en" class="bg-gray-100 font-serif text-gray-900">
  <!-- ... -->
</html>
```

----------------------------------------

TITLE: Migrating Deprecated Gap Utilities in HTML
DESCRIPTION: This HTML snippet illustrates the migration from deprecated `col-gap-{n}` and `row-gap-{n}` utilities to their new equivalents, `gap-x-{n}` and `gap-y-{n}`. The `<!-- [!code --] -->` and `<!-- [!code ++] -->` comments indicate the old and new class names respectively, showing a direct replacement.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-7/index.mdx#_snippet_14

LANGUAGE: html
CODE:
```
<div class="col-gap-4 row-gap-2 ..."> <!-- [!code --] -->
<div class="gap-x-4 gap-y-2 ..."> <!-- [!code ++] -->
</div>
```

----------------------------------------

TITLE: Hiding an Element with Tailwind CSS in React
DESCRIPTION: This React snippet demonstrates the use of the `hidden` Tailwind CSS utility class to completely remove an element from the document flow. The `hidden` class sets `display: none;`, making the element invisible and not occupying any space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#_snippet_13

LANGUAGE: jsx
CODE:
```
<div className="relative flex gap-4 rounded-lg text-center font-mono text-sm leading-6 font-bold text-white">
  <div className="absolute inset-0">
    <Stripes border className="h-full rounded-lg" />
  </div>
  <div className="hidden h-14 w-14 rounded-lg bg-purple-500 p-4">01</div>
  <div className="relative h-14 w-14 rounded-lg bg-purple-500 p-4">02</div>
  <div className="relative h-14 w-14 rounded-lg bg-purple-500 p-4">03</div>
</div>
```

----------------------------------------

TITLE: Defining a Functional Custom Utility in CSS
DESCRIPTION: This CSS snippet defines a functional custom utility `tab-*` that accepts an argument using the `@utility` directive. It uses the special `--value()` function to resolve the `tab-size` property based on a dynamic value, enabling utilities like `tab-2` or `tab-4`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_30

LANGUAGE: CSS
CODE:
```
@utility tab-* {
  /* prettier-ignore */
  tab-size: --value(--tab-size-*);
}
```

----------------------------------------

TITLE: Applying box-border Utility in HTML
DESCRIPTION: Demonstrates the use of the `box-border` utility in HTML to set an element's `box-sizing` to `border-box`. This ensures that an element's specified width and height include its padding and border, affecting the internal content area. It illustrates how a 128px x 128px element with padding and border maintains its declared dimensions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-sizing.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:box-border] -->
<div class="box-border size-32 border-4 p-4 ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Diverse Arbitrary Values in HTML
DESCRIPTION: This HTML snippet shows the versatility of Tailwind CSS's arbitrary value notation across different CSS properties. It applies a custom background color, font size, and pseudo-element content, demonstrating how to break out of predefined design tokens for specific styling needs.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<div class="bg-[#bada55] text-[22px] before:content-['Festivus']">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Nested Group Variants with Modifiers in HTML
DESCRIPTION: This HTML snippet demonstrates the use of variant modifiers for nested `group` elements, such as `group/sidebar` and `group/navitem`. This allows for disambiguation between multiple nested groups, enabling specific hover effects like `group-hover/navitem:bg-black/75` and `group-hover/sidebar:opacity-75`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#_snippet_31

LANGUAGE: HTML
CODE:
```
<div class="group/sidebar ...">
  <!-- ... -->
  <div class="group/navitem ...">
    <a href="#" class="opacity-50 group-hover/navitem:bg-black/75 group-hover/sidebar:opacity-75">
      <!-- ... -->
    </a>
  </div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Centering and Padding Tailwind Containers
DESCRIPTION: This HTML snippet demonstrates how to properly center a Tailwind CSS container and add horizontal padding. It uses the `mx-auto` utility for automatic horizontal margins and `px-4` for padding, addressing the default behavior of Tailwind's `container` utility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#_snippet_36

LANGUAGE: html
CODE:
```
<!-- [!code classes:mx-auto,px-4] -->
<div class="container mx-auto px-4">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Initializing Tailwind CSS Configuration in TypeScript
DESCRIPTION: This TypeScript code initializes the Tailwind CSS configuration with type checking. It imports the `Config` type from 'tailwindcss' and uses it to define the configuration object. The `satisfies Config` assertion ensures that the configuration object conforms to the expected type.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import type { Config } from 'tailwindcss'

export default {
  content: [],
  theme: {
    extend: {},
  },
  plugins: [],
} satisfies Config
```

----------------------------------------

TITLE: Allowing Flex Items to Grow with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use the `grow` utility class in Tailwind CSS to make a flex item expand and fill any available space within its flex container. The item with `grow` will take up the remaining space after other items have been laid out.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-grow.mdx#_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:grow] -->
<div class="flex ...">
  <div class="size-14 flex-none ...">01</div>
  <div class="size-14 grow ...">02</div>
  <div class="size-14 flex-none ...">03</div>
</div>
```

----------------------------------------

TITLE: Setting Font Size and Line Height with Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet illustrates the direct application of Tailwind CSS utility classes such as `text-sm/6`, `text-sm/7`, and `text-sm/8` to paragraph elements. These classes combine font size (`text-sm`) with specific line-height values, providing a concise way to control text appearance in standard HTML.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:text-sm/6,text-sm/7,text-sm/8] -->
<p class="text-sm/6 ...">So I started to walk into the water...</p>
<p class="text-sm/7 ...">So I started to walk into the water...</p>
<p class="text-sm/8 ...">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Applying Forced Colors Styles with Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet demonstrates how to use the `forced-colors` variant in Tailwind CSS to conditionally apply styles. It shows how to adjust element appearance (`appearance-auto`), toggle visibility (`block`, `hidden`), and change background colors based on whether the user's system is in a forced colors mode.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_58

LANGUAGE: HTML
CODE:
```
<label>
  <input type="radio" class="appearance-none forced-colors:appearance-auto" />
  <p class="hidden forced-colors:block">Cyan</p>
  <div class="bg-cyan-200 forced-colors:hidden ..."></div>
  <div class="bg-cyan-500 forced-colors:hidden ..."></div>
</label>
```

----------------------------------------

TITLE: Defining Container Query for @max-3xl (Width < 48rem) in CSS
DESCRIPTION: This snippet defines a container query associated with the @max-3xl breakpoint, applying styles when the container's width is less than 48 rem. It's used for responsive design based on the parent container's size rather than the viewport.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_188

LANGUAGE: CSS
CODE:
```
@container (width < 48rem)
```

----------------------------------------

TITLE: Demonstrating `peer` Limitation: Styling Previous Siblings in HTML
DESCRIPTION: This HTML snippet illustrates a common pitfall: the `peer` class and its variants can only target *subsequent* siblings in the DOM. Here, `peer` is on the `input`, but `peer-invalid:text-red-500` is on a *previous* sibling `span`. This configuration will not work as intended because CSS's subsequent-sibling combinator does not allow styling of preceding elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_32

LANGUAGE: html
CODE:
```
<!-- [!code classes:peer-invalid:text-red-500] -->
<!-- [!code classes:peer] -->
<label>
  <span class="peer-invalid:text-red-500 ...">Email</span>
  <input type="email" class="peer ..." />
</label>
```

----------------------------------------

TITLE: Basic Text Color Usage in React/JSX
DESCRIPTION: This React/JSX snippet demonstrates applying basic text color utilities like `text-blue-600` and `dark:text-sky-400` to a paragraph element, showcasing how to set text color for different themes within a React component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<div className="relative text-center text-xl leading-6 font-medium">
  <p className="text-blue-600 dark:text-sky-400">The quick brown fox jumps over the lazy dog.</p>
</div>
```

----------------------------------------

TITLE: Upgrading Tailwind CSS using npm (Bash)
DESCRIPTION: Provides the standard command-line instruction to upgrade Tailwind CSS to the latest version using the npm package manager. The `-D` flag installs it as a development dependency, which is typical for Tailwind CSS projects.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-2/index.mdx#_snippet_12

LANGUAGE: bash
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Implementing Max-Width Container Queries with Tailwind CSS
DESCRIPTION: This example illustrates the use of the new `@max-*` variant for max-width container queries in Tailwind CSS v4.0. The `@container` class is applied to the parent, and `@max-md:grid-cols-1` changes the grid layout when the container's width is below the 'md' breakpoint.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#_snippet_17

LANGUAGE: HTML
CODE:
```
<div class="@container">
  <div class="grid grid-cols-3 @max-md:grid-cols-1">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Adjusting Input Contrast with `contrast-more` in HTML
DESCRIPTION: This HTML snippet demonstrates applying `contrast-more` variants to an input field and a paragraph. Classes like `contrast-more:border-gray-400`, `contrast-more:placeholder-gray-500`, and `contrast-more:opacity-100` are used to increase the visibility of borders, placeholders, and text, respectively, when the user's system prefers higher contrast.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_55

LANGUAGE: html
CODE:
```
<!-- [!code classes:contrast-more:border-gray-400,contrast-more:placeholder-gray-500,contrast-more:opacity-100] -->
<label class="block">
  <span class="block text-sm font-medium text-gray-700">Social Security Number</span>
  <input
    class="border-gray-200 placeholder-gray-400 contrast-more:border-gray-400 contrast-more:placeholder-gray-500 ..."
  />
  <p class="text-gray-600 opacity-10 contrast-more:opacity-100 ...">We need this to steal your identity.</p>
</label>
```

----------------------------------------

TITLE: Forced Colors Media Query (CSS)
DESCRIPTION: This CSS media query applies styles when the user agent has an active forced colors mode, typically used for high-contrast themes to enhance accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_199

LANGUAGE: CSS
CODE:
```
@media (forced-colors: active)
```

----------------------------------------

TITLE: Replacing Deprecated flex-grow-* with Tailwind CSS grow-*
DESCRIPTION: This snippet demonstrates replacing the deprecated `flex-grow-*` utility with the modern `grow-*` utility in Tailwind CSS for controlling flex item growing behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_12

LANGUAGE: Tailwind CSS
CODE:
```
flex-grow-*
```

LANGUAGE: Tailwind CSS
CODE:
```
grow-*
```

----------------------------------------

TITLE: Styling Based on Parent State (group-hover)
DESCRIPTION: Illustrates how to use the `group-hover` variant to style a child element when a specific parent element is hovered. The parent needs the `group` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_22

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:group,group-hover:underline] -->
<a href="#" class="group rounded-lg p-8">
  <!-- ... -->
  <span class="group-hover:underline">Read more…</span>
</a>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
@media (hover: hover) {
  a:hover span {
    text-decoration-line: underline;
  }
}
```

----------------------------------------

TITLE: Implementing Virtualized Combobox React
DESCRIPTION: Demonstrates how to use Headless UI's Combobox component with list virtualization enabled via the `virtual` prop, suitable for handling large datasets efficiently. It filters a list of 'people' based on user input and renders options using a render prop function.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_6

LANGUAGE: JSX
CODE:
```
import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import { useState } from "react";

const people = [
  { id: 1, name: "Rossie Abernathy" },
  { id: 2, name: "Juana Abshire" },
  { id: 3, name: "Leonel Abshire" },
  { id: 4, name: "Llewellyn Abshire" },
  { id: 5, name: "Ramon Abshire" },
  // ...up to 1000 people
];

function Example() {
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState(people[0]);

  const filteredPeople =
    query === ""
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  return (
    <Combobox
      value={selected}
      // [!code highlight:2]
      virtual={{ options: filteredPeople }}
      onChange={(value) => setSelected(value)}
      onClose={() => setQuery("")}
    >
      <div>
        <ComboboxInput displayValue={(person) => person?.name} onChange={(event) => setQuery(event.target.value)} />
        <ComboboxButton>
          <ChevronDownIcon />
        </ComboboxButton>
      </div>
      <ComboboxOptions>
        // [!code highlight:6]
        {({ option: person }) => (
          <ComboboxOption key={person.id} value={person}>
            {person.name}
          </ComboboxOption>
        )}
      </ComboboxOptions>
    </Combobox>
  );
}
```

----------------------------------------

TITLE: Applying Responsive Columns in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to create a responsive multi-column layout using Tailwind CSS utility classes. It shows how to define a default 2-column layout with a 4-unit gap, which then adapts to a 3-column layout with an 8-unit gap on small (sm) screens, illustrating the mobile-first approach of Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/columns.mdx#_snippet_9

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:sm:gap-8,sm:columns-3] -->\n<div class="columns-2 gap-4 sm:columns-3 sm:gap-8 ...">\n  <img class="aspect-3/2 ..." src="/img/mountains-1.jpg" />\n  <img class="aspect-square ..." src="/img/mountains-2.jpg" />\n  <img class="aspect-square ..." src="/img/mountains-3.jpg" />\n  <!-- ... -->\n</div>
```

----------------------------------------

TITLE: Applying Arbitrary Variant with @supports Query in HTML
DESCRIPTION: This HTML snippet shows how to use an arbitrary variant based on a CSS `@supports` query. It applies `bg-white/50` and `backdrop-blur` classes only if the browser supports `backdrop-filter: blur(0)`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#_snippet_22

LANGUAGE: HTML
CODE:
```
<div
  class="bg-white [@supports(backdrop-filter:blur(0))]:bg-white/50 [@supports(backdrop-filter:blur(0))]:backdrop-blur"
>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Outline on Focus State with Tailwind CSS HTML
DESCRIPTION: This example illustrates how to conditionally apply an outline to an element when it receives focus. The `focus:outline-2` utility class is used to set a 2px outline width specifically on the focus state, enhancing accessibility and user feedback.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/outline-width.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:focus:outline-2] -->
<button class="outline-offset-2 outline-sky-500 focus:outline-2 ...">Save Changes</button>
```

----------------------------------------

TITLE: Comparing `wrap-break-word` and `wrap-anywhere` in Flex Containers (HTML)
DESCRIPTION: This example illustrates the difference between `wrap-break-word` and `wrap-anywhere` when used within a flex container. `wrap-anywhere` allows mid-word breaks and influences the element's intrinsic size, making it suitable for scenarios where content needs to shrink below its natural size, such as in flex layouts without explicitly setting `min-width: 0` on the child element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow-wrap.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:wrap-anywhere,wrap-break-word] -->
<div class="flex max-w-sm">
  <img class="size-16 rounded-full" src="/img/profile.jpg" />
  <div class="wrap-break-word">
    <p class="font-medium">Jay Riemenschneider</p>
    <p><EMAIL></p>
  </div>
</div>
<div class="flex max-w-sm">
  <img class="size-16 rounded-full" src="/img/profile.jpg" />
  <div class="wrap-anywhere">
    <p class="font-medium">Jay Riemenschneider</p>
    <p><EMAIL></p>
  </div>
</div>
```

----------------------------------------

TITLE: Using Logical `start-0` Property with Tailwind CSS (HTML)
DESCRIPTION: This HTML snippet demonstrates the `start-0` Tailwind CSS utility, which corresponds to the `inset-inline-start` logical property. It shows how `start-0` positions an element at the beginning of the inline direction, adapting automatically for both left-to-right (`ltr`) and right-to-left (`rtl`) text directions without needing separate `left-0` or `right-0` classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/top-right-bottom-left.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code classes:start-0] -->
<div dir="ltr">
  <div class="relative size-32 ...">
    <div class="absolute start-0 top-0 size-14 ..."></div>
  </div>
  <div>
    <div dir="rtl">
      <div class="relative size-32 ...">
        <div class="absolute start-0 top-0 size-14 ..."></div>
      </div>
      <div></div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Background Color - HTML - Tailwind CSS
DESCRIPTION: Illustrates how to apply a background color not defined in the theme using Tailwind's arbitrary value syntax. The `bg-[#316ff6]` class sets the background color directly using a hexadecimal value.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<button class="bg-[#316ff6] ...">
  Sign in with Facebook
</button>
```

----------------------------------------

TITLE: Defining Shared Theme Variables in a Separate CSS File
DESCRIPTION: This CSS snippet demonstrates how to define a collection of shared theme variables (e.g., spacing, fonts, colors) within a dedicated CSS file. This approach facilitates reusability and consistency across multiple projects by centralizing design tokens in a single, importable file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#_snippet_21

LANGUAGE: css
CODE:
```
@theme {
  --*: initial;

  --spacing: 4px;

  --font-body: Inter, sans-serif;

  --color-lagoon: oklch(0.72 0.11 221.19);
  --color-coral: oklch(0.74 0.17 40.24);
  --color-driftwood: oklch(0.79 0.06 74.59);
  --color-tide: oklch(0.49 0.08 205.88);
  --color-dusk: oklch(0.82 0.15 72.09);
}
```

----------------------------------------

TITLE: Setting Max Width with Arbitrary Value in Tailwind CSS
DESCRIPTION: Enables setting the maximum width to any arbitrary CSS value directly, enclosed in square brackets. This is useful for one-off values not covered by the default scale.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#_snippet_30

LANGUAGE: CSS
CODE:
```
max-width: <value>;
```

----------------------------------------

TITLE: HTML Example for Data Attribute-based Dark Mode Toggling
DESCRIPTION: This HTML snippet illustrates how setting `data-theme="dark"` on the `<html>` element activates dark mode utilities. Similar to the class-based approach, `dark:bg-black` will apply when this attribute is present. This method offers semantic clarity for theme management.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<html data-theme="dark">
  <body>
    <div class="bg-white dark:bg-black">
      <!-- ... -->
    </div>
  </body>
</html>
```

----------------------------------------

TITLE: Creating a Composable Text Field with Catalyst (JSX)
DESCRIPTION: This example illustrates how to create a text field using Catalyst's HTML-mirrored API. It utilizes separate, composable components like `Field`, `Label`, `Description`, and `Input` to build the form element. This design allows for greater flexibility in styling and rearranging individual parts of the field.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/introducing-catalyst/index.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import { Description, Field, Label } from "@/components/fieldset";
import { Input } from "@/components/input";

function Example() {
  return (
    <Field>
      <Label>Product name</Label>
      <Description>Use the name you'd like people to see in their cart.</Description>
      <Input name="product_name" />
    </Field>
  );
}
```

----------------------------------------

TITLE: Installing Tailwind CSS v4 Alpha for CLI - npm
DESCRIPTION: This npm command installs the alpha version of Tailwind CSS and its CLI package, providing the necessary tools for direct command-line compilation of your CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#_snippet_17

LANGUAGE: sh
CODE:
```
npm install tailwindcss@next @tailwindcss/cli@next
```

----------------------------------------

TITLE: Setting SVG Fill to Current Text Color - HTML
DESCRIPTION: This example illustrates the use of the `fill-current` utility to set an SVG's fill color to the current text color of its parent element. This allows for dynamic styling where the SVG's color automatically matches the surrounding text, often used for icons within buttons or text blocks. The `fill: currentColor;` CSS property is applied.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/fill.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:fill-current] -->
<button class="bg-white text-indigo-600 hover:bg-indigo-600 hover:text-white ...">
  <svg class="size-5 fill-current ...">
    <!-- ... -->
  </svg>
  Check for updates
</button>
```

----------------------------------------

TITLE: Applying Responsive Container Max Width in Tailwind CSS
DESCRIPTION: Defines a responsive container that sets its width to 100% by default and applies increasing maximum widths at different breakpoint sizes (40rem, 48rem, 64rem, 80rem, 96rem). This ensures content is constrained on larger screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#_snippet_28

LANGUAGE: CSS
CODE:
```
width: 100%;
@media (width >= 40rem) { max-width: 40rem; }
@media (width >= 48rem) { max-width: 48rem; }
@media (width >= 64rem) { max-width: 64rem; }
@media (width >= 80rem) { max-width: 80rem; }
@media (width >= 96rem) { max-width: 96rem; }
```

----------------------------------------

TITLE: Styling User-Invalid Inputs with :user-invalid in HTML
DESCRIPTION: This snippet illustrates how to apply a Tailwind CSS class to an input element when it is invalid and the user has interacted with it, using the `user-invalid` variant. This variant is useful for delaying error messages until the user has attempted to input data, applying a `border-red-500`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_257

LANGUAGE: HTML
CODE:
```
<input required class="border user-invalid:border-red-500" />
```

----------------------------------------

TITLE: Applying Custom Container Query Variant in HTML
DESCRIPTION: This HTML snippet illustrates how to use a custom container query variant, `@8xl:flex-row`, defined in the Tailwind CSS theme. By applying `@container` to a parent `div`, its child elements can respond to the custom `8xl` breakpoint, changing their layout from `flex-col` to `flex-row` when the container reaches the specified size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#_snippet_19

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @8xl:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Defining `sm` Media Query Breakpoint in CSS
DESCRIPTION: This CSS media query defines the `sm` (small) breakpoint, applying styles when the viewport width is 40rem (640px) or greater. It's a fundamental part of responsive web design, allowing layouts to adapt to different screen sizes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#_snippet_154

LANGUAGE: CSS
CODE:
```
@media (width >= 40rem)
```

----------------------------------------

TITLE: Updating Shadow, Radius, and Blur Utilities in HTML
DESCRIPTION: This snippet demonstrates how to migrate old Tailwind CSS v3 utility classes for shadows, radii, and blur to their new v4 equivalents. It shows the replacement of `shadow-sm` with `shadow-xs` and `shadow` with `shadow-sm` to align with the updated default scales. This update ensures consistency with the new naming conventions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<!-- [!code --:2] -->
<input class="shadow-sm" />
<!-- [!code ++:2] -->
<input class="shadow-xs" />

<!-- [!code --:2] -->
<input class="shadow" />
<!-- [!code ++:2] -->
<input class="shadow-sm" />
```

----------------------------------------

TITLE: Using Group Hover Utility HTML
DESCRIPTION: Provides an example of the `group-hover` utility. In v2.0, `group-hover` and `focus-within` are enabled by default for the same properties that support `hover` and `focus`, making interactive group styling easier.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_24

LANGUAGE: html
CODE:
```
<div class="group ...">
  <span class="group-hover:text-blue-600 ...">Da ba dee da ba daa</span>
</div>
```

----------------------------------------

TITLE: Loading Custom Fonts Using CSS @font-face Rule
DESCRIPTION: This CSS snippet illustrates how to use the `@font-face` at-rule to load a custom font, 'Oswald', into a web page. It specifies the font family name, style, weight range, display behavior, and the source URL for the font file, which is essential for self-hosting custom fonts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-family.mdx#_snippet_4

LANGUAGE: CSS
CODE:
```
@font-face {
  font-family: Oswald;
  font-style: normal;
  font-weight: 200 700;
  font-display: swap;
  src: url("/fonts/Oswald.woff2") format("woff2");
}
```