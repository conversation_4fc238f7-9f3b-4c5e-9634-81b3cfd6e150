export interface SelectProps {
    labelField: string;
    valueField: string;
    options: Option[];
    onChange: (value: any) => void;
    value?: any;
    placeholder?: string;
    disabled?: boolean;
    size?: 'small' | 'medium';
    variant?: 'outlined' | 'filled' | 'standard';
    dictionaryConfig?: DictionaryConfig;
    apiConfig?: ApiConfig;
}

export interface Option {
    label: string;
    value: any;
}

export interface ApiConfig {
    url: string;
    method: 'GET' | 'POST';
    params?: any;
    body?: any;
}

export interface DictionaryConfig {
    code: string;
    labelField: string;
    valueField: string;
}