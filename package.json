{"name": "agentflowfont", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "serve": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "check-format": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tanstack/react-query": "^5.49.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.7.4", "clsx": "^2.1.1", "framer-motion": "^12.11.3", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-markdown": "^10.1.0", "react-router-dom": "^6.23.1", "tailwind-merge": "^3.3.0", "zod": "^3.24.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}