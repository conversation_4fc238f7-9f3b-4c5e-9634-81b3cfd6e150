// Model related types

// Model data interface
export interface Model {
  id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  supportsFunctionCalling: boolean;
}

// Form data for creating/updating a model
export interface ModelFormData {
  name: string;
  description: string;
  type: string;
  status: string;
  supportsFunctionCalling: boolean;
}

// Model status update data
export interface ModelStatusUpdateData {
  id: string;
  status: string;
}
