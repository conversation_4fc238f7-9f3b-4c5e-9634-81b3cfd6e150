Table
=====

Tables display sets of data. They can be fully customized.


Fast apps run on Redis Cloud. Real\-time, fully managed, and built to scale.

ads via Carbon



Tables display information in a way that's easy to scan, so that users can look for patterns and insights. They can be embedded in primary content, such as cards. They can include:


* A corresponding visualization
* Navigation
* Tools to query and manipulate data


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Introduction
------------

Tables are implemented using a collection of related components:


* `<TableContainer />`: A wrapper that provides horizontally scrolling behavior for the `<Table />` component.
* `<Table />`: The main component for the table element. Renders as a `<table>` by default.
* `<TableHead />`: The container for the header row(s) of `<Table />`. Renders as a `<thead>` by default.
* `<TableBody />`: The container for the body rows of `<Table />`. Renders as a `<tbody>` by default.
* `<TableRow />`: A row in a table. Can be used in `<TableHead />`, `<TableBody />`, or `<TableFooter />`. Renders as a `<tr>` by default.
* `<TableCell />`: A cell in a table. Can be used in `<TableRow />` . Renders as a `<th>` in `<TableHead />` and `<td>` in `<TableBody />` by default.
* `<TableFooter />`: An optional container for the footer row(s) of the table. Renders as a `<tfoot>` by default.
* `<TablePagination />`: A component that provides controls for paginating table data. See the 'Sorting \& selecting' example and 'Custom Table Pagination Action' example.
* `<TableSortLabel />`: A component used to display sorting controls for column headers, allowing users to sort data in ascending or descending order. See the 'Sorting \& selecting' example.


Basic table
-----------

A simple example with no frills.




| Dessert (100g serving) | Calories | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- |
| Frozen yoghurt | 159 | 6 | 24 | 4 |
| Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
| Eclair | 262 | 16 | 24 | 6 |
| Cupcake | 305 | 3\.7 | 67 | 4\.3 |
| Gingerbread | 356 | 16 | 49 | 3\.9 |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData('Eclair', 262, 16.0, 24, 6.0),
  createData('Cupcake', 305, 3.7, 67, 4.3),
  createData('Gingerbread', 356, 16.0, 49, 3.9),
];

export default function BasicTable {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell>Dessert (100g serving)</TableCell>
            <TableCell align="right">Calories</TableCell>
            <TableCell align="right">Fat&nbsp;(g)</TableCell>
            <TableCell align="right">Carbs&nbsp;(g)</TableCell>
            <TableCell align="right">Protein&nbsp;(g)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow
              key={row.name}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {row.name}
              </TableCell>
              <TableCell align="right">{row.calories}</TableCell>
              <TableCell align="right">{row.fat}</TableCell>
              <TableCell align="right">{row.carbs}</TableCell>
              <TableCell align="right">{row.protein}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
) {
 return { name, calories, fat, carbs, protein };
}

const rows \= \[
 createData('Frozen yoghurt', 159, 6\.0, 24, 4\.0\),
 createData('Ice cream sandwich', 237, 9\.0, 37, 4\.3\),
 createData('Eclair', 262, 16\.0, 24, 6\.0\),
 createData('Cupcake', 305, 3\.7, 67, 4\.3\),
 createData('Gingerbread', 356, 16\.0, 49, 3\.9\),
];

export default function BasicTable {
 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 650 }} aria\-label\="simple table"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell\>Dessert (100g serving)\</TableCell\>
 \<TableCell align\="right"\>Calories\</TableCell\>
 \<TableCell align\="right"\>Fat\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Carbs\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Protein\&nbsp;(g)\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<TableRow
 key\={row.name}
 sx\={{ '\&:last\-child td, \&:last\-child th': { border: 0 } }}
 \>
 \<TableCell component\="th" scope\="row"\>
 {row.name}
 \</TableCell\>
 \<TableCell align\="right"\>{row.calories}\</TableCell\>
 \<TableCell align\="right"\>{row.fat}\</TableCell\>
 \<TableCell align\="right"\>{row.carbs}\</TableCell\>
 \<TableCell align\="right"\>{row.protein}\</TableCell\>
 \</TableRow\>
 ))}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Auth0** \- Boost your app’s Identity with Auth0\. Now with Custom Domain, Passwordless, and more!ad by CarbonData table
----------

The `Table` component has a close mapping to the native `<table>` elements.
This constraint makes building rich data tables challenging.


The `DataGrid` component is designed for use\-cases that are focused on handling large amounts of tabular data.
While it comes with a more rigid structure, in exchange, you gain more powerful features.


IDFirst nameLast nameAgeFull name1JonSnow35Jon Snow2CerseiLannister42Cersei Lannister3JaimeLannister45Jaime Lannister4AryaStark16Arya Stark5DaenerysTargaryenDaenerys TargaryenRows per page:

51–5 of 9

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Paper from '@mui/material/Paper';

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'firstName', headerName: 'First name', width: 130 },
  { field: 'lastName', headerName: 'Last name', width: 130 },
  {
    field: 'age',
    headerName: 'Age',
    type: 'number',
    width: 90,
  },
  {
    field: 'fullName',
    headerName: 'Full name',
    description: 'This column has a value getter and is not sortable.',
    sortable: false,
    width: 160,
    valueGetter: (value, row) => `${row.firstName || ''} ${row.lastName || ''}`,
  },
];

const rows = [
  { id: 1, lastName: 'Snow', firstName: 'Jon', age: 35 },
  { id: 2, lastName: 'Lannister', firstName: 'Cersei', age: 42 },
  { id: 3, lastName: 'Lannister', firstName: 'Jaime', age: 45 },
  { id: 4, lastName: 'Stark', firstName: 'Arya', age: 16 },
  { id: 5, lastName: 'Targaryen', firstName: 'Daenerys', age: null },
  { id: 6, lastName: 'Melisandre', firstName: null, age: 150 },
  { id: 7, lastName: 'Clifford', firstName: 'Ferrara', age: 44 },
  { id: 8, lastName: 'Frances', firstName: 'Rossini', age: 36 },
  { id: 9, lastName: 'Roxie', firstName: 'Harvey', age: 65 },
];

const paginationModel = { page: 0, pageSize: 5 };

export default function DataTable {
  return (
    <Paper sx={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        columns={columns}
        initialState={{ pagination: { paginationModel } }}
        pageSizeOptions={[5, 10]}
        checkboxSelection
        sx={{ border: 0 }}
      />
    </Paper>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridColDef } from '@mui/x\-data\-grid';
import Paper from '@mui/material/Paper';

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'ID', width: 70 },
 { field: 'firstName', headerName: 'First name', width: 130 },
 { field: 'lastName', headerName: 'Last name', width: 130 },
 {
 field: 'age',
 headerName: 'Age',
 type: 'number',
 width: 90,
 },
 {
 field: 'fullName',
 headerName: 'Full name',
 description: 'This column has a value getter and is not sortable.',
 sortable: false,
 width: 160,
 valueGetter: (value, row) \=\> \`${row.firstName \|\| ''} ${row.lastName \|\| ''}\`,
 },
];

const rows \= \[
 { id: 1, lastName: 'Snow', firstName: 'Jon', age: 35 },
 { id: 2, lastName: 'Lannister', firstName: 'Cersei', age: 42 },
 { id: 3, lastName: 'Lannister', firstName: 'Jaime', age: 45 },
 { id: 4, lastName: 'Stark', firstName: 'Arya', age: 16 },
 { id: 5, lastName: 'Targaryen', firstName: 'Daenerys', age: null },
 { id: 6, lastName: 'Melisandre', firstName: null, age: 150 },
 { id: 7, lastName: 'Clifford', firstName: 'Ferrara', age: 44 },
 { id: 8, lastName: 'Frances', firstName: 'Rossini', age: 36 },
 { id: 9, lastName: 'Roxie', firstName: 'Harvey', age: 65 },
];

const paginationModel \= { page: 0, pageSize: 5 };

export default function DataTable {
 return (
 \<Paper sx\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 rows\={rows}
 columns\={columns}
 initialState\={{ pagination: { paginationModel } }}
 pageSizeOptions\={\[5, 10]}
 checkboxSelection
 sx\={{ border: 0 }}
 /\>
 \</Paper\>
 );
}Press `Enter` to start editing**Auth0** \- You asked, we delivered! Our Free Plan now includes a Custom Domain, 5 Actions, and 25,000 MAUs.ad by CarbonDense table
-----------

A simple example of a dense table with no frills.




| Dessert (100g serving) | Calories | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- |
| Frozen yoghurt | 159 | 6 | 24 | 4 |
| Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
| Eclair | 262 | 16 | 24 | 6 |
| Cupcake | 305 | 3\.7 | 67 | 4\.3 |
| Gingerbread | 356 | 16 | 49 | 3\.9 |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData('Eclair', 262, 16.0, 24, 6.0),
  createData('Cupcake', 305, 3.7, 67, 4.3),
  createData('Gingerbread', 356, 16.0, 49, 3.9),
];

export default function DenseTable {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} size="small" aria-label="a dense table">
        <TableHead>
          <TableRow>
            <TableCell>Dessert (100g serving)</TableCell>
            <TableCell align="right">Calories</TableCell>
            <TableCell align="right">Fat&nbsp;(g)</TableCell>
            <TableCell align="right">Carbs&nbsp;(g)</TableCell>
            <TableCell align="right">Protein&nbsp;(g)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow
              key={row.name}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {row.name}
              </TableCell>
              <TableCell align="right">{row.calories}</TableCell>
              <TableCell align="right">{row.fat}</TableCell>
              <TableCell align="right">{row.carbs}</TableCell>
              <TableCell align="right">{row.protein}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
) {
 return { name, calories, fat, carbs, protein };
}

const rows \= \[
 createData('Frozen yoghurt', 159, 6\.0, 24, 4\.0\),
 createData('Ice cream sandwich', 237, 9\.0, 37, 4\.3\),
 createData('Eclair', 262, 16\.0, 24, 6\.0\),
 createData('Cupcake', 305, 3\.7, 67, 4\.3\),
 createData('Gingerbread', 356, 16\.0, 49, 3\.9\),
];

export default function DenseTable {
 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 650 }} size\="small" aria\-label\="a dense table"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell\>Dessert (100g serving)\</TableCell\>
 \<TableCell align\="right"\>Calories\</TableCell\>
 \<TableCell align\="right"\>Fat\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Carbs\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Protein\&nbsp;(g)\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<TableRow
 key\={row.name}
 sx\={{ '\&:last\-child td, \&:last\-child th': { border: 0 } }}
 \>
 \<TableCell component\="th" scope\="row"\>
 {row.name}
 \</TableCell\>
 \<TableCell align\="right"\>{row.calories}\</TableCell\>
 \<TableCell align\="right"\>{row.fat}\</TableCell\>
 \<TableCell align\="right"\>{row.carbs}\</TableCell\>
 \<TableCell align\="right"\>{row.protein}\</TableCell\>
 \</TableRow\>
 ))}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by CarbonSorting \& selecting
--------------------

This example demonstrates the use of `Checkbox` and clickable rows for selection, with a custom `Toolbar`. It uses the `TableSortLabel` component to help style column headings.


The Table has been given a fixed width to demonstrate horizontal scrolling. In order to prevent the pagination controls from scrolling, the TablePagination component is used outside of the Table. (The 'Custom Table Pagination Action' example below shows the pagination within the TableFooter.)


Nutrition

|  | Dessert (100g serving) | Caloriessorted ascending | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- | --- |
|  | Frozen yoghurt | 159 | 6 | 24 | 4 |
|  | Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
|  | Eclair | 262 | 16 | 24 | 6 |
|  | Cupcake | 305 | 3\.7 | 67 | 4\.3 |
|  | Marshmallow | 318 | 0 | 81 | 2 |

Rows per page:

51–5 of 13

Dense paddingJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Checkbox from '@mui/material/Checkbox';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterListIcon from '@mui/icons-material/FilterList';
import { visuallyHidden } from '@mui/utils';

interface Data {
  id: number;
  calories: number;
  carbs: number;
  fat: number;
  name: string;
  protein: number;
}

function createData(
  id: number,
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
): Data {
  return {
    id,
    name,
    calories,
    fat,
    carbs,
    protein,
  };
}

const rows = [
  createData(1, 'Cupcake', 305, 3.7, 67, 4.3),
  createData(2, 'Donut', 452, 25.0, 51, 4.9),
  createData(3, 'Eclair', 262, 16.0, 24, 6.0),
  createData(4, 'Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData(5, 'Gingerbread', 356, 16.0, 49, 3.9),
  createData(6, 'Honeycomb', 408, 3.2, 87, 6.5),
  createData(7, 'Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData(8, 'Jelly Bean', 375, 0.0, 94, 0.0),
  createData(9, 'KitKat', 518, 26.0, 65, 7.0),
  createData(10, 'Lollipop', 392, 0.2, 98, 0.0),
  createData(11, 'Marshmallow', 318, 0, 81, 2.0),
  createData(12, 'Nougat', 360, 19.0, 9, 37.0),
  createData(13, 'Oreo', 437, 18.0, 63, 4.0),
];

function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

type Order = 'asc' | 'desc';

function getComparator<Key extends keyof any>(
  order: Order,
  orderBy: Key,
): (
  a: { [key in Key]: number | string },
  b: { [key in Key]: number | string },
) => number {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

interface HeadCell {
  disablePadding: boolean;
  id: keyof Data;
  label: string;
  numeric: boolean;
}

const headCells: readonly HeadCell[] = [
  {
    id: 'name',
    numeric: false,
    disablePadding: true,
    label: 'Dessert (100g serving)',
  },
  {
    id: 'calories',
    numeric: true,
    disablePadding: false,
    label: 'Calories',
  },
  {
    id: 'fat',
    numeric: true,
    disablePadding: false,
    label: 'Fat (g)',
  },
  {
    id: 'carbs',
    numeric: true,
    disablePadding: false,
    label: 'Carbs (g)',
  },
  {
    id: 'protein',
    numeric: true,
    disablePadding: false,
    label: 'Protein (g)',
  },
];

interface EnhancedTableProps {
  numSelected: number;
  onRequestSort: (event: React.MouseEvent<unknown>, property: keyof Data) => void;
  onSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  order: Order;
  orderBy: string;
  rowCount: number;
}

function EnhancedTableHead(props: EnhancedTableProps) {
  const { onSelectAllClick, order, orderBy, numSelected, rowCount, onRequestSort } =
    props;
  const createSortHandler =
    (property: keyof Data) => (event: React.MouseEvent<unknown>) => {
      onRequestSort(event, property);
    };

  return (
    <TableHead>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            color="primary"
            indeterminate={numSelected > 0 && numSelected < rowCount}
            checked={rowCount > 0 && numSelected === rowCount}
            onChange={onSelectAllClick}
            inputProps={{
              'aria-label': 'select all desserts',
            }}
          />
        </TableCell>
        {headCells.map((headCell) => (
          <TableCell
            key={headCell.id}
            align={headCell.numeric ? 'right' : 'left'}
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
          >
            <TableSortLabel
              active={orderBy === headCell.id}
              direction={orderBy === headCell.id ? order : 'asc'}
              onClick={createSortHandler(headCell.id)}
            >
              {headCell.label}
              {orderBy === headCell.id ? (
                <Box component="span" sx={visuallyHidden}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box>
              ) : null}
            </TableSortLabel>
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}
interface EnhancedTableToolbarProps {
  numSelected: number;
}
function EnhancedTableToolbar(props: EnhancedTableToolbarProps) {
  const { numSelected } = props;
  return (
    <Toolbar
      sx={[
        {
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
        },
        numSelected > 0 && {
          bgcolor: (theme) =>
            alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
        },
      ]}
    >
      {numSelected > 0 ? (
        <Typography
          sx={{ flex: '1 1 100%' }}
          color="inherit"
          variant="subtitle1"
          component="div"
        >
          {numSelected} selected
        </Typography>
      ) : (
        <Typography
          sx={{ flex: '1 1 100%' }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          Nutrition
        </Typography>
      )}
      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <FilterListIcon />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}
export default function EnhancedTable {
  const [order, setOrder] = React.useState<Order>('asc');
  const [orderBy, setOrderBy] = React.useState<keyof Data>('calories');
  const [selected, setSelected] = React.useState<readonly number[]>([]);
  const [page, setPage] = React.useState(0);
  const [dense, setDense] = React.useState(false);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: keyof Data,
  ) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = rows.map((n) => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event: React.MouseEvent<unknown>, id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: readonly number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1),
      );
    }
    setSelected(newSelected);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleChangeDense = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDense(event.target.checked);
  };

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows =
    page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

  const visibleRows = React.useMemo(
     =>
      [...rows]
        .sort(getComparator(order, orderBy))
        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [order, orderBy, page, rowsPerPage],
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ width: '100%', mb: 2 }}>
        <EnhancedTableToolbar numSelected={selected.length} />
        <TableContainer>
          <Table
            sx={{ minWidth: 750 }}
            aria-labelledby="tableTitle"
            size={dense ? 'small' : 'medium'}
          >
            <EnhancedTableHead
              numSelected={selected.length}
              order={order}
              orderBy={orderBy}
              onSelectAllClick={handleSelectAllClick}
              onRequestSort={handleRequestSort}
              rowCount={rows.length}
            />
            <TableBody>
              {visibleRows.map((row, index) => {
                const isItemSelected = selected.includes(row.id);
                const labelId = `enhanced-table-checkbox-${index}`;

                return (
                  <TableRow
                    hover
                    onClick={(event) => handleClick(event, row.id)}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row.id}
                    selected={isItemSelected}
                    sx={{ cursor: 'pointer' }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        color="primary"
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': labelId,
                        }}
                      />
                    </TableCell>
                    <TableCell
                      component="th"
                      id={labelId}
                      scope="row"
                      padding="none"
                    >
                      {row.name}
                    </TableCell>
                    <TableCell align="right">{row.calories}</TableCell>
                    <TableCell align="right">{row.fat}</TableCell>
                    <TableCell align="right">{row.carbs}</TableCell>
                    <TableCell align="right">{row.protein}</TableCell>
                  </TableRow>
                );
              })}
              {emptyRows > 0 && (
                <TableRow
                  style={{
                    height: (dense ? 33 : 53) * emptyRows,
                  }}
                >
                  <TableCell colSpan={6} />
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      <FormControlLabel
        control={<Switch checked={dense} onChange={handleChangeDense} />}
        label="Dense padding"
      />
    </Box>
  );
}  

```
import \* as React from 'react';
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Checkbox from '@mui/material/Checkbox';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import DeleteIcon from '@mui/icons\-material/Delete';
import FilterListIcon from '@mui/icons\-material/FilterList';
import { visuallyHidden } from '@mui/utils';

interface Data {
 id: number;
 calories: number;
 carbs: number;
 fat: number;
 name: string;
 protein: number;
}

function createData(
 id: number,
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
): Data {
 return {
 id,
 name,
 calories,
 fat,
 carbs,
 protein,
 };
}

const rows \= \[
 createData(1, 'Cupcake', 305, 3\.7, 67, 4\.3\),
 createData(2, 'Donut', 452, 25\.0, 51, 4\.9\),
 createData(3, 'Eclair', 262, 16\.0, 24, 6\.0\),
 createData(4, 'Frozen yoghurt', 159, 6\.0, 24, 4\.0\),
 createData(5, 'Gingerbread', 356, 16\.0, 49, 3\.9\),
 createData(6, 'Honeycomb', 408, 3\.2, 87, 6\.5\),
 createData(7, 'Ice cream sandwich', 237, 9\.0, 37, 4\.3\),
 createData(8, 'Jelly Bean', 375, 0\.0, 94, 0\.0\),
 createData(9, 'KitKat', 518, 26\.0, 65, 7\.0\),
 createData(10, 'Lollipop', 392, 0\.2, 98, 0\.0\),
 createData(11, 'Marshmallow', 318, 0, 81, 2\.0\),
 createData(12, 'Nougat', 360, 19\.0, 9, 37\.0\),
 createData(13, 'Oreo', 437, 18\.0, 63, 4\.0\),
];

function descendingComparator\<T\>(a: T, b: T, orderBy: keyof T) {
 if (b\[orderBy] \< a\[orderBy]) {
 return \-1;
 }
 if (b\[orderBy] \> a\[orderBy]) {
 return 1;
 }
 return 0;
}

type Order \= 'asc' \| 'desc';

function getComparator\<Key extends keyof any\>(
 order: Order,
 orderBy: Key,
): (
 a: { \[key in Key]: number \| string },
 b: { \[key in Key]: number \| string },
) \=\> number {
 return order \=\=\= 'desc'
 ? (a, b) \=\> descendingComparator(a, b, orderBy)
 : (a, b) \=\> \-descendingComparator(a, b, orderBy);
}

interface HeadCell {
 disablePadding: boolean;
 id: keyof Data;
 label: string;
 numeric: boolean;
}

const headCells: readonly HeadCell\[] \= \[
 {
 id: 'name',
 numeric: false,
 disablePadding: true,
 label: 'Dessert (100g serving)',
 },
 {
 id: 'calories',
 numeric: true,
 disablePadding: false,
 label: 'Calories',
 },
 {
 id: 'fat',
 numeric: true,
 disablePadding: false,
 label: 'Fat (g)',
 },
 {
 id: 'carbs',
 numeric: true,
 disablePadding: false,
 label: 'Carbs (g)',
 },
 {
 id: 'protein',
 numeric: true,
 disablePadding: false,
 label: 'Protein (g)',
 },
];

interface EnhancedTableProps {
 numSelected: number;
 onRequestSort: (event: React.MouseEvent\<unknown\>, property: keyof Data) \=\> void;
 onSelectAllClick: (event: React.ChangeEvent\<HTMLInputElement\>) \=\> void;
 order: Order;
 orderBy: string;
 rowCount: number;
}

function EnhancedTableHead(props: EnhancedTableProps) {
 const { onSelectAllClick, order, orderBy, numSelected, rowCount, onRequestSort } \=
 props;
 const createSortHandler \=
 (property: keyof Data) \=\> (event: React.MouseEvent\<unknown\>) \=\> {
 onRequestSort(event, property);
 };

 return (
 \<TableHead\>
 \<TableRow\>
 \<TableCell padding\="checkbox"\>
 \<Checkbox
 color\="primary"
 indeterminate\={numSelected \> 0 \&\& numSelected \< rowCount}
 checked\={rowCount \> 0 \&\& numSelected \=\=\= rowCount}
 onChange\={onSelectAllClick}
 inputProps\={{
 'aria\-label': 'select all desserts',
 }}
 /\>
 \</TableCell\>
 {headCells.map((headCell) \=\> (
 \<TableCell
 key\={headCell.id}
 align\={headCell.numeric ? 'right' : 'left'}
 padding\={headCell.disablePadding ? 'none' : 'normal'}
 sortDirection\={orderBy \=\=\= headCell.id ? order : false}
 \>
 \<TableSortLabel
 active\={orderBy \=\=\= headCell.id}
 direction\={orderBy \=\=\= headCell.id ? order : 'asc'}
 onClick\={createSortHandler(headCell.id)}
 \>
 {headCell.label}
 {orderBy \=\=\= headCell.id ? (
 \<Box component\="span" sx\={visuallyHidden}\>
 {order \=\=\= 'desc' ? 'sorted descending' : 'sorted ascending'}
 \</Box\>
 ) : null}
 \</TableSortLabel\>
 \</TableCell\>
 ))}
 \</TableRow\>
 \</TableHead\>
 );
}
interface EnhancedTableToolbarProps {
 numSelected: number;
}
function EnhancedTableToolbar(props: EnhancedTableToolbarProps) {
 const { numSelected } \= props;
 return (
 \<Toolbar
 sx\={\[
 {
 pl: { sm: 2 },
 pr: { xs: 1, sm: 1 },
 },
 numSelected \> 0 \&\& {
 bgcolor: (theme) \=\>
 alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
 },
 ]}
 \>
 {numSelected \> 0 ? (
 \<Typography
 sx\={{ flex: '1 1 100%' }}
 color\="inherit"
 variant\="subtitle1"
 component\="div"
 \>
 {numSelected} selected
 \</Typography\>
 ) : (
 \<Typography
 sx\={{ flex: '1 1 100%' }}
 variant\="h6"
 id\="tableTitle"
 component\="div"
 \>
 Nutrition
 \</Typography\>
 )}
 {numSelected \> 0 ? (
 \<Tooltip title\="Delete"\>
 \<IconButton\>
 \<DeleteIcon /\>
 \</IconButton\>
 \</Tooltip\>
 ) : (
 \<Tooltip title\="Filter list"\>
 \<IconButton\>
 \<FilterListIcon /\>
 \</IconButton\>
 \</Tooltip\>
 )}
 \</Toolbar\>
 );
}
export default function EnhancedTable {
 const \[order, setOrder] \= React.useState\<Order\>('asc');
 const \[orderBy, setOrderBy] \= React.useState\<keyof Data\>('calories');
 const \[selected, setSelected] \= React.useState\<readonly number\[]\>(\[]);
 const \[page, setPage] \= React.useState(0\);
 const \[dense, setDense] \= React.useState(false);
 const \[rowsPerPage, setRowsPerPage] \= React.useState(5\);

 const handleRequestSort \= (
 event: React.MouseEvent\<unknown\>,
 property: keyof Data,
 ) \=\> {
 const isAsc \= orderBy \=\=\= property \&\& order \=\=\= 'asc';
 setOrder(isAsc ? 'desc' : 'asc');
 setOrderBy(property);
 };

 const handleSelectAllClick \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 if (event.target.checked) {
 const newSelected \= rows.map((n) \=\> n.id);
 setSelected(newSelected);
 return;
 }
 setSelected(\[]);
 };

 const handleClick \= (event: React.MouseEvent\<unknown\>, id: number) \=\> {
 const selectedIndex \= selected.indexOf(id);
 let newSelected: readonly number\[] \= \[];

 if (selectedIndex \=\=\= \-1\) {
 newSelected \= newSelected.concat(selected, id);
 } else if (selectedIndex \=\=\= 0\) {
 newSelected \= newSelected.concat(selected.slice(1\));
 } else if (selectedIndex \=\=\= selected.length \- 1\) {
 newSelected \= newSelected.concat(selected.slice(0, \-1\));
 } else if (selectedIndex \> 0\) {
 newSelected \= newSelected.concat(
 selected.slice(0, selectedIndex),
 selected.slice(selectedIndex \+ 1\),
 );
 }
 setSelected(newSelected);
 };

 const handleChangePage \= (event: unknown, newPage: number) \=\> {
 setPage(newPage);
 };

 const handleChangeRowsPerPage \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setRowsPerPage(parseInt(event.target.value, 10\));
 setPage(0\);
 };

 const handleChangeDense \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setDense(event.target.checked);
 };

 // Avoid a layout jump when reaching the last page with empty rows.
 const emptyRows \=
 page \> 0 ? Math.max(0, (1 \+ page) \* rowsPerPage \- rows.length) : 0;

 const visibleRows \= React.useMemo(
  \=\>
 \[...rows]
 .sort(getComparator(order, orderBy))
 .slice(page \* rowsPerPage, page \* rowsPerPage \+ rowsPerPage),
 \[order, orderBy, page, rowsPerPage],
 );

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Paper sx\={{ width: '100%', mb: 2 }}\>
 \<EnhancedTableToolbar numSelected\={selected.length} /\>
 \<TableContainer\>
 \<Table
 sx\={{ minWidth: 750 }}
 aria\-labelledby\="tableTitle"
 size\={dense ? 'small' : 'medium'}
 \>
 \<EnhancedTableHead
 numSelected\={selected.length}
 order\={order}
 orderBy\={orderBy}
 onSelectAllClick\={handleSelectAllClick}
 onRequestSort\={handleRequestSort}
 rowCount\={rows.length}
 /\>
 \<TableBody\>
 {visibleRows.map((row, index) \=\> {
 const isItemSelected \= selected.includes(row.id);
 const labelId \= \`enhanced\-table\-checkbox\-${index}\`;

 return (
 \<TableRow
 hover
 onClick\={(event) \=\> handleClick(event, row.id)}
 role\="checkbox"
 aria\-checked\={isItemSelected}
 tabIndex\={\-1}
 key\={row.id}
 selected\={isItemSelected}
 sx\={{ cursor: 'pointer' }}
 \>
 \<TableCell padding\="checkbox"\>
 \<Checkbox
 color\="primary"
 checked\={isItemSelected}
 inputProps\={{
 'aria\-labelledby': labelId,
 }}
 /\>
 \</TableCell\>
 \<TableCell
 component\="th"
 id\={labelId}
 scope\="row"
 padding\="none"
 \>
 {row.name}
 \</TableCell\>
 \<TableCell align\="right"\>{row.calories}\</TableCell\>
 \<TableCell align\="right"\>{row.fat}\</TableCell\>
 \<TableCell align\="right"\>{row.carbs}\</TableCell\>
 \<TableCell align\="right"\>{row.protein}\</TableCell\>
 \</TableRow\>
 );
 })}
 {emptyRows \> 0 \&\& (
 \<TableRow
 style\={{
 height: (dense ? 33 : 53\) \* emptyRows,
 }}
 \>
 \<TableCell colSpan\={6} /\>
 \</TableRow\>
 )}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 \<TablePagination
 rowsPerPageOptions\={\[5, 10, 25]}
 component\="div"
 count\={rows.length}
 rowsPerPage\={rowsPerPage}
 page\={page}
 onPageChange\={handleChangePage}
 onRowsPerPageChange\={handleChangeRowsPerPage}
 /\>
 \</Paper\>
 \<FormControlLabel
 control\={\<Switch checked\={dense} onChange\={handleChangeDense} /\>}
 label\="Dense padding"
 /\>
 \</Box\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.




| Dessert (100g serving) | Calories | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- |
| Frozen yoghurt | 159 | 6 | 24 | 4 |
| Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
| Eclair | 262 | 16 | 24 | 6 |
| Cupcake | 305 | 3\.7 | 67 | 4\.3 |
| Gingerbread | 356 | 16 | 49 | 3\.9 |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

function createData(
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData('Eclair', 262, 16.0, 24, 6.0),
  createData('Cupcake', 305, 3.7, 67, 4.3),
  createData('Gingerbread', 356, 16.0, 49, 3.9),
];

export default function CustomizedTables {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 700 }} aria-label="customized table">
        <TableHead>
          <TableRow>
            <StyledTableCell>Dessert (100g serving)</StyledTableCell>
            <StyledTableCell align="right">Calories</StyledTableCell>
            <StyledTableCell align="right">Fat&nbsp;(g)</StyledTableCell>
            <StyledTableCell align="right">Carbs&nbsp;(g)</StyledTableCell>
            <StyledTableCell align="right">Protein&nbsp;(g)</StyledTableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <StyledTableRow key={row.name}>
              <StyledTableCell component="th" scope="row">
                {row.name}
              </StyledTableCell>
              <StyledTableCell align="right">{row.calories}</StyledTableCell>
              <StyledTableCell align="right">{row.fat}</StyledTableCell>
              <StyledTableCell align="right">{row.carbs}</StyledTableCell>
              <StyledTableCell align="right">{row.protein}</StyledTableCell>
            </StyledTableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const StyledTableCell \= styled(TableCell)(({ theme }) \=\> ({
 \[\`\&.${tableCellClasses.head}\`]: {
 backgroundColor: theme.palette.common.black,
 color: theme.palette.common.white,
 },
 \[\`\&.${tableCellClasses.body}\`]: {
 fontSize: 14,
 },
}));

const StyledTableRow \= styled(TableRow)(({ theme }) \=\> ({
 '\&:nth\-of\-type(odd)': {
 backgroundColor: theme.palette.action.hover,
 },
 // hide last border
 '\&:last\-child td, \&:last\-child th': {
 border: 0,
 },
}));

function createData(
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
) {
 return { name, calories, fat, carbs, protein };
}

const rows \= \[
 createData('Frozen yoghurt', 159, 6\.0, 24, 4\.0\),
 createData('Ice cream sandwich', 237, 9\.0, 37, 4\.3\),
 createData('Eclair', 262, 16\.0, 24, 6\.0\),
 createData('Cupcake', 305, 3\.7, 67, 4\.3\),
 createData('Gingerbread', 356, 16\.0, 49, 3\.9\),
];

export default function CustomizedTables {
 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 700 }} aria\-label\="customized table"\>
 \<TableHead\>
 \<TableRow\>
 \<StyledTableCell\>Dessert (100g serving)\</StyledTableCell\>
 \<StyledTableCell align\="right"\>Calories\</StyledTableCell\>
 \<StyledTableCell align\="right"\>Fat\&nbsp;(g)\</StyledTableCell\>
 \<StyledTableCell align\="right"\>Carbs\&nbsp;(g)\</StyledTableCell\>
 \<StyledTableCell align\="right"\>Protein\&nbsp;(g)\</StyledTableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<StyledTableRow key\={row.name}\>
 \<StyledTableCell component\="th" scope\="row"\>
 {row.name}
 \</StyledTableCell\>
 \<StyledTableCell align\="right"\>{row.calories}\</StyledTableCell\>
 \<StyledTableCell align\="right"\>{row.fat}\</StyledTableCell\>
 \<StyledTableCell align\="right"\>{row.carbs}\</StyledTableCell\>
 \<StyledTableCell align\="right"\>{row.protein}\</StyledTableCell\>
 \</StyledTableRow\>
 ))}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by Carbon### Custom pagination options

It's possible to customize the options shown in the "Rows per page" select using the `rowsPerPageOptions` prop.
You should either provide an array of:


* **numbers**, each number will be used for the option's label and value.



```
<TablePagination rowsPerPageOptions={[10, 50]} />

```
CopyCopied(or Ctrl \+ C)
* **objects**, the `value` and `label` keys will be used respectively for the value and label of the option (useful for language strings such as 'All').



```
<TablePagination rowsPerPageOptions={[10, 50, { value: -1, label: 'All' }]} />

```
CopyCopied(or Ctrl \+ C)


### Custom pagination actions

The `ActionsComponent` prop of the `TablePagination` component allows the implementation of custom actions.




| Frozen yoghurt | 159 | 6 |
| --- | --- | --- |
| Ice cream sandwich | 237 | 9 |
| Eclair | 262 | 16 |
| Cupcake | 305 | 3\.7 |
| Marshmallow | 318 | 0 |
| Rows per page:51025All1–5 of 13 | | |
| --- | --- | --- |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableFooter from '@mui/material/TableFooter';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import FirstPageIcon from '@mui/icons-material/FirstPage';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import LastPageIcon from '@mui/icons-material/LastPage';

interface TablePaginationActionsProps {
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (
    event: React.MouseEvent<HTMLButtonElement>,
    newPage: number,
  ) => void;
}

function TablePaginationActions(props: TablePaginationActionsProps) {
  const theme = useTheme;
  const { count, page, rowsPerPage, onPageChange } = props;

  const handleFirstPageButtonClick = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    onPageChange(event, 0);
  };

  const handleBackButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onPageChange(event, page + 1);
  };

  const handleLastPageButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton
        onClick={handleFirstPageButtonClick}
        disabled={page === 0}
        aria-label="first page"
      >
        {theme.direction === 'rtl' ? <LastPageIcon /> : <FirstPageIcon />}
      </IconButton>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 0}
        aria-label="previous page"
      >
        {theme.direction === 'rtl' ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="next page"
      >
        {theme.direction === 'rtl' ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
      </IconButton>
      <IconButton
        onClick={handleLastPageButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="last page"
      >
        {theme.direction === 'rtl' ? <FirstPageIcon /> : <LastPageIcon />}
      </IconButton>
    </Box>
  );
}

function createData(name: string, calories: number, fat: number) {
  return { name, calories, fat };
}

const rows = [
  createData('Cupcake', 305, 3.7),
  createData('Donut', 452, 25.0),
  createData('Eclair', 262, 16.0),
  createData('Frozen yoghurt', 159, 6.0),
  createData('Gingerbread', 356, 16.0),
  createData('Honeycomb', 408, 3.2),
  createData('Ice cream sandwich', 237, 9.0),
  createData('Jelly Bean', 375, 0.0),
  createData('KitKat', 518, 26.0),
  createData('Lollipop', 392, 0.2),
  createData('Marshmallow', 318, 0),
  createData('Nougat', 360, 19.0),
  createData('Oreo', 437, 18.0),
].sort((a, b) => (a.calories < b.calories ? -1 : 1));

export default function CustomPaginationActionsTable {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows =
    page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

  const handleChangePage = (
    event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number,
  ) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 500 }} aria-label="custom pagination table">
        <TableBody>
          {(rowsPerPage > 0
            ? rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
            : rows
          ).map((row) => (
            <TableRow key={row.name}>
              <TableCell component="th" scope="row">
                {row.name}
              </TableCell>
              <TableCell style={{ width: 160 }} align="right">
                {row.calories}
              </TableCell>
              <TableCell style={{ width: 160 }} align="right">
                {row.fat}
              </TableCell>
            </TableRow>
          ))}
          {emptyRows > 0 && (
            <TableRow style={{ height: 53 * emptyRows }}>
              <TableCell colSpan={6} />
            </TableRow>
          )}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: 'All', value: -1 }]}
              colSpan={3}
              count={rows.length}
              rowsPerPage={rowsPerPage}
              page={page}
              slotProps={{
                select: {
                  inputProps: {
                    'aria-label': 'rows per page',
                  },
                  native: true,
                },
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              ActionsComponent={TablePaginationActions}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableFooter from '@mui/material/TableFooter';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import FirstPageIcon from '@mui/icons\-material/FirstPage';
import KeyboardArrowLeft from '@mui/icons\-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons\-material/KeyboardArrowRight';
import LastPageIcon from '@mui/icons\-material/LastPage';

interface TablePaginationActionsProps {
 count: number;
 page: number;
 rowsPerPage: number;
 onPageChange: (
 event: React.MouseEvent\<HTMLButtonElement\>,
 newPage: number,
 ) \=\> void;
}

function TablePaginationActions(props: TablePaginationActionsProps) {
 const theme \= useTheme;
 const { count, page, rowsPerPage, onPageChange } \= props;

 const handleFirstPageButtonClick \= (
 event: React.MouseEvent\<HTMLButtonElement\>,
 ) \=\> {
 onPageChange(event, 0\);
 };

 const handleBackButtonClick \= (event: React.MouseEvent\<HTMLButtonElement\>) \=\> {
 onPageChange(event, page \- 1\);
 };

 const handleNextButtonClick \= (event: React.MouseEvent\<HTMLButtonElement\>) \=\> {
 onPageChange(event, page \+ 1\);
 };

 const handleLastPageButtonClick \= (event: React.MouseEvent\<HTMLButtonElement\>) \=\> {
 onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) \- 1\));
 };

 return (
 \<Box sx\={{ flexShrink: 0, ml: 2\.5 }}\>
 \<IconButton
 onClick\={handleFirstPageButtonClick}
 disabled\={page \=\=\= 0}
 aria\-label\="first page"
 \>
 {theme.direction \=\=\= 'rtl' ? \<LastPageIcon /\> : \<FirstPageIcon /\>}
 \</IconButton\>
 \<IconButton
 onClick\={handleBackButtonClick}
 disabled\={page \=\=\= 0}
 aria\-label\="previous page"
 \>
 {theme.direction \=\=\= 'rtl' ? \<KeyboardArrowRight /\> : \<KeyboardArrowLeft /\>}
 \</IconButton\>
 \<IconButton
 onClick\={handleNextButtonClick}
 disabled\={page \>\= Math.ceil(count / rowsPerPage) \- 1}
 aria\-label\="next page"
 \>
 {theme.direction \=\=\= 'rtl' ? \<KeyboardArrowLeft /\> : \<KeyboardArrowRight /\>}
 \</IconButton\>
 \<IconButton
 onClick\={handleLastPageButtonClick}
 disabled\={page \>\= Math.ceil(count / rowsPerPage) \- 1}
 aria\-label\="last page"
 \>
 {theme.direction \=\=\= 'rtl' ? \<FirstPageIcon /\> : \<LastPageIcon /\>}
 \</IconButton\>
 \</Box\>
 );
}

function createData(name: string, calories: number, fat: number) {
 return { name, calories, fat };
}

const rows \= \[
 createData('Cupcake', 305, 3\.7\),
 createData('Donut', 452, 25\.0\),
 createData('Eclair', 262, 16\.0\),
 createData('Frozen yoghurt', 159, 6\.0\),
 createData('Gingerbread', 356, 16\.0\),
 createData('Honeycomb', 408, 3\.2\),
 createData('Ice cream sandwich', 237, 9\.0\),
 createData('Jelly Bean', 375, 0\.0\),
 createData('KitKat', 518, 26\.0\),
 createData('Lollipop', 392, 0\.2\),
 createData('Marshmallow', 318, 0\),
 createData('Nougat', 360, 19\.0\),
 createData('Oreo', 437, 18\.0\),
].sort((a, b) \=\> (a.calories \< b.calories ? \-1 : 1\));

export default function CustomPaginationActionsTable {
 const \[page, setPage] \= React.useState(0\);
 const \[rowsPerPage, setRowsPerPage] \= React.useState(5\);

 // Avoid a layout jump when reaching the last page with empty rows.
 const emptyRows \=
 page \> 0 ? Math.max(0, (1 \+ page) \* rowsPerPage \- rows.length) : 0;

 const handleChangePage \= (
 event: React.MouseEvent\<HTMLButtonElement\> \| null,
 newPage: number,
 ) \=\> {
 setPage(newPage);
 };

 const handleChangeRowsPerPage \= (
 event: React.ChangeEvent\<HTMLInputElement \| HTMLTextAreaElement\>,
 ) \=\> {
 setRowsPerPage(parseInt(event.target.value, 10\));
 setPage(0\);
 };

 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 500 }} aria\-label\="custom pagination table"\>
 \<TableBody\>
 {(rowsPerPage \> 0
 ? rows.slice(page \* rowsPerPage, page \* rowsPerPage \+ rowsPerPage)
 : rows
 ).map((row) \=\> (
 \<TableRow key\={row.name}\>
 \<TableCell component\="th" scope\="row"\>
 {row.name}
 \</TableCell\>
 \<TableCell style\={{ width: 160 }} align\="right"\>
 {row.calories}
 \</TableCell\>
 \<TableCell style\={{ width: 160 }} align\="right"\>
 {row.fat}
 \</TableCell\>
 \</TableRow\>
 ))}
 {emptyRows \> 0 \&\& (
 \<TableRow style\={{ height: 53 \* emptyRows }}\>
 \<TableCell colSpan\={6} /\>
 \</TableRow\>
 )}
 \</TableBody\>
 \<TableFooter\>
 \<TableRow\>
 \<TablePagination
 rowsPerPageOptions\={\[5, 10, 25, { label: 'All', value: \-1 }]}
 colSpan\={3}
 count\={rows.length}
 rowsPerPage\={rowsPerPage}
 page\={page}
 slotProps\={{
 select: {
 inputProps: {
 'aria\-label': 'rows per page',
 },
 native: true,
 },
 }}
 onPageChange\={handleChangePage}
 onRowsPerPageChange\={handleChangeRowsPerPage}
 ActionsComponent\={TablePaginationActions}
 /\>
 \</TableRow\>
 \</TableFooter\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Gitlab** \- If you haven't started using AI in your dev process, it's time. If you have, it's time to accelerate.ad by CarbonSticky header
-------------

Here is an example of a table with scrollable rows and fixed column headers.
It leverages the `stickyHeader` prop.




| Name | ISO Code | Population | Size (km²) | Density |
| --- | --- | --- | --- | --- |
| India | IN | 1,324,171,354 | 3,287,263 | 402\.82 |
| China | CN | 1,403,500,365 | 9,596,961 | 146\.24 |
| Italy | IT | 60,483,973 | 301,340 | 200\.72 |
| United States | US | 327,167,434 | 9,833,520 | 33\.27 |
| Canada | CA | 37,602,103 | 9,984,670 | 3\.77 |
| Australia | AU | 25,475,400 | 7,692,024 | 3\.31 |
| Germany | DE | 83,019,200 | 357,578 | 232\.17 |
| Ireland | IE | 4,857,000 | 70,273 | 69\.12 |
| Mexico | MX | 126,577,691 | 1,972,550 | 64\.17 |
| Japan | JP | 126,317,000 | 377,973 | 334\.20 |

Rows per page:

101–10 of 15

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';

interface Column {
  id: 'name' | 'code' | 'population' | 'size' | 'density';
  label: string;
  minWidth?: number;
  align?: 'right';
  format?: (value: number) => string;
}

const columns: readonly Column[] = [
  { id: 'name', label: 'Name', minWidth: 170 },
  { id: 'code', label: 'ISO\u00a0Code', minWidth: 100 },
  {
    id: 'population',
    label: 'Population',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toLocaleString('en-US'),
  },
  {
    id: 'size',
    label: 'Size\u00a0(km\u00b2)',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toLocaleString('en-US'),
  },
  {
    id: 'density',
    label: 'Density',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toFixed(2),
  },
];

interface Data {
  name: string;
  code: string;
  population: number;
  size: number;
  density: number;
}

function createData(
  name: string,
  code: string,
  population: number,
  size: number,
): Data {
  const density = population / size;
  return { name, code, population, size, density };
}

const rows = [
  createData('India', 'IN', 1324171354, 3287263),
  createData('China', 'CN', 1403500365, 9596961),
  createData('Italy', 'IT', 60483973, 301340),
  createData('United States', 'US', *********, 9833520),
  createData('Canada', 'CA', 37602103, 9984670),
  createData('Australia', 'AU', 25475400, 7692024),
  createData('Germany', 'DE', 83019200, 357578),
  createData('Ireland', 'IE', 4857000, 70273),
  createData('Mexico', 'MX', *********, 1972550),
  createData('Japan', 'JP', *********, 377973),
  createData('France', 'FR', 67022000, 640679),
  createData('United Kingdom', 'GB', 67545757, 242495),
  createData('Russia', 'RU', *********, 17098246),
  createData('Nigeria', 'NG', *********, 923768),
  createData('Brazil', 'BR', *********, 8515767),
];

export default function StickyHeadTable {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row) => {
                return (
                  <TableRow hover role="checkbox" tabIndex={-1} key={row.code}>
                    {columns.map((column) => {
                      const value = row[column.id];
                      return (
                        <TableCell key={column.id} align={column.align}>
                          {column.format && typeof value === 'number'
                            ? column.format(value)
                            : value}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 100]}
        component="div"
        count={rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}  

```
import \* as React from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';

interface Column {
 id: 'name' \| 'code' \| 'population' \| 'size' \| 'density';
 label: string;
 minWidth?: number;
 align?: 'right';
 format?: (value: number) \=\> string;
}

const columns: readonly Column\[] \= \[
 { id: 'name', label: 'Name', minWidth: 170 },
 { id: 'code', label: 'ISO\\u00a0Code', minWidth: 100 },
 {
 id: 'population',
 label: 'Population',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toLocaleString('en\-US'),
 },
 {
 id: 'size',
 label: 'Size\\u00a0(km\\u00b2\)',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toLocaleString('en\-US'),
 },
 {
 id: 'density',
 label: 'Density',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toFixed(2\),
 },
];

interface Data {
 name: string;
 code: string;
 population: number;
 size: number;
 density: number;
}

function createData(
 name: string,
 code: string,
 population: number,
 size: number,
): Data {
 const density \= population / size;
 return { name, code, population, size, density };
}

const rows \= \[
 createData('India', 'IN', 1324171354, 3287263\),
 createData('China', 'CN', 1403500365, 9596961\),
 createData('Italy', 'IT', 60483973, 301340\),
 createData('United States', 'US', *********, 9833520\),
 createData('Canada', 'CA', 37602103, 9984670\),
 createData('Australia', 'AU', 25475400, 7692024\),
 createData('Germany', 'DE', 83019200, 357578\),
 createData('Ireland', 'IE', 4857000, 70273\),
 createData('Mexico', 'MX', *********, 1972550\),
 createData('Japan', 'JP', *********, 377973\),
 createData('France', 'FR', 67022000, 640679\),
 createData('United Kingdom', 'GB', 67545757, 242495\),
 createData('Russia', 'RU', *********, 17098246\),
 createData('Nigeria', 'NG', *********, 923768\),
 createData('Brazil', 'BR', *********, 8515767\),
];

export default function StickyHeadTable {
 const \[page, setPage] \= React.useState(0\);
 const \[rowsPerPage, setRowsPerPage] \= React.useState(10\);

 const handleChangePage \= (event: unknown, newPage: number) \=\> {
 setPage(newPage);
 };

 const handleChangeRowsPerPage \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setRowsPerPage(\+event.target.value);
 setPage(0\);
 };

 return (
 \<Paper sx\={{ width: '100%', overflow: 'hidden' }}\>
 \<TableContainer sx\={{ maxHeight: 440 }}\>
 \<Table stickyHeader aria\-label\="sticky table"\>
 \<TableHead\>
 \<TableRow\>
 {columns.map((column) \=\> (
 \<TableCell
 key\={column.id}
 align\={column.align}
 style\={{ minWidth: column.minWidth }}
 \>
 {column.label}
 \</TableCell\>
 ))}
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows
 .slice(page \* rowsPerPage, page \* rowsPerPage \+ rowsPerPage)
 .map((row) \=\> {
 return (
 \<TableRow hover role\="checkbox" tabIndex\={\-1} key\={row.code}\>
 {columns.map((column) \=\> {
 const value \= row\[column.id];
 return (
 \<TableCell key\={column.id} align\={column.align}\>
 {column.format \&\& typeof value \=\=\= 'number'
 ? column.format(value)
 : value}
 \</TableCell\>
 );
 })}
 \</TableRow\>
 );
 })}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 \<TablePagination
 rowsPerPageOptions\={\[10, 25, 100]}
 component\="div"
 count\={rows.length}
 rowsPerPage\={rowsPerPage}
 page\={page}
 onPageChange\={handleChangePage}
 onRowsPerPageChange\={handleChangeRowsPerPage}
 /\>
 \</Paper\>
 );
}Press `Enter` to start editing**GetStream.io** \- Real\-time communication APIs loved by devs. Start building your proof of concept. No CC requiredad by CarbonColumn grouping
---------------

You can group column headers by rendering multiple table rows inside a table head:



```
<TableHead>
  <TableRow />
  <TableRow />
</TableHead>

```
CopyCopied(or Ctrl \+ C)


| Country | | Details | | |
| --- | --- | --- | --- | --- |
| Name | ISO Code | Population | Size (km²) | Density |
| India | IN | 1,324,171,354 | 3,287,263 | 402\.82 |
| China | CN | 1,403,500,365 | 9,596,961 | 146\.24 |
| Italy | IT | 60,483,973 | 301,340 | 200\.72 |
| United States | US | 327,167,434 | 9,833,520 | 33\.27 |
| Canada | CA | 37,602,103 | 9,984,670 | 3\.77 |
| Australia | AU | 25,475,400 | 7,692,024 | 3\.31 |
| Germany | DE | 83,019,200 | 357,578 | 232\.17 |
| Ireland | IE | 4,857,000 | 70,273 | 69\.12 |
| Mexico | MX | 126,577,691 | 1,972,550 | 64\.17 |
| Japan | JP | 126,317,000 | 377,973 | 334\.20 |

Rows per page:

101–10 of 15

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';

interface Column {
  id: 'name' | 'code' | 'population' | 'size' | 'density';
  label: string;
  minWidth?: number;
  align?: 'right';
  format?: (value: number) => string;
}

const columns: Column[] = [
  { id: 'name', label: 'Name', minWidth: 170 },
  { id: 'code', label: 'ISO\u00a0Code', minWidth: 100 },
  {
    id: 'population',
    label: 'Population',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toLocaleString('en-US'),
  },
  {
    id: 'size',
    label: 'Size\u00a0(km\u00b2)',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toLocaleString('en-US'),
  },
  {
    id: 'density',
    label: 'Density',
    minWidth: 170,
    align: 'right',
    format: (value: number) => value.toFixed(2),
  },
];

interface Data {
  name: string;
  code: string;
  population: number;
  size: number;
  density: number;
}

function createData(
  name: string,
  code: string,
  population: number,
  size: number,
): Data {
  const density = population / size;
  return { name, code, population, size, density };
}

const rows = [
  createData('India', 'IN', 1324171354, 3287263),
  createData('China', 'CN', 1403500365, 9596961),
  createData('Italy', 'IT', 60483973, 301340),
  createData('United States', 'US', *********, 9833520),
  createData('Canada', 'CA', 37602103, 9984670),
  createData('Australia', 'AU', 25475400, 7692024),
  createData('Germany', 'DE', 83019200, 357578),
  createData('Ireland', 'IE', 4857000, 70273),
  createData('Mexico', 'MX', *********, 1972550),
  createData('Japan', 'JP', *********, 377973),
  createData('France', 'FR', 67022000, 640679),
  createData('United Kingdom', 'GB', 67545757, 242495),
  createData('Russia', 'RU', *********, 17098246),
  createData('Nigeria', 'NG', *********, 923768),
  createData('Brazil', 'BR', *********, 8515767),
];

export default function ColumnGroupingTable {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  return (
    <Paper sx={{ width: '100%' }}>
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              <TableCell align="center" colSpan={2}>
                Country
              </TableCell>
              <TableCell align="center" colSpan={3}>
                Details
              </TableCell>
            </TableRow>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{ top: 57, minWidth: column.minWidth }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row) => {
                return (
                  <TableRow hover role="checkbox" tabIndex={-1} key={row.code}>
                    {columns.map((column) => {
                      const value = row[column.id];
                      return (
                        <TableCell key={column.id} align={column.align}>
                          {column.format && typeof value === 'number'
                            ? column.format(value)
                            : value}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 100]}
        component="div"
        count={rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}  

```
import \* as React from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';

interface Column {
 id: 'name' \| 'code' \| 'population' \| 'size' \| 'density';
 label: string;
 minWidth?: number;
 align?: 'right';
 format?: (value: number) \=\> string;
}

const columns: Column\[] \= \[
 { id: 'name', label: 'Name', minWidth: 170 },
 { id: 'code', label: 'ISO\\u00a0Code', minWidth: 100 },
 {
 id: 'population',
 label: 'Population',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toLocaleString('en\-US'),
 },
 {
 id: 'size',
 label: 'Size\\u00a0(km\\u00b2\)',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toLocaleString('en\-US'),
 },
 {
 id: 'density',
 label: 'Density',
 minWidth: 170,
 align: 'right',
 format: (value: number) \=\> value.toFixed(2\),
 },
];

interface Data {
 name: string;
 code: string;
 population: number;
 size: number;
 density: number;
}

function createData(
 name: string,
 code: string,
 population: number,
 size: number,
): Data {
 const density \= population / size;
 return { name, code, population, size, density };
}

const rows \= \[
 createData('India', 'IN', 1324171354, 3287263\),
 createData('China', 'CN', 1403500365, 9596961\),
 createData('Italy', 'IT', 60483973, 301340\),
 createData('United States', 'US', *********, 9833520\),
 createData('Canada', 'CA', 37602103, 9984670\),
 createData('Australia', 'AU', 25475400, 7692024\),
 createData('Germany', 'DE', 83019200, 357578\),
 createData('Ireland', 'IE', 4857000, 70273\),
 createData('Mexico', 'MX', *********, 1972550\),
 createData('Japan', 'JP', *********, 377973\),
 createData('France', 'FR', 67022000, 640679\),
 createData('United Kingdom', 'GB', 67545757, 242495\),
 createData('Russia', 'RU', *********, 17098246\),
 createData('Nigeria', 'NG', *********, 923768\),
 createData('Brazil', 'BR', *********, 8515767\),
];

export default function ColumnGroupingTable {
 const \[page, setPage] \= React.useState(0\);
 const \[rowsPerPage, setRowsPerPage] \= React.useState(10\);

 const handleChangePage \= (event: unknown, newPage: number) \=\> {
 setPage(newPage);
 };

 const handleChangeRowsPerPage \= (event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setRowsPerPage(\+event.target.value);
 setPage(0\);
 };

 return (
 \<Paper sx\={{ width: '100%' }}\>
 \<TableContainer sx\={{ maxHeight: 440 }}\>
 \<Table stickyHeader aria\-label\="sticky table"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell align\="center" colSpan\={2}\>
 Country
 \</TableCell\>
 \<TableCell align\="center" colSpan\={3}\>
 Details
 \</TableCell\>
 \</TableRow\>
 \<TableRow\>
 {columns.map((column) \=\> (
 \<TableCell
 key\={column.id}
 align\={column.align}
 style\={{ top: 57, minWidth: column.minWidth }}
 \>
 {column.label}
 \</TableCell\>
 ))}
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows
 .slice(page \* rowsPerPage, page \* rowsPerPage \+ rowsPerPage)
 .map((row) \=\> {
 return (
 \<TableRow hover role\="checkbox" tabIndex\={\-1} key\={row.code}\>
 {columns.map((column) \=\> {
 const value \= row\[column.id];
 return (
 \<TableCell key\={column.id} align\={column.align}\>
 {column.format \&\& typeof value \=\=\= 'number'
 ? column.format(value)
 : value}
 \</TableCell\>
 );
 })}
 \</TableRow\>
 );
 })}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 \<TablePagination
 rowsPerPageOptions\={\[10, 25, 100]}
 component\="div"
 count\={rows.length}
 rowsPerPage\={rowsPerPage}
 page\={page}
 onPageChange\={handleChangePage}
 onRowsPerPageChange\={handleChangeRowsPerPage}
 /\>
 \</Paper\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by CarbonCollapsible table
-----------------

An example of a table with expandable rows, revealing more information.
It utilizes the `Collapse` component.




|  | Dessert (100g serving) | Calories | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- | --- |
|  | Frozen yoghurt | 159 | 6 | 24 | 4 |
|  | | | | | |
|  | Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
|  | | | | | |
|  | Eclair | 262 | 16 | 24 | 6 |
|  | | | | | |
|  | Cupcake | 305 | 3\.7 | 67 | 4\.3 |
|  | | | | | |
|  | Gingerbread | 356 | 16 | 49 | 3\.9 |
|  | | | | | |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Collapse from '@mui/material/Collapse';
import IconButton from '@mui/material/IconButton';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

function createData(
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
  price: number,
) {
  return {
    name,
    calories,
    fat,
    carbs,
    protein,
    price,
    history: [
      {
        date: '2020-01-05',
        customerId: '11091700',
        amount: 3,
      },
      {
        date: '2020-01-02',
        customerId: 'Anonymous',
        amount: 1,
      },
    ],
  };
}

function Row(props: { row: ReturnType<typeof createData> }) {
  const { row } = props;
  const [open, setOpen] = React.useState(false);

  return (
    <React.Fragment>
      <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={ => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {row.name}
        </TableCell>
        <TableCell align="right">{row.calories}</TableCell>
        <TableCell align="right">{row.fat}</TableCell>
        <TableCell align="right">{row.carbs}</TableCell>
        <TableCell align="right">{row.protein}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                History
              </Typography>
              <Table size="small" aria-label="purchases">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell align="right">Total price ($)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {row.history.map((historyRow) => (
                    <TableRow key={historyRow.date}>
                      <TableCell component="th" scope="row">
                        {historyRow.date}
                      </TableCell>
                      <TableCell>{historyRow.customerId}</TableCell>
                      <TableCell align="right">{historyRow.amount}</TableCell>
                      <TableCell align="right">
                        {Math.round(historyRow.amount * row.price * 100) / 100}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}
const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0, 3.99),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3, 4.99),
  createData('Eclair', 262, 16.0, 24, 6.0, 3.79),
  createData('Cupcake', 305, 3.7, 67, 4.3, 2.5),
  createData('Gingerbread', 356, 16.0, 49, 3.9, 1.5),
];
export default function CollapsibleTable {
  return (
    <TableContainer component={Paper}>
      <Table aria-label="collapsible table">
        <TableHead>
          <TableRow>
            <TableCell />
            <TableCell>Dessert (100g serving)</TableCell>
            <TableCell align="right">Calories</TableCell>
            <TableCell align="right">Fat&nbsp;(g)</TableCell>
            <TableCell align="right">Carbs&nbsp;(g)</TableCell>
            <TableCell align="right">Protein&nbsp;(g)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <Row key={row.name} row={row} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Collapse from '@mui/material/Collapse';
import IconButton from '@mui/material/IconButton';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import KeyboardArrowDownIcon from '@mui/icons\-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons\-material/KeyboardArrowUp';

function createData(
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
 price: number,
) {
 return {
 name,
 calories,
 fat,
 carbs,
 protein,
 price,
 history: \[
 {
 date: '2020\-01\-05',
 customerId: '11091700',
 amount: 3,
 },
 {
 date: '2020\-01\-02',
 customerId: 'Anonymous',
 amount: 1,
 },
 ],
 };
}

function Row(props: { row: ReturnType\<typeof createData\> }) {
 const { row } \= props;
 const \[open, setOpen] \= React.useState(false);

 return (
 \<React.Fragment\>
 \<TableRow sx\={{ '\& \> \*': { borderBottom: 'unset' } }}\>
 \<TableCell\>
 \<IconButton
 aria\-label\="expand row"
 size\="small"
 onClick\={ \=\> setOpen(!open)}
 \>
 {open ? \<KeyboardArrowUpIcon /\> : \<KeyboardArrowDownIcon /\>}
 \</IconButton\>
 \</TableCell\>
 \<TableCell component\="th" scope\="row"\>
 {row.name}
 \</TableCell\>
 \<TableCell align\="right"\>{row.calories}\</TableCell\>
 \<TableCell align\="right"\>{row.fat}\</TableCell\>
 \<TableCell align\="right"\>{row.carbs}\</TableCell\>
 \<TableCell align\="right"\>{row.protein}\</TableCell\>
 \</TableRow\>
 \<TableRow\>
 \<TableCell style\={{ paddingBottom: 0, paddingTop: 0 }} colSpan\={6}\>
 \<Collapse in\={open} timeout\="auto" unmountOnExit\>
 \<Box sx\={{ margin: 1 }}\>
 \<Typography variant\="h6" gutterBottom component\="div"\>
 History
 \</Typography\>
 \<Table size\="small" aria\-label\="purchases"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell\>Date\</TableCell\>
 \<TableCell\>Customer\</TableCell\>
 \<TableCell align\="right"\>Amount\</TableCell\>
 \<TableCell align\="right"\>Total price ($)\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {row.history.map((historyRow) \=\> (
 \<TableRow key\={historyRow.date}\>
 \<TableCell component\="th" scope\="row"\>
 {historyRow.date}
 \</TableCell\>
 \<TableCell\>{historyRow.customerId}\</TableCell\>
 \<TableCell align\="right"\>{historyRow.amount}\</TableCell\>
 \<TableCell align\="right"\>
 {Math.round(historyRow.amount \* row.price \* 100\) / 100}
 \</TableCell\>
 \</TableRow\>
 ))}
 \</TableBody\>
 \</Table\>
 \</Box\>
 \</Collapse\>
 \</TableCell\>
 \</TableRow\>
 \</React.Fragment\>
 );
}
const rows \= \[
 createData('Frozen yoghurt', 159, 6\.0, 24, 4\.0, 3\.99\),
 createData('Ice cream sandwich', 237, 9\.0, 37, 4\.3, 4\.99\),
 createData('Eclair', 262, 16\.0, 24, 6\.0, 3\.79\),
 createData('Cupcake', 305, 3\.7, 67, 4\.3, 2\.5\),
 createData('Gingerbread', 356, 16\.0, 49, 3\.9, 1\.5\),
];
export default function CollapsibleTable {
 return (
 \<TableContainer component\={Paper}\>
 \<Table aria\-label\="collapsible table"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell /\>
 \<TableCell\>Dessert (100g serving)\</TableCell\>
 \<TableCell align\="right"\>Calories\</TableCell\>
 \<TableCell align\="right"\>Fat\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Carbs\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Protein\&nbsp;(g)\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<Row key\={row.name} row\={row} /\>
 ))}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Gitlab** \- Software. Faster. All in one platform. Give GitLab a try for free.ad by CarbonSpanning table
--------------

A simple example with spanning rows \& columns.




| Details | | | Price |
| --- | --- | --- | --- |
| Desc | Qty. | Unit | Sum |
| Paperclips (Box) | 100 | 1\.15 | 115\.00 |
| Paper (Case) | 10 | 45\.99 | 459\.90 |
| Waste Basket | 2 | 17\.99 | 35\.98 |
|  | Subtotal | | 610\.88 |
| Tax | 7 % | 42\.76 |
| Total | | 653\.64 |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const TAX_RATE = 0.07;

function ccyFormat(num: number) {
  return `${num.toFixed(2)}`;
}

function priceRow(qty: number, unit: number) {
  return qty * unit;
}

function createRow(desc: string, qty: number, unit: number) {
  const price = priceRow(qty, unit);
  return { desc, qty, unit, price };
}

interface Row {
  desc: string;
  qty: number;
  unit: number;
  price: number;
}

function subtotal(items: readonly Row[]) {
  return items.map(({ price }) => price).reduce((sum, i) => sum + i, 0);
}

const rows = [
  createRow('Paperclips (Box)', 100, 1.15),
  createRow('Paper (Case)', 10, 45.99),
  createRow('Waste Basket', 2, 17.99),
];

const invoiceSubtotal = subtotal(rows);
const invoiceTaxes = TAX_RATE * invoiceSubtotal;
const invoiceTotal = invoiceTaxes + invoiceSubtotal;

export default function SpanningTable {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 700 }} aria-label="spanning table">
        <TableHead>
          <TableRow>
            <TableCell align="center" colSpan={3}>
              Details
            </TableCell>
            <TableCell align="right">Price</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Desc</TableCell>
            <TableCell align="right">Qty.</TableCell>
            <TableCell align="right">Unit</TableCell>
            <TableCell align="right">Sum</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow key={row.desc}>
              <TableCell>{row.desc}</TableCell>
              <TableCell align="right">{row.qty}</TableCell>
              <TableCell align="right">{row.unit}</TableCell>
              <TableCell align="right">{ccyFormat(row.price)}</TableCell>
            </TableRow>
          ))}
          <TableRow>
            <TableCell rowSpan={3} />
            <TableCell colSpan={2}>Subtotal</TableCell>
            <TableCell align="right">{ccyFormat(invoiceSubtotal)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Tax</TableCell>
            <TableCell align="right">{`${(TAX_RATE * 100).toFixed(0)} %`}</TableCell>
            <TableCell align="right">{ccyFormat(invoiceTaxes)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={2}>Total</TableCell>
            <TableCell align="right">{ccyFormat(invoiceTotal)}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const TAX\_RATE \= 0\.07;

function ccyFormat(num: number) {
 return \`${num.toFixed(2\)}\`;
}

function priceRow(qty: number, unit: number) {
 return qty \* unit;
}

function createRow(desc: string, qty: number, unit: number) {
 const price \= priceRow(qty, unit);
 return { desc, qty, unit, price };
}

interface Row {
 desc: string;
 qty: number;
 unit: number;
 price: number;
}

function subtotal(items: readonly Row\[]) {
 return items.map(({ price }) \=\> price).reduce((sum, i) \=\> sum \+ i, 0\);
}

const rows \= \[
 createRow('Paperclips (Box)', 100, 1\.15\),
 createRow('Paper (Case)', 10, 45\.99\),
 createRow('Waste Basket', 2, 17\.99\),
];

const invoiceSubtotal \= subtotal(rows);
const invoiceTaxes \= TAX\_RATE \* invoiceSubtotal;
const invoiceTotal \= invoiceTaxes \+ invoiceSubtotal;

export default function SpanningTable {
 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 700 }} aria\-label\="spanning table"\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell align\="center" colSpan\={3}\>
 Details
 \</TableCell\>
 \<TableCell align\="right"\>Price\</TableCell\>
 \</TableRow\>
 \<TableRow\>
 \<TableCell\>Desc\</TableCell\>
 \<TableCell align\="right"\>Qty.\</TableCell\>
 \<TableCell align\="right"\>Unit\</TableCell\>
 \<TableCell align\="right"\>Sum\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<TableRow key\={row.desc}\>
 \<TableCell\>{row.desc}\</TableCell\>
 \<TableCell align\="right"\>{row.qty}\</TableCell\>
 \<TableCell align\="right"\>{row.unit}\</TableCell\>
 \<TableCell align\="right"\>{ccyFormat(row.price)}\</TableCell\>
 \</TableRow\>
 ))}
 \<TableRow\>
 \<TableCell rowSpan\={3} /\>
 \<TableCell colSpan\={2}\>Subtotal\</TableCell\>
 \<TableCell align\="right"\>{ccyFormat(invoiceSubtotal)}\</TableCell\>
 \</TableRow\>
 \<TableRow\>
 \<TableCell\>Tax\</TableCell\>
 \<TableCell align\="right"\>{\`${(TAX\_RATE \* 100\).toFixed(0\)} %\`}\</TableCell\>
 \<TableCell align\="right"\>{ccyFormat(invoiceTaxes)}\</TableCell\>
 \</TableRow\>
 \<TableRow\>
 \<TableCell colSpan\={2}\>Total\</TableCell\>
 \<TableCell align\="right"\>{ccyFormat(invoiceTotal)}\</TableCell\>
 \</TableRow\>
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonVirtualized table
-----------------

In the following example, we demonstrate how to use react\-virtuoso with the `Table` component.
It renders 200 rows and can easily handle more.
Virtualization helps with performance issues.




| First Name | Last Name | Age | State | Phone Number |
| --- | --- | --- | --- | --- |
| Seth | Lucas | 26 | Colorado | (775\) 614\-2403 |
| Todd | Kondo | 34 | Oklahoma | (617\) 700\-9729 |
| Ophelia | Becucci | 18 | Alabama | (381\) 636\-5722 |
| David | Colzi | 43 | Hawaii | (631\) 229\-4297 |
| Brian | Browne | 47 | Texas | (335\) 954\-1418 |
| Alberta | Patrick | 39 | Maine | (209\) 995\-8275 |
| Michael | Herrera | 29 | Tennessee | (724\) 716\-5456 |
|  |

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { TableVirtuoso, TableComponents } from 'react-virtuoso';
import Chance from 'chance';

interface Data {
  id: number;
  firstName: string;
  lastName: string;
  age: number;
  phone: string;
  state: string;
}

interface ColumnData {
  dataKey: keyof Data;
  label: string;
  numeric?: boolean;
  width?: number;
}

const chance = new Chance(42);

function createData(id: number): Data {
  return {
    id,
    firstName: chance.first,
    lastName: chance.last,
    age: chance.age,
    phone: chance.phone,
    state: chance.state({ full: true }),
  };
}

const columns: ColumnData[] = [
  {
    width: 100,
    label: 'First Name',
    dataKey: 'firstName',
  },
  {
    width: 100,
    label: 'Last Name',
    dataKey: 'lastName',
  },
  {
    width: 50,
    label: 'Age',
    dataKey: 'age',
    numeric: true,
  },
  {
    width: 110,
    label: 'State',
    dataKey: 'state',
  },
  {
    width: 130,
    label: 'Phone Number',
    dataKey: 'phone',
  },
];

const rows: Data[] = Array.from({ length: 200 }, (_, index) => createData(index));

const VirtuosoTableComponents: TableComponents<Data> = {
  Scroller: React.forwardRef<HTMLDivElement>((props, ref) => (
    <TableContainer component={Paper} {...props} ref={ref} />
  )),
  Table: (props) => (
    <Table {...props} sx={{ borderCollapse: 'separate', tableLayout: 'fixed' }} />
  ),
  TableHead: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
    <TableHead {...props} ref={ref} />
  )),
  TableRow,
  TableBody: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
    <TableBody {...props} ref={ref} />
  )),
};

function fixedHeaderContent {
  return (
    <TableRow>
      {columns.map((column) => (
        <TableCell
          key={column.dataKey}
          variant="head"
          align={column.numeric || false ? 'right' : 'left'}
          style={{ width: column.width }}
          sx={{ backgroundColor: 'background.paper' }}
        >
          {column.label}
        </TableCell>
      ))}
    </TableRow>
  );
}

function rowContent(_index: number, row: Data) {
  return (
    <React.Fragment>
      {columns.map((column) => (
        <TableCell
          key={column.dataKey}
          align={column.numeric || false ? 'right' : 'left'}
        >
          {row[column.dataKey]}
        </TableCell>
      ))}
    </React.Fragment>
  );
}

export default function ReactVirtualizedTable {
  return (
    <Paper style={{ height: 400, width: '100%' }}>
      <TableVirtuoso
        data={rows}
        components={VirtuosoTableComponents}
        fixedHeaderContent={fixedHeaderContent}
        itemContent={rowContent}
      />
    </Paper>
  );
}  

```
import \* as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { TableVirtuoso, TableComponents } from 'react\-virtuoso';
import Chance from 'chance';

interface Data {
 id: number;
 firstName: string;
 lastName: string;
 age: number;
 phone: string;
 state: string;
}

interface ColumnData {
 dataKey: keyof Data;
 label: string;
 numeric?: boolean;
 width?: number;
}

const chance \= new Chance(42\);

function createData(id: number): Data {
 return {
 id,
 firstName: chance.first,
 lastName: chance.last,
 age: chance.age,
 phone: chance.phone,
 state: chance.state({ full: true }),
 };
}

const columns: ColumnData\[] \= \[
 {
 width: 100,
 label: 'First Name',
 dataKey: 'firstName',
 },
 {
 width: 100,
 label: 'Last Name',
 dataKey: 'lastName',
 },
 {
 width: 50,
 label: 'Age',
 dataKey: 'age',
 numeric: true,
 },
 {
 width: 110,
 label: 'State',
 dataKey: 'state',
 },
 {
 width: 130,
 label: 'Phone Number',
 dataKey: 'phone',
 },
];

const rows: Data\[] \= Array.from({ length: 200 }, (\_, index) \=\> createData(index));

const VirtuosoTableComponents: TableComponents\<Data\> \= {
 Scroller: React.forwardRef\<HTMLDivElement\>((props, ref) \=\> (
 \<TableContainer component\={Paper} {...props} ref\={ref} /\>
 )),
 Table: (props) \=\> (
 \<Table {...props} sx\={{ borderCollapse: 'separate', tableLayout: 'fixed' }} /\>
 ),
 TableHead: React.forwardRef\<HTMLTableSectionElement\>((props, ref) \=\> (
 \<TableHead {...props} ref\={ref} /\>
 )),
 TableRow,
 TableBody: React.forwardRef\<HTMLTableSectionElement\>((props, ref) \=\> (
 \<TableBody {...props} ref\={ref} /\>
 )),
};

function fixedHeaderContent {
 return (
 \<TableRow\>
 {columns.map((column) \=\> (
 \<TableCell
 key\={column.dataKey}
 variant\="head"
 align\={column.numeric \|\| false ? 'right' : 'left'}
 style\={{ width: column.width }}
 sx\={{ backgroundColor: 'background.paper' }}
 \>
 {column.label}
 \</TableCell\>
 ))}
 \</TableRow\>
 );
}

function rowContent(\_index: number, row: Data) {
 return (
 \<React.Fragment\>
 {columns.map((column) \=\> (
 \<TableCell
 key\={column.dataKey}
 align\={column.numeric \|\| false ? 'right' : 'left'}
 \>
 {row\[column.dataKey]}
 \</TableCell\>
 ))}
 \</React.Fragment\>
 );
}

export default function ReactVirtualizedTable {
 return (
 \<Paper style\={{ height: 400, width: '100%' }}\>
 \<TableVirtuoso
 data\={rows}
 components\={VirtuosoTableComponents}
 fixedHeaderContent\={fixedHeaderContent}
 itemContent\={rowContent}
 /\>
 \</Paper\>
 );
}Press `Enter` to start editing**CloudBees** \- Don't just wait—conquer downtime. Leverage "The Power of Three" in CloudBees HA for faster delivery.ad by CarbonAccessibility
-------------

(WAI tutorial: 


### Caption

A caption functions like a heading for a table. Most screen readers announce the content of captions. Captions help users to find a table and understand what it's about and decide if they want to read it.




A basic table example with a caption
| Dessert (100g serving) | Calories | Fat (g) | Carbs (g) | Protein (g) |
| --- | --- | --- | --- | --- |
| Frozen yoghurt | 159 | 6 | 24 | 4 |
| Ice cream sandwich | 237 | 9 | 37 | 4\.3 |
| Eclair | 262 | 16 | 24 | 6 |

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
  name: string,
  calories: number,
  fat: number,
  carbs: number,
  protein: number,
) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData('Eclair', 262, 16.0, 24, 6.0),
];

export default function AccessibleTable {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="caption table">
        <caption>A basic table example with a caption</caption>
        <TableHead>
          <TableRow>
            <TableCell>Dessert (100g serving)</TableCell>
            <TableCell align="right">Calories</TableCell>
            <TableCell align="right">Fat&nbsp;(g)</TableCell>
            <TableCell align="right">Carbs&nbsp;(g)</TableCell>
            <TableCell align="right">Protein&nbsp;(g)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow key={row.name}>
              <TableCell component="th" scope="row">
                {row.name}
              </TableCell>
              <TableCell align="right">{row.calories}</TableCell>
              <TableCell align="right">{row.fat}</TableCell>
              <TableCell align="right">{row.carbs}</TableCell>
              <TableCell align="right">{row.protein}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}  

```
import \* as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

function createData(
 name: string,
 calories: number,
 fat: number,
 carbs: number,
 protein: number,
) {
 return { name, calories, fat, carbs, protein };
}

const rows \= \[
 createData('Frozen yoghurt', 159, 6\.0, 24, 4\.0\),
 createData('Ice cream sandwich', 237, 9\.0, 37, 4\.3\),
 createData('Eclair', 262, 16\.0, 24, 6\.0\),
];

export default function AccessibleTable {
 return (
 \<TableContainer component\={Paper}\>
 \<Table sx\={{ minWidth: 650 }} aria\-label\="caption table"\>
 \<caption\>A basic table example with a caption\</caption\>
 \<TableHead\>
 \<TableRow\>
 \<TableCell\>Dessert (100g serving)\</TableCell\>
 \<TableCell align\="right"\>Calories\</TableCell\>
 \<TableCell align\="right"\>Fat\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Carbs\&nbsp;(g)\</TableCell\>
 \<TableCell align\="right"\>Protein\&nbsp;(g)\</TableCell\>
 \</TableRow\>
 \</TableHead\>
 \<TableBody\>
 {rows.map((row) \=\> (
 \<TableRow key\={row.name}\>
 \<TableCell component\="th" scope\="row"\>
 {row.name}
 \</TableCell\>
 \<TableCell align\="right"\>{row.calories}\</TableCell\>
 \<TableCell align\="right"\>{row.fat}\</TableCell\>
 \<TableCell align\="right"\>{row.carbs}\</TableCell\>
 \<TableCell align\="right"\>{row.protein}\</TableCell\>
 \</TableRow\>
 ))}
 \</TableBody\>
 \</Table\>
 \</TableContainer\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by CarbonUnstyled
--------

If you would like to use an unstyled Table, you can use the primitive HTML elements and enhance the table with the TablePaginationUnstyled component.
See the demos in the unstyled table pagination docs


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Table />`
* `<TableBody />`
* `<TableCell />`
* `<TableContainer />`
* `<TableFooter />`
* `<TableHead />`
* `<TablePagination />`
* `<TableRow />`
* `<TableSortLabel />`



