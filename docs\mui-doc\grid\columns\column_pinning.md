Data Grid \- Column pinning
===========================

Pin columns to keep them visible at all time.


Pinned (or frozen, locked, or sticky) columns are columns that are visible at all time while the user scrolls the Data Grid horizontally.
They can be pinned either to the left or right side and cannot be reordered.


To pin a column, there are a few ways:


* Using the `initialState` prop
* Controlling the `pinnedColumns` and `onPinnedColumnsChange` props
* Dedicated buttons in the column menu
* Accessing the imperative API


Initializing the pinned columns
-------------------------------

To set pinned columns via `initialState`, pass an object with the following shape to this prop:



```
interface GridPinnedColumnFields {
  left?: string[]; // Optional field names to pin to the left
  right?: string[]; // Optional field names to pin to the right
}

```
CopyCopied(or Ctrl \+ C)
The following demos illustrates how this approach works:


NameEmailAgeDate <NAME_EMAIL>252025/2/152025/5/27 14:22:47R<PERSON><PERSON>@ri.ba362025/1/222025/5/27 19:20:33<PERSON><PERSON><PERSON>@guewolo.io192024/7/82025/5/28 00:24:01Zachary Ricedaz@uvsenin.sa282024/6/272025/5/28 05:24:09Daisy Maxwellaljopova@tade.eu232025/3/192025/5/27 15:43:26Jeffery Salazarhig@je.de272025/5/252025/5/27 14:43:43Sophia McDanielova@cavcu.hn182025/2/212025/5/27 22:01:35Glen Sotojoewa@vegki.so312025/1/172025/5/28 12:02:16Oscar Evansguira@danemzag.ly242025/5/252025/5/28 04:05:11Total Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  DataGridPro,
  GridColDef,
  GridRowsProp,
  GridActionsCellItem,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomTraderName,
  randomEmail,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';

export default function BasicColumnPinning {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        rows={rows}
        columns={columns}
        initialState={{ pinnedColumns: { left: ['name'], right: ['actions'] } }}
      />
    </div>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 160, editable: true },
  { field: 'email', headerName: 'Email', width: 200, editable: true },
  { field: 'age', headerName: 'Age', type: 'number', editable: true },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
    editable: true,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
    editable: true,
  },
  {
    field: 'actions',
    type: 'actions',
    width: 100,
    getActions:  => [
      <GridActionsCellItem icon={<EditIcon />} label="Edit" />,
      <GridActionsCellItem icon={<DeleteIcon />} label="Delete" />,
    ],
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    email: randomEmail,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    email: randomEmail,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    email: randomEmail,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    email: randomEmail,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    email: randomEmail,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 6,
    name: randomTraderName,
    email: randomEmail,
    age: 27,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 7,
    name: randomTraderName,
    email: randomEmail,
    age: 18,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 8,
    name: randomTraderName,
    email: randomEmail,
    age: 31,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 9,
    name: randomTraderName,
    email: randomEmail,
    age: 24,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 10,
    name: randomTraderName,
    email: randomEmail,
    age: 35,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import DeleteIcon from '@mui/icons\-material/Delete';
import EditIcon from '@mui/icons\-material/Edit';
import {
 DataGridPro,
 GridColDef,
 GridRowsProp,
 GridActionsCellItem,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomTraderName,
 randomEmail,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';

export default function BasicColumnPinning {
 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 initialState\={{ pinnedColumns: { left: \['name'], right: \['actions'] } }}
 /\>
 \</div\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 160, editable: true },
 { field: 'email', headerName: 'Email', width: 200, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
 {
 field: 'actions',
 type: 'actions',
 width: 100,
 getActions:  \=\> \[
 \<GridActionsCellItem icon\={\<EditIcon /\>} label\="Edit" /\>,
 \<GridActionsCellItem icon\={\<DeleteIcon /\>} label\="Delete" /\>,
 ],
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 email: randomEmail,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 email: randomEmail,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 email: randomEmail,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 email: randomEmail,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 email: randomEmail,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 6,
 name: randomTraderName,
 email: randomEmail,
 age: 27,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 7,
 name: randomTraderName,
 email: randomEmail,
 age: 18,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 8,
 name: randomTraderName,
 email: randomEmail,
 age: 31,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 9,
 name: randomTraderName,
 email: randomEmail,
 age: 24,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 10,
 name: randomTraderName,
 email: randomEmail,
 age: 35,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editing
The column pinning feature can be completely disabled with `disableColumnPinning`.



```
<DataGridPro disableColumnPinning />

```
CopyCopied(or Ctrl \+ C)

You may encounter issues if the sum of the widths of the pinned columns is larger than the width of the Grid.
Make sure that the Data Grid can properly accommodate these columns at a minimum.


Controlling the pinned columns
------------------------------

While the `initialState` prop only works for setting pinned columns during the initialization, the `pinnedColumns` prop allows you to modify which columns are pinned at any time.
The value passed to it follows the same shape from the previous approach.
Use it together with `onPinnedColumnsChange` to know when a column is pinned or unpinned.


`pinnedColumns: {"left":["name"]}`NameEmailAgeDate <NAME_EMAIL>252025/3/222025/5/28 01:28:24Amanda Medinaijinol@zukka.bv362024/6/132025/5/28 00:12:06Elizabeth Bradyvol@ovfice.nz192025/5/282025/5/27 22:03:53Sadie Gordonhis@ve.gh282025/2/232025/5/28 09:10:41Lilly Nguyenolu@tug.cy232024/12/312025/5/27 16:47:40Total Rows: 5JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGridPro,
  GridColDef,
  GridPinnedColumnFields,
  GridRowsProp,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomTraderName,
  randomEmail,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';

export default function ControlPinnedColumns {
  const [pinnedColumns, setPinnedColumns] = React.useState<GridPinnedColumnFields>({
    left: ['name'],
  });

  const handlePinnedColumnsChange = React.useCallback(
    (updatedPinnedColumns: GridPinnedColumnFields) => {
      setPinnedColumns(updatedPinnedColumns);
    },
    [],
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Alert severity="info">
        <code>pinnedColumns: {JSON.stringify(pinnedColumns)}</code>
      </Alert>
      <Box sx={{ height: 400, mt: 1 }}>
        <DataGridPro
          rows={rows}
          columns={columns}
          pinnedColumns={pinnedColumns}
          onPinnedColumnsChange={handlePinnedColumnsChange}
        />
      </Box>
    </Box>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 180, editable: true },
  { field: 'email', headerName: 'Email', width: 200, editable: true },
  { field: 'age', headerName: 'Age', type: 'number', editable: true },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
    editable: true,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
    editable: true,
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    email: randomEmail,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    email: randomEmail,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    email: randomEmail,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    email: randomEmail,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    email: randomEmail,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import {
 DataGridPro,
 GridColDef,
 GridPinnedColumnFields,
 GridRowsProp,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomTraderName,
 randomEmail,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';

export default function ControlPinnedColumns {
 const \[pinnedColumns, setPinnedColumns] \= React.useState\<GridPinnedColumnFields\>({
 left: \['name'],
 });

 const handlePinnedColumnsChange \= React.useCallback(
 (updatedPinnedColumns: GridPinnedColumnFields) \=\> {
 setPinnedColumns(updatedPinnedColumns);
 },
 \[],
 );

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Alert severity\="info"\>
 \<code\>pinnedColumns: {JSON.stringify(pinnedColumns)}\</code\>
 \</Alert\>
 \<Box sx\={{ height: 400, mt: 1 }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 pinnedColumns\={pinnedColumns}
 onPinnedColumnsChange\={handlePinnedColumnsChange}
 /\>
 \</Box\>
 \</Box\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 180, editable: true },
 { field: 'email', headerName: 'Email', width: 200, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 email: randomEmail,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 email: randomEmail,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 email: randomEmail,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 email: randomEmail,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 email: randomEmail,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editingDisable column pinning
----------------------

### For all columns

Column pinning is enabled by default, but you can easily disable this feature by setting the `disableColumnPinning` prop.



```
<DataGridPro disableColumnPinning />

```
CopyCopied(or Ctrl \+ C)
### For some columns

To disable the pinning of a single column, set the `pinnable` property in `GridColDef` to `false`.



```
<DataGridPro columns={[{ field: 'id', pinnable: false }]} /> // Default is `true`.

```
CopyCopied(or Ctrl \+ C)
### Pin non\-pinnable columns programmatically

It may be desirable to allow one or more columns to be pinned or unpinned programmatically that cannot be pinned or unpinned on the UI (that is columns for which prop `disableColumnPinning = true` or `colDef.pinnable = false`).
This can be done in one of the following ways.


* (A) Initializing the pinned columns
* (B) Controlling the pinned columns
* (C) Using the API method `setPinnedColumns` to set the pinned columns



```
// (A) Initializing the pinning
<DataGridPro
  disableColumnPinning
  initialState={{ pinnedColumns: { left: ['name'] } }}
/>

// (B) Controlling the pinned columns
<DataGridPro
  disableColumnPinning
  pinnedColumns={{ left: ['name'] }}
/>

// (C) Using the API method `setPinnedColumns` to set the pinned columns
<React.Fragment>
  <DataGridPro disableColumnPinning />
  <Button onClick={ => apiRef.current.setPinnedColumns({ left: ['name'] })}>
    Pin name column
  </Button>
</React.Fragment>

```
CopyCopied(or Ctrl \+ C)
The following demo uses method (A) to initialize the state of the pinned columns which pins a column `name` although the pinning feature is disabled.


NameEmailAgeDate <NAME_EMAIL>252024/9/22025/5/28 04:47:47Roxie Josephgulasid@misufu.bi362025/1/102025/5/28 01:22:48Christopher Garrettzuta@pizbe.lu192024/11/162025/5/27 14:06:00Mildred Hubbardwof@kasuc.ws282025/4/42025/5/27 21:21:38Alberta Swansonzejezgon@otagih.mw232024/12/72025/5/27 17:47:34Total Rows: 5JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro, GridColDef, GridRowsProp } from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomTraderName,
  randomEmail,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';

export default function DisableColumnPinningButtons {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        rows={rows}
        columns={columns}
        initialState={{ pinnedColumns: { left: ['name'] } }}
        disableColumnPinning
      />
    </div>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 160, editable: true },
  { field: 'email', headerName: 'Email', width: 200, editable: true },
  { field: 'age', headerName: 'Age', type: 'number', editable: true },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
    editable: true,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
    editable: true,
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    email: randomEmail,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    email: randomEmail,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    email: randomEmail,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    email: randomEmail,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    email: randomEmail,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import { DataGridPro, GridColDef, GridRowsProp } from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomTraderName,
 randomEmail,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';

export default function DisableColumnPinningButtons {
 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 initialState\={{ pinnedColumns: { left: \['name'] } }}
 disableColumnPinning
 /\>
 \</div\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 160, editable: true },
 { field: 'email', headerName: 'Email', width: 200, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 email: randomEmail,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 email: randomEmail,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 email: randomEmail,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 email: randomEmail,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 email: randomEmail,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editing
Another alternate option to disable pinning actions on the UI is to disable the pinning options in the column menu in one of the following ways.


1. Use `disableColumnMenu` prop to completely disable the column menu.
2. Use column menu API to hide the pinning options in the column menu. See Column Menu \- Hiding a menu item for more details.


Pinning the checkbox selection column
-------------------------------------

To pin the checkbox column added when using `checkboxSelection`, add `GRID_CHECKBOX_SELECTION_COL_DEF.field` to the list of pinned columns.


NameEmailAgeDate <NAME_EMAIL>252024/7/72025/5/27 18:04:15Nora Connerwutgub@heb.nr362025/4/102025/5/28 04:51:01Kevin Freemanvodohi@asilude.mt192025/5/202025/5/27 14:42:50Susan Owenszadnid@fa.ae282025/4/272025/5/28 01:48:33Gene Harperwonoddu@hago.ga232025/1/172025/5/28 04:21:44Total Rows: 5JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  DataGridPro,
  GridColDef,
  GridRowsProp,
  GridActionsCellItem,
  GRID_CHECKBOX_SELECTION_COL_DEF,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomTraderName,
  randomEmail,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';

export default function ColumnPinningWithCheckboxSelection {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        rows={rows}
        columns={columns}
        checkboxSelection
        initialState={{
          pinnedColumns: {
            left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
            right: ['actions'],
          },
        }}
      />
    </div>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 160, editable: true },
  { field: 'email', headerName: 'Email', width: 200, editable: true },
  { field: 'age', headerName: 'Age', type: 'number', editable: true },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
    editable: true,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
    editable: true,
  },
  {
    field: 'actions',
    type: 'actions',
    width: 100,
    getActions:  => [
      <GridActionsCellItem icon={<EditIcon />} label="Edit" />,
      <GridActionsCellItem icon={<DeleteIcon />} label="Delete" />,
    ],
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    email: randomEmail,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    email: randomEmail,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    email: randomEmail,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    email: randomEmail,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    email: randomEmail,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import DeleteIcon from '@mui/icons\-material/Delete';
import EditIcon from '@mui/icons\-material/Edit';
import {
 DataGridPro,
 GridColDef,
 GridRowsProp,
 GridActionsCellItem,
 GRID\_CHECKBOX\_SELECTION\_COL\_DEF,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomTraderName,
 randomEmail,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';

export default function ColumnPinningWithCheckboxSelection {
 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 checkboxSelection
 initialState\={{
 pinnedColumns: {
 left: \[GRID\_CHECKBOX\_SELECTION\_COL\_DEF.field],
 right: \['actions'],
 },
 }}
 /\>
 \</div\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 160, editable: true },
 { field: 'email', headerName: 'Email', width: 200, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
 {
 field: 'actions',
 type: 'actions',
 width: 100,
 getActions:  \=\> \[
 \<GridActionsCellItem icon\={\<EditIcon /\>} label\="Edit" /\>,
 \<GridActionsCellItem icon\={\<DeleteIcon /\>} label\="Delete" /\>,
 ],
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 email: randomEmail,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 email: randomEmail,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 email: randomEmail,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 email: randomEmail,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 email: randomEmail,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editingUsage with dynamic row height
-----------------------------

You can have both pinned columns and dynamic row height enabled at the same time.
However, if the rows change their content after the initial calculation, you may need to trigger a manual recalculation to avoid incorrect measurements.
You can do this by calling `apiRef.current.resetRowHeights` every time that the content changes.


The demo below contains an example of both features enabled:


Toggle edit \& deleteNameEmailAgeDate <NAME_EMAIL>252025/3/272025/5/27 18:23:53EditDeletePrintCarolyn Hardygiole@ba.lr362025/4/222025/5/28 08:15:06EditDeletePrintElnora Duncanilzaf@kaj.si192024/7/122025/5/28 07:13:11EditDeletePrintEthan Higginssag@zilwehgic.io282025/5/182025/5/27 21:15:20EditDeletePrintCordelia Masonal@poknujpad.fi232025/4/62025/5/28 03:58:30EditDeletePrintTotal Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import PrintIcon from '@mui/icons-material/Print';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import {
  DataGridPro,
  GridColDef,
  GridRowsProp,
  useGridApiRef,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomTraderName,
  randomEmail,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';

export default function ColumnPinningDynamicRowHeight {
  const apiRef = useGridApiRef;
  const [showEditDelete, setShowEditDelete] = React.useState(true);

  const columns: GridColDef[] = React.useMemo(
     => [
      { field: 'name', headerName: 'Name', width: 160, editable: true },
      { field: 'email', headerName: 'Email', width: 200, editable: true },
      { field: 'age', headerName: 'Age', type: 'number', editable: true },
      {
        field: 'dateCreated',
        headerName: 'Date Created',
        type: 'date',
        width: 180,
        editable: true,
      },
      {
        field: 'lastLogin',
        headerName: 'Last Login',
        type: 'dateTime',
        width: 220,
        editable: true,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        width: 100,
        renderCell:  => (
          <Stack spacing={1} sx={{ width: 1, py: 1 }}>
            {showEditDelete && (
              <React.Fragment>
                <Button variant="outlined" size="small" startIcon={<EditIcon />}>
                  Edit
                </Button>
                <Button variant="outlined" size="small" startIcon={<DeleteIcon />}>
                  Delete
                </Button>
              </React.Fragment>
            )}
            <Button variant="outlined" size="small" startIcon={<PrintIcon />}>
              Print
            </Button>
          </Stack>
        ),
      },
    ],
    [showEditDelete],
  );

  const handleToggleClick = React.useCallback( => {
    setShowEditDelete((prevShowEditDelete) => !prevShowEditDelete);
  }, []);

  React.useLayoutEffect( => {
    apiRef.current?.resetRowHeights;
  }, [apiRef, showEditDelete]);

  return (
    <div style={{ width: '100%' }}>
      <Button sx={{ mb: 1 }} onClick={handleToggleClick}>
        Toggle edit & delete
      </Button>
      <div style={{ height: 400 }}>
        <DataGridPro
          apiRef={apiRef}
          rows={rows}
          columns={columns}
          getRowHeight={ => 'auto'}
          initialState={{ pinnedColumns: { left: ['name'], right: ['actions'] } }}
        />
      </div>
    </div>
  );
}

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    email: randomEmail,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    email: randomEmail,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    email: randomEmail,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    email: randomEmail,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    email: randomEmail,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 6,
    name: randomTraderName,
    email: randomEmail,
    age: 27,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 7,
    name: randomTraderName,
    email: randomEmail,
    age: 18,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 8,
    name: randomTraderName,
    email: randomEmail,
    age: 31,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 9,
    name: randomTraderName,
    email: randomEmail,
    age: 24,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 10,
    name: randomTraderName,
    email: randomEmail,
    age: 35,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import DeleteIcon from '@mui/icons\-material/Delete';
import EditIcon from '@mui/icons\-material/Edit';
import PrintIcon from '@mui/icons\-material/Print';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import {
 DataGridPro,
 GridColDef,
 GridRowsProp,
 useGridApiRef,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomTraderName,
 randomEmail,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';

export default function ColumnPinningDynamicRowHeight {
 const apiRef \= useGridApiRef;
 const \[showEditDelete, setShowEditDelete] \= React.useState(true);

 const columns: GridColDef\[] \= React.useMemo(
  \=\> \[
 { field: 'name', headerName: 'Name', width: 160, editable: true },
 { field: 'email', headerName: 'Email', width: 200, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
 {
 field: 'actions',
 headerName: 'Actions',
 width: 100,
 renderCell:  \=\> (
 \<Stack spacing\={1} sx\={{ width: 1, py: 1 }}\>
 {showEditDelete \&\& (
 \<React.Fragment\>
 \<Button variant\="outlined" size\="small" startIcon\={\<EditIcon /\>}\>
 Edit
 \</Button\>
 \<Button variant\="outlined" size\="small" startIcon\={\<DeleteIcon /\>}\>
 Delete
 \</Button\>
 \</React.Fragment\>
 )}
 \<Button variant\="outlined" size\="small" startIcon\={\<PrintIcon /\>}\>
 Print
 \</Button\>
 \</Stack\>
 ),
 },
 ],
 \[showEditDelete],
 );

 const handleToggleClick \= React.useCallback( \=\> {
 setShowEditDelete((prevShowEditDelete) \=\> !prevShowEditDelete);
 }, \[]);

 React.useLayoutEffect( \=\> {
 apiRef.current?.resetRowHeights;
 }, \[apiRef, showEditDelete]);

 return (
 \<div style\={{ width: '100%' }}\>
 \<Button sx\={{ mb: 1 }} onClick\={handleToggleClick}\>
 Toggle edit \& delete
 \</Button\>
 \<div style\={{ height: 400 }}\>
 \<DataGridPro
 apiRef\={apiRef}
 rows\={rows}
 columns\={columns}
 getRowHeight\={ \=\> 'auto'}
 initialState\={{ pinnedColumns: { left: \['name'], right: \['actions'] } }}
 /\>
 \</div\>
 \</div\>
 );
}

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 email: randomEmail,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 email: randomEmail,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 email: randomEmail,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 email: randomEmail,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 email: randomEmail,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 6,
 name: randomTraderName,
 email: randomEmail,
 age: 27,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 7,
 name: randomTraderName,
 email: randomEmail,
 age: 18,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 8,
 name: randomTraderName,
 email: randomEmail,
 age: 31,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 9,
 name: randomTraderName,
 email: randomEmail,
 age: 24,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 10,
 name: randomTraderName,
 email: randomEmail,
 age: 35,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editingapiRef
------

The Data Grid exposes a set of methods via the `apiRef` object that are used internally in the implementation of the column pinning feature.
The reference below describes the relevant functions.
See API object for more details.



This API should only be used as a last resort when the Data Grid's built\-in props aren't sufficient for your specific use case.


### getPinnedColumnsReturns which columns are pinned.

###### Signature:

Copy(or Ctrl \+ C)
```
getPinnedColumns:  => GridPinnedColumnFields
```
### isColumnPinnedReturns which side a column is pinned to.

###### Signature:

Copy(or Ctrl \+ C)
```
isColumnPinned: (field: string) => GridPinnedColumnPosition | false
```
### pinColumnPins a column to the left or right side of the grid.

###### Signature:

Copy(or Ctrl \+ C)
```
pinColumn: (field: string, side: GridPinnedColumnPosition) => void
```
### setPinnedColumnsChanges the pinned columns.

###### Signature:

Copy(or Ctrl \+ C)
```
setPinnedColumns: (pinnedColumns: GridPinnedColumnFields) => void
```
### unpinColumnUnpins a column.

###### Signature:

Copy(or Ctrl \+ C)
```
unpinColumn: (field: string) => void
```
API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Column orderingRecipes

---

•

Blog•

Store
