import type { ApiResponse, SystemDictionary } from '@/types';

// Mock dictionary data
const mockSystemDictionary: SystemDictionary = {
  // Model Types
  modelTypes: [
    { label: 'GPT-4', value: 'gpt4' },
    { label: 'GPT-3.5', value: 'gpt35' },
    { label: 'Claude 3', value: 'claude3' },
    { label: 'Llama 3', value: 'llama3' },
    { label: 'Gemini', value: 'gemini' },
    { label: 'Mistral', value: 'mistral' },
  ],
  
  // Model Statuses
  modelStatuses: [
    { label: '在线', value: 'online' },
    { label: '离线', value: 'offline' },
    { label: '维护中', value: 'maintenance' },
    { label: '已禁用', value: 'disabled' },
    { label: '已过期', value: 'expired' },
  ],
  
  // Database Types
  databaseTypes: [
    { label: 'Pinecone', value: 'pinecone' },
    { label: 'Milvus', value: 'milvus' },
    { label: 'Weaviate', value: 'weaviate' },
    { label: 'Qdrant', value: 'qdrant' },
    { label: 'Chroma', value: 'chroma' },
    { label: 'Redis', value: 'redis' },
    { label: 'Neo4j', value: 'neo4j' },
    { label: 'MySQL', value: 'mysql' },
    { label: 'PostgreSQL', value: 'postgresql' },
  ],
  
  // Database Statuses
  databaseStatuses: [
    { label: '连接正常', value: 'connected' },
    { label: '连接失败', value: 'disconnected' },
    { label: '初始化中', value: 'initializing' },
    { label: '同步中', value: 'syncing' },
    { label: '已禁用', value: 'disabled' },
  ],
  
  // MCP Statuses
  mcpStatuses: [
    { label: '运行中', value: 'running' },
    { label: '已停止', value: 'stopped' },
    { label: '错误', value: 'error' },
    { label: '已暂停', value: 'paused' },
    { label: '初始化中', value: 'initializing' },
  ],
  
  // Tool Types
  toolTypes: [
    { label: '搜索工具', value: 'search' },
    { label: '计算工具', value: 'calculation' },
    { label: '翻译工具', value: 'translation' },
    { label: '数据分析', value: 'data_analysis' },
    { label: '代码生成', value: 'code_generation' },
    { label: '图像处理', value: 'image_processing' },
    { label: '文档处理', value: 'document_processing' },
  ],
  
  // Tool Statuses
  toolStatuses: [
    { label: '可用', value: 'available' },
    { label: '不可用', value: 'unavailable' },
    { label: '维护中', value: 'maintenance' },
    { label: '已禁用', value: 'disabled' },
    { label: '已过期', value: 'expired' },
  ],
  
  // Flow Types
  flowTypes: [
    { label: 'Langflow', value: 'langflow' },
    { label: 'Flowise', value: 'flowise' },
    { label: 'Make', value: 'make' },
    { label: 'Coze', value: 'coze' },
    { label: 'Dify', value: 'dify' },
    { label: 'FastGPT', value: 'fastgpt' },
    { label: 'Autogen', value: 'autogen' },
    { label: 'ADK', value: 'adk' },
    { label: 'Ango', value: 'ango' },
  ],
  
  // Flow Statuses
  flowStatuses: [
    { label: '运行中', value: 'running' },
    { label: '已停止', value: 'stopped' },
    { label: '已暂停', value: 'paused' },
    { label: '错误', value: 'error' },
    { label: '已完成', value: 'completed' },
    { label: '已禁用', value: 'disabled' },
  ],
  
  // RAG Types
  ragTypes: [
    { label: '知识库', value: 'knowledge_base' },
    { label: '文档库', value: 'document_library' },
    { label: '知识图谱', value: 'knowledge_graph' },
    { label: '混合检索', value: 'hybrid_retrieval' },
    { label: '语义检索', value: 'semantic_retrieval' },
  ],
  
  // RAG Statuses
  ragStatuses: [
    { label: '已索引', value: 'indexed' },
    { label: '索引中', value: 'indexing' },
    { label: '索引失败', value: 'index_failed' },
    { label: '已禁用', value: 'disabled' },
    { label: '已过期', value: 'expired' },
  ],
  
  // Agent Types
  agentTypes: [
    { label: '助手型', value: 'assistant' },
    { label: '专家型', value: 'expert' },
    { label: '团队协作型', value: 'team' },
    { label: '自主型', value: 'autonomous' },
    { label: '工具使用型', value: 'tool_user' },
    { label: '多模态型', value: 'multimodal' },
  ],
  
  // Agent Statuses
  agentStatuses: [
    { label: '空闲', value: 'idle' },
    { label: '工作中', value: 'working' },
    { label: '已暂停', value: 'paused' },
    { label: '已停止', value: 'stopped' },
    { label: '错误', value: 'error' },
    { label: '已禁用', value: 'disabled' },
  ],
  
  // Device Types
  deviceTypes: [
    { label: 'CPU', value: 'cpu' },
    { label: 'GPU', value: 'gpu' },
    { label: 'TPU', value: 'tpu' },
    { label: 'NPU', value: 'npu' },
    { label: '混合设备', value: 'hybrid' },
    { label: '云端设备', value: 'cloud' },
    { label: '边缘设备', value: 'edge' },
  ],
  
  // Device Statuses
  deviceStatuses: [
    { label: '在线', value: 'online' },
    { label: '离线', value: 'offline' },
    { label: '忙碌', value: 'busy' },
    { label: '空闲', value: 'idle' },
    { label: '维护中', value: 'maintenance' },
    { label: '已禁用', value: 'disabled' },
  ],
};

// Get system dictionary function
export const getSystemDictionary = async (): Promise<ApiResponse<SystemDictionary>> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '0',
        data: mockSystemDictionary,
        msg: 'System dictionary retrieved successfully',
      });
    }, 500); // Simulate network delay
  });
};

// Get individual dictionary items
export const getModelTypes = async (): Promise<ApiResponse<SystemDictionary['modelTypes']>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '0',
        data: mockSystemDictionary.modelTypes,
        msg: 'Model types retrieved successfully',
      });
    }, 300);
  });
};

export const getModelStatuses = async (): Promise<ApiResponse<SystemDictionary['modelStatuses']>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '0',
        data: mockSystemDictionary.modelStatuses,
        msg: 'Model statuses retrieved successfully',
      });
    }, 300);
  });
};

// Similar functions for other dictionary items can be added as needed
