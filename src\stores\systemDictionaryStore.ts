import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { 
  SystemDictionary, 
  ModelType, 
  ModelStatus, 
  DatabaseType, 
  DatabaseStatus, 
  MCPStatus, 
  ToolType, 
  ToolStatus, 
  FlowType, 
  FlowStatus, 
  RAGType, 
  RAGStatus, 
  AgentType, 
  AgentStatus, 
  DeviceType, 
  DeviceStatus 
} from '@/types';
import { getSystemDictionary } from '@/controllers/base/dictionary';

// Initial empty state
const initialState: SystemDictionary = {
  modelTypes: [],
  modelStatuses: [],
  databaseTypes: [],
  databaseStatuses: [],
  mcpStatuses: [],
  toolTypes: [],
  toolStatuses: [],
  flowTypes: [],
  flowStatuses: [],
  ragTypes: [],
  ragStatuses: [],
  agentTypes: [],
  agentStatuses: [],
  deviceTypes: [],
  deviceStatuses: [],
};

interface SystemDictionaryState extends SystemDictionary {
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchDictionary: () => Promise<void>;
  
  // Setters for individual dictionary types
  setModelTypes: (types: ModelType) => void;
  setModelStatuses: (statuses: ModelStatus) => void;
  setDatabaseTypes: (types: DatabaseType) => void;
  setDatabaseStatuses: (statuses: DatabaseStatus) => void;
  setMCPStatuses: (statuses: MCPStatus) => void;
  setToolTypes: (types: ToolType) => void;
  setToolStatuses: (statuses: ToolStatus) => void;
  setFlowTypes: (types: FlowType) => void;
  setFlowStatuses: (statuses: FlowStatus) => void;
  setRAGTypes: (types: RAGType) => void;
  setRAGStatuses: (statuses: RAGStatus) => void;
  setAgentTypes: (types: AgentType) => void;
  setAgentStatuses: (statuses: AgentStatus) => void;
  setDeviceTypes: (types: DeviceType) => void;
  setDeviceStatuses: (statuses: DeviceStatus) => void;
  
  // Reset the store
  reset: () => void;
}

export const useSystemDictionaryStore = create<SystemDictionaryState>()(
  persist(
    (set) => ({
      // Initial state
      ...initialState,
      isLoading: false,
      error: null,
      
      // Fetch all dictionary data
      fetchDictionary: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await getSystemDictionary();
          if (response.code === '0') {
            set({ 
              ...response.data,
              isLoading: false 
            });
          } else {
            set({ 
              isLoading: false, 
              error: response.msg 
            });
          }
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      },
      
      // Setters for individual dictionary types
      setModelTypes: (types) => set({ modelTypes: types }),
      setModelStatuses: (statuses) => set({ modelStatuses: statuses }),
      setDatabaseTypes: (types) => set({ databaseTypes: types }),
      setDatabaseStatuses: (statuses) => set({ databaseStatuses: statuses }),
      setMCPStatuses: (statuses) => set({ mcpStatuses: statuses }),
      setToolTypes: (types) => set({ toolTypes: types }),
      setToolStatuses: (statuses) => set({ toolStatuses: statuses }),
      setFlowTypes: (types) => set({ flowTypes: types }),
      setFlowStatuses: (statuses) => set({ flowStatuses: statuses }),
      setRAGTypes: (types) => set({ ragTypes: types }),
      setRAGStatuses: (statuses) => set({ ragStatuses: statuses }),
      setAgentTypes: (types) => set({ agentTypes: types }),
      setAgentStatuses: (statuses) => set({ agentStatuses: statuses }),
      setDeviceTypes: (types) => set({ deviceTypes: types }),
      setDeviceStatuses: (statuses) => set({ deviceStatuses: statuses }),
      
      // Reset to initial state
      reset: () => set(initialState),
    }),
    {
      name: 'system-dictionary-storage',
    }
  )
);
