Transfer List
=============

A Transfer List (or "shuttle") enables the user to move one or more list items between lists.Help us keep runningIf you don't mind tech\-related ads (no tracking or remarketing), and want to keep us running, please whitelist us in your blocker.Thank you! ❤️


* Feedback
* Bundle size
* Figma
* Sketch

Basic transfer list
-------------------

For completeness, this example includes buttons for "move all", but not every transfer list needs these.


List item 1List item 2List item 3List item 4≫\>\<≪List item 5List item 6List item 7List item 8JSTSShow codeEnhanced transfer list
----------------------

This example exchanges the "move all" buttons for a "select all / select none" checkbox and adds a counter.


Choices0/4 selected

---

List item 1List item 2List item 3List item 4\>\<Chosen0/4 selected

---

List item 5List item 6List item 7List item 8JSTSShow codeLimitations
-----------

The component comes with a couple of limitations:


* It only works on desktop.
If you have a limited amount of options to select, prefer the Autocomplete component.
If mobile support is important for you, have a look at \#27579.
* There are no high\-level components exported from npm. The demos are based on composition.
If this is important for you, have a look at \#27579.


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Checkbox />`
* `<List />`
* `<ListItem />`
* `<Switch />`



