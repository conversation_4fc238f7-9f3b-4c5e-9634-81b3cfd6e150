import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import type { MenuItem } from '@/types';
import { useMenuStore } from '@/stores/menuStore';
import { useDarkStore } from '@/stores/darkStore';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  useTheme,
  Avatar
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
// Import icons for menu items
import SettingsIcon from '@mui/icons-material/Settings';
import StorageIcon from '@mui/icons-material/Storage';
import AppsIcon from '@mui/icons-material/Apps';
import BuildIcon from '@mui/icons-material/Build';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import DevicesIcon from '@mui/icons-material/Devices';
import PersonIcon from '@mui/icons-material/Person';

interface SidebarProps {
  menuItems: MenuItem[];
}

const Sidebar: React.FC<SidebarProps> = ({ menuItems }) => {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});
  const { activeMenuItem, setActiveMenuItem } = useMenuStore();
  const { isDarkMode } = useDarkStore();
  const navigate = useNavigate();
  const theme = useTheme();

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleMenuItemClick = (item: MenuItem) => {
    if (item.type === 'directory') {
      toggleExpand(item.id);
    } else {
      setActiveMenuItem(item.id);
      navigate(item.path);
    }
  };

  // Function to get the appropriate icon based on menu item name or type
  const getMenuIcon = (item: MenuItem, isActive: boolean) => {
    const name = item.name.toLowerCase();
    const iconColor = '#fff'; // Always white for better contrast on colored backgrounds
    const iconSize = "small";

    // Define background colors for different menu types
    let bgColor = '#3f50b5'; // Default color
    let IconComponent;

    // Match icons based on menu item name
    if (name.includes('model') || name.includes('模型')) {
      IconComponent = SmartToyIcon;
      bgColor = '#2196f3'; // Blue
    } else if (name.includes('database') || name.includes('数据库')) {
      IconComponent = StorageIcon;
      bgColor = '#4caf50'; // Green
    } else if (name.includes('tool') || name.includes('工具')) {
      IconComponent = BuildIcon;
      bgColor = '#ff9800'; // Orange
    } else if (name.includes('flow') || name.includes('流程')) {
      IconComponent = AccountTreeIcon;
      bgColor = '#9c27b0'; // Purple
    } else if (name.includes('rag') || name.includes('知识库')) {
      IconComponent = MenuBookIcon;
      bgColor = '#f44336'; // Red
    } else if (name.includes('agent') || name.includes('代理')) {
      IconComponent = SmartToyIcon;
      bgColor = '#00bcd4'; // Cyan
    } else if (name.includes('device') || name.includes('设备')) {
      IconComponent = DevicesIcon;
      bgColor = '#795548'; // Brown
    } else if (name.includes('user') || name.includes('用户')) {
      IconComponent = PersonIcon;
      bgColor = '#607d8b'; // Blue Grey
    } else if (name.includes('setting') || name.includes('设置')) {
      IconComponent = SettingsIcon;
      bgColor = '#9e9e9e'; // Grey
    } else {
      IconComponent = AppsIcon;
      bgColor = '#3f50b5'; // Default blue
    }

    // Use a slightly different shade for active items
    const activeBgColor = isActive ? bgColor : bgColor;

    return (
      <Avatar
        sx={{
          bgcolor: activeBgColor,
          width: 30,
          height: 30,
          boxShadow: isActive ? '0 2px 4px 0 rgba(0,0,0,0.2)' : 'none',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.05)',
            boxShadow: '0 2px 8px 0 rgba(0,0,0,0.2)'
          },
          '& .MuiSvgIcon-root': {
            fontSize: '1.1rem'
          }
        }}
      >
        <IconComponent fontSize={iconSize} sx={{ color: iconColor }} />
      </Avatar>
    );
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    const isExpanded = expandedItems[item.id] || false;
    const isActive = activeMenuItem === item.id;
    const hasChildren = item.children && item.children.length > 0;
    const isDirectory = item.type === 'directory';

    // Create a single menu item
    const menuItem = (
      <ListItem
        onClick={() => handleMenuItemClick(item)}
        sx={{
          pl: level * 2 + 2,
          py: 1,
          color: isDarkMode ? '#fff' : '#000000', // Set text color to black
          position: 'relative',
          cursor: 'pointer', // Add pointer cursor for better UX
          backgroundColor: isActive ? 'rgba(63, 80, 181, 0.08)' : 'transparent',
          '&:hover': {
            backgroundColor: isActive ? 'rgba(63, 80, 181, 0.12)' : 'rgba(0, 0, 0, 0.04)' // Add hover effect
          }
        }}
      >
        <ListItemIcon sx={{
          minWidth: 40,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {getMenuIcon(item, isActive)}
        </ListItemIcon>
        <ListItemText
          primary={item.name}
          sx={{
            '& .MuiListItemText-primary': {
              fontSize: 14,
              fontWeight: isActive ? 500 : 400,
              fontFamily: "'Microsoft YaHei', sans-serif"
            }
          }}
        />
        {isDirectory && (
          <Box
            sx={{
              position: 'absolute',
              right: 16,
              top: '50%',
              transform: 'translateY(-50%)',
              transition: 'transform 0.3s',
              color: isActive ? '#3f50b5' : 'inherit'
            }}
          >
            <ExpandMoreIcon
              fontSize="small"
              sx={{
                transform: isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)',
                transition: 'transform 0.3s'
              }}
            />
          </Box>
        )}
      </ListItem>
    );

    // For items with children, wrap in a List with Collapse
    if (isDirectory && hasChildren) {
      return (
        <React.Fragment key={item.id}>
          {menuItem}
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children?.map((child) => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        </React.Fragment>
      );
    }

    // For items without children, just return the menu item
    return (
      <React.Fragment key={item.id}>
        {menuItem}
      </React.Fragment>
    );
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 256,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 256,
          boxSizing: 'border-box',
          backgroundColor: isDarkMode ? theme.palette.background.default : '#fff',
          borderRight: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.12)',
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        <List component="nav" aria-label="main menu">
          {menuItems.map((item) => renderMenuItem(item))}
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
