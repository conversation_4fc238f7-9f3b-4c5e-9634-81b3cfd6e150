import React, { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Box, useTheme } from '@mui/material';
import Header from '@/components/common/Header';
import Sidebar from '@/components/common/Sidebar';
import { getMenu } from '@/controllers/base/menu';
import { useMenuStore } from '@/stores/menuStore';
import { useDarkStore } from '@/stores/darkStore';
import type { MenuItem } from '@/types';

const HomePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { menuItems, setMenuItems } = useMenuStore();
  const { isDarkMode } = useDarkStore();

  useEffect(() => {
    const fetchMenuData = async () => {
      try {
        const response = await getMenu();
        if (response.code === '0') {
          setMenuItems(response.data);
        } else {
          setError(response.msg);
        }
      } catch (err) {
        setError('Failed to load menu data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMenuData();
  }, [setMenuItems]);

  const theme = useTheme();

  if (isLoading) {
    return (
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        bgcolor: isDarkMode ? theme.palette.background.default : '#fff'
      }}>
        <Box sx={{
          width: 40,
          height: 40,
          borderRadius: '50%',
          border: '3px solid rgba(0, 0, 0, 0.1)',
          borderTop: `3px solid ${theme.palette.primary.main}`,
          animation: 'spin 1s linear infinite',
          '@keyframes spin': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' }
          }
        }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        bgcolor: isDarkMode ? theme.palette.background.default : '#fff'
      }}>
        <Box sx={{ color: 'error.main', textAlign: 'center' }}>
          <Box component="p" sx={{ mb: 2 }}>Error: {error}</Box>
          <Box
            component="button"
            onClick={() => window.location.reload()}
            sx={{
              mt: 2,
              px: 3,
              py: 1,
              bgcolor: 'primary.main',
              color: 'white',
              border: 'none',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            Retry
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      bgcolor: isDarkMode ? theme.palette.background.default : '#fff'
    }}>
      <Header />
      <Box sx={{
        display: 'flex',
        flexGrow: 1,
        overflow: 'hidden'
      }}>
        <Sidebar menuItems={menuItems} />
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            overflow: 'auto',
            bgcolor: isDarkMode ? theme.palette.background.default : theme.palette.background.content,
          }}
        >
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
};

export default HomePage;
