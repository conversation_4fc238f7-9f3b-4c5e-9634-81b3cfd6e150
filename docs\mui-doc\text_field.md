Text Field
==========

Text Fields let users enter and edit text.


HP partners with Andela for groundbreaking Africa project. Read our innovation success story.

ads via Carbon



Text fields allow users to enter text into a UI. They typically appear in forms and dialogs.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Basic TextField
---------------

The `TextField` wrapper component is a complete form control including a label, input, and help text.
It comes with three variants: outlined (default), filled, and standard.


OutlinedOutlinedFilledStandardJSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField id="outlined-basic" label="Outlined" variant="outlined" />
<TextField id="filled-basic" label="Filled" variant="filled" />
<TextField id="standard-basic" label="Standard" variant="standard" />  

```
\<TextField id\="outlined\-basic" label\="Outlined" variant\="outlined" /\>
\<TextField id\="filled\-basic" label\="Filled" variant\="filled" /\>
\<TextField id\="standard\-basic" label\="Standard" variant\="standard" /\>Press `Enter` to start editing
The standard variant of the Text Field is no longer documented in the Material Design guidelines
(this article explains why),
but Material UI will continue to support it.


Form props
----------

Standard form attributes are supported, for example `required`, `disabled`, `type`, etc. as well as a `helperText` which is used to give context about a field's input, such as how the input will be used.


Required \*Required \*DisabledDisabledPasswordPasswordRead OnlyRead OnlyNumberNumberSearch fieldSearch fieldHelper textHelper textSome important text

Required \*DisabledPasswordRead OnlyNumberSearch fieldHelper textSome important text

Required \*DisabledPasswordRead OnlyNumberSearch fieldHelper textSome important text

JSTSShow codeControlling the HTML input
--------------------------

Use `slotProps.htmlInput` to pass attributes to the underlying `<input>` element.



```
<TextField slotProps={{ htmlInput: { 'data-testid': '…' } }} />

```
CopyCopied(or Ctrl \+ C)
The rendered HTML input will look like this:



```
<input
  aria-invalid="false"
  class="MuiInputBase-input MuiOutlinedInput-input"
  type="text"
  data-testid="…"
/>

```
CopyCopied(or Ctrl \+ C)

`slotProps.htmlInput` is not the same as `slotProps.input`.
`slotProps.input` refers to the React `<Input />` component that's rendered based on the specified variant prop.
`slotProps.htmlInput` refers to the HTML `<input>` element rendered within that Input component, regardless of the variant.


Validation
----------

The `error` prop toggles the error state.
The `helperText` prop can then be used to provide feedback to the user about the error.


ErrorErrorErrorErrorIncorrect entry.

ErrorErrorIncorrect entry.

ErrorErrorIncorrect entry.

JSTSShow codeMultiline
---------

The `multiline` prop transforms the Text Field into a MUI Base Textarea Autosize element.
Unless the `rows` prop is set, the height of the text field dynamically matches its content.
You can use the `minRows` and `maxRows` props to bound it.


MultilineMultilineMultiline PlaceholderMultiline PlaceholderMultilineDefault ValueMultilineMultilineMultiline PlaceholderMultilineDefault ValueMultilineMultiline PlaceholderMultilineDefault ValueJSTSShow codeSelect
------

The `select` prop makes the text field use the Select component internally.


Select€SelectPlease select your currency

Native select$€฿¥Native selectPlease select your currency

Select€Please select your currency

Native select$€฿¥Please select your currency

Select€Please select your currency

Native select$€฿¥Please select your currency

JSTSShow codeIcons
-----

There are multiple ways to display an icon with a text field.


With a start adornment​TextField​With sxJSTSShow code### Input Adornments

The main way is with an `InputAdornment`.
This can be used to add a prefix, a suffix, or an action to an input.
For instance, you can use an icon button to hide or reveal the password.


With normal TextFieldkg

With normal TextFieldkg

​Weight

PasswordPasswordAmount$

AmountWith normal TextFieldkg

kg

Weight

PasswordAmount$

With normal TextFieldkg

kg

Weight

PasswordAmount$

JSTSShow code#### Customizing adornments

You can apply custom styles to adornments, and trigger changes to one based on attributes from another.
For example, the demo below uses the label's `[data-shrink=true]` attribute to make the suffix visible (via opacity) when the label is in its shrunken state.


Outlinedlbs

OutlinedFilleddays

<EMAIL>

JSTSShow codeSizes
-----

Fancy smaller inputs? Use the `size` prop.


SizeSizeSizeSizeSizeSizeSizeSizeJSTSShow codeThe `filled` variant input height can be further reduced by rendering the label outside of it.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField
  hiddenLabel
  id="filled-hidden-label-small"
  defaultValue="Small"
  variant="filled"
  size="small"
/>
<TextField
  hiddenLabel
  id="filled-hidden-label-normal"
  defaultValue="Normal"
  variant="filled"
/>  

```
\<TextField
 hiddenLabel
 id\="filled\-hidden\-label\-small"
 defaultValue\="Small"
 variant\="filled"
 size\="small"
/\>
\<TextField
 hiddenLabel
 id\="filled\-hidden\-label\-normal"
 defaultValue\="Normal"
 variant\="filled"
/\>Press `Enter` to start editingMargin
------

The `margin` prop can be used to alter the vertical spacing of the text field.
Using `none` (default) doesn't apply margins to the `FormControl` whereas `dense` and `normal` do.


margin\="none"margin\="none"margin\="dense"margin\="dense"margin\="normal"margin\="normal"JSTSExpand codeCopy(or Ctrl \+ C)
```
<RedBar />
<TextField label={'margin="none"'} id="margin-none" />
<RedBar />
<TextField label={'margin="dense"'} id="margin-dense" margin="dense" />
<RedBar />
<TextField label={'margin="normal"'} id="margin-normal" margin="normal" />
<RedBar />  

```
\<RedBar /\>
\<TextField label\={'margin\="none"'} id\="margin\-none" /\>
\<RedBar /\>
\<TextField label\={'margin\="dense"'} id\="margin\-dense" margin\="dense" /\>
\<RedBar /\>
\<TextField label\={'margin\="normal"'} id\="margin\-normal" margin\="normal" /\>
\<RedBar /\>Press `Enter` to start editingFull width
----------

`fullWidth` can be used to make the input take up the full width of its container.


fullWidthfullWidthJSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField fullWidth label="fullWidth" id="fullWidth" />  

```
\<TextField fullWidth label\="fullWidth" id\="fullWidth" /\>Press `Enter` to start editingUncontrolled vs. Controlled
---------------------------

The component can be controlled or uncontrolled.



* A component is **controlled** when it's managed by its parent using props.
* A component is **uncontrolled** when it's managed by its own local state.


Learn more about controlled and uncontrolled components in the React documentation.


ControlledControlledUncontrolledUncontrolledJSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField
  id="outlined-controlled"
  label="Controlled"
  value={name}
  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  }}
/>
<TextField
  id="outlined-uncontrolled"
  label="Uncontrolled"
  defaultValue="foo"
/>  

```
\<TextField
 id\="outlined\-controlled"
 label\="Controlled"
 value\={name}
 onChange\={(event: React.ChangeEvent\<HTMLInputElement\>) \=\> {
 setName(event.target.value);
 }}
/\>
\<TextField
 id\="outlined\-uncontrolled"
 label\="Uncontrolled"
 defaultValue\="foo"
/\>Press `Enter` to start editingComponents
----------

`TextField` is composed of smaller components (
`FormControl`,
`Input`,
`FilledInput`,
`InputLabel`,
`OutlinedInput`,
and `FormHelperText`
) that you can leverage directly to significantly customize your form inputs.


You might also have noticed that some native HTML input properties are missing from the `TextField` component.
This is on purpose.
The component takes care of the most used properties.
Then, it's up to the user to use the underlying component shown in the following demo. Still, you can use `slotProps.htmlInput` (and `slotProps.input`, `slotProps.inputLabel` properties) if you want to avoid some boilerplate.


NameNameSome important helper text

NameDisabled

NameError

NameNameNameJSTSShow codeInputs
------

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Input defaultValue="Hello world" inputProps={ariaLabel} />
<Input placeholder="Placeholder" inputProps={ariaLabel} />
<Input disabled defaultValue="Disabled" inputProps={ariaLabel} />
<Input defaultValue="Error" error inputProps={ariaLabel} />  

```
\<Input defaultValue\="Hello world" inputProps\={ariaLabel} /\>
\<Input placeholder\="Placeholder" inputProps\={ariaLabel} /\>
\<Input disabled defaultValue\="Disabled" inputProps\={ariaLabel} /\>
\<Input defaultValue\="Error" error inputProps\={ariaLabel} /\>Press `Enter` to start editingColor
-----

The `color` prop changes the highlight color of the text field when focused.


Outlined secondaryOutlined secondaryFilled successStandard warningJSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField label="Outlined secondary" color="secondary" focused />
<TextField label="Filled success" variant="filled" color="success" focused />
<TextField
  label="Standard warning"
  variant="standard"
  color="warning"
  focused
/>  

```
\<TextField label\="Outlined secondary" color\="secondary" focused /\>
\<TextField label\="Filled success" variant\="filled" color\="success" focused /\>
\<TextField
 label\="Standard warning"
 variant\="standard"
 color\="warning"
 focused
/\>Press `Enter` to start editingCustomization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


### Using the styled API

BootstrapRedditCustom CSSCustom CSSCSS validation style \*CSS validation style \*JSTSShow code### Using the theme style overrides API

Use the `styleOverrides` key to change any style injected by Material UI into the DOM.
See the theme style overrides documentation for further details.


OutlinedOutlinedFilledStandardJSTSExpand codeCopy(or Ctrl \+ C)
```
<ThemeProvider theme={customTheme(outerTheme)}>
  <TextField label="Outlined" />
  <TextField label="Filled" variant="filled" />
  <TextField label="Standard" variant="standard" />
</ThemeProvider>  

```
\<ThemeProvider theme\={customTheme(outerTheme)}\>
 \<TextField label\="Outlined" /\>
 \<TextField label\="Filled" variant\="filled" /\>
 \<TextField label\="Standard" variant\="standard" /\>
\</ThemeProvider\>Press `Enter` to start editingCustomization does not stop at CSS.
You can use composition to build custom components and give your app a unique feel.
Below is an example using the `InputBase` component, inspired by Google Maps.


JSTSShow code🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


`useFormControl`
----------------

For advanced customization use cases, a `useFormControl` hook is exposed.
This hook returns the context value of the parent `FormControl` component.


**API**



```
import { useFormControl } from '@mui/material/FormControl';

```
CopyCopied(or Ctrl \+ C)
**Returns**


`value` (*object*):


* `value.adornedStart` (*bool*): Indicate whether the child `Input` or `Select` component has a start adornment.
* `value.setAdornedStart` (*func*): Setter function for `adornedStart` state value.
* `value.color` (*string*): The theme color is being used, inherited from `FormControl` `color` prop .
* `value.disabled` (*bool*): Indicate whether the component is being displayed in a disabled state, inherited from `FormControl` `disabled` prop.
* `value.error` (*bool*): Indicate whether the component is being displayed in an error state, inherited from `FormControl` `error` prop
* `value.filled` (*bool*): Indicate whether input is filled
* `value.focused` (*bool*): Indicate whether the component and its children are being displayed in a focused state
* `value.fullWidth` (*bool*): Indicate whether the component is taking up the full width of its container, inherited from `FormControl` `fullWidth` prop
* `value.hiddenLabel` (*bool*): Indicate whether the label is being hidden, inherited from `FormControl` `hiddenLabel` prop
* `value.required` (*bool*): Indicate whether the label is indicating that the input is required input, inherited from the `FormControl` `required` prop
* `value.size` (*string*): The size of the component, inherited from the `FormControl` `size` prop
* `value.variant` (*string*): The variant is being used by the `FormControl` component and its children, inherited from `FormControl` `variant` prop
* `value.onBlur` (*func*): Should be called when the input is blurred
* `value.onFocus` (*func*): Should be called when the input is focused
* `value.onEmpty` (*func*): Should be called when the input is emptied
* `value.onFilled` (*func*): Should be called when the input is filled


**Example**


​Helper text

JSTSExpand codeCopy(or Ctrl \+ C)
```
<form noValidate autoComplete="off">
  <FormControl sx={{ width: '25ch' }}>
    <OutlinedInput placeholder="Please enter text" />
    <MyFormHelperText />
  </FormControl>
</form>  

```
\<form noValidate autoComplete\="off"\>
 \<FormControl sx\={{ width: '25ch' }}\>
 \<OutlinedInput placeholder\="Please enter text" /\>
 \<MyFormHelperText /\>
 \</FormControl\>
\</form\>Press `Enter` to start editingPerformance
-----------

Global styles for the auto\-fill keyframes are injected and removed on each mount and unmount, respectively.
If you are loading a large number of Text Field components at once, it might be a good idea to change this default behavior by enabling `disableInjectingGlobalStyles` in `MuiInputBase`.
Make sure to inject `GlobalStyles` for the auto\-fill keyframes at the top of your application.



```
import { GlobalStyles, createTheme, ThemeProvider } from '@mui/material';

const theme = createTheme({
  components: {
    MuiInputBase: {
      defaultProps: {
        disableInjectingGlobalStyles: true,
      },
    },
  },
});

export default function App {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles
        styles={{
          '@keyframes mui-auto-fill': { from: { display: 'block' } },
          '@keyframes mui-auto-fill-cancel': { from: { display: 'block' } },
        }}
      />
      ...
    </ThemeProvider>
  );
}

```
CopyCopied(or Ctrl \+ C)
Limitations
-----------

### Shrink

The input label "shrink" state isn't always correct.
The input label is supposed to shrink as soon as the input is displaying something.
In some circumstances, we can't determine the "shrink" state (number input, datetime input, Stripe input). You might notice an overlap.



To workaround the issue, you can force the "shrink" state of the label.



```
<TextField slotProps={{ inputLabel: { shrink: true } }} />

```
CopyCopied(or Ctrl \+ C)
or



```
<InputLabel shrink>Count</InputLabel>

```
CopyCopied(or Ctrl \+ C)
### Floating label

The floating label is absolutely positioned.
It won't impact the layout of the page.
Make sure that the input is larger than the label to display correctly.


### type\="number"


We do not recommend using `type="number"` with a Text Field due to potential usability issues:


* it allows certain non\-numeric characters ('e', '\+', '\-', '.') and silently discards others
* the functionality of scrolling to increment/decrement the number can cause accidental and hard\-to\-notice changes
* and more—see Why the GOV.UK Design System team changed the input type for numbers for a more detailed explanation of the limitations of `<input type="number">`


If you need a text field with number validation, you can use MUI Base's Number Input instead.


You can follow this GitHub issue to track the progress of introducing the Number Input component to Material UI.


### Helper text

The helper text prop affects the height of the text field. If two text fields are placed side by side, one with a helper text and one without, they will have different heights. For example:


NameNamePlease enter your name

NameNameJSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField
  helperText="Please enter your name"
  id="demo-helper-text-misaligned"
  label="Name"
/>
<TextField id="demo-helper-text-misaligned-no-helper" label="Name" />  

```
\<TextField
 helperText\="Please enter your name"
 id\="demo\-helper\-text\-misaligned"
 label\="Name"
/\>
\<TextField id\="demo\-helper\-text\-misaligned\-no\-helper" label\="Name" /\>Press `Enter` to start editingThis can be fixed by passing a space character to the `helperText` prop:


NameNamePlease enter your name

NameName​

JSTSExpand codeCopy(or Ctrl \+ C)
```
<TextField
  helperText="Please enter your name"
  id="demo-helper-text-aligned"
  label="Name"
/>
<TextField
  helperText=" "
  id="demo-helper-text-aligned-no-helper"
  label="Name"
/>  

```
\<TextField
 helperText\="Please enter your name"
 id\="demo\-helper\-text\-aligned"
 label\="Name"
/\>
\<TextField
 helperText\=" "
 id\="demo\-helper\-text\-aligned\-no\-helper"
 label\="Name"
/\>Press `Enter` to start editingIntegration with 3rd party input libraries
------------------------------------------

You can use third\-party libraries to format an input.
You have to provide a custom implementation of the `<input>` element with the `inputComponent` property.


The following demo uses the react\-imask and react\-number\-format libraries. The same concept could be applied to, for example react\-stripe\-element.


react\-imaskreact\-number\-formatJSTSShow codeThe provided input component should expose a ref with a value that implements the following interface:



```
interface InputElement {
  focus: void;
  value?: string;
}

```
CopyCopied(or Ctrl \+ C)

```
const MyInputComponent = React.forwardRef((props, ref) => {
  const { component: Component, ...other } = props;

  // implement `InputElement` interface
  React.useImperativeHandle(ref,  => ({
    focus:  => {
      // logic to focus the rendered component from 3rd party belongs here
    },
    // hiding the value e.g. react-stripe-elements
  }));

  // `Component` will be your `SomeThirdPartyComponent` from below
  return <Component {...other} />;
});

// usage
<TextField
  slotProps={{
    input: {
      inputComponent: MyInputComponent,
      inputProps: {
        component: SomeThirdPartyComponent,
      },
    },
  }}
/>;

```
CopyCopied(or Ctrl \+ C)
Accessibility
-------------

In order for the text field to be accessible, **the input should be linked to the label and the helper text**. The underlying DOM nodes should have this structure:



```
<div class="form-control">
  <label for="my-input">Email address</label>
  <input id="my-input" aria-describedby="my-helper-text" />
  <span id="my-helper-text">We'll never share your email.</span>
</div>

```
CopyCopied(or Ctrl \+ C)
* If you are using the `TextField` component, you just have to provide a unique `id` unless you're using the `TextField` only client\-side.
Until the UI is hydrated `TextField` without an explicit `id` will not have associated labels.
* If you are composing the component:



```
<FormControl>
  <InputLabel htmlFor="my-input">Email address</InputLabel>
  <Input id="my-input" aria-describedby="my-helper-text" />
  <FormHelperText id="my-helper-text">We'll never share your email.</FormHelperText>
</FormControl>

```
CopyCopied(or Ctrl \+ C)
Supplementary projects
----------------------


For more advanced use cases, you might be able to take advantage of:


* react\-hook\-form\-mui: Material UI and react\-hook\-form combined.
* formik\-material\-ui: Bindings for using Material UI with formik.
* mui\-rff: Bindings for using Material UI with React Final Form.
* @ui\-schema/ds\-material Bindings for using Material UI with UI Schema. JSON Schema compatible.


Unstyled
--------

Use the Base UI Text Field for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<FilledInput />`
* `<FormControl />`
* `<FormHelperText />`
* `<Input />`
* `<InputAdornment />`
* `<InputBase />`
* `<InputLabel />`
* `<OutlinedInput />`
* `<TextField />`



