Badge
=====

Badge generates a small badge to the top\-right of its child(ren).


Sell products, services, content and more with Squarespace.

ads via Carbon



* Feedback
* Bundle size
* Source
* Figma
* Sketch

Basic badge
-----------

Examples of badges containing text, using primary and secondary colors. The badge is applied to its children.


4JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge badgeContent={4} color="primary">
  <MailIcon color="action" />
</Badge>  

```
\<Badge badgeContent\={4} color\="primary"\>
 \<MailIcon color\="action" /\>
\</Badge\>Press `Enter` to start editingColor
-----

Use `color` prop to apply theme palette to component.


44JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge badgeContent={4} color="secondary">
  <MailIcon color="action" />
</Badge>
<Badge badgeContent={4} color="success">
  <MailIcon color="action" />
</Badge>  

```
\<Badge badgeContent\={4} color\="secondary"\>
 \<MailIcon color\="action" /\>
\</Badge\>
\<Badge badgeContent\={4} color\="success"\>
 \<MailIcon color\="action" /\>
\</Badge\>Press `Enter` to start editingCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


4JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton aria-label="cart">
  <StyledBadge badgeContent={4} color="secondary">
    <ShoppingCartIcon />
  </StyledBadge>
</IconButton>  

```
\<IconButton aria\-label\="cart"\>
 \<StyledBadge badgeContent\={4} color\="secondary"\>
 \<ShoppingCartIcon /\>
 \</StyledBadge\>
\</IconButton\>Press `Enter` to start editingBadge visibility
----------------

The visibility of badges can be controlled using the `invisible` prop.


1Show BadgeJSTSShow codeThe badge hides automatically when `badgeContent` is zero. You can override this with the `showZero` prop.


0JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge color="secondary" badgeContent={0}>
  <MailIcon />
</Badge>
<Badge color="secondary" badgeContent={0} showZero>
  <MailIcon />
</Badge>  

```
\<Badge color\="secondary" badgeContent\={0}\>
 \<MailIcon /\>
\</Badge\>
\<Badge color\="secondary" badgeContent\={0} showZero\>
 \<MailIcon /\>
\</Badge\>Press `Enter` to start editingMaximum value
-------------

You can use the `max` prop to cap the value of the badge content.


9999\+999\+JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge color="secondary" badgeContent={99}>
  <MailIcon />
</Badge>
<Badge color="secondary" badgeContent={100}>
  <MailIcon />
</Badge>
<Badge color="secondary" badgeContent={1000} max={999}>
  <MailIcon />
</Badge>  

```
\<Badge color\="secondary" badgeContent\={99}\>
 \<MailIcon /\>
\</Badge\>
\<Badge color\="secondary" badgeContent\={100}\>
 \<MailIcon /\>
\</Badge\>
\<Badge color\="secondary" badgeContent\={1000} max\={999}\>
 \<MailIcon /\>
\</Badge\>Press `Enter` to start editingDot badge
---------

The `dot` prop changes a badge into a small dot. This can be used as a notification that something has changed without giving a count.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge color="secondary" variant="dot">
  <MailIcon />
</Badge>  

```
\<Badge color\="secondary" variant\="dot"\>
 \<MailIcon /\>
\</Badge\>Press `Enter` to start editingBadge overlap
-------------

You can use the `overlap` prop to place the badge relative to the corner of the wrapped element.


  JSTSExpand codeCopy(or Ctrl \+ C)
```
<Badge color="secondary" badgeContent=" ">
  {rectangle}
</Badge>
<Badge color="secondary" badgeContent=" " variant="dot">
  {rectangle}
</Badge>
<Badge color="secondary" overlap="circular" badgeContent=" ">
  {circle}
</Badge>
<Badge color="secondary" overlap="circular" badgeContent=" " variant="dot">
  {circle}
</Badge>  

```
\<Badge color\="secondary" badgeContent\=" "\>
 {rectangle}
\</Badge\>
\<Badge color\="secondary" badgeContent\=" " variant\="dot"\>
 {rectangle}
\</Badge\>
\<Badge color\="secondary" overlap\="circular" badgeContent\=" "\>
 {circle}
\</Badge\>
\<Badge color\="secondary" overlap\="circular" badgeContent\=" " variant\="dot"\>
 {circle}
\</Badge\>Press `Enter` to start editingBadge alignment
---------------

You can use the `anchorOrigin` prop to move the badge to any corner of the wrapped element.


VerticalTopBottomHorizontalRightLeft11299\+999\+Copy(or Ctrl \+ C)
```
<Badge
  anchorOrigin={{
    vertical: 'top',
    horizontal: 'right',
  }}
>
```
Accessibility
-------------

You can't rely on the content of the badge to be announced correctly.
You should provide a full description, for instance, with `aria-label`:


99\+JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton aria-label={notificationsLabel(100)}>
  <Badge badgeContent={100} color="secondary">
    <MailIcon />
  </Badge>
</IconButton>  

```
\<IconButton aria\-label\={notificationsLabel(100\)}\>
 \<Badge badgeContent\={100} color\="secondary"\>
 \<MailIcon /\>
 \</Badge\>
\</IconButton\>Press `Enter` to start editingUnstyled
--------

Use the Base UI Badge for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Badge />`



