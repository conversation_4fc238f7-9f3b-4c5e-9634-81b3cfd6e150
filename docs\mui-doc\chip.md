Chip
====

Chips are compact elements that represent an input, attribute, or action.


Which platform wins when it comes to ease\-of\-use, reporting, and costs?

ads via Carbon



Chips allow users to enter information, make selections, filter content, or trigger actions.


While included here as a standalone component, the most common use will
be in some form of input, so some of the behavior demonstrated here is
not shown in context.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Basic chip
----------

The `Chip` component supports outlined and filled styling.


Chip FilledChip OutlinedJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip label="Chip Filled" />
<Chip label="Chip Outlined" variant="outlined" />  

```
\<Chip label\="Chip Filled" /\>
\<Chip label\="Chip Outlined" variant\="outlined" /\>Press `Enter` to start editingChip actions
------------

You can use the following actions.


* Chips with the `onClick` prop defined change appearance on focus, hover, and click.
* Chips with the `onDelete` prop defined will display a delete icon which changes appearance on hover.


### Clickable

ClickableClickableJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip label="Clickable" onClick={handleClick} />
<Chip label="Clickable" variant="outlined" onClick={handleClick} />  

```
\<Chip label\="Clickable" onClick\={handleClick} /\>
\<Chip label\="Clickable" variant\="outlined" onClick\={handleClick} /\>Press `Enter` to start editing### Deletable

DeletableDeletableJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip label="Deletable" onDelete={handleDelete} />
<Chip label="Deletable" variant="outlined" onDelete={handleDelete} />  

```
\<Chip label\="Deletable" onDelete\={handleDelete} /\>
\<Chip label\="Deletable" variant\="outlined" onDelete\={handleDelete} /\>Press `Enter` to start editing### Clickable and deletable

Clickable DeletableClickable DeletableJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip
  label="Clickable Deletable"
  onClick={handleClick}
  onDelete={handleDelete}
/>
<Chip
  label="Clickable Deletable"
  variant="outlined"
  onClick={handleClick}
  onDelete={handleDelete}
/>  

```
\<Chip
 label\="Clickable Deletable"
 onClick\={handleClick}
 onDelete\={handleDelete}
/\>
\<Chip
 label\="Clickable Deletable"
 variant\="outlined"
 onClick\={handleClick}
 onDelete\={handleDelete}
/\>Press `Enter` to start editing### Clickable link

Clickable LinkClickable LinkJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip label="Clickable Link" component="a" href="#basic-chip" clickable />
<Chip
  label="Clickable Link"
  component="a"
  href="#basic-chip"
  variant="outlined"
  clickable
/>  

```
\<Chip label\="Clickable Link" component\="a" clickable /\>
\<Chip
 label\="Clickable Link"
 component\="a"
 
 variant\="outlined"
 clickable
/\>Press `Enter` to start editing### Custom delete icon

Custom delete iconCustom delete iconJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip
  label="Custom delete icon"
  onClick={handleClick}
  onDelete={handleDelete}
  deleteIcon={<DoneIcon />}
/>
<Chip
  label="Custom delete icon"
  onClick={handleClick}
  onDelete={handleDelete}
  deleteIcon={<DeleteIcon />}
  variant="outlined"
/>  

```
\<Chip
 label\="Custom delete icon"
 onClick\={handleClick}
 onDelete\={handleDelete}
 deleteIcon\={\<DoneIcon /\>}
/\>
\<Chip
 label\="Custom delete icon"
 onClick\={handleClick}
 onDelete\={handleDelete}
 deleteIcon\={\<DeleteIcon /\>}
 variant\="outlined"
/\>Press `Enter` to start editingChip adornments
---------------

You can add ornaments to the beginning of the component.


Use the `avatar` prop to add an avatar or use the `icon` prop to add an icon.


### Avatar chip

MAvatarAvatarJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip avatar={<Avatar>M</Avatar>} label="Avatar" />
<Chip
  avatar={<Avatar alt="Natacha" src="/static/images/avatar/1.jpg" />}
  label="Avatar"
  variant="outlined"
/>  

```
\<Chip avatar\={\<Avatar\>M\</Avatar\>} label\="Avatar" /\>
\<Chip
 avatar\={\<Avatar alt\="Natacha" src\="/static/images/avatar/1\.jpg" /\>}
 label\="Avatar"
 variant\="outlined"
/\>Press `Enter` to start editing### Icon chip

With IconWith IconJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip icon={<FaceIcon />} label="With Icon" />
<Chip icon={<FaceIcon />} label="With Icon" variant="outlined" />  

```
\<Chip icon\={\<FaceIcon /\>} label\="With Icon" /\>
\<Chip icon\={\<FaceIcon /\>} label\="With Icon" variant\="outlined" /\>Press `Enter` to start editingColor chip
----------

You can use the `color` prop to define a color from theme palette.


primarysuccessprimarysuccessJSTSExpand codeCopy(or Ctrl \+ C)
```
<Stack direction="row" spacing={1}>
  <Chip label="primary" color="primary" />
  <Chip label="success" color="success" />
</Stack>
<Stack direction="row" spacing={1}>
  <Chip label="primary" color="primary" variant="outlined" />
  <Chip label="success" color="success" variant="outlined" />
</Stack>  

```
\<Stack direction\="row" spacing\={1}\>
 \<Chip label\="primary" color\="primary" /\>
 \<Chip label\="success" color\="success" /\>
\</Stack\>
\<Stack direction\="row" spacing\={1}\>
 \<Chip label\="primary" color\="primary" variant\="outlined" /\>
 \<Chip label\="success" color\="success" variant\="outlined" /\>
\</Stack\>Press `Enter` to start editingSizes chip
----------

You can use the `size` prop to define a small Chip.


SmallSmallJSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip label="Small" size="small" />
<Chip label="Small" size="small" variant="outlined" />  

```
\<Chip label\="Small" size\="small" /\>
\<Chip label\="Small" size\="small" variant\="outlined" /\>Press `Enter` to start editingMultiline chip
--------------

By default, Chips displays labels only in a single line.
To have them support multiline content, use the `sx` prop to add `height:auto` to the Chip component, and `whiteSpace: normal` to the `label` styles.


This is a chip that has multiple lines.JSTSExpand codeCopy(or Ctrl \+ C)
```
<Chip
  sx={{
    height: 'auto',
    '& .MuiChip-label': {
      display: 'block',
      whiteSpace: 'normal',
    },
  }}
  label="This is a chip that has multiple lines."
/>  

```
\<Chip
 sx\={{
 height: 'auto',
 '\& .MuiChip\-label': {
 display: 'block',
 whiteSpace: 'normal',
 },
 }}
 label\="This is a chip that has multiple lines."
/\>Press `Enter` to start editingChip array
----------

An example of rendering multiple chips from an array of values.
Deleting a chip removes it from the array. Note that since no
`onClick` prop is defined, the `Chip` can be focused, but does not
gain depth while clicked or touched.


* Angular
* jQuery
* Polymer
* React
* Vue.js
JSTSShow codeChip playground
---------------

Chip ComponentvariantfilledoutlinedcolordefaultprimarysecondaryerrorinfosuccesswarningsizemediumsmalliconnoneiconavatarnoneletterimgonDeletenonedefaultcustomCopy(or Ctrl \+ C)
```
<Chip />
```
Accessibility
-------------

If the Chip is deletable or clickable then it is a button in tab order. When the Chip is focused (for example when tabbing) releasing (`keyup` event) `Backspace` or `Delete` will call the `onDelete` handler while releasing `Escape` will blur the Chip.


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Chip />`



