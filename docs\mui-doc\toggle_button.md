Toggle Button
=============

A Toggle Button can be used to group related options.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



To emphasize groups of related Toggle buttons,
a group should share a common container.
The `ToggleButtonGroup` controls the selected state of its child buttons when given its own `value` prop.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Exclusive selection
-------------------

With exclusive selection, selecting one option deselects any other.


In this example, text justification toggle buttons present options for left, center, right, and fully justified text (disabled), with only one item available for selection at a time.


**Note**: Exclusive selection does not enforce that a button must be active. For that effect see enforce value set.


JSTSShow codeMultiple selection
------------------

Multiple selection allows for logically\-grouped options, like bold, italic, and underline, to have multiple options selected.


JSTSShow codeSize
----

For larger or smaller buttons, use the `size` prop.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<ToggleButtonGroup size="small" {...control} aria-label="Small sizes">
  {children}
</ToggleButtonGroup>
<ToggleButtonGroup {...control} aria-label="Medium sizes">
  {children}
</ToggleButtonGroup>
<ToggleButtonGroup size="large" {...control} aria-label="Large sizes">
  {children}
</ToggleButtonGroup>  

```
\<ToggleButtonGroup size\="small" {...control} aria\-label\="Small sizes"\>
 {children}
\</ToggleButtonGroup\>
\<ToggleButtonGroup {...control} aria\-label\="Medium sizes"\>
 {children}
\</ToggleButtonGroup\>
\<ToggleButtonGroup size\="large" {...control} aria\-label\="Large sizes"\>
 {children}
\</ToggleButtonGroup\>Press `Enter` to start editingColor
-----

WebAndroidiOSJSTSExpand codeCopy(or Ctrl \+ C)
```
<ToggleButtonGroup
  color="primary"
  value={alignment}
  exclusive
  onChange={handleChange}
  aria-label="Platform"
>
  <ToggleButton value="web">Web</ToggleButton>
  <ToggleButton value="android">Android</ToggleButton>
  <ToggleButton value="ios">iOS</ToggleButton>
</ToggleButtonGroup>  

```
\<ToggleButtonGroup
 color\="primary"
 value\={alignment}
 exclusive
 onChange\={handleChange}
 aria\-label\="Platform"
\>
 \<ToggleButton value\="web"\>Web\</ToggleButton\>
 \<ToggleButton value\="android"\>Android\</ToggleButton\>
 \<ToggleButton value\="ios"\>iOS\</ToggleButton\>
\</ToggleButtonGroup\>Press `Enter` to start editingVertical buttons
----------------

The buttons can be stacked vertically with the `orientation` prop set to "vertical".


JSTSExpand codeCopy(or Ctrl \+ C)
```
<ToggleButtonGroup
  orientation="vertical"
  value={view}
  exclusive
  onChange={handleChange}
>
  <ToggleButton value="list" aria-label="list">
    <ViewListIcon />
  </ToggleButton>
  <ToggleButton value="module" aria-label="module">
    <ViewModuleIcon />
  </ToggleButton>
  <ToggleButton value="quilt" aria-label="quilt">
    <ViewQuiltIcon />
  </ToggleButton>
</ToggleButtonGroup>  

```
\<ToggleButtonGroup
 orientation\="vertical"
 value\={view}
 exclusive
 onChange\={handleChange}
\>
 \<ToggleButton value\="list" aria\-label\="list"\>
 \<ViewListIcon /\>
 \</ToggleButton\>
 \<ToggleButton value\="module" aria\-label\="module"\>
 \<ViewModuleIcon /\>
 \</ToggleButton\>
 \<ToggleButton value\="quilt" aria\-label\="quilt"\>
 \<ViewQuiltIcon /\>
 \</ToggleButton\>
\</ToggleButtonGroup\>Press `Enter` to start editingEnforce value set
-----------------

If you want to enforce that at least one button must be active, you can adapt your handleChange function.



```
const handleAlignment = (event, newAlignment) => {
  if (newAlignment !== null) {
    setAlignment(newAlignment);
  }
};

const handleDevices = (event, newDevices) => {
  if (newDevices.length) {
    setDevices(newDevices);
  }
};

```
CopyCopied(or Ctrl \+ C)
JSTSShow codeStandalone toggle button
------------------------

JSTSExpand codeCopy(or Ctrl \+ C)
```
<ToggleButton
  value="check"
  selected={selected}
  onChange={ => setSelected((prevSelected) => !prevSelected)}
>
  <CheckIcon />
</ToggleButton>  

```
\<ToggleButton
 value\="check"
 selected\={selected}
 onChange\={ \=\> setSelected((prevSelected) \=\> !prevSelected)}
\>
 \<CheckIcon /\>
\</ToggleButton\>Press `Enter` to start editingCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


JSTSShow codeAccessibility
-------------

### ARIA

* ToggleButtonGroup has `role="group"`. You should provide an accessible label with `aria-label="label"`, `aria-labelledby="id"` or `<label>`.
* ToggleButton sets `aria-pressed="<bool>"` according to the button state. You should label each button with `aria-label`.


### Keyboard

At present, toggle buttons are in DOM order. Navigate between them with the tab key. The button behavior follows standard keyboard semantics.


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<ToggleButton />`
* `<ToggleButtonGroup />`



