Data Grid \- Row definition
===========================

Define your rows.


Squarespace tools make it easy to create a beautiful and unique website.

ads via Carbon



Feeding data
------------

The rows can be defined with the `rows` prop, which expects an array of objects.



The `rows` prop should keep the same reference between two renders except when you want to apply new rows.
Otherwise, the Data Grid will re\-apply heavy work like sorting and filtering.


nameReactMUIRows per page:

1001–2 of 2

JSTSExpand codeCopy(or Ctrl \+ C)
```
<DataGrid
  columns={[{ field: 'name' }]}
  rows={[
    { id: 1, name: 'React' },
    { id: 2, name: '<PERSON><PERSON>' },
  ]}
/>  

```
\<DataGrid
 columns\={\[{ field: 'name' }]}
 rows\={\[
 { id: 1, name: 'React' },
 { id: 2, name: '<PERSON><PERSON>' },
 ]}
/\>Press `Enter` to start editingRow identifier
--------------

Each row must have a unique identifier.


This identifier is used internally to identify the row in the various models—for instance, the row selection model—and to track the row across updates.


By default, the Data Grid looks for a property named `id` in the data set to get that identifier.


If the row's identifier is not called `id`, then you need to use the `getRowId` prop to tell the Data Grid where it's located.


The following demo shows how to use `getRowId` to grab the unique identifier from a property named `internalId`:



```
function getRowId(row) {
  return row.internalId;
}

<DataGrid getRowId={getRowId} />;

```
CopyCopied(or Ctrl \+ C)
nameReactMUIRows per page:

1001–2 of 2

JSTSShow codeIf no such unique identifier exists in the data set, then you must create it by some other means, but this scenario should be avoided because it leads to issues with other features of the Data Grid.


Note that it is not necessary to create a column to display the unique identifier data.
The Data Grid pulls this information directly from the data set itself, not from anything displayed on the screen.



Just like the `rows` prop, the `getRowId` function should keep the same JavaScript reference between two renders.
Otherwise, the Data Grid will re\-apply heavy work like sorting and filtering.


It could be achieved by either defining the prop outside the component scope or by memoizing using the `React.useCallback` hook if the function reuses something from the component scope.


Styling rows
------------

You can check the styling rows section for more information.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

RecipesRow updates

---

•

Blog•

Store
