import type { ApiResponse } from '@/types/api';
import type { LoginFormData, User } from '@/types';

// Mock login function
export const login = async (data: LoginFormData): Promise<ApiResponse<User>> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      // Mock successful login
      if (data.username === 'admin' && data.password === 'admin') {
        resolve({
          code: '0',
          data: {
            id: '1',
            username: 'admin',
            token: 'mock-token-12345',
            role: 'admin',
          },
          msg: 'Login successful',
        });
      } else {
        // Mock failed login
        resolve({
          code: '1',
          data: {} as User,
          msg: 'Invalid username or password',
        });
      }
    }, 500); // Simulate network delay
  });
};
