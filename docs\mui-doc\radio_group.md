Radio Group
===========

The Radio Group allows the user to select one option from a set.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



Use radio buttons when the user needs to see all available options.
If available options can be collapsed, consider using a Select component because it uses less space.


Radio buttons should have the most commonly used option selected by default.


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Radio group
-----------

`RadioGroup` is a helpful wrapper used to group `Radio` components that provides an easier API, and proper keyboard accessibility to the group.


GenderFemaleMaleOtherJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormControl>
  <FormLabel id="demo-radio-buttons-group-label">Gender</FormLabel>
  <RadioGroup
    aria-labelledby="demo-radio-buttons-group-label"
    defaultValue="female"
    name="radio-buttons-group"
  >
    <FormControlLabel value="female" control={<Radio />} label="Female" />
    <FormControlLabel value="male" control={<Radio />} label="Male" />
    <FormControlLabel value="other" control={<Radio />} label="Other" />
  </RadioGroup>
</FormControl>  

```
\<FormControl\>
 \<FormLabel id\="demo\-radio\-buttons\-group\-label"\>Gender\</FormLabel\>
 \<RadioGroup
 aria\-labelledby\="demo\-radio\-buttons\-group\-label"
 defaultValue\="female"
 name\="radio\-buttons\-group"
 \>
 \<FormControlLabel value\="female" control\={\<Radio /\>} label\="Female" /\>
 \<FormControlLabel value\="male" control\={\<Radio /\>} label\="Male" /\>
 \<FormControlLabel value\="other" control\={\<Radio /\>} label\="Other" /\>
 \</RadioGroup\>
\</FormControl\>Press `Enter` to start editing### Direction

To lay out the buttons horizontally, set the `row` prop:


GenderFemaleMaleOtherotherJSTSShow code### Controlled

You can control the radio with the `value` and `onChange` props:


GenderFemaleMaleJSTSExpand codeCopy(or Ctrl \+ C)
```
<FormControl>
  <FormLabel id="demo-controlled-radio-buttons-group">Gender</FormLabel>
  <RadioGroup
    aria-labelledby="demo-controlled-radio-buttons-group"
    name="controlled-radio-buttons-group"
    value={value}
    onChange={handleChange}
  >
    <FormControlLabel value="female" control={<Radio />} label="Female" />
    <FormControlLabel value="male" control={<Radio />} label="Male" />
  </RadioGroup>
</FormControl>  

```
\<FormControl\>
 \<FormLabel id\="demo\-controlled\-radio\-buttons\-group"\>Gender\</FormLabel\>
 \<RadioGroup
 aria\-labelledby\="demo\-controlled\-radio\-buttons\-group"
 name\="controlled\-radio\-buttons\-group"
 value\={value}
 onChange\={handleChange}
 \>
 \<FormControlLabel value\="female" control\={\<Radio /\>} label\="Female" /\>
 \<FormControlLabel value\="male" control\={\<Radio /\>} label\="Male" /\>
 \</RadioGroup\>
\</FormControl\>Press `Enter` to start editingStandalone radio buttons
------------------------

`Radio` can also be used standalone, without the RadioGroup wrapper.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Radio
  checked={selectedValue === 'a'}
  onChange={handleChange}
  value="a"
  name="radio-buttons"
  inputProps={{ 'aria-label': 'A' }}
/>
<Radio
  checked={selectedValue === 'b'}
  onChange={handleChange}
  value="b"
  name="radio-buttons"
  inputProps={{ 'aria-label': 'B' }}
/>  

```
\<Radio
 checked\={selectedValue \=\=\= 'a'}
 onChange\={handleChange}
 value\="a"
 name\="radio\-buttons"
 inputProps\={{ 'aria\-label': 'A' }}
/\>
\<Radio
 checked\={selectedValue \=\=\= 'b'}
 onChange\={handleChange}
 value\="b"
 name\="radio\-buttons"
 inputProps\={{ 'aria\-label': 'B' }}
/\>Press `Enter` to start editingSize
----

Use the `size` prop or customize the font size of the svg icons to change the size of the radios.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Radio {...controlProps('a')} size="small" />
<Radio {...controlProps('b')} />
<Radio
  {...controlProps('c')}
  sx={{
    '& .MuiSvgIcon-root': {
      fontSize: 28,
    },
  }}
/>  

```
\<Radio {...controlProps('a')} size\="small" /\>
\<Radio {...controlProps('b')} /\>
\<Radio
 {...controlProps('c')}
 sx\={{
 '\& .MuiSvgIcon\-root': {
 fontSize: 28,
 },
 }}
/\>Press `Enter` to start editingColor
-----

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Radio {...controlProps('a')} />
<Radio {...controlProps('b')} color="secondary" />
<Radio {...controlProps('c')} color="success" />
<Radio {...controlProps('d')} color="default" />
<Radio
  {...controlProps('e')}
  sx={{
    color: pink[800],
    '&.Mui-checked': {
      color: pink[600],
    },
  }}
/>  

```
\<Radio {...controlProps('a')} /\>
\<Radio {...controlProps('b')} color\="secondary" /\>
\<Radio {...controlProps('c')} color\="success" /\>
\<Radio {...controlProps('d')} color\="default" /\>
\<Radio
 {...controlProps('e')}
 sx\={{
 color: pink\[800],
 '\&.Mui\-checked': {
 color: pink\[600],
 },
 }}
/\>Press `Enter` to start editingLabel placement
---------------

You can change the placement of the label with the `FormControlLabel` component's `labelPlacement` prop:


Label placementBottomEndJSTSShow codeShow error
----------

In general, radio buttons should have a value selected by default. If this is not the case, you can display an error if no value is selected when the form is submitted:


Pop quiz: MUI is...The best!The worst.Choose wisely

Check AnswerJSTSShow codeCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


GenderFemaleMaleOther(Disabled option)JSTSShow code`useRadioGroup`
---------------

For advanced customization use cases, a `useRadioGroup` hook is exposed.
It returns the context value of the parent radio group.
The Radio component uses this hook internally.


### API


```
import { useRadioGroup } from '@mui/material/RadioGroup';

```
CopyCopied(or Ctrl \+ C)
#### Returns

`value` (*object*):


* `value.name` (*string* \[optional]): The name used to reference the value of the control.
* `value.onChange` (*func* \[optional]): Callback fired when a radio button is selected.
* `value.value` (*any* \[optional]): Value of the selected radio button.


#### Example

FirstSecondJSTSExpand codeCopy(or Ctrl \+ C)
```
<RadioGroup name="use-radio-group" defaultValue="first">
  <MyFormControlLabel value="first" label="First" control={<Radio />} />
  <MyFormControlLabel value="second" label="Second" control={<Radio />} />
</RadioGroup>  

```
\<RadioGroup name\="use\-radio\-group" defaultValue\="first"\>
 \<MyFormControlLabel value\="first" label\="First" control\={\<Radio /\>} /\>
 \<MyFormControlLabel value\="second" label\="Second" control\={\<Radio /\>} /\>
\</RadioGroup\>Press `Enter` to start editingWhen to use
-----------

* Checkboxes vs. Radio Buttons


Accessibility
-------------

(WAI\-ARIA: 


* All form controls should have labels, and this includes radio buttons, checkboxes, and switches. In most cases, this is done by using the `<label>` element (FormControlLabel).
* When a label can't be used, it's necessary to add an attribute directly to the input component.
In this case, you can apply the additional attribute (for example `aria-label`, `aria-labelledby`, `title`) via the `inputProps` property.



```
<Radio
  value="radioA"
  inputProps={{
    'aria-label': 'Radio A',
  }}
/>

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<FormControl />`
* `<FormControlLabel />`
* `<FormLabel />`
* `<Radio />`
* `<RadioGroup />`



