Breadcrumbs
===========

A breadcrumbs is a list of links that help visualize a page's location within a site's hierarchical structure, it allows navigation up to any of the ancestors.


Sell products, services, content and more with Squarespace.

ads via Carbon



* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Figma
* Sketch

Basic breadcrumbs
-----------------

1. MUI
2. /
3. Core
4. /
5. Breadcrumbs
JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function BasicBreadcrumbs {
  return (
    <div role="presentation" onClick={handleClick}>
      <Breadcrumbs aria-label="breadcrumb">
        <Link underline="hover" color="inherit" href="/">
          MUI
        </Link>
        <Link
          underline="hover"
          color="inherit"
          href="/material-ui/getting-started/installation/"
        >
          Core
        </Link>
        <Typography sx={{ color: 'text.primary' }}>Breadcrumbs</Typography>
      </Breadcrumbs>
    </div>
  );
}  

```
import \* as React from 'react';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent\<HTMLDivElement, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function BasicBreadcrumbs {
 return (
 \<div role\="presentation" onClick\={handleClick}\>
 \<Breadcrumbs aria\-label\="breadcrumb"\>
 \<Link underline\="hover" color\="inherit" \>
 MUI
 \</Link\>
 \<Link
 underline\="hover"
 color\="inherit"
 
 \>
 Core
 \</Link\>
 \<Typography sx\={{ color: 'text.primary' }}\>Breadcrumbs\</Typography\>
 \</Breadcrumbs\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- Use GitLab end to end. From security to development to operations. It's all here.ad by CarbonActive last breadcrumb
----------------------

Keep the last breadcrumb interactive.


1. MUI
2. /
3. Core
4. /
5. Breadcrumbs
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function ActiveLastBreadcrumb {
  return (
    <div role="presentation" onClick={handleClick}>
      <Breadcrumbs aria-label="breadcrumb">
        <Link underline="hover" color="inherit" href="/">
          MUI
        </Link>
        <Link
          underline="hover"
          color="inherit"
          href="/material-ui/getting-started/installation/"
        >
          Core
        </Link>
        <Link
          underline="hover"
          color="text.primary"
          href="/material-ui/react-breadcrumbs/"
          aria-current="page"
        >
          Breadcrumbs
        </Link>
      </Breadcrumbs>
    </div>
  );
}  

```
import \* as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent\<HTMLDivElement, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function ActiveLastBreadcrumb {
 return (
 \<div role\="presentation" onClick\={handleClick}\>
 \<Breadcrumbs aria\-label\="breadcrumb"\>
 \<Link underline\="hover" color\="inherit" \>
 MUI
 \</Link\>
 \<Link
 underline\="hover"
 color\="inherit"
 
 \>
 Core
 \</Link\>
 \<Link
 underline\="hover"
 color\="text.primary"
 
 aria\-current\="page"
 \>
 Breadcrumbs
 \</Link\>
 \</Breadcrumbs\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonCustom separator
----------------

In the following examples, we are using two string separators and an SVG icon.


1. MUI
2. ›
3. Core
4. ›
5. Breadcrumb
1. MUI
2. \-
3. Core
4. \-
5. Breadcrumb
1. MUI
2. 
3. Core
4. 
5. Breadcrumb
JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

function handleClick(event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function CustomSeparator {
  const breadcrumbs = [
    <Link underline="hover" key="1" color="inherit" href="/" onClick={handleClick}>
      MUI
    </Link>,
    <Link
      underline="hover"
      key="2"
      color="inherit"
      href="/material-ui/getting-started/installation/"
      onClick={handleClick}
    >
      Core
    </Link>,
    <Typography key="3" sx={{ color: 'text.primary' }}>
      Breadcrumb
    </Typography>,
  ];

  return (
    <Stack spacing={2}>
      <Breadcrumbs separator="›" aria-label="breadcrumb">
        {breadcrumbs}
      </Breadcrumbs>
      <Breadcrumbs separator="-" aria-label="breadcrumb">
        {breadcrumbs}
      </Breadcrumbs>
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
      >
        {breadcrumbs}
      </Breadcrumbs>
    </Stack>
  );
}  

```
import \* as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import NavigateNextIcon from '@mui/icons\-material/NavigateNext';

function handleClick(event: React.MouseEvent\<HTMLAnchorElement, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function CustomSeparator {
 const breadcrumbs \= \[
 \<Link underline\="hover" key\="1" color\="inherit" onClick\={handleClick}\>
 MUI
 \</Link\>,
 \<Link
 underline\="hover"
 key\="2"
 color\="inherit"
 
 onClick\={handleClick}
 \>
 Core
 \</Link\>,
 \<Typography key\="3" sx\={{ color: 'text.primary' }}\>
 Breadcrumb
 \</Typography\>,
 ];

 return (
 \<Stack spacing\={2}\>
 \<Breadcrumbs separator\="›" aria\-label\="breadcrumb"\>
 {breadcrumbs}
 \</Breadcrumbs\>
 \<Breadcrumbs separator\="\-" aria\-label\="breadcrumb"\>
 {breadcrumbs}
 \</Breadcrumbs\>
 \<Breadcrumbs
 separator\={\<NavigateNextIcon fontSize\="small" /\>}
 aria\-label\="breadcrumb"
 \>
 {breadcrumbs}
 \</Breadcrumbs\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonBreadcrumbs with icons
----------------------

1. MUI
2. /
3. Core
4. /
5. Breadcrumb
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import HomeIcon from '@mui/icons-material/Home';
import WhatshotIcon from '@mui/icons-material/Whatshot';
import GrainIcon from '@mui/icons-material/Grain';

function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function IconBreadcrumbs {
  return (
    <div role="presentation" onClick={handleClick}>
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          underline="hover"
          sx={{ display: 'flex', alignItems: 'center' }}
          color="inherit"
          href="/"
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          MUI
        </Link>
        <Link
          underline="hover"
          sx={{ display: 'flex', alignItems: 'center' }}
          color="inherit"
          href="/material-ui/getting-started/installation/"
        >
          <WhatshotIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Core
        </Link>
        <Typography
          sx={{ color: 'text.primary', display: 'flex', alignItems: 'center' }}
        >
          <GrainIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Breadcrumb
        </Typography>
      </Breadcrumbs>
    </div>
  );
}  

```
import \* as React from 'react';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import HomeIcon from '@mui/icons\-material/Home';
import WhatshotIcon from '@mui/icons\-material/Whatshot';
import GrainIcon from '@mui/icons\-material/Grain';

function handleClick(event: React.MouseEvent\<HTMLDivElement, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function IconBreadcrumbs {
 return (
 \<div role\="presentation" onClick\={handleClick}\>
 \<Breadcrumbs aria\-label\="breadcrumb"\>
 \<Link
 underline\="hover"
 sx\={{ display: 'flex', alignItems: 'center' }}
 color\="inherit"
 
 \>
 \<HomeIcon sx\={{ mr: 0\.5 }} fontSize\="inherit" /\>
 MUI
 \</Link\>
 \<Link
 underline\="hover"
 sx\={{ display: 'flex', alignItems: 'center' }}
 color\="inherit"
 
 \>
 \<WhatshotIcon sx\={{ mr: 0\.5 }} fontSize\="inherit" /\>
 Core
 \</Link\>
 \<Typography
 sx\={{ color: 'text.primary', display: 'flex', alignItems: 'center' }}
 \>
 \<GrainIcon sx\={{ mr: 0\.5 }} fontSize\="inherit" /\>
 Breadcrumb
 \</Typography\>
 \</Breadcrumbs\>
 \</div\>
 );
}Press `Enter` to start editing**CloudBees** \- No more pipeline interruptions. Experience faster delivery with CloudBees CI.ad by CarbonCollapsed breadcrumbs
---------------------

1. Home
2. /
3. 
4. /
5. Belts
JSTSCollapse codeCopy(or Ctrl \+ C)
```
/* eslint-disable jsx-a11y/anchor-is-valid */
import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function CollapsedBreadcrumbs {
  return (
    <div role="presentation" onClick={handleClick}>
      <Breadcrumbs maxItems={2} aria-label="breadcrumb">
        <Link underline="hover" color="inherit" href="#">
          Home
        </Link>
        <Link underline="hover" color="inherit" href="#">
          Catalog
        </Link>
        <Link underline="hover" color="inherit" href="#">
          Accessories
        </Link>
        <Link underline="hover" color="inherit" href="#">
          New Collection
        </Link>
        <Typography sx={{ color: 'text.primary' }}>Belts</Typography>
      </Breadcrumbs>
    </div>
  );
}  

```
/\* eslint\-disable jsx\-a11y/anchor\-is\-valid \*/
import \* as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';

function handleClick(event: React.MouseEvent\<HTMLDivElement, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function CollapsedBreadcrumbs {
 return (
 \<div role\="presentation" onClick\={handleClick}\>
 \<Breadcrumbs maxItems\={2} aria\-label\="breadcrumb"\>
 \<Link underline\="hover" color\="inherit" \>
 Home
 \</Link\>
 \<Link underline\="hover" color\="inherit" \>
 Catalog
 \</Link\>
 \<Link underline\="hover" color\="inherit" \>
 Accessories
 \</Link\>
 \<Link underline\="hover" color\="inherit" \>
 New Collection
 \</Link\>
 \<Typography sx\={{ color: 'text.primary' }}\>Belts\</Typography\>
 \</Breadcrumbs\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- One platform to empower Dev, Sec, and Ops teams. Start building secure software today.ad by CarbonCondensed with menu
-------------------

As an alternative, consider adding a Menu component to display the condensed links in a dropdown list:


1. Breadcrumb 1
2. /
3. 
4. /
5. Breadcrumb 5
6. /
7. Breadcrumb 6
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';

export default function CondensedWithMenu {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement> | null) => {
    if (event) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose =  => {
    setAnchorEl(null);
  };

  return (
    <React.Fragment>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        aria-labelledby="with-menu-demo-breadcrumbs"
      >
        <MenuItem onClick={handleClose}>Breadcrumb 2</MenuItem>
        <MenuItem onClick={handleClose}>Breadcrumb 3</MenuItem>
        <MenuItem onClick={handleClose}>Breadcrumb 4</MenuItem>
      </Menu>
      <Breadcrumbs aria-label="breadcrumbs">
        <Link color="primary" href="#condensed-with-menu">
          Breadcrumb 1
        </Link>
        <IconButton color="primary" size="small" onClick={handleClick}>
          <MoreHorizIcon />
        </IconButton>
        <Link color="primary" href="#condensed-with-menu">
          Breadcrumb 5
        </Link>
        <Link color="primary" href="#condensed-with-menu">
          Breadcrumb 6
        </Link>
      </Breadcrumbs>
    </React.Fragment>
  );
}  

```
import \* as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import MoreHorizIcon from '@mui/icons\-material/MoreHoriz';

export default function CondensedWithMenu {
 const \[anchorEl, setAnchorEl] \= React.useState\<HTMLButtonElement \| null\>(null);
 const open \= Boolean(anchorEl);

 const handleClick \= (event: React.MouseEvent\<HTMLButtonElement\> \| null) \=\> {
 if (event) {
 setAnchorEl(event.currentTarget);
 }
 };

 const handleClose \=  \=\> {
 setAnchorEl(null);
 };

 return (
 \<React.Fragment\>
 \<Menu
 anchorEl\={anchorEl}
 open\={open}
 onClose\={handleClose}
 aria\-labelledby\="with\-menu\-demo\-breadcrumbs"
 \>
 \<MenuItem onClick\={handleClose}\>Breadcrumb 2\</MenuItem\>
 \<MenuItem onClick\={handleClose}\>Breadcrumb 3\</MenuItem\>
 \<MenuItem onClick\={handleClose}\>Breadcrumb 4\</MenuItem\>
 \</Menu\>
 \<Breadcrumbs aria\-label\="breadcrumbs"\>
 \<Link color\="primary" \>
 Breadcrumb 1
 \</Link\>
 \<IconButton color\="primary" size\="small" onClick\={handleClick}\>
 \<MoreHorizIcon /\>
 \</IconButton\>
 \<Link color\="primary" \>
 Breadcrumb 5
 \</Link\>
 \<Link color\="primary" \>
 Breadcrumb 6
 \</Link\>
 \</Breadcrumbs\>
 \</React.Fragment\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonCustomization
-------------

Here is an example of customizing the component.
You can learn more about this in the overrides documentation page.


1. Home
2. /
3. Catalog
4. /
5. Accessories
JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { emphasize, styled } from '@mui/material/styles';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Chip from '@mui/material/Chip';
import HomeIcon from '@mui/icons-material/Home';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const StyledBreadcrumb = styled(Chip)(({ theme }) => {
  return {
    backgroundColor: theme.palette.grey[100],
    height: theme.spacing(3),
    color: (theme.vars || theme).palette.text.primary,
    fontWeight: theme.typography.fontWeightRegular,
    '&:hover, &:focus': {
      backgroundColor: emphasize(theme.palette.grey[100], 0.06),
      ...theme.applyStyles('dark', {
        backgroundColor: emphasize(theme.palette.grey[800], 0.06),
      }),
    },
    '&:active': {
      boxShadow: theme.shadows[1],
      backgroundColor: emphasize(theme.palette.grey[100], 0.12),
      ...theme.applyStyles('dark', {
        backgroundColor: emphasize(theme.palette.grey[800], 0.12),
      }),
    },
    ...theme.applyStyles('dark', {
      backgroundColor: theme.palette.grey[800],
    }),
  };
}) as typeof Chip; // TypeScript only: need a type cast here because 

function handleClick(event: React.MouseEvent<Element, MouseEvent>) {
  event.preventDefault;
  console.info('You clicked a breadcrumb.');
}

export default function CustomizedBreadcrumbs {
  return (
    <div role="presentation" onClick={handleClick}>
      <Breadcrumbs aria-label="breadcrumb">
        <StyledBreadcrumb
          component="a"
          href="#"
          label="Home"
          icon={<HomeIcon fontSize="small" />}
        />
        <StyledBreadcrumb component="a" href="#" label="Catalog" />
        <StyledBreadcrumb
          label="Accessories"
          deleteIcon={<ExpandMoreIcon />}
          onDelete={handleClick}
        />
      </Breadcrumbs>
    </div>
  );
}  

```
import \* as React from 'react';
import { emphasize, styled } from '@mui/material/styles';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Chip from '@mui/material/Chip';
import HomeIcon from '@mui/icons\-material/Home';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';

const StyledBreadcrumb \= styled(Chip)(({ theme }) \=\> {
 return {
 backgroundColor: theme.palette.grey\[100],
 height: theme.spacing(3\),
 color: (theme.vars \|\| theme).palette.text.primary,
 fontWeight: theme.typography.fontWeightRegular,
 '\&:hover, \&:focus': {
 backgroundColor: emphasize(theme.palette.grey\[100], 0\.06\),
 ...theme.applyStyles('dark', {
 backgroundColor: emphasize(theme.palette.grey\[800], 0\.06\),
 }),
 },
 '\&:active': {
 boxShadow: theme.shadows\[1],
 backgroundColor: emphasize(theme.palette.grey\[100], 0\.12\),
 ...theme.applyStyles('dark', {
 backgroundColor: emphasize(theme.palette.grey\[800], 0\.12\),
 }),
 },
 ...theme.applyStyles('dark', {
 backgroundColor: theme.palette.grey\[800],
 }),
 };
}) as typeof Chip; // TypeScript only: need a type cast here because 

function handleClick(event: React.MouseEvent\<Element, MouseEvent\>) {
 event.preventDefault;
 console.info('You clicked a breadcrumb.');
}

export default function CustomizedBreadcrumbs {
 return (
 \<div role\="presentation" onClick\={handleClick}\>
 \<Breadcrumbs aria\-label\="breadcrumb"\>
 \<StyledBreadcrumb
 component\="a"
 
 label\="Home"
 icon\={\<HomeIcon fontSize\="small" /\>}
 /\>
 \<StyledBreadcrumb component\="a" label\="Catalog" /\>
 \<StyledBreadcrumb
 label\="Accessories"
 deleteIcon\={\<ExpandMoreIcon /\>}
 onDelete\={handleClick}
 /\>
 \</Breadcrumbs\>
 \</div\>
 );
}Press `Enter` to start editing**AG Grid** \- Create high\-performance JavaScript data grids with advanced features in just a few lines of code.ad by CarbonIntegration with react\-router
------------------------------

1. Home
2. /
3. Inbox
* Inbox
* + Important
* Trash
* Spam
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Link, { LinkProps } from '@mui/material/Link';
import { ListItemProps } from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import Collapse from '@mui/material/Collapse';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import {
  Link as RouterLink,
  Route,
  Routes,
  MemoryRouter,
  useLocation,
} from 'react-router';

interface ListItemLinkProps extends ListItemProps {
  to: string;
  open?: boolean;
}

const breadcrumbNameMap: { [key: string]: string } = {
  '/inbox': 'Inbox',
  '/inbox/important': 'Important',
  '/trash': 'Trash',
  '/spam': 'Spam',
  '/drafts': 'Drafts',
};

function ListItemLink(props: ListItemLinkProps) {
  const { to, open, ...other } = props;
  const primary = breadcrumbNameMap[to];

  let icon = null;
  if (open != null) {
    icon = open ? <ExpandLess /> : <ExpandMore />;
  }

  return (
    <li>
      <ListItemButton component={RouterLink as any} to={to} {...other}>
        <ListItemText primary={primary} />
        {icon}
      </ListItemButton>
    </li>
  );
}

interface LinkRouterProps extends LinkProps {
  to: string;
  replace?: boolean;
}

function LinkRouter(props: LinkRouterProps) {
  return <Link {...props} component={RouterLink as any} />;
}

function Page {
  const location = useLocation;
  const pathnames = location.pathname.split('/').filter((x) => x);

  return (
    <Breadcrumbs aria-label="breadcrumb">
      <LinkRouter underline="hover" color="inherit" to="/">
        Home
      </LinkRouter>
      {pathnames.map((value, index) => {
        const last = index === pathnames.length - 1;
        const to = `/${pathnames.slice(0, index + 1).join('/')}`;

        return last ? (
          <Typography key={to} sx={{ color: 'text.primary' }}>
            {breadcrumbNameMap[to]}
          </Typography>
        ) : (
          <LinkRouter underline="hover" color="inherit" to={to} key={to}>
            {breadcrumbNameMap[to]}
          </LinkRouter>
        );
      })}
    </Breadcrumbs>
  );
}

export default function RouterBreadcrumbs {
  const [open, setOpen] = React.useState(true);

  const handleClick =  => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <MemoryRouter initialEntries={['/inbox']} initialIndex={0}>
      <Box sx={{ display: 'flex', flexDirection: 'column', width: 360 }}>
        <Routes>
          <Route path="*" element={<Page />} />
        </Routes>
        <Box
          sx={{ bgcolor: 'background.paper', mt: 1 }}
          component="nav"
          aria-label="mailbox folders"
        >
          <List>
            <ListItemLink to="/inbox" open={open} onClick={handleClick} />
            <Collapse component="li" in={open} timeout="auto" unmountOnExit>
              <List disablePadding>
                <ListItemLink sx={{ pl: 4 }} to="/inbox/important" />
              </List>
            </Collapse>
            <ListItemLink to="/trash" />
            <ListItemLink to="/spam" />
          </List>
        </Box>
      </Box>
    </MemoryRouter>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Link, { LinkProps } from '@mui/material/Link';
import { ListItemProps } from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import Collapse from '@mui/material/Collapse';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import ExpandLess from '@mui/icons\-material/ExpandLess';
import ExpandMore from '@mui/icons\-material/ExpandMore';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import {
 Link as RouterLink,
 Route,
 Routes,
 MemoryRouter,
 useLocation,
} from 'react\-router';

interface ListItemLinkProps extends ListItemProps {
 to: string;
 open?: boolean;
}

const breadcrumbNameMap: { \[key: string]: string } \= {
 '/inbox': 'Inbox',
 '/inbox/important': 'Important',
 '/trash': 'Trash',
 '/spam': 'Spam',
 '/drafts': 'Drafts',
};

function ListItemLink(props: ListItemLinkProps) {
 const { to, open, ...other } \= props;
 const primary \= breadcrumbNameMap\[to];

 let icon \= null;
 if (open !\= null) {
 icon \= open ? \<ExpandLess /\> : \<ExpandMore /\>;
 }

 return (
 \<li\>
 \<ListItemButton component\={RouterLink as any} to\={to} {...other}\>
 \<ListItemText primary\={primary} /\>
 {icon}
 \</ListItemButton\>
 \</li\>
 );
}

interface LinkRouterProps extends LinkProps {
 to: string;
 replace?: boolean;
}

function LinkRouter(props: LinkRouterProps) {
 return \<Link {...props} component\={RouterLink as any} /\>;
}

function Page {
 const location \= useLocation;
 const pathnames \= location.pathname.split('/').filter((x) \=\> x);

 return (
 \<Breadcrumbs aria\-label\="breadcrumb"\>
 \<LinkRouter underline\="hover" color\="inherit" to\="/"\>
 Home
 \</LinkRouter\>
 {pathnames.map((value, index) \=\> {
 const last \= index \=\=\= pathnames.length \- 1;
 const to \= \`/${pathnames.slice(0, index \+ 1\).join('/')}\`;

 return last ? (
 \<Typography key\={to} sx\={{ color: 'text.primary' }}\>
 {breadcrumbNameMap\[to]}
 \</Typography\>
 ) : (
 \<LinkRouter underline\="hover" color\="inherit" to\={to} key\={to}\>
 {breadcrumbNameMap\[to]}
 \</LinkRouter\>
 );
 })}
 \</Breadcrumbs\>
 );
}

export default function RouterBreadcrumbs {
 const \[open, setOpen] \= React.useState(true);

 const handleClick \=  \=\> {
 setOpen((prevOpen) \=\> !prevOpen);
 };

 return (
 \<MemoryRouter initialEntries\={\['/inbox']} initialIndex\={0}\>
 \<Box sx\={{ display: 'flex', flexDirection: 'column', width: 360 }}\>
 \<Routes\>
 \<Route path\="\*" element\={\<Page /\>} /\>
 \</Routes\>
 \<Box
 sx\={{ bgcolor: 'background.paper', mt: 1 }}
 component\="nav"
 aria\-label\="mailbox folders"
 \>
 \<List\>
 \<ListItemLink to\="/inbox" open\={open} onClick\={handleClick} /\>
 \<Collapse component\="li" in\={open} timeout\="auto" unmountOnExit\>
 \<List disablePadding\>
 \<ListItemLink sx\={{ pl: 4 }} to\="/inbox/important" /\>
 \</List\>
 \</Collapse\>
 \<ListItemLink to\="/trash" /\>
 \<ListItemLink to\="/spam" /\>
 \</List\>
 \</Box\>
 \</Box\>
 \</MemoryRouter\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by CarbonAccessibility
-------------

(WAI\-ARIA: 


Be sure to add a `aria-label` description on the `Breadcrumbs` component.


The accessibility of this component relies on:


* The set of links is structured using an ordered list (`<ol>` element).
* To prevent screen reader announcement of the visual separators between links, they are hidden with `aria-hidden`.
* A nav element labeled with `aria-label` identifies the structure as a breadcrumb trail and makes it a navigation landmark so that it is easy to locate.


Experimental APIs \- Toolpad
----------------------------

### Page Container

The PageContainer component in `@toolpad/core` is the ideal wrapper for the content of your dashboard. It makes the Material UI Container navigation\-aware and extends it with page title, breadcrumbs, actions, and more.


1. Inbox
2. /
3. All
#### All

DownloadPrintJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, createTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import {
  PageContainer,
  PageHeader,
  PageHeaderToolbar,
} from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import PrintIcon from '@mui/icons-material/Print';
import DownloadIcon from '@mui/icons-material/Download';

const NAVIGATION: Navigation = [
  { segment: 'inbox', title: 'Inbox' },
  {
    segment: 'inbox/all',
    title: 'All',
    icon: <DashboardIcon />,
  },
];

function useDemoRouter(initialPath: string): Router {
  const [pathname, setPathname] = React.useState(initialPath);

  const router = React.useMemo( => {
    return {
      pathname,
      searchParams: new URLSearchParams,
      navigate: (path: string | URL) => setPathname(String(path)),
    };
  }, [pathname]);

  return router;
}

const Skeleton = styled('div')<{ height: number }>(({ theme, height }) => ({
  backgroundColor: (theme.vars || theme).palette.action.hover,
  borderRadius: (theme.vars || theme).shape.borderRadius,
  height,
  content: '" "',
}));

function CustomPageToolbar {
  return (
    <PageHeaderToolbar>
      <Stack direction="row" spacing={1} alignItems="center">
        <Button
          variant="outlined"
          size="small"
          color="neutral"
          startIcon={<DownloadIcon fontSize="inherit" />}
        >
          Download
        </Button>
        <Button
          variant="outlined"
          size="small"
          color="neutral"
          startIcon={<PrintIcon fontSize="inherit" />}
        >
          Print
        </Button>
      </Stack>
    </PageHeaderToolbar>
  );
}

function CustomPageHeader {
  return <PageHeader slots={{ toolbar: CustomPageToolbar }} />;
}

const demoTheme = createTheme({
  colorSchemes: { light: true, dark: true },
});

export default function PageContainerBasic(props: any) {
  const { window } = props;
  const router = useDemoRouter('/inbox/all');
  // Remove this const when copying and pasting into your project.
  const demoWindow = window ? window : undefined;

  return (
    <AppProvider
      navigation={NAVIGATION}
      router={router}
      theme={demoTheme}
      window={demoWindow}
      branding={{
        title: 'ACME Inc.',
      }}
    >
      <Paper sx={{ p: 2, width: '100%' }}>
        <PageContainer
          slots={{
            header: CustomPageHeader,
          }}
        >
          <Grid container spacing={1}>
            <Grid size={5} />
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={4}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={8}>
              <Skeleton height={100} />
            </Grid>
          </Grid>
        </PageContainer>
      </Paper>
    </AppProvider>
  );
}  

```
import \* as React from 'react';
import { styled, createTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons\-material/Dashboard';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import {
 PageContainer,
 PageHeader,
 PageHeaderToolbar,
} from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import PrintIcon from '@mui/icons\-material/Print';
import DownloadIcon from '@mui/icons\-material/Download';

const NAVIGATION: Navigation \= \[
 { segment: 'inbox', title: 'Inbox' },
 {
 segment: 'inbox/all',
 title: 'All',
 icon: \<DashboardIcon /\>,
 },
];

function useDemoRouter(initialPath: string): Router {
 const \[pathname, setPathname] \= React.useState(initialPath);

 const router \= React.useMemo( \=\> {
 return {
 pathname,
 searchParams: new URLSearchParams,
 navigate: (path: string \| URL) \=\> setPathname(String(path)),
 };
 }, \[pathname]);

 return router;
}

const Skeleton \= styled('div')\<{ height: number }\>(({ theme, height }) \=\> ({
 backgroundColor: (theme.vars \|\| theme).palette.action.hover,
 borderRadius: (theme.vars \|\| theme).shape.borderRadius,
 height,
 content: '" "',
}));

function CustomPageToolbar {
 return (
 \<PageHeaderToolbar\>
 \<Stack direction\="row" spacing\={1} alignItems\="center"\>
 \<Button
 variant\="outlined"
 size\="small"
 color\="neutral"
 startIcon\={\<DownloadIcon fontSize\="inherit" /\>}
 \>
 Download
 \</Button\>
 \<Button
 variant\="outlined"
 size\="small"
 color\="neutral"
 startIcon\={\<PrintIcon fontSize\="inherit" /\>}
 \>
 Print
 \</Button\>
 \</Stack\>
 \</PageHeaderToolbar\>
 );
}

function CustomPageHeader {
 return \<PageHeader slots\={{ toolbar: CustomPageToolbar }} /\>;
}

const demoTheme \= createTheme({
 colorSchemes: { light: true, dark: true },
});

export default function PageContainerBasic(props: any) {
 const { window } \= props;
 const router \= useDemoRouter('/inbox/all');
 // Remove this const when copying and pasting into your project.
 const demoWindow \= window ? window : undefined;

 return (
 \<AppProvider
 navigation\={NAVIGATION}
 router\={router}
 theme\={demoTheme}
 window\={demoWindow}
 branding\={{
 title: 'ACME Inc.',
 }}
 \>
 \<Paper sx\={{ p: 2, width: '100%' }}\>
 \<PageContainer
 slots\={{
 header: CustomPageHeader,
 }}
 \>
 \<Grid container spacing\={1}\>
 \<Grid size\={5} /\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={4}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={8}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \</Grid\>
 \</PageContainer\>
 \</Paper\>
 \</AppProvider\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by CarbonAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Breadcrumbs />`
* `<Link />`
* `<Typography />`



