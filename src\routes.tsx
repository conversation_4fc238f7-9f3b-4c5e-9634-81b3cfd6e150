import React from 'react';
import type {  RouteObject } from 'react-router-dom';
import { Navigate } from 'react-router-dom';
import LoginPage from '@/pages/login/LoginPage';
import HomePage from '@/pages/home/<USER>';
import EmptyPage from '@/components/common/EmptyPage';
import Dashboard from '@/components/dashboard/Dashboard';
import SystemDictionaryExample from '@/components/common/SystemDictionaryExample';
import TextModelPage from '@/pages/model/TextModelPage';
import { useAuthStore } from '@/stores/authStore';

// Protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get authentication state directly from the store
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Public route component (accessible only when not authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get authentication state directly from the store
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Routes configuration
export const routes: RouteObject[] = [
  {
    path: '/login',
    element: (
      <PublicRoute>
        <LoginPage />
      </PublicRoute>
    ),
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <HomePage />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'system-dictionary',
        element: <SystemDictionaryExample />,
      },
      {
        path: 'model/text',
        element: <TextModelPage />,
      },
      {
        path: '*',
        element: <EmptyPage />,
      },
    ],
  },
];
