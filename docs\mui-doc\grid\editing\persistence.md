Data Grid \- Editing persistence
================================

Persisting edited rows.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



The `processRowUpdate` callback
---------------------------------

When the user performs an action to stop editing, the `processRowUpdate` callback is triggered.
Use it to send the new values to the server and save them into a database or other storage method.
The callback is called with three arguments:


1. The updated row with the new values returned by the `valueSetter`.
2. The original values of the row before editing.
3. An object with additional properties such as `rowId`.


Please note that the `processRowUpdate` must return the row object to update the Data Grid internal state.
The value returned is used later as an argument on a call to `apiRef.current.updateRows`.



```
<DataGrid
  rows={rows}
  columns={columns}
  processRowUpdate={(updatedRow, originalRow) =>
    mySaveOnServerFunction(updatedRow);
  }
  onProcessRowUpdateError={handleProcessRowUpdateError}
/>

```
CopyCopied(or Ctrl \+ C)
If you want to delete a row from the internal state of the Data Grid, you can return an additional property `_action: 'delete'` in the row object from the `processRowUpdate` callback.
This removes the row from the internal state of the Data Grid.
It is a more performant way to delete a row as compared to updating the `rows` prop or using `setRows` API method because `processRowUpdate` uses the `updateRows` under the hood which doesn't cause a full regeneration of the row tree.



```
<DataGrid
  {...otherProps}
  processRowUpdate={(updatedRow, originalRow) => {
    if (shouldDeleteRow(updatedRow)) {
      return { ...updatedRow, _action: 'delete' };
    }
    return updatedRow;
  }}
/>

```
CopyCopied(or Ctrl \+ C)
In the example above, `shouldDeleteRow` is a function that determines whether a row should be deleted based on the updated row data.
If `shouldDeleteRow` returns `true`, the row will be deleted from the Data Grid's internal state.


Server\-side validation
-----------------------

If you need to cancel the save process on `processRowUpdate`—for instance, when a database validation fails, or the user wants to reject the changes—there are two options:


1. Reject the promise so that the internal state is not updated and the cell remains in edit mode.
2. Resolve the promise with the second argument (original row before editing), so that the internal state is not updated, and the cell exits edit mode.


The following demo implements the first option: rejecting the promise.
Instead of validating while typing, it simulates validation on the server.
If the new name is empty, the promise responsible for saving the row will be rejected, and the cell will remain in edit mode.


The demo also shows that `processRowUpdate` can pre\-process the row model that will be saved into the internal state.


Additionally, `onProcessRowUpdateError` is called to display the error message.


To exit edit mode, press `Escape` or enter a valid name.


NameAgeDate CreatedLast LoginPhillip McGuire252024/8/142025/5/28 02:31:18Austin Hammond362024/10/252025/5/27 15:08:12Randall Wagner192025/1/262025/5/27 23:53:13Bobby Wade282024/6/302025/5/28 00:08:17Noah Hines232025/1/262025/5/28 11:29:45Rows per page:

1001–5 of 5

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridRowModel,
  GridColDef,
  GridRowId,
  GridRowsProp,
} from '@mui/x-data-grid';
import {
  randomCreatedDate,
  randomTraderName,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';
import Snackbar from '@mui/material/Snackbar';
import Alert, { AlertProps } from '@mui/material/Alert';

interface User {
  name: string;
  age: number;
  id: GridRowId;
  dateCreated: Date;
  lastLogin: Date;
}

const useFakeMutation =  => {
  return React.useCallback(
    (user: Partial<User>) =>
      new Promise<Partial<User>>((resolve, reject) => {
        setTimeout( => {
          if (user.name?.trim === '') {
            reject(new Error('Error while saving user: name cannot be empty.'));
          } else {
            resolve({ ...user, name: user.name?.toUpperCase });
          }
        }, 200);
      }),
    [],
  );
};

export default function ServerSidePersistence {
  const mutateRow = useFakeMutation;

  const [snackbar, setSnackbar] = React.useState<Pick<
    AlertProps,
    'children' | 'severity'
  > | null>(null);

  const handleCloseSnackbar =  => setSnackbar(null);

  const processRowUpdate = React.useCallback(
    async (newRow: GridRowModel) => {
      // Make the HTTP request to save in the backend
      const response = await mutateRow(newRow);
      setSnackbar({ children: 'User successfully saved', severity: 'success' });
      return response;
    },
    [mutateRow],
  );

  const handleProcessRowUpdateError = React.useCallback((error: Error) => {
    setSnackbar({ children: error.message, severity: 'error' });
  }, []);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        columns={columns}
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={handleProcessRowUpdateError}
      />
      {!!snackbar && (
        <Snackbar
          open
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          onClose={handleCloseSnackbar}
          autoHideDuration={6000}
        >
          <Alert {...snackbar} onClose={handleCloseSnackbar} />
        </Snackbar>
      )}
    </div>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 180, editable: true },
  {
    field: 'age',
    headerName: 'Age',
    type: 'number',
    editable: true,
    align: 'left',
    headerAlign: 'left',
  },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
    editable: true,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
    editable: true,
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import {
 DataGrid,
 GridRowModel,
 GridColDef,
 GridRowId,
 GridRowsProp,
} from '@mui/x\-data\-grid';
import {
 randomCreatedDate,
 randomTraderName,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';
import Snackbar from '@mui/material/Snackbar';
import Alert, { AlertProps } from '@mui/material/Alert';

interface User {
 name: string;
 age: number;
 id: GridRowId;
 dateCreated: Date;
 lastLogin: Date;
}

const useFakeMutation \=  \=\> {
 return React.useCallback(
 (user: Partial\<User\>) \=\>
 new Promise\<Partial\<User\>\>((resolve, reject) \=\> {
 setTimeout( \=\> {
 if (user.name?.trim \=\=\= '') {
 reject(new Error('Error while saving user: name cannot be empty.'));
 } else {
 resolve({ ...user, name: user.name?.toUpperCase });
 }
 }, 200\);
 }),
 \[],
 );
};

export default function ServerSidePersistence {
 const mutateRow \= useFakeMutation;

 const \[snackbar, setSnackbar] \= React.useState\<Pick\<
 AlertProps,
 'children' \| 'severity'
 \> \| null\>(null);

 const handleCloseSnackbar \=  \=\> setSnackbar(null);

 const processRowUpdate \= React.useCallback(
 async (newRow: GridRowModel) \=\> {
 // Make the HTTP request to save in the backend
 const response \= await mutateRow(newRow);
 setSnackbar({ children: 'User successfully saved', severity: 'success' });
 return response;
 },
 \[mutateRow],
 );

 const handleProcessRowUpdateError \= React.useCallback((error: Error) \=\> {
 setSnackbar({ children: error.message, severity: 'error' });
 }, \[]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 rows\={rows}
 columns\={columns}
 processRowUpdate\={processRowUpdate}
 onProcessRowUpdateError\={handleProcessRowUpdateError}
 /\>
 {!!snackbar \&\& (
 \<Snackbar
 open
 anchorOrigin\={{ vertical: 'bottom', horizontal: 'center' }}
 onClose\={handleCloseSnackbar}
 autoHideDuration\={6000}
 \>
 \<Alert {...snackbar} onClose\={handleCloseSnackbar} /\>
 \</Snackbar\>
 )}
 \</div\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 180, editable: true },
 {
 field: 'age',
 headerName: 'Age',
 type: 'number',
 editable: true,
 align: 'left',
 headerAlign: 'left',
 },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 editable: true,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 editable: true,
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonConfirm before saving
---------------------

The second option—resolving the promise with the second argument—lets the user cancel the save process by rejecting the changes and exiting the edit mode.
In this case, `processRowUpdate` is resolved with the original value(s) of the row.


The following demo shows how this approach can be used to ask for confirmation before sending the data to the server.
If the user accepts the change, the internal state is updated with the values.
But if the changes are rejected, the internal state remains unchanged, and the cell is reverted back to its original value.
The demo also employs validation to prevent entering an empty name.


NameAgeDate CreatedLast LoginBeatrice Carr252024/10/182025/5/27 14:07:45Owen McDaniel362025/1/282025/5/27 17:14:46Augusta Moody192025/5/232025/5/28 01:02:40Ernest Hicks282025/3/32025/5/27 18:43:03Estella Allison232024/8/122025/5/28 09:18:51Rows per page:

1001–5 of 5

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGrid,
  GridRowModel,
  GridColDef,
  GridRowId,
  GridRowsProp,
} from '@mui/x-data-grid';
import {
  randomCreatedDate,
  randomTraderName,
  randomUpdatedDate,
} from '@mui/x-data-grid-generator';
import Snackbar from '@mui/material/Snackbar';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Alert, { AlertProps } from '@mui/material/Alert';

interface User {
  name: string;
  age: number;
  id: GridRowId;
  dateCreated: Date;
  lastLogin: Date;
}

const useFakeMutation =  => {
  return React.useCallback(
    (user: Partial<User>) =>
      new Promise<Partial<User>>((resolve, reject) => {
        setTimeout( => {
          if (user.name?.trim === '') {
            reject;
          } else {
            resolve(user);
          }
        }, 200);
      }),
    [],
  );
};

function computeMutation(newRow: GridRowModel, oldRow: GridRowModel) {
  if (newRow.name !== oldRow.name) {
    return `Name from '${oldRow.name}' to '${newRow.name}'`;
  }
  if (newRow.age !== oldRow.age) {
    return `Age from '${oldRow.age || ''}' to '${newRow.age || ''}'`;
  }
  return null;
}

export default function AskConfirmationBeforeSave {
  const mutateRow = useFakeMutation;
  const noButtonRef = React.useRef<HTMLButtonElement>(null);
  const [promiseArguments, setPromiseArguments] = React.useState<any>(null);

  const [snackbar, setSnackbar] = React.useState<Pick<
    AlertProps,
    'children' | 'severity'
  > | null>(null);

  const handleCloseSnackbar =  => setSnackbar(null);

  const processRowUpdate = React.useCallback(
    (newRow: GridRowModel, oldRow: GridRowModel) =>
      new Promise<GridRowModel>((resolve, reject) => {
        const mutation = computeMutation(newRow, oldRow);
        if (mutation) {
          // Save the arguments to resolve or reject the promise later
          setPromiseArguments({ resolve, reject, newRow, oldRow });
        } else {
          resolve(oldRow); // Nothing was changed
        }
      }),
    [],
  );

  const handleNo =  => {
    const { oldRow, resolve } = promiseArguments;
    resolve(oldRow); // Resolve with the old row to not update the internal state
    setPromiseArguments(null);
  };

  const handleYes = async  => {
    const { newRow, oldRow, reject, resolve } = promiseArguments;

    try {
      // Make the HTTP request to save in the backend
      const response = await mutateRow(newRow);
      setSnackbar({ children: 'User successfully saved', severity: 'success' });
      resolve(response);
      setPromiseArguments(null);
    } catch (error) {
      setSnackbar({ children: 'Name cannot be empty', severity: 'error' });
      reject(oldRow);
      setPromiseArguments(null);
    }
  };

  const handleEntered =  => {
    // The `autoFocus` is not used because, if used, the same Enter that saves
    // the cell triggers "No". Instead, we manually focus the "No" button once
    // the dialog is fully open.
    // noButtonRef.current?.focus;
  };

  const renderConfirmDialog =  => {
    if (!promiseArguments) {
      return null;
    }

    const { newRow, oldRow } = promiseArguments;
    const mutation = computeMutation(newRow, oldRow);

    return (
      <Dialog
        maxWidth="xs"
        TransitionProps={{ onEntered: handleEntered }}
        open={!!promiseArguments}
      >
        <DialogTitle>Are you sure?</DialogTitle>
        <DialogContent dividers>
          {`Pressing 'Yes' will change ${mutation}.`}
        </DialogContent>
        <DialogActions>
          <Button ref={noButtonRef} onClick={handleNo}>
            No
          </Button>
          <Button onClick={handleYes}>Yes</Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <div style={{ height: 400, width: '100%' }}>
      {renderConfirmDialog}
      <DataGrid rows={rows} columns={columns} processRowUpdate={processRowUpdate} />
      {!!snackbar && (
        <Snackbar open onClose={handleCloseSnackbar} autoHideDuration={6000}>
          <Alert {...snackbar} onClose={handleCloseSnackbar} />
        </Snackbar>
      )}
    </div>
  );
}

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 180, editable: true },
  { field: 'age', headerName: 'Age', type: 'number', editable: true },
  {
    field: 'dateCreated',
    headerName: 'Date Created',
    type: 'date',
    width: 180,
  },
  {
    field: 'lastLogin',
    headerName: 'Last Login',
    type: 'dateTime',
    width: 220,
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    name: randomTraderName,
    age: 25,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 2,
    name: randomTraderName,
    age: 36,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 3,
    name: randomTraderName,
    age: 19,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 4,
    name: randomTraderName,
    age: 28,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
  {
    id: 5,
    name: randomTraderName,
    age: 23,
    dateCreated: randomCreatedDate,
    lastLogin: randomUpdatedDate,
  },
];  

```
import \* as React from 'react';
import {
 DataGrid,
 GridRowModel,
 GridColDef,
 GridRowId,
 GridRowsProp,
} from '@mui/x\-data\-grid';
import {
 randomCreatedDate,
 randomTraderName,
 randomUpdatedDate,
} from '@mui/x\-data\-grid\-generator';
import Snackbar from '@mui/material/Snackbar';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Alert, { AlertProps } from '@mui/material/Alert';

interface User {
 name: string;
 age: number;
 id: GridRowId;
 dateCreated: Date;
 lastLogin: Date;
}

const useFakeMutation \=  \=\> {
 return React.useCallback(
 (user: Partial\<User\>) \=\>
 new Promise\<Partial\<User\>\>((resolve, reject) \=\> {
 setTimeout( \=\> {
 if (user.name?.trim \=\=\= '') {
 reject;
 } else {
 resolve(user);
 }
 }, 200\);
 }),
 \[],
 );
};

function computeMutation(newRow: GridRowModel, oldRow: GridRowModel) {
 if (newRow.name !\=\= oldRow.name) {
 return \`Name from '${oldRow.name}' to '${newRow.name}'\`;
 }
 if (newRow.age !\=\= oldRow.age) {
 return \`Age from '${oldRow.age \|\| ''}' to '${newRow.age \|\| ''}'\`;
 }
 return null;
}

export default function AskConfirmationBeforeSave {
 const mutateRow \= useFakeMutation;
 const noButtonRef \= React.useRef\<HTMLButtonElement\>(null);
 const \[promiseArguments, setPromiseArguments] \= React.useState\<any\>(null);

 const \[snackbar, setSnackbar] \= React.useState\<Pick\<
 AlertProps,
 'children' \| 'severity'
 \> \| null\>(null);

 const handleCloseSnackbar \=  \=\> setSnackbar(null);

 const processRowUpdate \= React.useCallback(
 (newRow: GridRowModel, oldRow: GridRowModel) \=\>
 new Promise\<GridRowModel\>((resolve, reject) \=\> {
 const mutation \= computeMutation(newRow, oldRow);
 if (mutation) {
 // Save the arguments to resolve or reject the promise later
 setPromiseArguments({ resolve, reject, newRow, oldRow });
 } else {
 resolve(oldRow); // Nothing was changed
 }
 }),
 \[],
 );

 const handleNo \=  \=\> {
 const { oldRow, resolve } \= promiseArguments;
 resolve(oldRow); // Resolve with the old row to not update the internal state
 setPromiseArguments(null);
 };

 const handleYes \= async  \=\> {
 const { newRow, oldRow, reject, resolve } \= promiseArguments;

 try {
 // Make the HTTP request to save in the backend
 const response \= await mutateRow(newRow);
 setSnackbar({ children: 'User successfully saved', severity: 'success' });
 resolve(response);
 setPromiseArguments(null);
 } catch (error) {
 setSnackbar({ children: 'Name cannot be empty', severity: 'error' });
 reject(oldRow);
 setPromiseArguments(null);
 }
 };

 const handleEntered \=  \=\> {
 // The \`autoFocus\` is not used because, if used, the same Enter that saves
 // the cell triggers "No". Instead, we manually focus the "No" button once
 // the dialog is fully open.
 // noButtonRef.current?.focus;
 };

 const renderConfirmDialog \=  \=\> {
 if (!promiseArguments) {
 return null;
 }

 const { newRow, oldRow } \= promiseArguments;
 const mutation \= computeMutation(newRow, oldRow);

 return (
 \<Dialog
 maxWidth\="xs"
 TransitionProps\={{ onEntered: handleEntered }}
 open\={!!promiseArguments}
 \>
 \<DialogTitle\>Are you sure?\</DialogTitle\>
 \<DialogContent dividers\>
 {\`Pressing 'Yes' will change ${mutation}.\`}
 \</DialogContent\>
 \<DialogActions\>
 \<Button ref\={noButtonRef} onClick\={handleNo}\>
 No
 \</Button\>
 \<Button onClick\={handleYes}\>Yes\</Button\>
 \</DialogActions\>
 \</Dialog\>
 );
 };

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 {renderConfirmDialog}
 \<DataGrid rows\={rows} columns\={columns} processRowUpdate\={processRowUpdate} /\>
 {!!snackbar \&\& (
 \<Snackbar open onClose\={handleCloseSnackbar} autoHideDuration\={6000}\>
 \<Alert {...snackbar} onClose\={handleCloseSnackbar} /\>
 \</Snackbar\>
 )}
 \</div\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 180, editable: true },
 { field: 'age', headerName: 'Age', type: 'number', editable: true },
 {
 field: 'dateCreated',
 headerName: 'Date Created',
 type: 'date',
 width: 180,
 },
 {
 field: 'lastLogin',
 headerName: 'Last Login',
 type: 'dateTime',
 width: 220,
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 name: randomTraderName,
 age: 25,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 2,
 name: randomTraderName,
 age: 36,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 3,
 name: randomTraderName,
 age: 19,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 4,
 name: randomTraderName,
 age: 28,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
 {
 id: 5,
 name: randomTraderName,
 age: 23,
 dateCreated: randomCreatedDate,
 lastLogin: randomUpdatedDate,
 },
];Press `Enter` to start editing**Gitlab** \- A single, unified DevSecOps platform is what you need to deliver software faster. Try it for free.ad by CarbonAPI
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

OverviewCustom edit component

---

•

Blog•

Store
