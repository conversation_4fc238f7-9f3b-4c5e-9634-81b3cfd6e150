Data Grid \- Row selection
==========================

Row selection allows the user to select and highlight a single or multiple rows that they can then take action on.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



Single row selection
--------------------

Single row selection comes enabled by default for the MIT Data Grid component.
You can select a row by clicking it, or using the keyboard shortcuts.
To unselect a row, hold the `Ctrl` (`Cmd` on MacOS) key and click on it.


DeskCommodityTrader NameTrader EmailQuantityD\-9227<NAME_EMAIL>83,356D\-3716<NAME_EMAIL>44,347D\-7257Sugar No.14Effie Elliottokietiewe@lidu.bi92,287D\-490<NAME_EMAIL>92,126D\-4390Sugar No.11Herman Caseylocusid@sicas.ec38,199D\-514Sugar No.11<PERSON><PERSON><PERSON>@oktaz.mq53,368D\-3145SoybeansLeroy Weaverto@nuejojom.kz5,392D\-9981<NAME_EMAIL>74,179D\-9046OatsBettie McBridebotfidov@mahdebeh.ht78,624Rows per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function SingleRowSelectionGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function SingleRowSelectionGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- All the essential DevSecOps tools in one place with GitLab. Give it a try for free.ad by CarbonMultiple row selection
----------------------

On the Data Grid Pro and Data Grid Premium components, you can select multiple rows in two ways:


* To select multiple independent rows, hold the `Ctrl` (`Cmd` on MacOS) key while selecting rows.
* To select a range of rows, hold the `Shift` key while selecting rows.
* To disable multiple row selection, use `disableMultipleRowSelection={true}`.


DeskCommodityTrader NameTrader EmailQuantityD\-4848<NAME_EMAIL>61,544D\-375CornRay Pittmanwifolsak@voztuj.pn69,555D\-4373CornLinnie Garciabil@iciwu.dk70,647D\-5505Frozen Concentrated <NAME_EMAIL>55,184D\-9214<NAME_EMAIL>23,926D\-8939<NAME_EMAIL>21,640D\-841CocoaVincent Hamiltongifnudoc@vir.fm3,276D\-8096SoybeansWayne Gutierrezajo@hisvaes.gi67,156D\-1649Sugar No.14Ola Wilsonnali@cavaw.ss81,6711 row selected1–10 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function MultipleRowSelectionGrid {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        {...data}
        loading={loading}
        pagination
        initialState={{
          ...data.initialState,
          pagination: {
            paginationModel: {
              pageSize: 10,
            },
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function MultipleRowSelectionGrid {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 {...data}
 loading\={loading}
 pagination
 initialState\={{
 ...data.initialState,
 pagination: {
 paginationModel: {
 pageSize: 10,
 },
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editingDisable row selection on click
------------------------------

You might have interactive content in the cells and need to disable the selection of the row on click. Use the `disableRowSelectionOnClick` prop in this case.


DeskCommodityTrader NameTrader EmailQuantityD\-2278<NAME_EMAIL>23,432D\-6425Frozen Concentrated <NAME_EMAIL>18,941D\-3491CocoaDorothy Murraytulkesi@lorwi.sv36,270D\-7179CornOphelia Normanciz@cekvi.bj73,562D\-780SoybeansChristopher Alvaradoholkajuse@mihun.pe28,168D\-2309<NAME_EMAIL>75,878D\-6124<NAME_EMAIL>2,618D\-9398<NAME_EMAIL>18,241D\-1658<NAME_EMAIL>9,236Rows per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function DisableClickSelectionGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid checkboxSelection disableRowSelectionOnClick {...data} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function DisableClickSelectionGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid checkboxSelection disableRowSelectionOnClick {...data} /\>
 \</div\>
 );
}Press `Enter` to start editing**CloudBees** \- Don't just wait—conquer downtime. Leverage "The Power of Three" in CloudBees HA for faster delivery.ad by CarbonDisable selection on certain rows
---------------------------------

Use the `isRowSelectable` prop to indicate if a row can be selected.
It's called with a `GridRowParams` object and should return a boolean value.
If not specified, all rows are selectable.


In the demo below only rows with quantity above 50,000 can be selected:


DeskCommodityTrader NameTrader EmailQuantityD\-4848<NAME_EMAIL>61,544D\-375CornRay Pittmanwifolsak@voztuj.pn69,555D\-4373CornLinnie Garciabil@iciwu.dk70,647D\-5505Frozen Concentrated <NAME_EMAIL>55,184D\-9214<NAME_EMAIL>23,926D\-8939<NAME_EMAIL>21,640D\-841CocoaVincent Hamiltongifnudoc@vir.fm3,276D\-8096SoybeansWayne Gutierrezajo@hisvaes.gi67,156D\-1649Sugar No.14Ola Wilsonnali@cavaw.ss81,671Rows per page:

1001–100 of 100

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridRowParams } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function DisableRowSelection {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        isRowSelectable={(params: GridRowParams) => params.row.quantity > 50000}
        checkboxSelection
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridRowParams } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function DisableRowSelection {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 isRowSelectable\={(params: GridRowParams) \=\> params.row.quantity \> 50000}
 checkboxSelection
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by CarbonRow selection with filtering
----------------------------

By default, when the rows are filtered the selection is cleared from the rows that don't meet the filter criteria.
To keep those rows selected even when they're not visible, set the `keepNonExistentRowsSelected` prop.


​​DeskCommodityTrader NameTrader EmailQuantityD\-666Cotton No.2Nettie Pauliduhigke@hiebe.ca89,279D\-134CornHarriet Allisongugpuit@zuh.ar3,570D\-9999Sugar No.14Olive Herrerabubuja@awhu.cl80,102D\-2413CornHulda McCarthyeg@sic.ci19,056D\-7239CocoaLuke McGuireru@cozelenog.sn15,702D\-3886<NAME_EMAIL>91,082D\-8854Sugar No.14Olive Hodgescufec@faut.ie56,099D\-3380SoybeansDaisy Gilbertcasair@esuhimu.fi50,748Rows per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function KeepNonExistentRowsSelected {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        checkboxSelection
        disableRowSelectionOnClick
        keepNonExistentRowsSelected
        showToolbar
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function KeepNonExistentRowsSelected {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 checkboxSelection
 disableRowSelectionOnClick
 keepNonExistentRowsSelected
 showToolbar
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- All the connections, none of the limits. Unlimited Okta and social connections on our Free Plan.ad by CarbonControlled row selection
------------------------

Use the `rowSelectionModel` prop to control the selection.
Each time this prop changes, the `onRowSelectionModelChange` callback is called with the new selection value.


DeskCommodityTrader NameTrader EmailQuantityD\-3983<NAME_EMAIL>63,863D\-7628WheatKate Alexanderjagkuwomu@bihoc.pm36,640D\-3770<NAME_EMAIL>56,354D\-4576<NAME_EMAIL>44,834D\-6539Sugar No.14Ricky Schwartzzolak@mannej.mq58,024D\-3485SoybeansInez Piercefel@zel.mo12,995D\-5089Sugar No.11Cole Elliotttomfij@dizokune.gq25,861D\-1887MilkAlice Loganla@rissinmos.np59,604D\-9196SoybeansCaroline Lawsongem@ut.ch95,783Rows per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridRowSelectionModel } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function ControlledSelectionGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 6,
  });

  const [rowSelectionModel, setRowSelectionModel] =
    React.useState<GridRowSelectionModel>({ type: 'include', ids: new Set });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        checkboxSelection
        onRowSelectionModelChange={(newRowSelectionModel) => {
          setRowSelectionModel(newRowSelectionModel);
        }}
        rowSelectionModel={rowSelectionModel}
        {...data}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridRowSelectionModel } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function ControlledSelectionGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 6,
 });

 const \[rowSelectionModel, setRowSelectionModel] \=
 React.useState\<GridRowSelectionModel\>({ type: 'include', ids: new Set });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 checkboxSelection
 onRowSelectionModelChange\={(newRowSelectionModel) \=\> {
 setRowSelectionModel(newRowSelectionModel);
 }}
 rowSelectionModel\={rowSelectionModel}
 {...data}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonCheckbox selection
------------------

To activate checkbox selection set `checkboxSelection={true}`.


checkboxSelectiondisableMultipleRowSelectionDeskCommodityTrader NameTrader EmailD\-4563CornFrances <EMAIL>\-3132SoybeansHettie <EMAIL>\-5322Sugar No.11Mike <EMAIL>\-9893<NAME_EMAIL>\-5446Cotton No.2Elijah <EMAIL>\-1716<NAME_EMAIL>\-3055Sugar No.14Betty <EMAIL>\-1229<NAME_EMAIL>\-1409SoybeansMatthew <EMAIL> per page:

1001–10 of 10

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Box from '@mui/material/Box';

export default function CheckboxSelectionGrid {
  const [checkboxSelection, setCheckboxSelection] = React.useState(true);
  const [disableMultipleRowSelection, setDisableMultipleRowSelection] =
    React.useState(false);

  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 5,
  });

  return (
    <div style={{ width: '100%' }}>
      <Box sx={{ mb: 1 }}>
        <FormControlLabel
          label="checkboxSelection"
          control={
            <Switch
              checked={checkboxSelection}
              onChange={(event) => setCheckboxSelection(event.target.checked)}
            />
          }
        />
        <FormControlLabel
          label="disableMultipleRowSelection"
          control={
            <Switch
              checked={disableMultipleRowSelection}
              onChange={(event) =>
                setDisableMultipleRowSelection(event.target.checked)
              }
            />
          }
        />
      </Box>
      <div style={{ height: 400 }}>
        <DataGrid
          {...data}
          checkboxSelection={checkboxSelection}
          disableMultipleRowSelection={disableMultipleRowSelection}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Box from '@mui/material/Box';

export default function CheckboxSelectionGrid {
 const \[checkboxSelection, setCheckboxSelection] \= React.useState(true);
 const \[disableMultipleRowSelection, setDisableMultipleRowSelection] \=
 React.useState(false);

 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 5,
 });

 return (
 \<div style\={{ width: '100%' }}\>
 \<Box sx\={{ mb: 1 }}\>
 \<FormControlLabel
 label\="checkboxSelection"
 control\={
 \<Switch
 checked\={checkboxSelection}
 onChange\={(event) \=\> setCheckboxSelection(event.target.checked)}
 /\>
 }
 /\>
 \<FormControlLabel
 label\="disableMultipleRowSelection"
 control\={
 \<Switch
 checked\={disableMultipleRowSelection}
 onChange\={(event) \=\>
 setDisableMultipleRowSelection(event.target.checked)
 }
 /\>
 }
 /\>
 \</Box\>
 \<div style\={{ height: 400 }}\>
 \<DataGrid
 {...data}
 checkboxSelection\={checkboxSelection}
 disableMultipleRowSelection\={disableMultipleRowSelection}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Custom checkbox column

If you provide a custom checkbox column to the Data Grid with the `GRID_CHECKBOX_SELECTION_FIELD` field, the Data Grid will not add its own.


We strongly recommend to use the `GRID_CHECKBOX_SELECTION_COL_DEF` variable instead of re\-defining all the custom properties yourself.


In the following demo, the checkbox column has been moved to the right and its width has been increased to 100px.


DeskCommodityTrader NameTrader EmailD\-5678Sugar No.14Cora <EMAIL>\-2653SoybeansCraig <EMAIL>\-1049CornVirginia <EMAIL>\-3690<NAME_EMAIL>\-7712OatsLuis <EMAIL>\-1631Sugar No.14Carrie <EMAIL>\-1963Cotton No.2Hallie <EMAIL>\-9254<NAME_EMAIL>\-2379<NAME_EMAIL> per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GRID_CHECKBOX_SELECTION_COL_DEF } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function CheckboxSelectionCustom {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 5,
  });

  const columns = React.useMemo(
     => [
      ...data.columns,
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
        width: 100,
      },
    ],
    [data.columns],
  );

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} checkboxSelection columns={columns} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GRID\_CHECKBOX\_SELECTION\_COL\_DEF } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function CheckboxSelectionCustom {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 5,
 });

 const columns \= React.useMemo(
  \=\> \[
 ...data.columns,
 {
 ...GRID\_CHECKBOX\_SELECTION\_COL\_DEF,
 width: 100,
 },
 ],
 \[data.columns],
 );

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} checkboxSelection columns\={columns} /\>
 \</div\>
 );
}Press `Enter` to start editing**SciChart** \- Accelerate Time To Market, Reduce Technical Debt And Build High\-Performance Charts.ad by Carbon
Always set the `checkboxSelection` prop to `true` even when providing a custom checkbox column.
Otherwise, the Data Grid might remove your column.


### Visible rows selection

By default, when you click the "Select All" checkbox, all rows in the Data Grid are selected.
If you want to change this behavior and only select the rows that are currently visible on the page, you can use the `checkboxSelectionVisibleOnly` prop.


checkboxSelectionVisibleOnlyDeskCommodityTrader NameTrader EmailD\-344CocoaClarence <EMAIL>\-953<NAME_EMAIL>\-5890<NAME_EMAIL>\-3461CornJeffrey <EMAIL>\-1801SoybeansLoretta <EMAIL>\-8130CocoaAbbie <EMAIL>\-5375Frozen Concentrated <NAME_EMAIL>\-5142<NAME_EMAIL>\-6128Frozen Concentrated <NAME_EMAIL> per page:

501–50 of 300

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Box from '@mui/material/Box';

export default function CheckboxSelectionVisibleOnlyGrid {
  const [checkboxSelectionVisibleOnly, setCheckboxSelectionVisibleOnly] =
    React.useState(false);

  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 300,
    maxColumns: 5,
  });

  return (
    <div style={{ width: '100%' }}>
      <Box sx={{ mb: 1 }}>
        <FormControlLabel
          label="checkboxSelectionVisibleOnly"
          control={
            <Switch
              checked={checkboxSelectionVisibleOnly}
              onChange={(event) =>
                setCheckboxSelectionVisibleOnly(event.target.checked)
              }
            />
          }
        />
      </Box>
      <div style={{ height: 400 }}>
        <DataGridPro
          {...data}
          loading={loading}
          initialState={{
            ...data.initialState,
            pagination: { paginationModel: { pageSize: 50 } },
          }}
          pageSizeOptions={[5, 10, 25, 50, 100]}
          pagination
          checkboxSelection
          checkboxSelectionVisibleOnly={checkboxSelectionVisibleOnly}
        />
      </div>
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Box from '@mui/material/Box';

export default function CheckboxSelectionVisibleOnlyGrid {
 const \[checkboxSelectionVisibleOnly, setCheckboxSelectionVisibleOnly] \=
 React.useState(false);

 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 300,
 maxColumns: 5,
 });

 return (
 \<div style\={{ width: '100%' }}\>
 \<Box sx\={{ mb: 1 }}\>
 \<FormControlLabel
 label\="checkboxSelectionVisibleOnly"
 control\={
 \<Switch
 checked\={checkboxSelectionVisibleOnly}
 onChange\={(event) \=\>
 setCheckboxSelectionVisibleOnly(event.target.checked)
 }
 /\>
 }
 /\>
 \</Box\>
 \<div style\={{ height: 400 }}\>
 \<DataGridPro
 {...data}
 loading\={loading}
 initialState\={{
 ...data.initialState,
 pagination: { paginationModel: { pageSize: 50 } },
 }}
 pageSizeOptions\={\[5, 10, 25, 50, 100]}
 pagination
 checkboxSelection
 checkboxSelectionVisibleOnly\={checkboxSelectionVisibleOnly}
 /\>
 \</div\>
 \</div\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonUsage with server\-side pagination
----------------------------------

Using the controlled selection with `paginationMode="server"` may result in selected rows being lost when the page is changed.
This happens because the Data Grid cross\-checks with the `rows` prop and only calls `onRowSelectionModelChange` with existing row IDs.
Depending on your server\-side implementation, when the page changes and the new value for the `rows` prop does not include previously selected rows, the Data Grid will call `onRowSelectionModelChange` with an empty value.
To prevent this, enable the `keepNonExistentRowsSelected` prop to keep the rows selected even if they do not exist.



```
<DataGrid keepNonExistentRowsSelected />

```
CopyCopied(or Ctrl \+ C)
By using this approach, clicking in the **Select All** checkbox may still leave some rows selected.
It is up to you to clean the selection model, using the `rowSelectionModel` prop.
The following demo shows the prop in action:


DeskCommodityTrader NameTrader EmailQuantityD\-4848<NAME_EMAIL>61,544D\-375CornRay Pittmanwifolsak@voztuj.pn69,555D\-4373CornLinnie Garciabil@iciwu.dk70,647D\-5505Frozen Concentrated <NAME_EMAIL>55,184D\-9214<NAME_EMAIL>23,9261–5 of 100

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid, GridRowsProp, GridRowSelectionModel } from '@mui/x-data-grid';
import { GridDemoData, useDemoData, randomInt } from '@mui/x-data-grid-generator';

function loadServerRows(page: number, data: GridDemoData): Promise<any> {
  return new Promise((resolve) => {
    setTimeout(
       => {
        resolve(data.rows.slice(page * 5, (page + 1) * 5));
      },
      randomInt(100, 600),
    ); // simulate network latency
  });
}

export default function ControlledSelectionServerPaginationGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 6,
  });

  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 5,
  });
  const [rows, setRows] = React.useState<GridRowsProp>([]);
  const [loading, setLoading] = React.useState(false);
  const [rowSelectionModel, setRowSelectionModel] =
    React.useState<GridRowSelectionModel>({ type: 'include', ids: new Set });

  React.useEffect( => {
    let active = true;

    (async  => {
      setLoading(true);
      const newRows = await loadServerRows(paginationModel.page, data);

      if (!active) {
        return;
      }

      setRows(newRows);
      setLoading(false);
    });

    return  => {
      active = false;
    };
  }, [paginationModel.page, data]);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        {...data}
        rows={rows}
        pagination
        checkboxSelection
        paginationModel={paginationModel}
        pageSizeOptions={[5]}
        rowCount={100}
        paginationMode="server"
        onPaginationModelChange={setPaginationModel}
        onRowSelectionModelChange={(newRowSelectionModel) => {
          setRowSelectionModel(newRowSelectionModel);
        }}
        rowSelectionModel={rowSelectionModel}
        loading={loading}
        keepNonExistentRowsSelected
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid, GridRowsProp, GridRowSelectionModel } from '@mui/x\-data\-grid';
import { GridDemoData, useDemoData, randomInt } from '@mui/x\-data\-grid\-generator';

function loadServerRows(page: number, data: GridDemoData): Promise\<any\> {
 return new Promise((resolve) \=\> {
 setTimeout(
  \=\> {
 resolve(data.rows.slice(page \* 5, (page \+ 1\) \* 5\));
 },
 randomInt(100, 600\),
 ); // simulate network latency
 });
}

export default function ControlledSelectionServerPaginationGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 6,
 });

 const \[paginationModel, setPaginationModel] \= React.useState({
 page: 0,
 pageSize: 5,
 });
 const \[rows, setRows] \= React.useState\<GridRowsProp\>(\[]);
 const \[loading, setLoading] \= React.useState(false);
 const \[rowSelectionModel, setRowSelectionModel] \=
 React.useState\<GridRowSelectionModel\>({ type: 'include', ids: new Set });

 React.useEffect( \=\> {
 let active \= true;

 (async  \=\> {
 setLoading(true);
 const newRows \= await loadServerRows(paginationModel.page, data);

 if (!active) {
 return;
 }

 setRows(newRows);
 setLoading(false);
 });

 return  \=\> {
 active \= false;
 };
 }, \[paginationModel.page, data]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 rows\={rows}
 pagination
 checkboxSelection
 paginationModel\={paginationModel}
 pageSizeOptions\={\[5]}
 rowCount\={100}
 paginationMode\="server"
 onPaginationModelChange\={setPaginationModel}
 onRowSelectionModelChange\={(newRowSelectionModel) \=\> {
 setRowSelectionModel(newRowSelectionModel);
 }}
 rowSelectionModel\={rowSelectionModel}
 loading\={loading}
 keepNonExistentRowsSelected
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- Use GitLab end to end. From security to development to operations. It's all here.ad by CarbonapiRef
------

The Data Grid exposes a set of methods via the `apiRef` object that are used internally in the implementation of the row selection feature.
The reference below describes the relevant functions.
See API object for more details.



This API should only be used as a last resort when the Data Grid's built\-in props aren't sufficient for your specific use case.


### getSelectedRowsReturns an array of the selected rows.

###### Signature:

Copy(or Ctrl \+ C)
```
getSelectedRows:  => Map<GridRowId, GridRowModel>
```
### isRowSelectableDetermines if a row can be selected or not.

###### Signature:

Copy(or Ctrl \+ C)
```
isRowSelectable: (id: GridRowId) => boolean
```
### isRowSelectedDetermines if a row is selected or not.

###### Signature:

Copy(or Ctrl \+ C)
```
isRowSelected: (id: GridRowId) => boolean
```
### selectRowChange the selection state of a row.

###### Signature:

Copy(or Ctrl \+ C)
```
selectRow: (id: GridRowId, isSelected?: boolean, resetSelection?: boolean) => void
```
### setRowSelectionModelSets the new row selection model.⚠️ Caution: `setRowSelectionModel` doesn't apply the selection propagation automatically. Pass model returned by API method `getPropagatedRowSelectionModel` instead to apply the selection propagation.

###### Signature:

Copy(or Ctrl \+ C)
```
setRowSelectionModel: (rowSelectionModel: GridRowSelectionModel, reason?: GridControlledStateReasonLookup['rowSelection']) => void
```
### getPropagatedRowSelectionModelReturns the modified selection model after applying row selection propagation.Use this to achieve proper `rowSelectionPropagation` behavior when setting the selection model using `setRowSelectionModel`.

###### Signature:

Copy(or Ctrl \+ C)
```
getPropagatedRowSelectionModel: (inputSelectionModel: GridRowSelectionModel) => GridRowSelectionModel
```
### selectRowRangeChange the selection state of all the selectable rows in a range.

###### Signature:

Copy(or Ctrl \+ C)
```
selectRowRange: (range: { startId: GridRowId; endId: GridRowId }, isSelected?: boolean, resetSelection?: boolean) => void
```
### selectRowsChange the selection state of multiple rows.

###### Signature:

Copy(or Ctrl \+ C)
```
selectRows: (ids: GridRowId[], isSelected?: boolean, resetSelection?: boolean) => void
```
API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

PaginationCell selection

---

•

Blog•

Store
