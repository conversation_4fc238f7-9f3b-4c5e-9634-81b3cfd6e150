Paper
=====

The Paper component is a container for displaying content on an elevated surface.


Build the perfect GPU setup tailored to your model and workload needs with NVIDIA Blackwell GPUs.

ads via Carbon



* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Introduction
------------

In Material Design, surface components and shadow styles are heavily influenced by their real\-world physical counterparts.


Material UI implements this concept with the Paper component, a container\-like surface that features the `elevation` prop for pulling box\-shadow values from the theme.



The Paper component is ideally suited for designs that follow Material Design's elevation system, which is meant to replicate how light casts shadows in the physical world.


If you just need a generic container, you may prefer to use the Box or Container components.


JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';

export default function SimplePaper {
  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        '& > :not(style)': {
          m: 1,
          width: 128,
          height: 128,
        },
      }}
    >
      <Paper elevation={0} />
      <Paper />
      <Paper elevation={3} />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';

export default function SimplePaper {
 return (
 \<Box
 sx\={{
 display: 'flex',
 flexWrap: 'wrap',
 '\& \> :not(style)': {
 m: 1,
 width: 128,
 height: 128,
 },
 }}
 \>
 \<Paper elevation\={0} /\>
 \<Paper /\>
 \<Paper elevation\={3} /\>
 \</Box\>
 );
}Press `Enter` to start editing**GetStream.io** \- Built by devs, for devs. Start Coding FREE. No CC requiredad by CarbonComponent
---------


```
import Paper from '@mui/material/Paper';

```
CopyCopied(or Ctrl \+ C)
Customization
-------------

### Elevation

Use the `elevation` prop to establish hierarchy through the use of shadows.
The Paper component's default elevation level is `1`.
The prop accepts values from `0` to `24`.
The higher the number, the further away the Paper appears to be from its background.


In dark mode, increasing the elevation also makes the background color lighter.
This is done by applying a semi\-transparent gradient with the `background-image` CSS property.



The aforementioned dark mode behavior can lead to confusion when overriding the Paper component, because changing the `background-color` property won't affect the lighter shading.
To override it, you must either use a new background value, or customize the values for both `background-color` and `background-image`.


elevation\=0elevation\=1elevation\=2elevation\=3elevation\=4elevation\=6elevation\=8elevation\=12elevation\=16elevation\=24elevation\=0elevation\=1elevation\=2elevation\=3elevation\=4elevation\=6elevation\=8elevation\=12elevation\=16elevation\=24JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import { createTheme, ThemeProvider, styled } from '@mui/material/styles';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  textAlign: 'center',
  color: theme.palette.text.secondary,
  height: 60,
  lineHeight: '60px',
}));

const darkTheme = createTheme({ palette: { mode: 'dark' } });
const lightTheme = createTheme({ palette: { mode: 'light' } });

export default function Elevation {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2}>
        {[lightTheme, darkTheme].map((theme, index) => (
          <Grid key={index} size={6}>
            <ThemeProvider theme={theme}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: 'background.default',
                  display: 'grid',
                  gridTemplateColumns: { md: '1fr 1fr' },
                  gap: 2,
                }}
              >
                {[0, 1, 2, 3, 4, 6, 8, 12, 16, 24].map((elevation) => (
                  <Item key={elevation} elevation={elevation}>
                    {`elevation=${elevation}`}
                  </Item>
                ))}
              </Box>
            </ThemeProvider>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}  

```
import \* as React from 'react';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import { createTheme, ThemeProvider, styled } from '@mui/material/styles';

const Item \= styled(Paper)(({ theme }) \=\> ({
 ...theme.typography.body2,
 textAlign: 'center',
 color: theme.palette.text.secondary,
 height: 60,
 lineHeight: '60px',
}));

const darkTheme \= createTheme({ palette: { mode: 'dark' } });
const lightTheme \= createTheme({ palette: { mode: 'light' } });

export default function Elevation {
 return (
 \<Box sx\={{ flexGrow: 1 }}\>
 \<Grid container spacing\={2}\>
 {\[lightTheme, darkTheme].map((theme, index) \=\> (
 \<Grid key\={index} size\={6}\>
 \<ThemeProvider theme\={theme}\>
 \<Box
 sx\={{
 p: 2,
 borderRadius: 2,
 bgcolor: 'background.default',
 display: 'grid',
 gridTemplateColumns: { md: '1fr 1fr' },
 gap: 2,
 }}
 \>
 {\[0, 1, 2, 3, 4, 6, 8, 12, 16, 24].map((elevation) \=\> (
 \<Item key\={elevation} elevation\={elevation}\>
 {\`elevation\=${elevation}\`}
 \</Item\>
 ))}
 \</Box\>
 \</ThemeProvider\>
 \</Grid\>
 ))}
 \</Grid\>
 \</Box\>
 );
}Press `Enter` to start editing**Gitlab** \- One platform to empower Dev, Sec, and Ops teams. Start building secure software today.ad by Carbon### Variants

Set the `variant` prop to `"outlined"` for a flat, outlined Paper with no shadows:


default variantoutlined variantJSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

const DemoPaper = styled(Paper)(({ theme }) => ({
  width: 120,
  height: 120,
  padding: theme.spacing(2),
  ...theme.typography.body2,
  textAlign: 'center',
}));

export default function Variants {
  return (
    <Stack direction="row" spacing={2}>
      <DemoPaper variant="elevation">default variant</DemoPaper>
      <DemoPaper variant="outlined">outlined variant</DemoPaper>
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

const DemoPaper \= styled(Paper)(({ theme }) \=\> ({
 width: 120,
 height: 120,
 padding: theme.spacing(2\),
 ...theme.typography.body2,
 textAlign: 'center',
}));

export default function Variants {
 return (
 \<Stack direction\="row" spacing\={2}\>
 \<DemoPaper variant\="elevation"\>default variant\</DemoPaper\>
 \<DemoPaper variant\="outlined"\>outlined variant\</DemoPaper\>
 \</Stack\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Corners

The Paper component features rounded corners by default.
Add the `square` prop for square corners:


rounded cornerssquare cornersJSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

const DemoPaper = styled(Paper)(({ theme }) => ({
  width: 120,
  height: 120,
  padding: theme.spacing(2),
  ...theme.typography.body2,
  textAlign: 'center',
}));

export default function SquareCorners {
  return (
    <Stack direction="row" spacing={2}>
      <DemoPaper square={false}>rounded corners</DemoPaper>
      <DemoPaper square>square corners</DemoPaper>
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

const DemoPaper \= styled(Paper)(({ theme }) \=\> ({
 width: 120,
 height: 120,
 padding: theme.spacing(2\),
 ...theme.typography.body2,
 textAlign: 'center',
}));

export default function SquareCorners {
 return (
 \<Stack direction\="row" spacing\={2}\>
 \<DemoPaper square\={false}\>rounded corners\</DemoPaper\>
 \<DemoPaper square\>square corners\</DemoPaper\>
 \</Stack\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonAnatomy
-------

The Paper component is composed of a single root `<div>` that wraps around its contents:



```
<div class="MuiPaper-root">
  <!-- Paper contents -->
</div>

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Paper />`



