Button
======

Buttons allow users to take actions, and make choices, with a single tap.**Premium Themes**. Kickstart your application development with a ready\-made theme.ad by M<PERSON>


Buttons communicate actions that users can take. They are typically placed throughout your UI, in places like:


* Modal windows
* Forms
* Cards
* Toolbars


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Basic button
------------

The `Button` comes with three variants: text (default), contained, and outlined.


TextContainedOutlinedJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button variant="text">Text</Button>
<Button variant="contained">Contained</Button>
<Button variant="outlined">Outlined</Button>  

```
\<Button variant\="text"\>Text\</Button\>
\<Button variant\="contained"\>Contained\</Button\>
\<Button variant\="outlined"\>Outlined\</Button\>Press `Enter` to start editing### Text button

Text buttons
are typically used for less\-pronounced actions, including those located: in dialogs, in cards.
In cards, text buttons help maintain an emphasis on card content.


PrimaryDisabledLinkJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button>Primary</Button>
<Button disabled>Disabled</Button>
<Button href="#text-buttons">Link</Button>  

```
\<Button\>Primary\</Button\>
\<Button disabled\>Disabled\</Button\>
\<Button \>Link\</Button\>Press `Enter` to start editing### Contained button

Contained buttons
are high\-emphasis, distinguished by their use of elevation and fill.
They contain actions that are primary to your app.


ContainedDisabledLinkJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button variant="contained">Contained</Button>
<Button variant="contained" disabled>
  Disabled
</Button>
<Button variant="contained" href="#contained-buttons">
  Link
</Button>  

```
\<Button variant\="contained"\>Contained\</Button\>
\<Button variant\="contained" disabled\>
 Disabled
\</Button\>
\<Button variant\="contained" \>
 Link
\</Button\>Press `Enter` to start editingYou can remove the elevation with the `disableElevation` prop.


Disable elevationJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button variant="contained" disableElevation>
  Disable elevation
</Button>  

```
\<Button variant\="contained" disableElevation\>
 Disable elevation
\</Button\>Press `Enter` to start editing### Outlined button

Outlined buttons are medium\-emphasis buttons.
They contain actions that are important but aren't the primary action in an app.


Outlined buttons are also a lower emphasis alternative to contained buttons,
or a higher emphasis alternative to text buttons.


PrimaryDisabledLinkJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button variant="outlined">Primary</Button>
<Button variant="outlined" disabled>
  Disabled
</Button>
<Button variant="outlined" href="#outlined-buttons">
  Link
</Button>  

```
\<Button variant\="outlined"\>Primary\</Button\>
\<Button variant\="outlined" disabled\>
 Disabled
\</Button\>
\<Button variant\="outlined" \>
 Link
\</Button\>Press `Enter` to start editingHandling clicks
---------------

All components accept an `onClick` handler that is applied to the root DOM element.



```
<Button
  onClick={ => {
    alert('clicked');
  }}
>
  Click me
</Button>

```
CopyCopied(or Ctrl \+ C)
Note that the documentation avoids mentioning native props (there are a lot) in the API section of the components.


Color
-----

SecondarySuccessErrorJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button color="secondary">Secondary</Button>
<Button variant="contained" color="success">
  Success
</Button>
<Button variant="outlined" color="error">
  Error
</Button>  

```
\<Button color\="secondary"\>Secondary\</Button\>
\<Button variant\="contained" color\="success"\>
 Success
\</Button\>
\<Button variant\="outlined" color\="error"\>
 Error
\</Button\>Press `Enter` to start editingIn addition to using the default button colors, you can add custom ones, or disable any you don't need. See the Adding new colors examples for more info.


Sizes
-----

For larger or smaller buttons, use the `size` prop.


SmallMediumLargeSmallMediumLargeSmallMediumLargeJSTSShow codeButtons with icons and label
----------------------------

Sometimes you might want to have icons for certain buttons to enhance the UX of the application as we recognize logos more easily than plain text. For example, if you have a delete button you can label it with a dustbin icon.


DeleteSendJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button variant="outlined" startIcon={<DeleteIcon />}>
  Delete
</Button>
<Button variant="contained" endIcon={<SendIcon />}>
  Send
</Button>  

```
\<Button variant\="outlined" startIcon\={\<DeleteIcon /\>}\>
 Delete
\</Button\>
\<Button variant\="contained" endIcon\={\<SendIcon /\>}\>
 Send
\</Button\>Press `Enter` to start editingIcon button
-----------

Icon buttons are commonly found in app bars and toolbars.


Icons are also appropriate for toggle buttons that allow a single choice to be selected or
deselected, such as adding or removing a star to an item.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton aria-label="delete">
  <DeleteIcon />
</IconButton>
<IconButton aria-label="delete" disabled color="primary">
  <DeleteIcon />
</IconButton>
<IconButton color="secondary" aria-label="add an alarm">
  <AlarmIcon />
</IconButton>
<IconButton color="primary" aria-label="add to shopping cart">
  <AddShoppingCartIcon />
</IconButton>  

```
\<IconButton aria\-label\="delete"\>
 \<DeleteIcon /\>
\</IconButton\>
\<IconButton aria\-label\="delete" disabled color\="primary"\>
 \<DeleteIcon /\>
\</IconButton\>
\<IconButton color\="secondary" aria\-label\="add an alarm"\>
 \<AlarmIcon /\>
\</IconButton\>
\<IconButton color\="primary" aria\-label\="add to shopping cart"\>
 \<AddShoppingCartIcon /\>
\</IconButton\>Press `Enter` to start editing### Sizes

For larger or smaller icon buttons, use the `size` prop.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton aria-label="delete" size="small">
  <DeleteIcon fontSize="inherit" />
</IconButton>
<IconButton aria-label="delete" size="small">
  <DeleteIcon fontSize="small" />
</IconButton>
<IconButton aria-label="delete" size="large">
  <DeleteIcon />
</IconButton>
<IconButton aria-label="delete" size="large">
  <DeleteIcon fontSize="inherit" />
</IconButton>  

```
\<IconButton aria\-label\="delete" size\="small"\>
 \<DeleteIcon fontSize\="inherit" /\>
\</IconButton\>
\<IconButton aria\-label\="delete" size\="small"\>
 \<DeleteIcon fontSize\="small" /\>
\</IconButton\>
\<IconButton aria\-label\="delete" size\="large"\>
 \<DeleteIcon /\>
\</IconButton\>
\<IconButton aria\-label\="delete" size\="large"\>
 \<DeleteIcon fontSize\="inherit" /\>
\</IconButton\>Press `Enter` to start editing### Colors

Use `color` prop to apply theme color palette to component.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton aria-label="fingerprint" color="secondary">
  <Fingerprint />
</IconButton>
<IconButton aria-label="fingerprint" color="success">
  <Fingerprint />
</IconButton>  

```
\<IconButton aria\-label\="fingerprint" color\="secondary"\>
 \<Fingerprint /\>
\</IconButton\>
\<IconButton aria\-label\="fingerprint" color\="success"\>
 \<Fingerprint /\>
\</IconButton\>Press `Enter` to start editing### Loading

Starting from v6\.4\.0, use `loading` prop to set icon buttons in a loading state and disable interactions.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Tooltip title="Click to see loading">
  <IconButton onClick={ => setLoading(true)} loading={loading}>
    <ShoppingCartIcon />
  </IconButton>
</Tooltip>  

```
\<Tooltip title\="Click to see loading"\>
 \<IconButton onClick\={ \=\> setLoading(true)} loading\={loading}\>
 \<ShoppingCartIcon /\>
 \</IconButton\>
\</Tooltip\>Press `Enter` to start editing### Badge

You can use the `Badge` component to add a badge to an `IconButton`.


2JSTSExpand codeCopy(or Ctrl \+ C)
```
<IconButton>
  <ShoppingCartIcon fontSize="small" />
  <CartBadge badgeContent={2} color="primary" overlap="circular" />
</IconButton>  

```
\<IconButton\>
 \<ShoppingCartIcon fontSize\="small" /\>
 \<CartBadge badgeContent\={2} color\="primary" overlap\="circular" /\>
\</IconButton\>Press `Enter` to start editingFile upload
-----------

To create a file upload button, turn the button into a label using `component="label"` and then create a visually\-hidden input with type `file`.


Upload filesJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button
  component="label"
  role={undefined}
  variant="contained"
  tabIndex={-1}
  startIcon={<CloudUploadIcon />}
>
  Upload files
  <VisuallyHiddenInput
    type="file"
    onChange={(event) => console.log(event.target.files)}
    multiple
  />
</Button>  

```
\<Button
 component\="label"
 role\={undefined}
 variant\="contained"
 tabIndex\={\-1}
 startIcon\={\<CloudUploadIcon /\>}
\>
 Upload files
 \<VisuallyHiddenInput
 type\="file"
 onChange\={(event) \=\> console.log(event.target.files)}
 multiple
 /\>
\</Button\>Press `Enter` to start editingLoading
-------

Starting from v6\.4\.0, use the `loading` prop to set buttons in a loading state and disable interactions.


SubmitLoading…Fetch dataSaveFull widthFull widthSubmitSubmitSaveJSTSShow codeToggle the loading switch to see the transition between the different states.


LoadingDisabledLoading…Fetch dataSendSaveDisabledLoading…Fetch dataSendSaveJSTSShow code
When the `loading` prop is set to `boolean`, the loading wrapper is always present in the DOM to prevent a Google Translation Crash.


The `loading` value should always be `null` or `boolean`. The pattern below is not recommended as it can cause the Google Translation crash:



```
<Button {...(isFetching && { loading: true })}> // ❌ Don't do this

```
CopyCopied(or Ctrl \+ C)
Customization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


Custom CSSBootstrapJSTSShow code🎨 If you are looking for inspiration, you can check MUI Treasury's customization examples.


Complex button
--------------

The Text Buttons, Contained Buttons, Floating Action Buttons and Icon Buttons are built on top of the same component: the `ButtonBase`.
You can take advantage of this lower\-level component to build custom interactions.


BreakfastBurgersCameraJSTSShow codeThird\-party routing library
----------------------------

One frequent use case is to perform navigation on the client only, without an HTTP round\-trip to the server.
The `ButtonBase` component provides the `component` prop to handle this use case.
Here is a more detailed guide.


Limitations
-----------

### Cursor not\-allowed

The ButtonBase component sets `pointer-events: none;` on disabled buttons, which prevents the appearance of a disabled cursor.


If you wish to use `not-allowed`, you have two options:


1. **CSS only**. You can remove the pointer\-events style on the disabled state of the `<button>` element:



```
.MuiButtonBase-root:disabled {
  cursor: not-allowed;
  pointer-events: auto;
}

```
CopyCopied(or Ctrl \+ C)
However:


* You should add `pointer-events: none;` back when you need to display tooltips on disabled elements.
* The cursor won't change if you render something other than a button element, for instance, a link `<a>` element.


2. **DOM change**. You can wrap the button:



```
<span style={{ cursor: 'not-allowed' }}>
  <Button component={Link} disabled>
    disabled
  </Button>
</span>

```
CopyCopied(or Ctrl \+ C)
This has the advantage of supporting any element, for instance, a link `<a>` element.


Unstyled
--------

Use the Base UI Button for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Button />`
* `<ButtonBase />`
* `<IconButton />`



