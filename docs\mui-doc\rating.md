Rating
======

Ratings provide insight regarding others' opinions and experiences, and can allow the user to submit a rating of their own.


Streamline secure access to AWS resources and eliminate infrastructure complexity.

ads via Carbon



* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Figma
* Sketch

Basic rating
------------

Controlled1 Star2 Stars3 Stars4 Stars5 StarsEmptyUncontrolled1 Star2 Stars3 Stars4 Stars5 StarsEmptyRead onlyDisabled1 Star2 Stars3 Stars4 Stars5 StarsNo rating given1 Star2 Stars3 Stars4 Stars5 StarsEmptyJSTSShow codeRating precision
----------------

The rating can display any float number with the `value` prop.
Use the `precision` prop to define the minimum increment value change allowed.


0\.5 Stars1 Star1\.5 Stars2 Stars2\.5 Stars3 Stars3\.5 Stars4 Stars4\.5 Stars5 StarsEmptyJSTSExpand codeCopy(or Ctrl \+ C)
```
<Rating name="half-rating" defaultValue={2.5} precision={0.5} />
<Rating name="half-rating-read" defaultValue={2.5} precision={0.5} readOnly />  

```
\<Rating name\="half\-rating" defaultValue\={2\.5} precision\={0\.5} /\>
\<Rating name\="half\-rating\-read" defaultValue\={2\.5} precision\={0\.5} readOnly /\>Press `Enter` to start editingHover feedback
--------------

You can display a label on hover to help the user pick the correct rating value.
The demo uses the `onChangeActive` prop.


0\.5 Stars, Useless1 Star, Useless\+1\.5 Stars, Poor2 Stars, Poor\+2\.5 Stars, Ok3 Stars, Ok\+3\.5 Stars, Good4 Stars, Good\+4\.5 Stars, Excellent5 Stars, Excellent\+EmptyPoor\+JSTSExpand codeCopy(or Ctrl \+ C)
```
<Rating
  name="hover-feedback"
  value={value}
  precision={0.5}
  getLabelText={getLabelText}
  onChange={(event, newValue) => {
    setValue(newValue);
  }}
  onChangeActive={(event, newHover) => {
    setHover(newHover);
  }}
  emptyIcon={<StarIcon style={{ opacity: 0.55 }} fontSize="inherit" />}
/>
{value !== null && (
  <Box sx={{ ml: 2 }}>{labels[hover !== -1 ? hover : value]}</Box>
)}  

```
\<Rating
 name\="hover\-feedback"
 value\={value}
 precision\={0\.5}
 getLabelText\={getLabelText}
 onChange\={(event, newValue) \=\> {
 setValue(newValue);
 }}
 onChangeActive\={(event, newHover) \=\> {
 setHover(newHover);
 }}
 emptyIcon\={\<StarIcon style\={{ opacity: 0\.55 }} fontSize\="inherit" /\>}
/\>
{value !\=\= null \&\& (
 \<Box sx\={{ ml: 2 }}\>{labels\[hover !\=\= \-1 ? hover : value]}\</Box\>
)}Press `Enter` to start editingSizes
-----

For larger or smaller ratings use the `size` prop.


1 Star2 Stars3 Stars4 Stars5 StarsEmpty1 Star2 Stars3 Stars4 Stars5 StarsEmpty1 Star2 Stars3 Stars4 Stars5 StarsEmptyJSTSExpand codeCopy(or Ctrl \+ C)
```
<Rating name="size-small" defaultValue={2} size="small" />
<Rating name="size-medium" defaultValue={2} />
<Rating name="size-large" defaultValue={2} size="large" />  

```
\<Rating name\="size\-small" defaultValue\={2} size\="small" /\>
\<Rating name\="size\-medium" defaultValue\={2} /\>
\<Rating name\="size\-large" defaultValue\={2} size\="large" /\>Press `Enter` to start editingCustomization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


Custom icon and color0\.5 Hearts1 Heart1\.5 Hearts2 Hearts2\.5 Hearts3 Hearts3\.5 Hearts4 Hearts4\.5 Hearts5 HeartsEmpty10 stars1 Star2 Stars3 Stars4 Stars5 Stars6 Stars7 Stars8 Stars9 Stars10 StarsEmptyJSTSExpand codeCopy(or Ctrl \+ C)
```
<Typography component="legend">Custom icon and color</Typography>
<StyledRating
  name="customized-color"
  defaultValue={2}
  getLabelText={(value: number) => `${value} Heart${value !== 1 ? 's' : ''}`}
  precision={0.5}
  icon={<FavoriteIcon fontSize="inherit" />}
  emptyIcon={<FavoriteBorderIcon fontSize="inherit" />}
/>
<Typography component="legend">10 stars</Typography>
<Rating name="customized-10" defaultValue={2} max={10} />  

```
\<Typography component\="legend"\>Custom icon and color\</Typography\>
\<StyledRating
 name\="customized\-color"
 defaultValue\={2}
 getLabelText\={(value: number) \=\> \`${value} Heart${value !\=\= 1 ? 's' : ''}\`}
 precision\={0\.5}
 icon\={\<FavoriteIcon fontSize\="inherit" /\>}
 emptyIcon\={\<FavoriteBorderIcon fontSize\="inherit" /\>}
/\>
\<Typography component\="legend"\>10 stars\</Typography\>
\<Rating name\="customized\-10" defaultValue\={2} max\={10} /\>Press `Enter` to start editingRadio group
-----------

The rating is implemented with a radio group, set `highlightSelectedOnly` to restore the natural behavior.


Very DissatisfiedDissatisfiedNeutralSatisfiedVery SatisfiedEmptyJSTSExpand codeCopy(or Ctrl \+ C)
```
<StyledRating
  name="highlight-selected-only"
  defaultValue={2}
  IconContainerComponent={IconContainer}
  getLabelText={(value: number) => customIcons[value].label}
  highlightSelectedOnly
/>  

```
\<StyledRating
 name\="highlight\-selected\-only"
 defaultValue\={2}
 IconContainerComponent\={IconContainer}
 getLabelText\={(value: number) \=\> customIcons\[value].label}
 highlightSelectedOnly
/\>Press `Enter` to start editingAccessibility
-------------

(WAI tutorial)


The accessibility of this component relies on:


* A radio group with its fields visually hidden.
It contains six radio buttons, one for each star, and another for 0 stars that is checked by default. Be sure to provide a value for the `name` prop that is unique to the parent form.
* Labels for the radio buttons containing actual text ("1 Star", "2 Stars", …).
Be sure to provide a suitable function to the `getLabelText` prop when the page is in a language other than English. You can use the included locales, or provide your own.
* A visually distinct appearance for the rating icons.
By default, the rating component uses both a difference of color and shape (filled and empty icons) to indicate the value. In the event that you are using color as the only means to indicate the value, the information should also be also displayed as text, as in this demo. This is important to match success Criterion 1\.4\.1 of WCAG2\.1\.


GoodJSTSExpand codeCopy(or Ctrl \+ C)
```
<Rating
  name="text-feedback"
  value={value}
  readOnly
  precision={0.5}
  emptyIcon={<StarIcon style={{ opacity: 0.55 }} fontSize="inherit" />}
/>
<Box sx={{ ml: 2 }}>{labels[value]}</Box>  

```
\<Rating
 name\="text\-feedback"
 value\={value}
 readOnly
 precision\={0\.5}
 emptyIcon\={\<StarIcon style\={{ opacity: 0\.55 }} fontSize\="inherit" /\>}
/\>
\<Box sx\={{ ml: 2 }}\>{labels\[value]}\</Box\>Press `Enter` to start editing### ARIA

The read only rating has a role of "img", and an aria\-label that describes the displayed rating.


### Keyboard

Because the rating component uses radio buttons, keyboard interaction follows the native browser behavior. Tab will focus the current rating, and cursor keys control the selected rating.


The read only rating is not focusable.


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Rating />`



