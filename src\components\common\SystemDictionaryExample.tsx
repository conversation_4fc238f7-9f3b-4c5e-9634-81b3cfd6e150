import React, { useEffect } from 'react';
import useSystemDictionary from '@/hooks/useSystemDictionary';

/**
 * Example component demonstrating how to use the system dictionary cache
 */
const SystemDictionaryExample: React.FC = () => {
  const {
    modelTypes,
    modelStatuses,
    databaseTypes,
    databaseStatuses,
    mcpStatuses,
    toolTypes,
    toolStatuses,
    flowTypes,
    flowStatuses,
    ragTypes,
    ragStatuses,
    agentTypes,
    agentStatuses,
    deviceTypes,
    deviceStatuses,
    isLoading,
    error,
    fetchDictionary,
  } = useSystemDictionary(true); // Auto-fetch on mount

  // Manual fetch example (if needed)
  const handleRefresh = () => {
    fetchDictionary();
  };

  if (isLoading) {
    return <div className="p-4">Loading dictionary data...</div>;
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error loading dictionary data: {error}
        <button 
          onClick={handleRefresh}
          className="ml-2 px-2 py-1 bg-blue-500 text-white rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">System Dictionary Example</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Model Types */}
        <div className="border p-4 rounded shadow">
          <h3 className="font-bold mb-2">Model Types</h3>
          <ul>
            {modelTypes.map((item) => (
              <li key={item.value} className="mb-1">
                {item.label} ({item.value})
              </li>
            ))}
          </ul>
        </div>

        {/* Model Statuses */}
        <div className="border p-4 rounded shadow">
          <h3 className="font-bold mb-2">Model Statuses</h3>
          <ul>
            {modelStatuses.map((item) => (
              <li key={item.value} className="mb-1">
                {item.label} ({item.value})
              </li>
            ))}
          </ul>
        </div>

        {/* Database Types */}
        <div className="border p-4 rounded shadow">
          <h3 className="font-bold mb-2">Database Types</h3>
          <ul>
            {databaseTypes.map((item) => (
              <li key={item.value} className="mb-1">
                {item.label} ({item.value})
              </li>
            ))}
          </ul>
        </div>

        {/* More sections can be added as needed */}
      </div>

      <button 
        onClick={handleRefresh}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Refresh Dictionary Data
      </button>
    </div>
  );
};

export default SystemDictionaryExample;
