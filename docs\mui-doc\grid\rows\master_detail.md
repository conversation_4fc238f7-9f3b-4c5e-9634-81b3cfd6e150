Data Grid \- Master detail
==========================

Expand your rows to display additional information.


The master detail feature allows expanding a row to display additional information inside a panel.
To use this feature, pass a function to the `getDetailPanelContent` prop with the content to be rendered inside the panel.
Any valid React element can be used as the row detail, even another grid.


By default, the detail panel height is 500px.
You can customize it by passing a function to the `getDetailPanelHeight` prop.
This function must return either a number or the `"auto"` string.
If it returns a number, then the panel will use that value (in pixels) for the height.
If it returns `"auto"`, then the height will be derived from the content.



```
<DataGridPro
  getDetailPanelContent={({ row }) => <div>Row ID: {row.id}</div>}
  getDetailPanelHeight={({ row }) => 100} // Optional, default is 500px.
/>

// or

<DataGridPro
  getDetailPanelContent={({ row }) => <div>Row ID: {row.id}</div>}
  getDetailPanelHeight={({ row }) => 'auto'} // Height based on the content.
/>

```
CopyCopied(or Ctrl \+ C)

Both props are called with a `GridRowParams` object, which lets you return a different value for each row.


To expand a row, click on the **\+** icon or press `Space` inside the detail toggle column.
Returning `null` or `undefined` as the value of `getDetailPanelContent` will prevent the respective row from being expanded.


Order IDCustomerPlaced atCurrencyTotal1Matheus2025/3/27USD2,354\.2472Olivier2024/7/12BRL3,652\.6563Flavien2024/6/29TRY2,038\.9954Danail2025/5/10TRY2,069\.7185Alexandre2025/4/18MXN401\.52Total Rows: 5JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import { DataGridPro, DataGridProProps, GridColDef } from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomPrice,
  randomCurrency,
  randomCountry,
  randomCity,
  randomEmail,
  randomInt,
  randomAddress,
  randomCommodity,
} from '@mui/x-data-grid-generator';

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
  return (
    <Stack
      sx={{ py: 2, height: '100%', boxSizing: 'border-box' }}
      direction="column"
    >
      <Paper sx={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}>
        <Stack direction="column" spacing={1} sx={{ height: 1 }}>
          <Typography variant="h6">{`Order #${rowProp.id}`}</Typography>
          <Grid container>
            <Grid size={{ md: 6 }}>
              <Typography variant="body2" color="textSecondary">
                Customer information
              </Typography>
              <Typography variant="body1">{rowProp.customer}</Typography>
              <Typography variant="body1">{rowProp.email}</Typography>
            </Grid>
            <Grid size={{ md: 6 }}>
              <Typography variant="body2" align="right" color="textSecondary">
                Shipping address
              </Typography>
              <Typography variant="body1" align="right">
                {rowProp.address}
              </Typography>
              <Typography
                variant="body1"
                align="right"
              >{`${rowProp.city}, ${rowProp.country.label}`}</Typography>
            </Grid>
          </Grid>
          <DataGridPro
            density="compact"
            columns={[
              { field: 'name', headerName: 'Product', flex: 1 },
              {
                field: 'quantity',
                headerName: 'Quantity',
                align: 'center',
                type: 'number',
              },
              { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
              {
                field: 'total',
                headerName: 'Total',
                type: 'number',
                valueGetter: (value, row) => row.quantity * row.unitPrice,
              },
            ]}
            rows={rowProp.products}
            sx={{ flex: 1 }}
            hideFooter
          />
        </Stack>
      </Paper>
    </Stack>
  );
}

const columns: GridColDef<(typeof rows)[number]>[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'date', type: 'date', headerName: 'Placed at' },
  { field: 'currency', headerName: 'Currency' },
  {
    field: 'total',
    type: 'number',
    headerName: 'Total',
    valueGetter: (value, row) => {
      const subtotal = row.products.reduce(
        (acc: number, product: any) => product.unitPrice * product.quantity,
        0,
      );
      const taxes = subtotal * 0.05;
      return subtotal + taxes;
    },
  },
];

function generateProducts {
  const quantity = randomInt(1, 5);
  return [...Array(quantity)].map((_, index) => ({
    id: index,
    name: randomCommodity,
    quantity: randomInt(1, 5),
    unitPrice: randomPrice(1, 1000),
  }));
}

const rows = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
];

type Customer = (typeof rows)[number];

export default function BasicDetailPanels {
  const getDetailPanelContent = React.useCallback<
    NonNullable<DataGridProProps['getDetailPanelContent']>
  >(({ row }) => <DetailPanelContent row={row} />, []);

  const getDetailPanelHeight = React.useCallback( => 400, []);

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <DataGridPro
        columns={columns}
        rows={rows}
        getDetailPanelHeight={getDetailPanelHeight}
        getDetailPanelContent={getDetailPanelContent}
      />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import { DataGridPro, DataGridProProps, GridColDef } from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomPrice,
 randomCurrency,
 randomCountry,
 randomCity,
 randomEmail,
 randomInt,
 randomAddress,
 randomCommodity,
} from '@mui/x\-data\-grid\-generator';

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
 return (
 \<Stack
 sx\={{ py: 2, height: '100%', boxSizing: 'border\-box' }}
 direction\="column"
 \>
 \<Paper sx\={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}\>
 \<Stack direction\="column" spacing\={1} sx\={{ height: 1 }}\>
 \<Typography variant\="h6"\>{\`Order \#${rowProp.id}\`}\</Typography\>
 \<Grid container\>
 \<Grid size\={{ md: 6 }}\>
 \<Typography variant\="body2" color\="textSecondary"\>
 Customer information
 \</Typography\>
 \<Typography variant\="body1"\>{rowProp.customer}\</Typography\>
 \<Typography variant\="body1"\>{rowProp.email}\</Typography\>
 \</Grid\>
 \<Grid size\={{ md: 6 }}\>
 \<Typography variant\="body2" align\="right" color\="textSecondary"\>
 Shipping address
 \</Typography\>
 \<Typography variant\="body1" align\="right"\>
 {rowProp.address}
 \</Typography\>
 \<Typography
 variant\="body1"
 align\="right"
 \>{\`${rowProp.city}, ${rowProp.country.label}\`}\</Typography\>
 \</Grid\>
 \</Grid\>
 \<DataGridPro
 density\="compact"
 columns\={\[
 { field: 'name', headerName: 'Product', flex: 1 },
 {
 field: 'quantity',
 headerName: 'Quantity',
 align: 'center',
 type: 'number',
 },
 { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
 {
 field: 'total',
 headerName: 'Total',
 type: 'number',
 valueGetter: (value, row) \=\> row.quantity \* row.unitPrice,
 },
 ]}
 rows\={rowProp.products}
 sx\={{ flex: 1 }}
 hideFooter
 /\>
 \</Stack\>
 \</Paper\>
 \</Stack\>
 );
}

const columns: GridColDef\<(typeof rows)\[number]\>\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'date', type: 'date', headerName: 'Placed at' },
 { field: 'currency', headerName: 'Currency' },
 {
 field: 'total',
 type: 'number',
 headerName: 'Total',
 valueGetter: (value, row) \=\> {
 const subtotal \= row.products.reduce(
 (acc: number, product: any) \=\> product.unitPrice \* product.quantity,
 0,
 );
 const taxes \= subtotal \* 0\.05;
 return subtotal \+ taxes;
 },
 },
];

function generateProducts {
 const quantity \= randomInt(1, 5\);
 return \[...Array(quantity)].map((\_, index) \=\> ({
 id: index,
 name: randomCommodity,
 quantity: randomInt(1, 5\),
 unitPrice: randomPrice(1, 1000\),
 }));
}

const rows \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
];

type Customer \= (typeof rows)\[number];

export default function BasicDetailPanels {
 const getDetailPanelContent \= React.useCallback\<
 NonNullable\<DataGridProProps\['getDetailPanelContent']\>
 \>(({ row }) \=\> \<DetailPanelContent row\={row} /\>, \[]);

 const getDetailPanelHeight \= React.useCallback( \=\> 400, \[]);

 return (
 \<Box sx\={{ width: '100%', height: 400 }}\>
 \<DataGridPro
 columns\={columns}
 rows\={rows}
 getDetailPanelHeight\={getDetailPanelHeight}
 getDetailPanelContent\={getDetailPanelContent}
 /\>
 \</Box\>
 );
}Press `Enter` to start editing
Always memoize the function provided to `getDetailPanelContent` and `getDetailPanelHeight`.
The Data Grid depends on the referential value of these props to cache their values and optimize the rendering.



```
const getDetailPanelContent = React.useCallback( => { ... }, []);

<DataGridPro getDetailPanelContent={getDetailPanelContent} />

```
CopyCopied(or Ctrl \+ C)
Infer height from the content
-----------------------------

Like dynamic row height, you can also derive the detail panel height from its content.
For this, pass a function to the `getDetailPanelHeight` prop returning `"auto"`, as below:



```
<DataGridPro getDetailPanelHeight={ => 'auto'} />

```
CopyCopied(or Ctrl \+ C)
The following example demonstrates this option in action:


Order IDCustomerPlaced atCurrencyTotal1Matheus2025/1/6CAD565\.6772Olivier2025/3/31CAD475\.5453Flavien2024/7/31TRY1,815\.9334Danail2024/11/23AUD481\.0375Alexandre2024/10/8EUR506\.919Total Rows: 5JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  DataGridPro,
  GridColDef,
  DataGridProProps,
  useGridApiContext,
  GridActionsCellItem,
} from '@mui/x-data-grid-pro';
import {
  randomId,
  randomCreatedDate,
  randomPrice,
  randomCurrency,
  randomCountry,
  randomCity,
  randomEmail,
  randomInt,
  randomAddress,
  randomCommodity,
} from '@mui/x-data-grid-generator';

function generateProduct {
  return {
    id: randomId,
    name: randomCommodity,
    quantity: randomInt(1, 5),
    unitPrice: randomPrice(1, 1000),
  };
}

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
  const apiRef = useGridApiContext;

  const addProduct = React.useCallback( => {
    const newProduct = generateProduct;
    apiRef.current.updateRows([
      { ...rowProp, products: [...rowProp.products, newProduct] },
    ]);
  }, [apiRef, rowProp]);

  const deleteProduct = React.useCallback(
    (productId: string) =>  => {
      const newProducts = rowProp.products.filter(
        (product) => product.id !== productId,
      );
      apiRef.current.updateRows([{ ...rowProp, products: newProducts }]);
    },
    [apiRef, rowProp],
  );

  const columns = React.useMemo<GridColDef<Customer['products'][number]>[]>(
     => [
      { field: 'name', headerName: 'Product', flex: 1, editable: true },
      {
        field: 'quantity',
        headerName: 'Quantity',
        align: 'center',
        headerAlign: 'center',
        type: 'number',
        editable: true,
      },
      { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
      {
        field: 'total',
        headerName: 'Total',
        type: 'number',
        valueGetter: (value, row) => row.quantity * row.unitPrice,
      },
      {
        field: 'actions',
        headerName: '',
        type: 'actions',
        width: 50,
        getActions: ({ row }) => [
          <GridActionsCellItem
            icon={<DeleteIcon />}
            label="delete"
            onClick={deleteProduct(row.id)}
          />,
        ],
      },
    ],
    [deleteProduct],
  );

  return (
    <Stack sx={{ py: 2, height: 1, boxSizing: 'border-box' }} direction="column">
      <Paper sx={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}>
        <Stack direction="column" spacing={1} sx={{ height: 1 }}>
          <Typography variant="h6">{`Order #${rowProp.id}`}</Typography>
          <Grid container>
            <Grid size={{ md: 6 }}>
              <Typography variant="body2" color="textSecondary">
                Customer information
              </Typography>
              <Typography variant="body1">{rowProp.customer}</Typography>
              <Typography variant="body1">{rowProp.email}</Typography>
            </Grid>
            <Grid size={{ md: 6 }}>
              <Typography variant="body2" align="right" color="textSecondary">
                Shipping address
              </Typography>
              <Typography variant="body1" align="right">
                {rowProp.address}
              </Typography>
              <Typography
                variant="body1"
                align="right"
              >{`${rowProp.city}, ${rowProp.country.label}`}</Typography>
            </Grid>
          </Grid>
          <div>
            <Button variant="outlined" size="small" onClick={addProduct}>
              Add Product
            </Button>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            <DataGridPro
              density="compact"
              columns={columns}
              rows={rowProp.products}
              hideFooter
            />
          </div>
        </Stack>
      </Paper>
    </Stack>
  );
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'date', type: 'date', headerName: 'Placed at' },
  { field: 'currency', headerName: 'Currency' },
  {
    field: 'total',
    type: 'number',
    headerName: 'Total',
    valueGetter: (value, row) => {
      const subtotal = row.products.reduce(
        (acc: number, product: any) => product.unitPrice * product.quantity,
        0,
      );
      const taxes = subtotal * 0.05;
      return subtotal + taxes;
    },
  },
];

function generateProducts {
  const quantity = randomInt(1, 5);
  return [...Array(quantity)].map(generateProduct);
}

const rows = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
];

type Customer = (typeof rows)[number];

export default function DetailPanelAutoHeight {
  const getDetailPanelContent = React.useCallback<
    NonNullable<DataGridProProps['getDetailPanelContent']>
  >(({ row }) => <DetailPanelContent row={row} />, []);

  const getDetailPanelHeight = React.useCallback<
    NonNullable<DataGridProProps['getDetailPanelHeight']>
  >( => 'auto' as const, []);

  return (
    <Box sx={{ width: 1, height: 400 }}>
      <DataGridPro
        columns={columns}
        rows={rows}
        getDetailPanelHeight={getDetailPanelHeight}
        getDetailPanelContent={getDetailPanelContent}
      />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import DeleteIcon from '@mui/icons\-material/Delete';
import {
 DataGridPro,
 GridColDef,
 DataGridProProps,
 useGridApiContext,
 GridActionsCellItem,
} from '@mui/x\-data\-grid\-pro';
import {
 randomId,
 randomCreatedDate,
 randomPrice,
 randomCurrency,
 randomCountry,
 randomCity,
 randomEmail,
 randomInt,
 randomAddress,
 randomCommodity,
} from '@mui/x\-data\-grid\-generator';

function generateProduct {
 return {
 id: randomId,
 name: randomCommodity,
 quantity: randomInt(1, 5\),
 unitPrice: randomPrice(1, 1000\),
 };
}

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
 const apiRef \= useGridApiContext;

 const addProduct \= React.useCallback( \=\> {
 const newProduct \= generateProduct;
 apiRef.current.updateRows(\[
 { ...rowProp, products: \[...rowProp.products, newProduct] },
 ]);
 }, \[apiRef, rowProp]);

 const deleteProduct \= React.useCallback(
 (productId: string) \=\>  \=\> {
 const newProducts \= rowProp.products.filter(
 (product) \=\> product.id !\=\= productId,
 );
 apiRef.current.updateRows(\[{ ...rowProp, products: newProducts }]);
 },
 \[apiRef, rowProp],
 );

 const columns \= React.useMemo\<GridColDef\<Customer\['products']\[number]\>\[]\>(
  \=\> \[
 { field: 'name', headerName: 'Product', flex: 1, editable: true },
 {
 field: 'quantity',
 headerName: 'Quantity',
 align: 'center',
 headerAlign: 'center',
 type: 'number',
 editable: true,
 },
 { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
 {
 field: 'total',
 headerName: 'Total',
 type: 'number',
 valueGetter: (value, row) \=\> row.quantity \* row.unitPrice,
 },
 {
 field: 'actions',
 headerName: '',
 type: 'actions',
 width: 50,
 getActions: ({ row }) \=\> \[
 \<GridActionsCellItem
 icon\={\<DeleteIcon /\>}
 label\="delete"
 onClick\={deleteProduct(row.id)}
 /\>,
 ],
 },
 ],
 \[deleteProduct],
 );

 return (
 \<Stack sx\={{ py: 2, height: 1, boxSizing: 'border\-box' }} direction\="column"\>
 \<Paper sx\={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}\>
 \<Stack direction\="column" spacing\={1} sx\={{ height: 1 }}\>
 \<Typography variant\="h6"\>{\`Order \#${rowProp.id}\`}\</Typography\>
 \<Grid container\>
 \<Grid size\={{ md: 6 }}\>
 \<Typography variant\="body2" color\="textSecondary"\>
 Customer information
 \</Typography\>
 \<Typography variant\="body1"\>{rowProp.customer}\</Typography\>
 \<Typography variant\="body1"\>{rowProp.email}\</Typography\>
 \</Grid\>
 \<Grid size\={{ md: 6 }}\>
 \<Typography variant\="body2" align\="right" color\="textSecondary"\>
 Shipping address
 \</Typography\>
 \<Typography variant\="body1" align\="right"\>
 {rowProp.address}
 \</Typography\>
 \<Typography
 variant\="body1"
 align\="right"
 \>{\`${rowProp.city}, ${rowProp.country.label}\`}\</Typography\>
 \</Grid\>
 \</Grid\>
 \<div\>
 \<Button variant\="outlined" size\="small" onClick\={addProduct}\>
 Add Product
 \</Button\>
 \</div\>
 \<div style\={{ display: 'flex', flexDirection: 'column', width: '100%' }}\>
 \<DataGridPro
 density\="compact"
 columns\={columns}
 rows\={rowProp.products}
 hideFooter
 /\>
 \</div\>
 \</Stack\>
 \</Paper\>
 \</Stack\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'date', type: 'date', headerName: 'Placed at' },
 { field: 'currency', headerName: 'Currency' },
 {
 field: 'total',
 type: 'number',
 headerName: 'Total',
 valueGetter: (value, row) \=\> {
 const subtotal \= row.products.reduce(
 (acc: number, product: any) \=\> product.unitPrice \* product.quantity,
 0,
 );
 const taxes \= subtotal \* 0\.05;
 return subtotal \+ taxes;
 },
 },
];

function generateProducts {
 const quantity \= randomInt(1, 5\);
 return \[...Array(quantity)].map(generateProduct);
}

const rows \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
];

type Customer \= (typeof rows)\[number];

export default function DetailPanelAutoHeight {
 const getDetailPanelContent \= React.useCallback\<
 NonNullable\<DataGridProProps\['getDetailPanelContent']\>
 \>(({ row }) \=\> \<DetailPanelContent row\={row} /\>, \[]);

 const getDetailPanelHeight \= React.useCallback\<
 NonNullable\<DataGridProProps\['getDetailPanelHeight']\>
 \>( \=\> 'auto' as const, \[]);

 return (
 \<Box sx\={{ width: 1, height: 400 }}\>
 \<DataGridPro
 columns\={columns}
 rows\={rows}
 getDetailPanelHeight\={getDetailPanelHeight}
 getDetailPanelContent\={getDetailPanelContent}
 /\>
 \</Box\>
 );
}Press `Enter` to start editingControlling expanded detail panels
----------------------------------

To control which rows are expanded, pass a set of row IDs to the `detailPanelExpandedRowIds` prop.
Passing a callback to the `onDetailPanelExpandedRowIds` prop can be used to detect when a row gets expanded or collapsed.


On the other hand, if you only want to initialize the Data Grid with some rows already expanded, use the `initialState` prop as follows:



```
<DataGridPro initialState={{ detailPanel: { expandedRowIds: new Set([1, 2, 3]) } }}>

```
CopyCopied(or Ctrl \+ C)
`detailPanelExpandedRowIds: []`Order IDCustomerPlaced atCurrencyTotal1Matheus2024/6/13CHF303\.162Olivier2024/11/11NZD474\.363Flavien2024/11/13NZD799\.564Danail2025/3/14ARS212\.95Alexandre2025/5/4ARS870\.08Total Rows: 5JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import {
  DataGridPro,
  GridColDef,
  GridRowsProp,
  GridRowId,
  DataGridProProps,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomCurrency,
  randomEmail,
  randomPrice,
} from '@mui/x-data-grid-generator';
import Alert from '@mui/material/Alert';

export default function ControlMasterDetail {
  const [detailPanelExpandedRowIds, setDetailPanelExpandedRowIds] = React.useState(
     => new Set<GridRowId>,
  );

  const handleDetailPanelExpandedRowIdsChange = React.useCallback<
    NonNullable<DataGridProProps['onDetailPanelExpandedRowIdsChange']>
  >((newIds) => {
    setDetailPanelExpandedRowIds(newIds);
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <Alert severity="info">
        <code>
          detailPanelExpandedRowIds: {JSON.stringify(detailPanelExpandedRowIds)}
        </code>
      </Alert>
      <Box sx={{ height: 400, mt: 1 }}>
        <DataGridPro
          rows={rows}
          columns={columns}
          getDetailPanelContent={({ row }) => (
            <Box sx={{ p: 2 }}>{`Order #${row.id}`}</Box>
          )}
          getDetailPanelHeight={ => 50}
          detailPanelExpandedRowIds={detailPanelExpandedRowIds}
          onDetailPanelExpandedRowIdsChange={handleDetailPanelExpandedRowIdsChange}
        />
      </Box>
    </Box>
  );
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'date', type: 'date', headerName: 'Placed at' },
  { field: 'currency', headerName: 'Currency' },
  { field: 'total', type: 'number', headerName: 'Total' },
];

const rows: GridRowsProp = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
];  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import {
 DataGridPro,
 GridColDef,
 GridRowsProp,
 GridRowId,
 DataGridProProps,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomCurrency,
 randomEmail,
 randomPrice,
} from '@mui/x\-data\-grid\-generator';
import Alert from '@mui/material/Alert';

export default function ControlMasterDetail {
 const \[detailPanelExpandedRowIds, setDetailPanelExpandedRowIds] \= React.useState(
  \=\> new Set\<GridRowId\>,
 );

 const handleDetailPanelExpandedRowIdsChange \= React.useCallback\<
 NonNullable\<DataGridProProps\['onDetailPanelExpandedRowIdsChange']\>
 \>((newIds) \=\> {
 setDetailPanelExpandedRowIds(newIds);
 }, \[]);

 return (
 \<Box sx\={{ width: '100%' }}\>
 \<Alert severity\="info"\>
 \<code\>
 detailPanelExpandedRowIds: {JSON.stringify(detailPanelExpandedRowIds)}
 \</code\>
 \</Alert\>
 \<Box sx\={{ height: 400, mt: 1 }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 getDetailPanelContent\={({ row }) \=\> (
 \<Box sx\={{ p: 2 }}\>{\`Order \#${row.id}\`}\</Box\>
 )}
 getDetailPanelHeight\={ \=\> 50}
 detailPanelExpandedRowIds\={detailPanelExpandedRowIds}
 onDetailPanelExpandedRowIdsChange\={handleDetailPanelExpandedRowIdsChange}
 /\>
 \</Box\>
 \</Box\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'date', type: 'date', headerName: 'Placed at' },
 { field: 'currency', headerName: 'Currency' },
 { field: 'total', type: 'number', headerName: 'Total' },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
];Press `Enter` to start editingLazy loading detail panel content
---------------------------------

You don't need to provide the content for detail panels upfront.
Instead, you can load it lazily when the row is expanded.


In the following example, the `DetailPanelContent` component is fetching the data on mount.
This component is used by the `getDetailPanelContent` prop to render the detail panel content.


CustomerEmailRussell <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> Rows: 100JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import { DataGridPro, DataGridProProps, GridColDef } from '@mui/x-data-grid-pro';
import {
  randomEmail,
  randomInt,
  randomCommodity,
  randomPrice,
  randomTraderName,
  randomId,
} from '@mui/x-data-grid-generator';
import { DataGridProps, GridRowId } from '@mui/x-data-grid';

type Products = Awaited<ReturnType<typeof getProducts>>;

const DetailPanelDataCache = React.createContext(new Map<GridRowId, Products>);

async function getProducts(orderId: Customer['id']) {
  await new Promise((resolve) => {
    setTimeout(resolve, 1000);
  });

  const quantity = randomInt(1, 5);
  return [...Array(quantity)].map((_, index) => ({
    id: index,
    orderId,
    name: randomCommodity,
    quantity: randomInt(1, 5),
    unitPrice: randomPrice(1, 1000),
  }));
}

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
  const [isLoading, setLoading] = React.useState(true);
  const [products, setProducts] = React.useState<
    Awaited<ReturnType<typeof getProducts>>
  >([]);

  const detailPanelDataCache = React.useContext(DetailPanelDataCache);

  React.useEffect( => {
    let isMounted = true;
    (async  => {
      if (!detailPanelDataCache.has(rowProp.id)) {
        console.log('fetching detail panel content for row', rowProp.id);
        const response = await getProducts(rowProp.id);
        // Store the data in cache so that when detail panel unmounts due to virtualization, the data is not lost
        detailPanelDataCache.set(rowProp.id, response);
      }

      const result = detailPanelDataCache.get(rowProp.id)!;

      if (!isMounted) {
        return;
      }

      setProducts(result);
      setLoading(false);
    });

    return  => {
      isMounted = false;
    };
  }, [rowProp.id, detailPanelDataCache]);

  return (
    <Stack
      sx={{ py: 2, height: '100%', boxSizing: 'border-box' }}
      direction="column"
    >
      <Paper sx={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}>
        <Stack direction="column" spacing={1} sx={{ height: 1 }}>
          <Typography variant="h6">{`Order #${rowProp.id}`}</Typography>
          <DataGridPro
            density="compact"
            loading={isLoading}
            columns={[
              { field: 'name', headerName: 'Product', flex: 1 },
              {
                field: 'quantity',
                headerName: 'Quantity',
                align: 'center',
                type: 'number',
              },
              { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
              {
                field: 'total',
                headerName: 'Total',
                type: 'number',
                valueGetter: (value, row) => row.quantity * row.unitPrice,
              },
            ]}
            rows={products}
            sx={{ flex: 1 }}
            hideFooter
          />
        </Stack>
      </Paper>
    </Stack>
  );
}

const columns: GridColDef[] = [
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'email', headerName: 'Email', width: 200 },
];

function getRow {
  return {
    id: randomId,
    customer: randomTraderName,
    email: randomEmail,
  };
}

const rows: ReturnType<typeof getRow>[] = [];
for (let i = 0; i < 100; i += 1) {
  rows.push(getRow);
}

type Customer = (typeof rows)[number];

const getDetailPanelContent: DataGridProps['getDetailPanelContent'] = (params) => (
  <DetailPanelContent row={params.row} />
);

const getDetailPanelHeight =  => 240;

export default function LazyLoadingDetailPanel {
  const detailPanelDataCache = React.useRef(new Map<GridRowId, Products>).current;

  const handleDetailPanelExpansionChange = React.useCallback<
    NonNullable<DataGridProProps['onDetailPanelExpandedRowIdsChange']>
  >(
    (newExpandedRowIds) => {
      // Only keep cached data for detail panels that are still expanded
      for (const [id] of detailPanelDataCache) {
        if (!newExpandedRowIds.has(id)) {
          detailPanelDataCache.delete(id);
        }
      }
    },
    [detailPanelDataCache],
  );

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <DetailPanelDataCache.Provider value={detailPanelDataCache}>
        <DataGridPro
          columns={columns}
          rows={rows}
          getDetailPanelHeight={getDetailPanelHeight}
          getDetailPanelContent={getDetailPanelContent}
          onDetailPanelExpandedRowIdsChange={handleDetailPanelExpansionChange}
        />
      </DetailPanelDataCache.Provider>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import { DataGridPro, DataGridProProps, GridColDef } from '@mui/x\-data\-grid\-pro';
import {
 randomEmail,
 randomInt,
 randomCommodity,
 randomPrice,
 randomTraderName,
 randomId,
} from '@mui/x\-data\-grid\-generator';
import { DataGridProps, GridRowId } from '@mui/x\-data\-grid';

type Products \= Awaited\<ReturnType\<typeof getProducts\>\>;

const DetailPanelDataCache \= React.createContext(new Map\<GridRowId, Products\>);

async function getProducts(orderId: Customer\['id']) {
 await new Promise((resolve) \=\> {
 setTimeout(resolve, 1000\);
 });

 const quantity \= randomInt(1, 5\);
 return \[...Array(quantity)].map((\_, index) \=\> ({
 id: index,
 orderId,
 name: randomCommodity,
 quantity: randomInt(1, 5\),
 unitPrice: randomPrice(1, 1000\),
 }));
}

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
 const \[isLoading, setLoading] \= React.useState(true);
 const \[products, setProducts] \= React.useState\<
 Awaited\<ReturnType\<typeof getProducts\>\>
 \>(\[]);

 const detailPanelDataCache \= React.useContext(DetailPanelDataCache);

 React.useEffect( \=\> {
 let isMounted \= true;
 (async  \=\> {
 if (!detailPanelDataCache.has(rowProp.id)) {
 console.log('fetching detail panel content for row', rowProp.id);
 const response \= await getProducts(rowProp.id);
 // Store the data in cache so that when detail panel unmounts due to virtualization, the data is not lost
 detailPanelDataCache.set(rowProp.id, response);
 }

 const result \= detailPanelDataCache.get(rowProp.id)!;

 if (!isMounted) {
 return;
 }

 setProducts(result);
 setLoading(false);
 });

 return  \=\> {
 isMounted \= false;
 };
 }, \[rowProp.id, detailPanelDataCache]);

 return (
 \<Stack
 sx\={{ py: 2, height: '100%', boxSizing: 'border\-box' }}
 direction\="column"
 \>
 \<Paper sx\={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}\>
 \<Stack direction\="column" spacing\={1} sx\={{ height: 1 }}\>
 \<Typography variant\="h6"\>{\`Order \#${rowProp.id}\`}\</Typography\>
 \<DataGridPro
 density\="compact"
 loading\={isLoading}
 columns\={\[
 { field: 'name', headerName: 'Product', flex: 1 },
 {
 field: 'quantity',
 headerName: 'Quantity',
 align: 'center',
 type: 'number',
 },
 { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
 {
 field: 'total',
 headerName: 'Total',
 type: 'number',
 valueGetter: (value, row) \=\> row.quantity \* row.unitPrice,
 },
 ]}
 rows\={products}
 sx\={{ flex: 1 }}
 hideFooter
 /\>
 \</Stack\>
 \</Paper\>
 \</Stack\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'email', headerName: 'Email', width: 200 },
];

function getRow {
 return {
 id: randomId,
 customer: randomTraderName,
 email: randomEmail,
 };
}

const rows: ReturnType\<typeof getRow\>\[] \= \[];
for (let i \= 0; i \< 100; i \+\= 1\) {
 rows.push(getRow);
}

type Customer \= (typeof rows)\[number];

const getDetailPanelContent: DataGridProps\['getDetailPanelContent'] \= (params) \=\> (
 \<DetailPanelContent row\={params.row} /\>
);

const getDetailPanelHeight \=  \=\> 240;

export default function LazyLoadingDetailPanel {
 const detailPanelDataCache \= React.useRef(new Map\<GridRowId, Products\>).current;

 const handleDetailPanelExpansionChange \= React.useCallback\<
 NonNullable\<DataGridProProps\['onDetailPanelExpandedRowIdsChange']\>
 \>(
 (newExpandedRowIds) \=\> {
 // Only keep cached data for detail panels that are still expanded
 for (const \[id] of detailPanelDataCache) {
 if (!newExpandedRowIds.has(id)) {
 detailPanelDataCache.delete(id);
 }
 }
 },
 \[detailPanelDataCache],
 );

 return (
 \<Box sx\={{ width: '100%', height: 400 }}\>
 \<DetailPanelDataCache.Provider value\={detailPanelDataCache}\>
 \<DataGridPro
 columns\={columns}
 rows\={rows}
 getDetailPanelHeight\={getDetailPanelHeight}
 getDetailPanelContent\={getDetailPanelContent}
 onDetailPanelExpandedRowIdsChange\={handleDetailPanelExpansionChange}
 /\>
 \</DetailPanelDataCache.Provider\>
 \</Box\>
 );
}Press `Enter` to start editingUsing a detail panel as a form
------------------------------

As an alternative to the built\-in row editing, a form component can be rendered inside the detail panel, allowing the user to edit the current row values.


The following demo shows integration with react\-hook\-form, but other form libraries are also supported.


Order IDCustomerEmail1Matheusgo@la.co2Olivierzewar@si.cx3Flavienmiwen@jaflara.gov4Danailjerabicur@<EMAIL> Rows: 5JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { useForm, Controller } from 'react-hook-form';
import {
  DataGridPro,
  GridColDef,
  GridRowModelUpdate,
  useGridApiContext,
  GridRowParams,
} from '@mui/x-data-grid-pro';
import { randomEmail } from '@mui/x-data-grid-generator';

function DetailPanelContent({ row }: { row: Customer }) {
  const apiRef = useGridApiContext;
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm({
    defaultValues: row,
    mode: 'onChange',
  });

  const onSubmit = (data: GridRowModelUpdate) => {
    apiRef.current.updateRows([data]);
    apiRef.current.toggleDetailPanel(row.id);
  };

  return (
    <Stack
      sx={{ py: 2, height: '100%', boxSizing: 'border-box' }}
      direction="column"
    >
      <Paper sx={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}>
        <Stack
          component="form"
          justifyContent="space-between"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ height: 1 }}
        >
          <Typography variant="h6">{`Edit Order #${row.id}`}</Typography>
          <Controller
            control={control}
            name="customer"
            rules={{ required: true }}
            render={({ field, fieldState: { invalid } }) => (
              <TextField
                label="Customer"
                size="small"
                error={invalid}
                required
                fullWidth
                {...field}
              />
            )}
          />
          <Controller
            control={control}
            name="email"
            rules={{ required: true }}
            render={({ field, fieldState: { invalid } }) => (
              <TextField
                label="Email"
                size="small"
                error={invalid}
                required
                fullWidth
                {...field}
              />
            )}
          />
          <div>
            <Button
              type="submit"
              variant="outlined"
              size="small"
              disabled={!isValid}
            >
              Save
            </Button>
          </div>
        </Stack>
      </Paper>
    </Stack>
  );
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'email', headerName: 'Email', width: 200 },
];

const rows = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
  },
];

type Customer = (typeof rows)[number];

export default function FormDetailPanel {
  const getDetailPanelContent = React.useCallback(
    ({ row }: GridRowParams) => <DetailPanelContent row={row} />,
    [],
  );

  const getDetailPanelHeight = React.useCallback( => 240, []);

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <DataGridPro
        columns={columns}
        rows={rows}
        getDetailPanelHeight={getDetailPanelHeight}
        getDetailPanelContent={getDetailPanelContent}
      />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { useForm, Controller } from 'react\-hook\-form';
import {
 DataGridPro,
 GridColDef,
 GridRowModelUpdate,
 useGridApiContext,
 GridRowParams,
} from '@mui/x\-data\-grid\-pro';
import { randomEmail } from '@mui/x\-data\-grid\-generator';

function DetailPanelContent({ row }: { row: Customer }) {
 const apiRef \= useGridApiContext;
 const {
 control,
 handleSubmit,
 formState: { isValid },
 } \= useForm({
 defaultValues: row,
 mode: 'onChange',
 });

 const onSubmit \= (data: GridRowModelUpdate) \=\> {
 apiRef.current.updateRows(\[data]);
 apiRef.current.toggleDetailPanel(row.id);
 };

 return (
 \<Stack
 sx\={{ py: 2, height: '100%', boxSizing: 'border\-box' }}
 direction\="column"
 \>
 \<Paper sx\={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}\>
 \<Stack
 component\="form"
 justifyContent\="space\-between"
 onSubmit\={handleSubmit(onSubmit)}
 sx\={{ height: 1 }}
 \>
 \<Typography variant\="h6"\>{\`Edit Order \#${row.id}\`}\</Typography\>
 \<Controller
 control\={control}
 name\="customer"
 rules\={{ required: true }}
 render\={({ field, fieldState: { invalid } }) \=\> (
 \<TextField
 label\="Customer"
 size\="small"
 error\={invalid}
 required
 fullWidth
 {...field}
 /\>
 )}
 /\>
 \<Controller
 control\={control}
 name\="email"
 rules\={{ required: true }}
 render\={({ field, fieldState: { invalid } }) \=\> (
 \<TextField
 label\="Email"
 size\="small"
 error\={invalid}
 required
 fullWidth
 {...field}
 /\>
 )}
 /\>
 \<div\>
 \<Button
 type\="submit"
 variant\="outlined"
 size\="small"
 disabled\={!isValid}
 \>
 Save
 \</Button\>
 \</div\>
 \</Stack\>
 \</Paper\>
 \</Stack\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'email', headerName: 'Email', width: 200 },
];

const rows \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 },
];

type Customer \= (typeof rows)\[number];

export default function FormDetailPanel {
 const getDetailPanelContent \= React.useCallback(
 ({ row }: GridRowParams) \=\> \<DetailPanelContent row\={row} /\>,
 \[],
 );

 const getDetailPanelHeight \= React.useCallback( \=\> 240, \[]);

 return (
 \<Box sx\={{ width: '100%', height: 400 }}\>
 \<DataGridPro
 columns\={columns}
 rows\={rows}
 getDetailPanelHeight\={getDetailPanelHeight}
 getDetailPanelContent\={getDetailPanelContent}
 /\>
 \</Box\>
 );
}Press `Enter` to start editingCustomizing the detail panel toggle
-----------------------------------

To change the icon used for the toggle, you can provide a different component for the icon slot as follow:



```
<DataGridPro
  slots={{
    detailPanelExpandIcon: CustomExpandIcon,
    detailPanelCollapseIcon: CustomCollapseIcon,
  }}
/>

```
CopyCopied(or Ctrl \+ C)
If this is not sufficient, the entire toggle component can be overridden.
To fully customize it, add another column with `field: GRID_DETAIL_PANEL_TOGGLE_FIELD` to your set of columns.
The grid will detect that there is already a toggle column defined and it will not add another toggle in the default position.
The new toggle component can be provided via `renderCell` in the same as any other column.
By only setting the `field`, is up to you to configure the remaining options (for example disable the column menu, filtering, sorting).
To already start with a few suggested options configured, spread `GRID_DETAIL_PANEL_TOGGLE_COL_DEF` when defining the column.



```
<DataGridPro
  columns={[
    {
      field: GRID_DETAIL_PANEL_TOGGLE_FIELD,
      renderCell: (params) => <CustomDetailPanelToggle {...params} />
    },
  ]}
/>

// or

<DataGridPro
  columns={[
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF, // Already contains the right field
      renderCell: (params) => <CustomDetailPanelToggle {...params}>
    },
  ]}
/>

```
CopyCopied(or Ctrl \+ C)
This approach can also be used to change the location of the toggle column, as shown below.


Order IDCustomerPlaced atCurrencyTotal1Matheus2024/12/29USD674\.072Olivier2024/9/25BRL418\.743Flavien2024/12/25AUD848\.544Danail2024/7/20CHF98\.15Alexandre2025/5/4TRY204\.05Total Rows: 5JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  DataGridPro,
  GridColDef,
  GridRowsProp,
  GridRenderCellParams,
  GridRowParams,
  useGridSelector,
  useGridApiContext,
  gridDetailPanelExpandedRowsContentCacheSelector,
  gridDetailPanelExpandedRowIdsSelector,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomCurrency,
  randomEmail,
  randomPrice,
} from '@mui/x-data-grid-generator';

export default function CustomizeDetailPanelToggle {
  const getDetailPanelContent = React.useCallback(
    ({ row }: GridRowParams) =>
      row.id % 2 === 0 ? <Box sx={{ p: 2 }}>{`Order #${row.id}`}</Box> : null,
    [],
  );

  const getDetailPanelHeight = React.useCallback( => 50, []);

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        rows={rows}
        columns={columns}
        getDetailPanelContent={getDetailPanelContent}
        getDetailPanelHeight={getDetailPanelHeight}
      />
    </div>
  );
}

function CustomDetailPanelToggle(props: Pick<GridRenderCellParams, 'id' | 'value'>) {
  const { id } = props;
  const apiRef = useGridApiContext;

  // To avoid calling ´getDetailPanelContent` all the time, the following selector
  // gives an object with the detail panel content for each row id.
  const contentCache = useGridSelector(
    apiRef,
    gridDetailPanelExpandedRowsContentCacheSelector,
  );

  const expandedRowIds = useGridSelector(
    apiRef,
    gridDetailPanelExpandedRowIdsSelector,
  );

  const isExpanded = expandedRowIds.has(id);

  // If the value is not a valid React element, it means that the row has no detail panel.
  const hasDetail = React.isValidElement(contentCache[id]);

  return (
    <IconButton
      size="small"
      tabIndex={-1}
      disabled={!hasDetail}
      aria-label={isExpanded ? 'Close' : 'Open'}
    >
      <ExpandMoreIcon
        sx={(theme) => ({
          transform: `rotateZ(${isExpanded ? 180 : 0}deg)`,
          transition: theme.transitions.create('transform', {
            duration: theme.transitions.duration.shortest,
          }),
        })}
        fontSize="inherit"
      />
    </IconButton>
  );
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'date', type: 'date', headerName: 'Placed at' },
  { field: 'currency', headerName: 'Currency' },
  { field: 'total', type: 'number', headerName: 'Total' },
  {
    ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
    renderCell: (params) => (
      <CustomDetailPanelToggle id={params.id} value={params.value} />
    ),
  },
];

const rows: GridRowsProp = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
    date: randomCreatedDate,
    currency: randomCurrency,
    total: randomPrice(1, 1000),
  },
];  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import ExpandMoreIcon from '@mui/icons\-material/ExpandMore';
import {
 DataGridPro,
 GridColDef,
 GridRowsProp,
 GridRenderCellParams,
 GridRowParams,
 useGridSelector,
 useGridApiContext,
 gridDetailPanelExpandedRowsContentCacheSelector,
 gridDetailPanelExpandedRowIdsSelector,
 GRID\_DETAIL\_PANEL\_TOGGLE\_COL\_DEF,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomCurrency,
 randomEmail,
 randomPrice,
} from '@mui/x\-data\-grid\-generator';

export default function CustomizeDetailPanelToggle {
 const getDetailPanelContent \= React.useCallback(
 ({ row }: GridRowParams) \=\>
 row.id % 2 \=\=\= 0 ? \<Box sx\={{ p: 2 }}\>{\`Order \#${row.id}\`}\</Box\> : null,
 \[],
 );

 const getDetailPanelHeight \= React.useCallback( \=\> 50, \[]);

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 rows\={rows}
 columns\={columns}
 getDetailPanelContent\={getDetailPanelContent}
 getDetailPanelHeight\={getDetailPanelHeight}
 /\>
 \</div\>
 );
}

function CustomDetailPanelToggle(props: Pick\<GridRenderCellParams, 'id' \| 'value'\>) {
 const { id } \= props;
 const apiRef \= useGridApiContext;

 // To avoid calling ´getDetailPanelContent\` all the time, the following selector
 // gives an object with the detail panel content for each row id.
 const contentCache \= useGridSelector(
 apiRef,
 gridDetailPanelExpandedRowsContentCacheSelector,
 );

 const expandedRowIds \= useGridSelector(
 apiRef,
 gridDetailPanelExpandedRowIdsSelector,
 );

 const isExpanded \= expandedRowIds.has(id);

 // If the value is not a valid React element, it means that the row has no detail panel.
 const hasDetail \= React.isValidElement(contentCache\[id]);

 return (
 \<IconButton
 size\="small"
 tabIndex\={\-1}
 disabled\={!hasDetail}
 aria\-label\={isExpanded ? 'Close' : 'Open'}
 \>
 \<ExpandMoreIcon
 sx\={(theme) \=\> ({
 transform: \`rotateZ(${isExpanded ? 180 : 0}deg)\`,
 transition: theme.transitions.create('transform', {
 duration: theme.transitions.duration.shortest,
 }),
 })}
 fontSize\="inherit"
 /\>
 \</IconButton\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'date', type: 'date', headerName: 'Placed at' },
 { field: 'currency', headerName: 'Currency' },
 { field: 'total', type: 'number', headerName: 'Total' },
 {
 ...GRID\_DETAIL\_PANEL\_TOGGLE\_COL\_DEF,
 renderCell: (params) \=\> (
 \<CustomDetailPanelToggle id\={params.id} value\={params.value} /\>
 ),
 },
];

const rows: GridRowsProp \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 date: randomCreatedDate,
 currency: randomCurrency,
 total: randomPrice(1, 1000\),
 },
];Press `Enter` to start editing
As any ordinary cell renderer, the `value` prop is also available, and it corresponds to the state of the row: `true` when expanded and `false` when collapsed.


Custom header for detail panel column
-------------------------------------

To render a custom header for the detail panel column, use the `renderHeader` property in the column definition.
This property receives a `GridRenderHeaderParams` object that contains `colDef` (the column definition) and `field`.
The following example demonstrates how to render a custom header for the detail panel column:



```
const columns = [
  {
    ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
    renderHeader: (params) => (
      <div>
        <span>{params.colDef.headerName}</span>
        <button onClick={ => console.log('Custom action')}>Custom action</button>
      </div>
    ),
  },
  //... other columns
];

```
CopyCopied(or Ctrl \+ C)

For a more advanced example check out the Expand or collapse all detail panels recipe.


Disable detail panel content scroll
-----------------------------------

By default, the detail panel has a width that is the sum of the widths of all columns.
This means that when a horizontal scrollbar is present, scrolling it will also scroll the panel content.
To avoid this behavior, set the size of the detail panel to the outer size of the Data Grid.
Use `apiRef.current.getRootDimensions` to get the latest dimension values.
Finally, to prevent the panel from scrolling, set `position: sticky` and `left: 0`.


The following demo shows how this can be achieved.
Notice that the toggle column is pinned to make sure that it will always be visible when the Data Grid is scrolled horizontally.


<NAME_EMAIL>2024/11/21USD991 Nuwi PassJobedweg, French Southern Territories2,002\.5392Oliviertijij@cujze.la2025/4/18EUR1155 Ruwo RoadNupusdow, Finland2,834\.4023Flavienvac@ub.aq2024/10/8GBP139 Kate CenterNarehzo, Benin981\.4564Danaileda@eheg.lb2024/12/2CAD1977 Bevov ExtensionZefpaug, Kosovo2,711\.5525Alexandreavu@darib.edu2024/7/31USD1661 Ojuti ParkwayGidwuez, French Southern Territories384\.5836Josére@pickosfuv.lu2025/1/14THB99 Neru ManorHibuknah, Belgium420\.368Total Rows: 6JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import {
  DataGridPro,
  GridColDef,
  useGridApiContext,
  GridRowParams,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GridDimensions,
} from '@mui/x-data-grid-pro';
import {
  randomCreatedDate,
  randomPrice,
  randomCurrency,
  randomCountry,
  randomCity,
  randomEmail,
  randomInt,
  randomAddress,
  randomCommodity,
} from '@mui/x-data-grid-generator';

const getDetailPanelWidth = (gridDimensions: GridDimensions) => {
  return gridDimensions.viewportInnerSize.width;
};

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
  const apiRef = useGridApiContext;
  const [width, setWidth] = React.useState( =>
    getDetailPanelWidth(apiRef.current.getRootDimensions),
  );

  const handleViewportInnerSizeChange = React.useCallback( => {
    setWidth(getDetailPanelWidth(apiRef.current.getRootDimensions));
  }, [apiRef]);

  React.useEffect( => {
    return apiRef.current.subscribeEvent(
      'viewportInnerSizeChange',
      handleViewportInnerSizeChange,
    );
  }, [apiRef, handleViewportInnerSizeChange]);

  return (
    <Stack
      sx={{
        py: 2,
        height: '100%',
        boxSizing: 'border-box',
        position: 'sticky',
        left: 0,
        width,
      }}
      direction="column"
    >
      <Paper sx={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}>
        <Stack direction="column" spacing={1} sx={{ height: 1 }}>
          <Typography variant="h6">{`Order #${rowProp.id}`}</Typography>
          <DataGridPro
            density="compact"
            columns={[
              { field: 'name', headerName: 'Product', flex: 1 },
              {
                field: 'quantity',
                headerName: 'Quantity',
                align: 'center',
                type: 'number',
              },
              { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
              {
                field: 'total',
                headerName: 'Total',
                type: 'number',
                valueGetter: (value, row) => row.quantity * row.unitPrice,
              },
            ]}
            rows={rowProp.products}
            sx={{ flex: 1 }}
            hideFooter
          />
        </Stack>
      </Paper>
    </Stack>
  );
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'Order ID' },
  { field: 'customer', headerName: 'Customer', width: 200 },
  { field: 'email', headerName: 'Email' },
  { field: 'date', type: 'date', headerName: 'Placed at' },
  { field: 'currency', headerName: 'Currency' },
  { field: 'address', headerName: 'Address' },
  {
    field: 'city',
    headerName: 'City',
    valueGetter: (value, row) => `${row.city}, ${row.country.label}`,
  },
  {
    field: 'total',
    type: 'number',
    headerName: 'Total',
    valueGetter: (value, row) => {
      const subtotal = row.products.reduce(
        (acc: number, product: any) => product.unitPrice * product.quantity,
        0,
      );
      const taxes = subtotal * 0.05;
      return subtotal + taxes;
    },
  },
];

function generateProducts {
  const quantity = randomInt(1, 5);
  return [...Array(quantity)].map((_, index) => ({
    id: index,
    name: randomCommodity,
    quantity: randomInt(1, 5),
    unitPrice: randomPrice(1, 1000),
  }));
}

const rows = [
  {
    id: 1,
    customer: 'Matheus',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 2,
    customer: 'Olivier',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 3,
    customer: 'Flavien',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 4,
    customer: 'Danail',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 5,
    customer: 'Alexandre',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
  {
    id: 6,
    customer: 'José',
    email: randomEmail,
    date: randomCreatedDate,
    address: randomAddress,
    country: randomCountry,
    city: randomCity,
    currency: randomCurrency,
    products: generateProducts,
  },
];

type Customer = (typeof rows)[number];

export default function FullWidthDetailPanel {
  const getDetailPanelContent = React.useCallback(
    ({ row }: GridRowParams) => <DetailPanelContent row={row} />,
    [],
  );

  const getDetailPanelHeight = React.useCallback( => 400, []);

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <DataGridPro
        columns={columns}
        rows={rows}
        initialState={{
          pinnedColumns: {
            left: [GRID_DETAIL_PANEL_TOGGLE_FIELD],
          },
        }}
        getDetailPanelHeight={getDetailPanelHeight}
        getDetailPanelContent={getDetailPanelContent}
        sx={{
          '& .MuiDataGrid-detailPanel': {
            overflow: 'visible',
          },
        }}
      />
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import {
 DataGridPro,
 GridColDef,
 useGridApiContext,
 GridRowParams,
 GRID\_DETAIL\_PANEL\_TOGGLE\_FIELD,
 GridDimensions,
} from '@mui/x\-data\-grid\-pro';
import {
 randomCreatedDate,
 randomPrice,
 randomCurrency,
 randomCountry,
 randomCity,
 randomEmail,
 randomInt,
 randomAddress,
 randomCommodity,
} from '@mui/x\-data\-grid\-generator';

const getDetailPanelWidth \= (gridDimensions: GridDimensions) \=\> {
 return gridDimensions.viewportInnerSize.width;
};

function DetailPanelContent({ row: rowProp }: { row: Customer }) {
 const apiRef \= useGridApiContext;
 const \[width, setWidth] \= React.useState( \=\>
 getDetailPanelWidth(apiRef.current.getRootDimensions),
 );

 const handleViewportInnerSizeChange \= React.useCallback( \=\> {
 setWidth(getDetailPanelWidth(apiRef.current.getRootDimensions));
 }, \[apiRef]);

 React.useEffect( \=\> {
 return apiRef.current.subscribeEvent(
 'viewportInnerSizeChange',
 handleViewportInnerSizeChange,
 );
 }, \[apiRef, handleViewportInnerSizeChange]);

 return (
 \<Stack
 sx\={{
 py: 2,
 height: '100%',
 boxSizing: 'border\-box',
 position: 'sticky',
 left: 0,
 width,
 }}
 direction\="column"
 \>
 \<Paper sx\={{ flex: 1, mx: 'auto', width: '90%', p: 1 }}\>
 \<Stack direction\="column" spacing\={1} sx\={{ height: 1 }}\>
 \<Typography variant\="h6"\>{\`Order \#${rowProp.id}\`}\</Typography\>
 \<DataGridPro
 density\="compact"
 columns\={\[
 { field: 'name', headerName: 'Product', flex: 1 },
 {
 field: 'quantity',
 headerName: 'Quantity',
 align: 'center',
 type: 'number',
 },
 { field: 'unitPrice', headerName: 'Unit Price', type: 'number' },
 {
 field: 'total',
 headerName: 'Total',
 type: 'number',
 valueGetter: (value, row) \=\> row.quantity \* row.unitPrice,
 },
 ]}
 rows\={rowProp.products}
 sx\={{ flex: 1 }}
 hideFooter
 /\>
 \</Stack\>
 \</Paper\>
 \</Stack\>
 );
}

const columns: GridColDef\[] \= \[
 { field: 'id', headerName: 'Order ID' },
 { field: 'customer', headerName: 'Customer', width: 200 },
 { field: 'email', headerName: 'Email' },
 { field: 'date', type: 'date', headerName: 'Placed at' },
 { field: 'currency', headerName: 'Currency' },
 { field: 'address', headerName: 'Address' },
 {
 field: 'city',
 headerName: 'City',
 valueGetter: (value, row) \=\> \`${row.city}, ${row.country.label}\`,
 },
 {
 field: 'total',
 type: 'number',
 headerName: 'Total',
 valueGetter: (value, row) \=\> {
 const subtotal \= row.products.reduce(
 (acc: number, product: any) \=\> product.unitPrice \* product.quantity,
 0,
 );
 const taxes \= subtotal \* 0\.05;
 return subtotal \+ taxes;
 },
 },
];

function generateProducts {
 const quantity \= randomInt(1, 5\);
 return \[...Array(quantity)].map((\_, index) \=\> ({
 id: index,
 name: randomCommodity,
 quantity: randomInt(1, 5\),
 unitPrice: randomPrice(1, 1000\),
 }));
}

const rows \= \[
 {
 id: 1,
 customer: 'Matheus',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 2,
 customer: 'Olivier',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 3,
 customer: 'Flavien',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 4,
 customer: 'Danail',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 5,
 customer: 'Alexandre',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
 {
 id: 6,
 customer: 'José',
 email: randomEmail,
 date: randomCreatedDate,
 address: randomAddress,
 country: randomCountry,
 city: randomCity,
 currency: randomCurrency,
 products: generateProducts,
 },
];

type Customer \= (typeof rows)\[number];

export default function FullWidthDetailPanel {
 const getDetailPanelContent \= React.useCallback(
 ({ row }: GridRowParams) \=\> \<DetailPanelContent row\={row} /\>,
 \[],
 );

 const getDetailPanelHeight \= React.useCallback( \=\> 400, \[]);

 return (
 \<Box sx\={{ width: '100%', height: 400 }}\>
 \<DataGridPro
 columns\={columns}
 rows\={rows}
 initialState\={{
 pinnedColumns: {
 left: \[GRID\_DETAIL\_PANEL\_TOGGLE\_FIELD],
 },
 }}
 getDetailPanelHeight\={getDetailPanelHeight}
 getDetailPanelContent\={getDetailPanelContent}
 sx\={{
 '\& .MuiDataGrid\-detailPanel': {
 overflow: 'visible',
 },
 }}
 /\>
 \</Box\>
 );
}Press `Enter` to start editingRecipes
-------

More examples of how to customize the detail panel:


* One expanded detail panel at a time
* Expand or collapse all detail panels
* Toggling detail panels on row click


apiRef
------

The Data Grid exposes a set of methods via the `apiRef` object that are used internally in the implementation of the master detail feature.
The reference below describes the relevant functions.
See API object for more details.



This API should only be used as a last resort when the Data Grid's built\-in props aren't sufficient for your specific use case.


### getExpandedDetailPanelsReturns the rows whose detail panel is open.

###### Signature:

Copy(or Ctrl \+ C)
```
getExpandedDetailPanels:  => Set<GridRowId>
```
### setExpandedDetailPanelsChanges which rows to expand the detail panel.

###### Signature:

Copy(or Ctrl \+ C)
```
setExpandedDetailPanels: (ids: Set<GridRowId>) => void
```
### toggleDetailPanelExpands or collapses the detail panel of a row.

###### Signature:

Copy(or Ctrl \+ C)
```
toggleDetailPanel: (id: GridRowId) => void
```
API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Row spanningRow ordering

---

•

Blog•

Store
