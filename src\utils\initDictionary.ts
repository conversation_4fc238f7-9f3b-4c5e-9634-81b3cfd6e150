import { useSystemDictionaryStore } from '@/stores/systemDictionaryStore';

/**
 * Initialize the system dictionary cache
 * This function can be called when the application starts
 * to ensure the dictionary data is loaded
 */
export const initSystemDictionary = async (): Promise<void> => {
  const { fetchDictionary } = useSystemDictionaryStore.getState();
  
  try {
    await fetchDictionary();
    console.log('System dictionary initialized successfully');
  } catch (error) {
    console.error('Failed to initialize system dictionary:', error);
  }
};

export default initSystemDictionary;
