import type { ApiResponse } from '@/types/api';
import type { MenuItem } from '@/types';

// Mock menu data
const mockMenuData: MenuItem[] = [
  {
    id: '1',
    name: '模型设置',
    path: '/model',
    type: 'directory',
    children: [
      { id: '1-1', name: '文本模型', path: '/model/text', type: 'page' },
      { id: '1-2', name: '文->图模型', path: '/model/text-to-image', type: 'page' },
      { id: '1-3', name: '文->语音模型', path: '/model/text-to-speech', type: 'page' },
      { id: '1-4', name: '文->视频模型', path: '/model/text-to-video', type: 'page' },
      { id: '1-5', name: '图->文模型', path: '/model/image-to-text', type: 'page' },
      { id: '1-6', name: '语音->文模型', path: '/model/speech-to-text', type: 'page' },
      { id: '1-7', name: '视频->文模型', path: '/model/video-to-text', type: 'page' },
      { id: '1-8', name: '图像模型', path: '/model/image', type: 'page' },
      { id: '1-9', name: '召回模型', path: '/model/recall', type: 'page' },
      { id: '1-10', name: '向量模型', path: '/model/vector', type: 'page' },
      { id: '1-11', name: '专业模型', path: '/model/professional', type: 'page' },
      { id: '1-12', name: '共享设置', path: '/model/shared', type: 'page' },
      { id: '1-13', name: '模型市场', path: '/model/market', type: 'page' },
    ],
  },
  {
    id: '2',
    name: '数据管理',
    path: '/data',
    type: 'directory',
    children: [
      { id: '2-1', name: '向量数据库', path: '/data/vector', type: 'page' },
      { id: '2-2', name: 'Redis数据库', path: '/data/redis', type: 'page' },
      { id: '2-3', name: '图数据库', path: '/data/graph', type: 'page' },
      { id: '2-4', name: '关系数据库', path: '/data/relational', type: 'page' },
      { id: '2-5', name: '共享设置', path: '/data/shared', type: 'page' },
    ],
  },
  {
    id: '3',
    name: 'MCP管理',
    path: '/mcp',
    type: 'directory',
    children: [
      { id: '3-1', name: 'MCP列表', path: '/mcp/list', type: 'page' },
      { id: '3-2', name: 'MCP市场', path: '/mcp/market', type: 'page' },
      { id: '3-3', name: '共享设置', path: '/mcp/shared', type: 'page' },
    ],
  },
  {
    id: '4',
    name: '工具管理',
    path: '/tools',
    type: 'directory',
    children: [
      { id: '4-1', name: '工具列表', path: '/tools/list', type: 'page' },
      { id: '4-2', name: '工具市场', path: '/tools/market', type: 'page' },
      { id: '4-3', name: '共享设置', path: '/tools/shared', type: 'page' },
    ],
  },
  {
    id: '5',
    name: 'Flow管理',
    path: '/flow',
    type: 'directory',
    children: [
      { id: '5-1', name: 'langflow', path: '/flow/langflow', type: 'page' },
      { id: '5-2', name: 'flowise', path: '/flow/flowise', type: 'page' },
      { id: '5-3', name: 'make', path: '/flow/make', type: 'page' },
      { id: '5-4', name: 'coze', path: '/flow/coze', type: 'page' },
      { id: '5-5', name: 'dify', path: '/flow/dify', type: 'page' },
      { id: '5-6', name: 'fastgpt', path: '/flow/fastgpt', type: 'page' },
      { id: '5-7', name: 'autogen', path: '/flow/autogen', type: 'page' },
      { id: '5-8', name: 'adk', path: '/flow/adk', type: 'page' },
      { id: '5-9', name: 'ango', path: '/flow/ango', type: 'page' },
    ],
  },
  {
    id: '6',
    name: 'RAG管理',
    path: '/rag',
    type: 'directory',
    children: [
      { id: '6-1', name: '知识库', path: '/rag/knowledge', type: 'page' },
      { id: '6-2', name: '文档库', path: '/rag/document', type: 'page' },
      { id: '6-3', name: '知识图谱', path: '/rag/knowledge-graph', type: 'page' },
      { id: '6-4', name: '共享设置', path: '/rag/shared', type: 'page' },
    ],
  },
  {
    id: '7',
    name: 'Agent管理',
    path: '/agent',
    type: 'directory',
    children: [
      { id: '7-1', name: 'Agent团队', path: '/agent/team', type: 'page' },
      { id: '7-2', name: 'Agent列表', path: '/agent/list', type: 'page' },
      { id: '7-3', name: '中止函数', path: '/agent/abort', type: 'page' },
      { id: '7-4', name: 'Agent市场', path: '/agent/market', type: 'page' },
      { id: '7-5', name: '共享设置', path: '/agent/shared', type: 'page' },
    ],
  },
  {
    id: '8',
    name: '设备管理',
    path: '/device',
    type: 'directory',
    children: [
      { id: '8-1', name: '设备列表', path: '/device/list', type: 'page' },
      { id: '8-2', name: '共享设置', path: '/device/shared', type: 'page' },
    ],
  },
  {
    id: '9',
    name: '个人中心',
    path: '/profile',
    type: 'directory',
    children: [
      { id: '9-1', name: '个人中心', path: '/profile/center', type: 'page' },
      { id: '9-2', name: '积分兑换', path: '/profile/points', type: 'page' },
      { id: '9-3', name: '数据分析', path: '/profile/analytics', type: 'page' },
      { id: '9-4', name: '接入文档', path: '/profile/docs', type: 'page' },
    ],
  },
];

// Mock getMenu function
export const getMenu = async (): Promise<ApiResponse<MenuItem[]>> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '0',
        data: mockMenuData,
        msg: 'Menu data retrieved successfully',
      });
    }, 500); // Simulate network delay
  });
};
