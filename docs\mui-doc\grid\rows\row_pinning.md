Data Grid \- Row pinning
========================

Pin rows to keep them visible at all times.


Pinned (or frozen, locked, or floating) rows are those visible at all times while the user scrolls the Data Grid vertically.


You can pin rows at the top or bottom of the Data Grid by passing pinned rows data through the `pinnedRows` prop:



```
const pinnedRows: GridPinnedRowsProp = {
  top: [{ id: 0, brand: 'Nike' }],
  bottom: [
    { id: 1, brand: 'Adidas' },
    { id: 2, brand: 'Puma' },
  ],
};

<DataGridPro pinnedRows={pinnedRows} />;

```
CopyCopied(or Ctrl \+ C)
The data format for pinned rows is the same as for the `rows` prop (see Feeding data).


Pinned rows data should also meet Row identifier requirements.


NameCityUsernameEmailAgeLuis <PERSON>@beggewigevur@soutomel.gf50Devin McLaughlin<PERSON>@vicoreziwkiej@ehcu.ag53Gary SandovalFalujida@bakefika@gimon.fm14J<PERSON>@kucsad@fin.ky77<PERSON><PERSON><PERSON>@amabepja<PERSON><PERSON>@omowo.im50<PERSON><PERSON><PERSON>@gibvuato@di.je46Jeffrey <PERSON>Tothokcis@sikawezam@najez.mh31Barry SuttonRibeznah@ojepwejov@irievcuc.lu64Brett McLaughlinIhkaupa@fucojledzak@so.lb12Rosetta BerryZuwwubom@guzja@pero.nf51Eugene GarciaNoimilij@jukesaju@daw.uz65Total Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro, GridPinnedRowsProp, GridColDef } from '@mui/x-data-grid-pro';
import {
  randomCity,
  randomEmail,
  randomId,
  randomInt,
  randomTraderName,
  randomUserName,
} from '@mui/x-data-grid-generator';

const columns: GridColDef[] = [
  { field: 'name', headerName: 'Name', width: 150 },
  { field: 'city', headerName: 'City', width: 150 },
  { field: 'username', headerName: 'Username' },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'age', type: 'number', headerName: 'Age' },
];

const rows: object[] = [];

function getRow {
  return {
    id: randomId,
    name: randomTraderName,
    city: randomCity,
    username: randomUserName,
    email: randomEmail,
    age: randomInt(10, 80),
  };
}

for (let i = 0; i < 10; i += 1) {
  rows.push(getRow);
}

const pinnedRows: GridPinnedRowsProp = {
  top: [getRow, getRow],
  bottom: [getRow],
};

export default function RowPinning {
  return (
    <div style={{ height: 500, width: '100%' }}>
      <DataGridPro columns={columns} rows={rows} pinnedRows={pinnedRows} />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro, GridPinnedRowsProp, GridColDef } from '@mui/x\-data\-grid\-pro';
import {
 randomCity,
 randomEmail,
 randomId,
 randomInt,
 randomTraderName,
 randomUserName,
} from '@mui/x\-data\-grid\-generator';

const columns: GridColDef\[] \= \[
 { field: 'name', headerName: 'Name', width: 150 },
 { field: 'city', headerName: 'City', width: 150 },
 { field: 'username', headerName: 'Username' },
 { field: 'email', headerName: 'Email', width: 200 },
 { field: 'age', type: 'number', headerName: 'Age' },
];

const rows: object\[] \= \[];

function getRow {
 return {
 id: randomId,
 name: randomTraderName,
 city: randomCity,
 username: randomUserName,
 email: randomEmail,
 age: randomInt(10, 80\),
 };
}

for (let i \= 0; i \< 10; i \+\= 1\) {
 rows.push(getRow);
}

const pinnedRows: GridPinnedRowsProp \= {
 top: \[getRow, getRow],
 bottom: \[getRow],
};

export default function RowPinning {
 return (
 \<div style\={{ height: 500, width: '100%' }}\>
 \<DataGridPro columns\={columns} rows\={rows} pinnedRows\={pinnedRows} /\>
 \</div\>
 );
}Press `Enter` to start editing
Just like the `rows` prop, `pinnedRows` prop should keep the same reference between two renders.
Otherwise, the Data Grid will re\-apply heavy work like sorting and filtering.


Controlling pinned rows
-----------------------

You can control which rows are pinned by changing `pinnedRows`.


In the demo below we use `actions` column type to add buttons to pin a row either at the top or bottom and change `pinnedRows` prop dynamically.


NameCityUsernameEmailBrett HarmonOhlezju@<EMAIL> SwansonGukwumin@<EMAIL> BrooksHinzewpe@<EMAIL> NunezBafmefom@<EMAIL> RobinsonReoroek@<EMAIL> ByrdHanceeh@<EMAIL> PhelpsRasadgow@<EMAIL> NewtonTekjehoz@<EMAIL> ReevesCupitiagi@<EMAIL> CampbellRihcezulo@<EMAIL> LambEdsopwus@<EMAIL> Rows: 20JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGridPro,
  GridRowModel,
  GridActionsCellItem,
  GridColDef,
  GridRowId,
} from '@mui/x-data-grid-pro';
import ArrowUpIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownIcon from '@mui/icons-material/ArrowDownward';
import Tooltip from '@mui/material/Tooltip';
import {
  randomId,
  randomTraderName,
  randomCity,
  randomUserName,
  randomEmail,
} from '@mui/x-data-grid-generator';

const data: GridRowModel[] = [];

function getRow {
  return {
    id: randomId,
    name: randomTraderName,
    city: randomCity,
    username: randomUserName,
    email: randomEmail,
  };
}

for (let i = 0; i < 20; i += 1) {
  data.push(getRow);
}

export default function RowPinningWithActions {
  const [pinnedRowsIds, setPinnedRowsIds] = React.useState<{
    top: GridRowId[];
    bottom: GridRowId[];
  }>({
    top: [],
    bottom: [],
  });

  const { rows, pinnedRows } = React.useMemo( => {
    const rowsData: GridRowModel[] = [];
    const pinnedRowsData: { top: GridRowModel[]; bottom: GridRowModel[] } = {
      top: [],
      bottom: [],
    };

    data.forEach((row) => {
      if (pinnedRowsIds.top.includes(row.id)) {
        pinnedRowsData.top.push(row);
      } else if (pinnedRowsIds.bottom.includes(row.id)) {
        pinnedRowsData.bottom.push(row);
      } else {
        rowsData.push(row);
      }
    });

    return {
      rows: rowsData,
      pinnedRows: pinnedRowsData,
    };
  }, [pinnedRowsIds]);

  const columns = React.useMemo<GridColDef<(typeof data)[number]>[]>(
     => [
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        getActions: (params) => {
          const isPinnedTop = pinnedRowsIds.top.includes(params.id);
          const isPinnedBottom = pinnedRowsIds.bottom.includes(params.id);
          if (isPinnedTop || isPinnedBottom) {
            return [
              <GridActionsCellItem
                label="Unpin"
                icon={
                  <Tooltip title="Unpin">
                    {isPinnedTop ? <ArrowDownIcon /> : <ArrowUpIcon />}
                  </Tooltip>
                }
                onClick={ =>
                  setPinnedRowsIds((prevPinnedRowsIds) => ({
                    top: prevPinnedRowsIds.top.filter(
                      (rowId) => rowId !== params.id,
                    ),
                    bottom: prevPinnedRowsIds.bottom.filter(
                      (rowId) => rowId !== params.id,
                    ),
                  }))
                }
              />,
            ];
          }
          return [
            <GridActionsCellItem
              icon={
                <Tooltip title="Pin at the top">
                  <ArrowUpIcon />
                </Tooltip>
              }
              label="Pin at the top"
              onClick={ =>
                setPinnedRowsIds((prevPinnedRowsIds) => ({
                  ...prevPinnedRowsIds,
                  top: [...prevPinnedRowsIds.top, params.id],
                }))
              }
            />,
            <GridActionsCellItem
              icon={
                <Tooltip title="Pin at the bottom">
                  <ArrowDownIcon />
                </Tooltip>
              }
              label="Pin at the bottom"
              onClick={ =>
                setPinnedRowsIds((prevPinnedRowsIds) => ({
                  ...prevPinnedRowsIds,
                  bottom: [...prevPinnedRowsIds.bottom, params.id],
                }))
              }
            />,
          ];
        },
      },
      { field: 'name', headerName: 'Name', width: 150 },
      { field: 'city', headerName: 'City', width: 150 },
      { field: 'username', headerName: 'Username' },
      { field: 'email', headerName: 'Email', width: 200 },
    ],
    [pinnedRowsIds],
  );

  return (
    <div style={{ height: 500, width: '100%' }}>
      <DataGridPro columns={columns} pinnedRows={pinnedRows} rows={rows} />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGridPro,
 GridRowModel,
 GridActionsCellItem,
 GridColDef,
 GridRowId,
} from '@mui/x\-data\-grid\-pro';
import ArrowUpIcon from '@mui/icons\-material/ArrowUpward';
import ArrowDownIcon from '@mui/icons\-material/ArrowDownward';
import Tooltip from '@mui/material/Tooltip';
import {
 randomId,
 randomTraderName,
 randomCity,
 randomUserName,
 randomEmail,
} from '@mui/x\-data\-grid\-generator';

const data: GridRowModel\[] \= \[];

function getRow {
 return {
 id: randomId,
 name: randomTraderName,
 city: randomCity,
 username: randomUserName,
 email: randomEmail,
 };
}

for (let i \= 0; i \< 20; i \+\= 1\) {
 data.push(getRow);
}

export default function RowPinningWithActions {
 const \[pinnedRowsIds, setPinnedRowsIds] \= React.useState\<{
 top: GridRowId\[];
 bottom: GridRowId\[];
 }\>({
 top: \[],
 bottom: \[],
 });

 const { rows, pinnedRows } \= React.useMemo( \=\> {
 const rowsData: GridRowModel\[] \= \[];
 const pinnedRowsData: { top: GridRowModel\[]; bottom: GridRowModel\[] } \= {
 top: \[],
 bottom: \[],
 };

 data.forEach((row) \=\> {
 if (pinnedRowsIds.top.includes(row.id)) {
 pinnedRowsData.top.push(row);
 } else if (pinnedRowsIds.bottom.includes(row.id)) {
 pinnedRowsData.bottom.push(row);
 } else {
 rowsData.push(row);
 }
 });

 return {
 rows: rowsData,
 pinnedRows: pinnedRowsData,
 };
 }, \[pinnedRowsIds]);

 const columns \= React.useMemo\<GridColDef\<(typeof data)\[number]\>\[]\>(
  \=\> \[
 {
 field: 'actions',
 type: 'actions',
 width: 100,
 getActions: (params) \=\> {
 const isPinnedTop \= pinnedRowsIds.top.includes(params.id);
 const isPinnedBottom \= pinnedRowsIds.bottom.includes(params.id);
 if (isPinnedTop \|\| isPinnedBottom) {
 return \[
 \<GridActionsCellItem
 label\="Unpin"
 icon\={
 \<Tooltip title\="Unpin"\>
 {isPinnedTop ? \<ArrowDownIcon /\> : \<ArrowUpIcon /\>}
 \</Tooltip\>
 }
 onClick\={ \=\>
 setPinnedRowsIds((prevPinnedRowsIds) \=\> ({
 top: prevPinnedRowsIds.top.filter(
 (rowId) \=\> rowId !\=\= params.id,
 ),
 bottom: prevPinnedRowsIds.bottom.filter(
 (rowId) \=\> rowId !\=\= params.id,
 ),
 }))
 }
 /\>,
 ];
 }
 return \[
 \<GridActionsCellItem
 icon\={
 \<Tooltip title\="Pin at the top"\>
 \<ArrowUpIcon /\>
 \</Tooltip\>
 }
 label\="Pin at the top"
 onClick\={ \=\>
 setPinnedRowsIds((prevPinnedRowsIds) \=\> ({
 ...prevPinnedRowsIds,
 top: \[...prevPinnedRowsIds.top, params.id],
 }))
 }
 /\>,
 \<GridActionsCellItem
 icon\={
 \<Tooltip title\="Pin at the bottom"\>
 \<ArrowDownIcon /\>
 \</Tooltip\>
 }
 label\="Pin at the bottom"
 onClick\={ \=\>
 setPinnedRowsIds((prevPinnedRowsIds) \=\> ({
 ...prevPinnedRowsIds,
 bottom: \[...prevPinnedRowsIds.bottom, params.id],
 }))
 }
 /\>,
 ];
 },
 },
 { field: 'name', headerName: 'Name', width: 150 },
 { field: 'city', headerName: 'City', width: 150 },
 { field: 'username', headerName: 'Username' },
 { field: 'email', headerName: 'Email', width: 200 },
 ],
 \[pinnedRowsIds],
 );

 return (
 \<div style\={{ height: 500, width: '100%' }}\>
 \<DataGridPro columns\={columns} pinnedRows\={pinnedRows} rows\={rows} /\>
 \</div\>
 );
}Press `Enter` to start editingUsage with other features
-------------------------

Pinned rows are not affected by sorting and filtering.


Pagination does not impact pinned rows as well \- they stay pinned regardless the page number or page size.


DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledStatusD\-4595WheatLida Frazierkadazol@cekice.kg87,88976\.18 %FilledD\-4492<NAME_EMAIL>6,18674\.717 %Partially FilledD\-7361<NAME_EMAIL>10,45925\.672 %FilledD\-9257MilkDella Nunezgakletkap@cofovcuf.nu77,64797\.54 %OpenD\-2613CocoaNancy Berryfido@gevekewu.so56,85461\.03 %FilledD\-4794<NAME_EMAIL>85,91790\.019 %Partially FilledD\-5997<NAME_EMAIL>47,59933\.841 %OpenD\-2866MilkRoger Payneajdamvu@ad.mg22,99576\.038 %Partially FilledD\-306<NAME_EMAIL>53,25924\.49 %FilledD\-8931<NAME_EMAIL>52,66127\.709 %Partially FilledD\-799SoybeansVera Butlerleriles@omam.ch85,01870\.58 %RejectedRows per page:

251–25 of 97

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function RowPinningWithPagination {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 100,
    maxColumns: 20,
    editable: true,
  });

  const rowsData = React.useMemo( => {
    if (!data.rows || data.rows.length === 0) {
      return { rows: data.rows };
    }
    const [firstRow, secondRow, thirdRow, ...rows] = data.rows;
    return {
      rows,
      pinnedRows: {
        top: [firstRow],
        bottom: [secondRow, thirdRow],
      },
    };
  }, [data.rows]);

  return (
    <div style={{ height: 500, width: '100%' }}>
      <DataGridPro
        {...data}
        loading={loading}
        rows={rowsData.rows}
        pinnedRows={rowsData.pinnedRows}
        initialState={{
          ...data.initialState,
          pagination: {
            ...data.initialState?.pagination,
            paginationModel: { pageSize: 25 },
          },
        }}
        pagination
        pageSizeOptions={[5, 10, 25, 50, 100]}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPro } from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function RowPinningWithPagination {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 100,
 maxColumns: 20,
 editable: true,
 });

 const rowsData \= React.useMemo( \=\> {
 if (!data.rows \|\| data.rows.length \=\=\= 0\) {
 return { rows: data.rows };
 }
 const \[firstRow, secondRow, thirdRow, ...rows] \= data.rows;
 return {
 rows,
 pinnedRows: {
 top: \[firstRow],
 bottom: \[secondRow, thirdRow],
 },
 };
 }, \[data.rows]);

 return (
 \<div style\={{ height: 500, width: '100%' }}\>
 \<DataGridPro
 {...data}
 loading\={loading}
 rows\={rowsData.rows}
 pinnedRows\={rowsData.pinnedRows}
 initialState\={{
 ...data.initialState,
 pagination: {
 ...data.initialState?.pagination,
 paginationModel: { pageSize: 25 },
 },
 }}
 pagination
 pageSizeOptions\={\[5, 10, 25, 50, 100]}
 /\>
 \</div\>
 );
}Press `Enter` to start editing
Pinned rows do not support the following features:


* selection
* row grouping
* tree data
* row reordering
* master detail


but you can still use these features with the rows that aren't pinned.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Row orderingRecipes

---

•

Blog•

Store
