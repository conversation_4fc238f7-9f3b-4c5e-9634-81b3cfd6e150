
Box
===

The Box component is a generic, theme\-aware container with access to CSS utilities from MUI System.


Add high\-performance JavaScript data grids to your app in minutes with AG Grid. Try for free today.

ads via Carbon



* Feedback
* Bundle size
* Source

Introduction
------------

The Box component is a generic container for grouping other components.
It's a fundamental building block when working with Material UI—you can think of it as a `<div>` with extra built\-in features, like access to your app's theme and the `sx` prop.


### Usage

The Box component differs from other containers available in Material UI in that its usage is intended to be multipurpose and open\-ended, just like a `<div>`.
Components like Container, Stack and Paper, by contrast, feature usage\-specific props that make them ideal for certain use cases: Container for main layout orientation, Stack for one\-dimensional layouts, and Paper for elevated surfaces.


Basics
------


```
import Box from '@mui/material/Box';

```
CopyCopied(or Ctrl \+ C)
The Box component renders as a `<div>` by default, but you can swap in any other valid HTML tag or React component using the `component` prop.
The demo below replaces the `<div>` with a `<section>` element:


This Box renders as an HTML section element.JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';

export default function BoxBasic {
  return (
    <Box component="section" sx={{ p: 2, border: '1px dashed grey' }}>
      This Box renders as an HTML section element.
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';

export default function BoxBasic {
 return (
 \<Box component\="section" sx\={{ p: 2, border: '1px dashed grey' }}\>
 This Box renders as an HTML section element.
 \</Box\>
 );
}Press `Enter` to start editingCustomization
-------------

### With the sx prop

Use the `sx` prop to quickly customize any Box instance using a superset of CSS that has access to all the style functions and theme\-aware properties exposed in the MUI System package.
The demo below shows how to apply colors from the theme using this prop:


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import { ThemeProvider } from '@mui/material/styles';

export default function BoxSx {
  return (
    <ThemeProvider
      theme={{
        palette: {
          primary: {
            main: '#007FFF',
            dark: '#0066CC',
          },
        },
      }}
    >
      <Box
        sx={{
          width: 100,
          height: 100,
          borderRadius: 1,
          bgcolor: 'primary.main',
          '&:hover': {
            bgcolor: 'primary.dark',
          },
        }}
      />
    </ThemeProvider>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import { ThemeProvider } from '@mui/material/styles';

export default function BoxSx {
 return (
 \<ThemeProvider
 theme\={{
 palette: {
 primary: {
 main: '\#007FFF',
 dark: '\#0066CC',
 },
 },
 }}
 \>
 \<Box
 sx\={{
 width: 100,
 height: 100,
 borderRadius: 1,
 bgcolor: 'primary.main',
 '\&:hover': {
 bgcolor: 'primary.dark',
 },
 }}
 /\>
 \</ThemeProvider\>
 );
}Press `Enter` to start editing### With MUI System props


System props are deprecated and will be removed in the next major release. Please use the `sx` prop instead.



```
- <Box mt={2} />
+ <Box sx={{ mt: 2 }} />

```
CopyCopied(or Ctrl \+ C)
Anatomy
-------

The Box component is composed of a single root `<div>` element:



```
<div className="MuiBox-root">
  <!-- contents of the Box -->
</div>

```
CopyCopied(or Ctrl \+ C)
API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Box />`



