import type { ApiResponse } from '@/types/api';
import type { Model, ModelFormData, ModelStatusUpdateData } from '@/types/model';
import moment from 'moment';

// Mock model data
const mockModels: Model[] = [
  {
    id: '1',
    name: 'GPT-4 Turbo',
    description: '最新的GPT-4模型，支持更长的上下文和更快的响应速度',
    type: 'gpt4',
    status: 'online',
    createdAt: '2023-11-15T08:00:00Z',
    updatedAt: '2023-12-20T10:30:00Z',
    supportsFunctionCalling: true,
  },
  {
    id: '2',
    name: 'Claude 3 Opus',
    description: 'Anthropic的最新大型语言模型，具有强大的推理能力',
    type: 'claude3',
    status: 'online',
    createdAt: '2024-03-04T09:15:00Z',
    updatedAt: '2024-03-04T09:15:00Z',
    supportsFunctionCalling: true,
  },
  {
    id: '3',
    name: 'GPT-3.5 Turbo',
    description: '性能良好的语言模型，适合一般任务',
    type: 'gpt35',
    status: 'online',
    createdAt: '2022-11-30T14:20:00Z',
    updatedAt: '2023-09-25T11:45:00Z',
    supportsFunctionCalling: true,
  },
  {
    id: '4',
    name: 'Llama 3 70B',
    description: 'Meta的开源大型语言模型，参数量700亿',
    type: 'llama3',
    status: 'maintenance',
    createdAt: '2024-04-18T16:30:00Z',
    updatedAt: '2024-04-18T16:30:00Z',
    supportsFunctionCalling: false,
  },
  {
    id: '5',
    name: 'Gemini Pro',
    description: 'Google的多模态AI模型',
    type: 'gemini',
    status: 'offline',
    createdAt: '2023-12-13T10:00:00Z',
    updatedAt: '2024-02-15T08:20:00Z',
    supportsFunctionCalling: true,
  },
];

// Get all text models
export const getTextModels = async (): Promise<ApiResponse<Model[]>> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '0',
        data: mockModels,
        msg: 'Text models retrieved successfully',
      });
    }, 500); // Simulate network delay
  });
};

// Get a specific text model by ID
export const getTextModelById = async (id: string): Promise<ApiResponse<Model>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const model = mockModels.find((m) => m.id === id);
      
      if (model) {
        resolve({
          code: '0',
          data: model,
          msg: 'Text model retrieved successfully',
        });
      } else {
        resolve({
          code: '7', // Data not found
          data: {} as Model,
          msg: 'Text model not found',
        });
      }
    }, 300);
  });
};

// Create a new text model
export const createTextModel = async (data: ModelFormData): Promise<ApiResponse<Model>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Generate a new model with the provided data
      const newModel: Model = {
        id: `${mockModels.length + 1}`,
        name: data.name,
        description: data.description,
        type: data.type,
        status: data.status,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        supportsFunctionCalling: data.supportsFunctionCalling,
      };
      
      // In a real application, this would add to the database
      // For mock purposes, we're just returning the new model
      resolve({
        code: '0',
        data: newModel,
        msg: 'Text model created successfully',
      });
    }, 700);
  });
};

// Update an existing text model
export const updateTextModel = async (id: string, data: ModelFormData): Promise<ApiResponse<Model>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const modelIndex = mockModels.findIndex((m) => m.id === id);
      
      if (modelIndex !== -1) {
        // Update the model
        const updatedModel: Model = {
          ...mockModels[modelIndex],
          name: data.name,
          description: data.description,
          type: data.type,
          status: data.status,
          updatedAt: moment().toISOString(),
          supportsFunctionCalling: data.supportsFunctionCalling,
        };
        
        // In a real application, this would update the database
        resolve({
          code: '0',
          data: updatedModel,
          msg: 'Text model updated successfully',
        });
      } else {
        resolve({
          code: '7', // Data not found
          data: {} as Model,
          msg: 'Text model not found',
        });
      }
    }, 500);
  });
};

// Delete a text model
export const deleteTextModel = async (id: string): Promise<ApiResponse<null>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const modelIndex = mockModels.findIndex((m) => m.id === id);
      
      if (modelIndex !== -1) {
        // In a real application, this would delete from the database
        resolve({
          code: '0',
          data: null,
          msg: 'Text model deleted successfully',
        });
      } else {
        resolve({
          code: '7', // Data not found
          data: null,
          msg: 'Text model not found',
        });
      }
    }, 400);
  });
};

// Update model status
export const updateTextModelStatus = async (data: ModelStatusUpdateData): Promise<ApiResponse<Model>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const modelIndex = mockModels.findIndex((m) => m.id === data.id);
      
      if (modelIndex !== -1) {
        // Update the model status
        const updatedModel: Model = {
          ...mockModels[modelIndex],
          status: data.status,
          updatedAt: moment().toISOString(),
        };
        
        // In a real application, this would update the database
        resolve({
          code: '0',
          data: updatedModel,
          msg: 'Text model status updated successfully',
        });
      } else {
        resolve({
          code: '7', // Data not found
          data: {} as Model,
          msg: 'Text model not found',
        });
      }
    }, 300);
  });
};

// Deploy a text model
export const deployTextModel = async (id: string): Promise<ApiResponse<null>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const modelIndex = mockModels.findIndex((m) => m.id === id);
      
      if (modelIndex !== -1) {
        // In a real application, this would trigger a deployment process
        resolve({
          code: '0',
          data: null,
          msg: 'Text model deployment initiated successfully',
        });
      } else {
        resolve({
          code: '7', // Data not found
          data: null,
          msg: 'Text model not found',
        });
      }
    }, 800);
  });
};
