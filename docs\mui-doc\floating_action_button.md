Floating Action Button
======================

A Floating Action Button (FAB) performs the primary, or most common, action on a screen.


Reinvent DevOps by turning Playwright tests into synthetic monitors, starting at $0/m.

ads via Carbon



A floating action button appears in front of all screen content, typically as a circular shape with an icon in its center.
FABs come in two types: regular, and extended.


Only use a FAB if it is the most suitable way to present a screen's primary action.
Only one component is recommended per screen to represent the most common action.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Basic FAB
---------

NavigateJSTSExpand codeCopy(or Ctrl \+ C)
```
<Fab color="primary" aria-label="add">
  <AddIcon />
</Fab>
<Fab color="secondary" aria-label="edit">
  <EditIcon />
</Fab>
<Fab variant="extended">
  <NavigationIcon sx={{ mr: 1 }} />
  Navigate
</Fab>
<Fab disabled aria-label="like">
  <FavoriteIcon />
</Fab>  

```
\<Fab color\="primary" aria\-label\="add"\>
 \<AddIcon /\>
\</Fab\>
\<Fab color\="secondary" aria\-label\="edit"\>
 \<EditIcon /\>
\</Fab\>
\<Fab variant\="extended"\>
 \<NavigationIcon sx\={{ mr: 1 }} /\>
 Navigate
\</Fab\>
\<Fab disabled aria\-label\="like"\>
 \<FavoriteIcon /\>
\</Fab\>Press `Enter` to start editingSize
----

By default, the size is `large`. Use the `size` prop for smaller floating action buttons.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Fab size="small" color="secondary" aria-label="add">
  <AddIcon />
</Fab>
<Fab size="medium" color="secondary" aria-label="add">
  <AddIcon />
</Fab>
<Fab color="secondary" aria-label="add">
  <AddIcon />
</Fab>  

```
\<Fab size\="small" color\="secondary" aria\-label\="add"\>
 \<AddIcon /\>
\</Fab\>
\<Fab size\="medium" color\="secondary" aria\-label\="add"\>
 \<AddIcon /\>
\</Fab\>
\<Fab color\="secondary" aria\-label\="add"\>
 \<AddIcon /\>
\</Fab\>Press `Enter` to start editingExtendedExtendedExtendedJSTSExpand codeCopy(or Ctrl \+ C)
```
<Fab variant="extended" size="small" color="primary">
  <NavigationIcon sx={{ mr: 1 }} />
  Extended
</Fab>
<Fab variant="extended" size="medium" color="primary">
  <NavigationIcon sx={{ mr: 1 }} />
  Extended
</Fab>
<Fab variant="extended" color="primary">
  <NavigationIcon sx={{ mr: 1 }} />
  Extended
</Fab>  

```
\<Fab variant\="extended" size\="small" color\="primary"\>
 \<NavigationIcon sx\={{ mr: 1 }} /\>
 Extended
\</Fab\>
\<Fab variant\="extended" size\="medium" color\="primary"\>
 \<NavigationIcon sx\={{ mr: 1 }} /\>
 Extended
\</Fab\>
\<Fab variant\="extended" color\="primary"\>
 \<NavigationIcon sx\={{ mr: 1 }} /\>
 Extended
\</Fab\>Press `Enter` to start editingAnimation
---------

The floating action button animates onto the screen as an expanding piece of material, by default.


A floating action button that spans multiple lateral screens (such as tabbed screens) should briefly disappear,
then reappear if its action changes.


The Zoom transition can be used to achieve this. Note that since both the exiting and entering
animations are triggered at the same time, we use `enterDelay` to allow the outgoing Floating Action Button's
animation to finish before the new one enters.


Item OneItem TwoItem ThreeItem OneJSTSShow codeAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Fab />`



