Data Grid \- Row ordering
=========================

Drag and drop your rows to reorder them.


Row reordering lets users rearrange rows by dragging the special reordering cell.


By default, row reordering is disabled.
To enable it, you need to add the `rowReordering` prop.



```
<DataGridPro rowReordering />

```
CopyCopied(or Ctrl \+ C)
 DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledStatus8de574de\-f50f\-5a10\-bba2\-c0c6f4f6f7a1D\-1465Soybean OilAnn <PERSON>li@du.za58,7984\.781 %Rejected7f755eab\-6d5b\-5d5e\-a699\-9f44f7f7eed1D\-5280CornEarl Ortegaenuki@pamvawo.mt14,7998\.717 %Rejected58dd87f6\-176a\-5d57\-8682\-4eae9b44fc44D\-8006Sugar No.11L<PERSON><PERSON>@ru.ac13,49929\.528 %Rejected425d4db4\-8472\-5bed\-9f03\-0fa74c561977D\-3505Frozen Concentrated <NAME_EMAIL>25,51685\.848 %Filled53e2bf31\-a47b\-597f\-a66a\-c10f02729edeD\-5549<NAME_EMAIL>84,35455\.729 %Partially Filled583a37b3\-17f3\-5fa5\-af56\-bcf143c66822D\-9620CornVernon Kellywipki@huj.mh78,7891\.95 %Rejected88f47ce8\-aaa5\-5528\-bda7\-b5b9506bceb2D\-3685<NAME_EMAIL>4,5690\.482 %Openf96e28b9\-927f\-50d7\-9d39\-158cf6809e92D\-7621SoybeansMaggie Gutierrezgomuiha@ecacocos.za5,79178\.259 %Partially Filledd4acef26\-7de9\-5f7c\-a105\-84eb31f13a47D\-9578<NAME_EMAIL>6,98088\.596 %FilledTotal Rows: 20JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGridPro,
  GridRowModel,
  GridRowOrderChangeParams,
} from '@mui/x-data-grid-pro';
import { useDemoData } from '@mui/x-data-grid-generator';

function updateRowPosition(
  initialIndex: number,
  newIndex: number,
  rows: Array<GridRowModel>,
): Promise<any> {
  return new Promise((resolve) => {
    setTimeout(
       => {
        const rowsClone = [...rows];
        const row = rowsClone.splice(initialIndex, 1)[0];
        rowsClone.splice(newIndex, 0, row);
        resolve(rowsClone);
      },
      Math.random * 500 + 100,
    ); // simulate network latency
  });
}

export default function RowOrderingGrid {
  const { data, loading: initialLoadingState } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 20,
  });

  const [rows, setRows] = React.useState(data.rows);
  const [loading, setLoading] = React.useState(initialLoadingState);

  React.useEffect( => {
    setRows(data.rows);
  }, [data]);

  React.useEffect( => {
    setLoading(initialLoadingState);
  }, [initialLoadingState]);

  const handleRowOrderChange = async (params: GridRowOrderChangeParams) => {
    setLoading(true);
    const newRows = await updateRowPosition(
      params.oldIndex,
      params.targetIndex,
      rows,
    );

    setRows(newRows);
    setLoading(false);
  };

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPro
        {...data}
        loading={loading}
        rows={rows}
        rowReordering
        onRowOrderChange={handleRowOrderChange}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGridPro,
 GridRowModel,
 GridRowOrderChangeParams,
} from '@mui/x\-data\-grid\-pro';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

function updateRowPosition(
 initialIndex: number,
 newIndex: number,
 rows: Array\<GridRowModel\>,
): Promise\<any\> {
 return new Promise((resolve) \=\> {
 setTimeout(
  \=\> {
 const rowsClone \= \[...rows];
 const row \= rowsClone.splice(initialIndex, 1\)\[0];
 rowsClone.splice(newIndex, 0, row);
 resolve(rowsClone);
 },
 Math.random \* 500 \+ 100,
 ); // simulate network latency
 });
}

export default function RowOrderingGrid {
 const { data, loading: initialLoadingState } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 20,
 });

 const \[rows, setRows] \= React.useState(data.rows);
 const \[loading, setLoading] \= React.useState(initialLoadingState);

 React.useEffect( \=\> {
 setRows(data.rows);
 }, \[data]);

 React.useEffect( \=\> {
 setLoading(initialLoadingState);
 }, \[initialLoadingState]);

 const handleRowOrderChange \= async (params: GridRowOrderChangeParams) \=\> {
 setLoading(true);
 const newRows \= await updateRowPosition(
 params.oldIndex,
 params.targetIndex,
 rows,
 );

 setRows(newRows);
 setLoading(false);
 };

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPro
 {...data}
 loading\={loading}
 rows\={rows}
 rowReordering
 onRowOrderChange\={handleRowOrderChange}
 /\>
 \</div\>
 );
}Press `Enter` to start editingTo capture changes in the order of the dragged row, you can pass a callback to the `onRowOrderChange` prop. This callback is called with a `GridRowOrderChangeParams` object.


In addition, you can import the following events to customize the row reordering experience:


* `rowDragStart`: emitted when dragging of a row starts.
* `rowDragOver`: emitted when dragging a row over another row.
* `rowDragEnd`: emitted when dragging of a row stops.


Customizing the reorder value
-----------------------------

By default, when you start dragging a row, the `id` is displayed in the draggable box.
To change this, you can give a value to the `__reorder__` field for each row.



```
const columns: GridColDef[] = [{ field: 'brand' }];

const rows: GridRowsProp = [
  { id: 0, brand: 'Nike', __reorder__: 'Nike' },
  { id: 1, brand: 'Adidas', __reorder__: 'Adidas' },
  { id: 2, brand: 'Puma', __reorder__: 'Puma' },
];

<DataGridPro rows={rows} columns={columns} rowReordering />;

```
CopyCopied(or Ctrl \+ C)
Customizing the row reordering icon
-----------------------------------

To change the icon used for the row reordering, you can provide a different component for the icon slot as follow:



```
<DataGridPro
  slots={{
    rowReorderIcon: CustomMoveIcon,
  }}
/>

```
CopyCopied(or Ctrl \+ C)
Another way to customize is to add a column with `field: __reorder__` to your set of columns.
That way, you can overwrite any of the properties from the `GRID_REORDER_COL_DEF` column.
The grid will detect that there is already a reorder column defined and it will not add another one in the default position.
If you only set the `field`, then it is up to you to configure the remaining options (for example disable the column menu, filtering, sorting).
To start with our suggested configuration, spread `GRID_REORDER_COL_DEF` when defining the column.



```
<DataGridPro
  columns={[
    {
      ...GRID_REORDER_COL_DEF, // Already contains the right field
      width: 40,
    },
  ]}
/>

```
CopyCopied(or Ctrl \+ C)
This approach can also be used to change the location of the toggle column.



For now, row reordering is disabled if sorting is applied to the Data Grid.


Reordering with tree data and grouping 🚧
----------------------------------------


This feature isn't available yet, but it is planned—you can 👍 upvote this GitHub issue to help us prioritize it.
Please don't hesitate to leave a comment there to describe your needs, especially if you have a use case we should address or you're facing specific pain points with your current solution.


With this feature, users would be able to reorder rows in use cases that also involve tree data and/or row grouping.


API
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Master detailRow pinning

---

•

Blog•

Store
