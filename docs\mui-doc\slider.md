Slider
======

Sliders allow users to make selections from a range of values.Help us keep runningIf you don't mind tech\-related ads (no tracking or remarketing), and want to keep us running, please whitelist us in your blocker.Thank you! ❤️


Sliders reflect a range of values along a bar, from which users may select a single value. They are ideal for adjusting settings such as volume, brightness, or applying image filters.


* Feedback
* Bundle size
* Source
* WAI\-ARIA
* Material Design
* Figma
* Sketch

Continuous sliders
------------------

Continuous sliders allow users to select a value along a subjective range.


JSTSExpand codeCopy(or Ctrl \+ C)
```
<Stack spacing={2} direction="row" sx={{ alignItems: 'center', mb: 1 }}>
  <VolumeDown />
  <Slider aria-label="Volume" value={value} onChange={handleChange} />
  <VolumeUp />
</Stack>
<Slider disabled defaultValue={30} aria-label="Disabled slider" />  

```
\<Stack spacing\={2} direction\="row" sx\={{ alignItems: 'center', mb: 1 }}\>
 \<VolumeDown /\>
 \<Slider aria\-label\="Volume" value\={value} onChange\={handleChange} /\>
 \<VolumeUp /\>
\</Stack\>
\<Slider disabled defaultValue\={30} aria\-label\="Disabled slider" /\>Press `Enter` to start editingSizes
-----

For smaller slider, use the prop `size="small"`.


7050JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  size="small"
  defaultValue={70}
  aria-label="Small"
  valueLabelDisplay="auto"
/>
<Slider defaultValue={50} aria-label="Default" valueLabelDisplay="auto" />  

```
\<Slider
 size\="small"
 defaultValue\={70}
 aria\-label\="Small"
 valueLabelDisplay\="auto"
/\>
\<Slider defaultValue\={50} aria\-label\="Default" valueLabelDisplay\="auto" /\>Press `Enter` to start editingDiscrete sliders
----------------

Discrete sliders can be adjusted to a specific value by referencing its value indicator.
You can generate a mark for each step with `marks={true}`.


30JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Temperature"
  defaultValue={30}
  getAriaValueText={valuetext}
  valueLabelDisplay="auto"
  shiftStep={30}
  step={10}
  marks
  min={10}
  max={110}
/>
<Slider defaultValue={30} step={10} marks min={10} max={110} disabled />  

```
\<Slider
 aria\-label\="Temperature"
 defaultValue\={30}
 getAriaValueText\={valuetext}
 valueLabelDisplay\="auto"
 shiftStep\={30}
 step\={10}
 marks
 min\={10}
 max\={110}
/\>
\<Slider defaultValue\={30} step\={10} marks min\={10} max\={110} disabled /\>Press `Enter` to start editing### Small steps

You can change the default step increment.
Make sure to adjust the `shiftStep` prop (the granularity with which the slider can step when using Page Up/Down or Shift \+ Arrow Up/Down) to a value divisible by the `step`.


5e\-8JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Small steps"
  defaultValue={0.00000005}
  getAriaValueText={valuetext}
  step={0.00000001}
  marks
  min={-0.00000005}
  max={0.0000001}
  valueLabelDisplay="auto"
/>  

```
\<Slider
 aria\-label\="Small steps"
 defaultValue\={0\.00000005}
 getAriaValueText\={valuetext}
 step\={0\.00000001}
 marks
 min\={\-0\.00000005}
 max\={0\.0000001}
 valueLabelDisplay\="auto"
/\>Press `Enter` to start editing### Custom marks

You can have custom marks by providing a rich array to the `marks` prop.


0°C20°C37°C100°C20JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Custom marks"
  defaultValue={20}
  getAriaValueText={valuetext}
  step={10}
  valueLabelDisplay="auto"
  marks={marks}
/>  

```
\<Slider
 aria\-label\="Custom marks"
 defaultValue\={20}
 getAriaValueText\={valuetext}
 step\={10}
 valueLabelDisplay\="auto"
 marks\={marks}
/\>Press `Enter` to start editing### Restricted values

You can restrict the selectable values to those provided with the `marks` prop with `step={null}`.


0°C20°C37°C100°C20JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Restricted values"
  defaultValue={20}
  getAriaValueText={valuetext}
  step={null}
  valueLabelDisplay="auto"
  marks={marks}
/>  

```
\<Slider
 aria\-label\="Restricted values"
 defaultValue\={20}
 getAriaValueText\={valuetext}
 step\={null}
 valueLabelDisplay\="auto"
 marks\={marks}
/\>Press `Enter` to start editing### Label always visible

You can force the thumb label to be always visible with `valueLabelDisplay="on"`.


0°C20°C37°C100°C80JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Always visible"
  defaultValue={80}
  getAriaValueText={valuetext}
  step={10}
  marks={marks}
  valueLabelDisplay="on"
/>  

```
\<Slider
 aria\-label\="Always visible"
 defaultValue\={80}
 getAriaValueText\={valuetext}
 step\={10}
 marks\={marks}
 valueLabelDisplay\="on"
/\>Press `Enter` to start editingRange slider
------------

The slider can be used to set the start and end of a range by supplying an array of values to the `value` prop.


2037JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  getAriaLabel={ => 'Temperature range'}
  value={value}
  onChange={handleChange}
  valueLabelDisplay="auto"
  getAriaValueText={valuetext}
/>  

```
\<Slider
 getAriaLabel\={ \=\> 'Temperature range'}
 value\={value}
 onChange\={handleChange}
 valueLabelDisplay\="auto"
 getAriaValueText\={valuetext}
/\>Press `Enter` to start editing### Minimum distance

You can enforce a minimum distance between values in the `onChange` event handler.
By default, when you move the pointer over a thumb while dragging another thumb, the active thumb will swap to the hovered thumb. You can disable this behavior with the `disableSwap` prop.
If you want the range to shift when reaching minimum distance, you can utilize the `activeThumb` parameter in `onChange`.


20372037JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  getAriaLabel={ => 'Minimum distance'}
  value={value1}
  onChange={handleChange1}
  valueLabelDisplay="auto"
  getAriaValueText={valuetext}
  disableSwap
/>
<Slider
  getAriaLabel={ => 'Minimum distance shift'}
  value={value2}
  onChange={handleChange2}
  valueLabelDisplay="auto"
  getAriaValueText={valuetext}
  disableSwap
/>  

```
\<Slider
 getAriaLabel\={ \=\> 'Minimum distance'}
 value\={value1}
 onChange\={handleChange1}
 valueLabelDisplay\="auto"
 getAriaValueText\={valuetext}
 disableSwap
/\>
\<Slider
 getAriaLabel\={ \=\> 'Minimum distance shift'}
 value\={value2}
 onChange\={handleChange2}
 valueLabelDisplay\="auto"
 getAriaValueText\={valuetext}
 disableSwap
/\>Press `Enter` to start editingSlider with input field
-----------------------

In this example, an input allows a discrete value to be set.


Volume

JSTSShow codeColor
-----

JSTSExpand codeCopy(or Ctrl \+ C)
```
<Slider
  aria-label="Temperature"
  defaultValue={30}
  getAriaValueText={valuetext}
  color="secondary"
/>  

```
\<Slider
 aria\-label\="Temperature"
 defaultValue\={30}
 getAriaValueText\={valuetext}
 color\="secondary"
/\>Press `Enter` to start editingCustomization
-------------

Here are some examples of customizing the component.
You can learn more about this in the overrides documentation page.


iOS

60pretto.fr

20Tooltip value label

Airbnb

JSTSShow code### Music player

Jun Pulse**คนเก่าเขาทำไว้ดี (Can't win)**

Chilling Sunday — คนเก่าเขาทำไว้ดี

0:32

\-2:48

JSTSShow codeVertical sliders
----------------

Set the `orientation` prop to `"vertical"` to create vertical sliders. The thumb will track vertical movement instead of horizontal movement.


30300°C20°C37°C100°C2037JSTSShow code
Chrome versions below 124 implement `aria-orientation` incorrectly for vertical sliders and expose them as `'horizontal'` in the accessibility tree. (Chromium issue \#40736841)


The `-webkit-appearance: slider-vertical` CSS property can be used to correct this for these older versions, with the trade\-off of causing a console warning in newer Chrome versions:



```
.MuiSlider-thumb input {
  -webkit-appearance: slider-vertical;
}

```
CopyCopied(or Ctrl \+ C)
Marks placement
---------------

You can customize your slider by adding and repositioning marks for minimum and maximum values.


00 min

100 max

JSTSShow codeTrack
-----

The track shows the range available for user selection.


### Removed track

The track can be turned off with `track={false}`.


Removed track

0°C20°C37°C100°CRemoved track range slider

0°C20°C37°C100°CJSTSShow code### Inverted track

The track can be inverted with `track="inverted"`.


Inverted track

0°C20°C37°C100°CInverted track range

0°C20°C37°C100°CJSTSShow codeNon\-linear scale
-----------------

You can use the `scale` prop to represent the `value` on a different scale.


In the following demo, the value *x* represents the value *2^x*.
Increasing *x* by one increases the represented value by factor *2*.


Storage: 1 MB

1 MBJSTSExpand codeCopy(or Ctrl \+ C)
```
<Typography id="non-linear-slider" gutterBottom>
  Storage: {valueLabelFormat(calculateValue(value))}
</Typography>
<Slider
  value={value}
  min={5}
  step={1}
  max={30}
  scale={calculateValue}
  getAriaValueText={valueLabelFormat}
  valueLabelFormat={valueLabelFormat}
  onChange={handleChange}
  valueLabelDisplay="auto"
  aria-labelledby="non-linear-slider"
/>  

```
\<Typography id\="non\-linear\-slider" gutterBottom\>
 Storage: {valueLabelFormat(calculateValue(value))}
\</Typography\>
\<Slider
 value\={value}
 min\={5}
 step\={1}
 max\={30}
 scale\={calculateValue}
 getAriaValueText\={valueLabelFormat}
 valueLabelFormat\={valueLabelFormat}
 onChange\={handleChange}
 valueLabelDisplay\="auto"
 aria\-labelledby\="non\-linear\-slider"
/\>Press `Enter` to start editingAccessibility
-------------

(WAI\-ARIA: 


The component handles most of the work necessary to make it accessible.
However, you need to make sure that:


* Each thumb has a user\-friendly label (`aria-label`, `aria-labelledby` or `getAriaLabel` prop).
* Each thumb has a user\-friendly text for its current value.
This is not required if the value matches the semantics of the label.
You can change the name with the `getAriaValueText` or `aria-valuetext` prop.


Unstyled
--------

Use the Base UI Slider for complete ownership of the component's design, with no Material UI or Joy UI styles to override.
This unstyled version of the component is the ideal choice for heavy customization with a smaller bundle size.
 


API
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Slider />`



