Drawer
======

The navigation drawers (or "sidebars") provide ergonomic access to destinations in a site or app functionality such as switching accounts.


Sell products, services, content and more with Squarespace.

ads via Carbon



A navigation drawer can either be permanently on\-screen or controlled by a navigation menu icon.


Side sheets are supplementary surfaces primarily used on tablet and desktop.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Temporary drawer
----------------

Temporary navigation drawers can toggle open or closed. Closed by default, the drawer opens temporarily above all other content until a section is selected.


The Drawer can be cancelled by clicking the overlay or pressing the Esc key.
It closes when an item is selected, handled by controlling the `open` prop.


Open drawerJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button onClick={toggleDrawer(true)}>Open drawer</Button>
<Drawer open={open} onClose={toggleDrawer(false)}>
  {DrawerList}
</Drawer>  

```
\<Button onClick\={toggleDrawer(true)}\>Open drawer\</Button\>
\<Drawer open\={open} onClose\={toggleDrawer(false)}\>
 {DrawerList}
\</Drawer\>Press `Enter` to start editing**CloudBees** \- Don't just wait—conquer downtime. Leverage "The Power of Three" in CloudBees HA for faster delivery.ad by Carbon### Anchor

Use the `anchor` prop to specify which side of the screen the Drawer should originate from.


The default value is `left`.


leftrighttopbottomJSTSExpand codeCopy(or Ctrl \+ C)
```
{(['left', 'right', 'top', 'bottom'] as const).map((anchor) => (
  <React.Fragment key={anchor}>
    <Button onClick={toggleDrawer(anchor, true)}>{anchor}</Button>
    <Drawer
      anchor={anchor}
      open={state[anchor]}
      onClose={toggleDrawer(anchor, false)}
    >
      {list(anchor)}
    </Drawer>
  </React.Fragment>
))}  

```
{(\['left', 'right', 'top', 'bottom'] as const).map((anchor) \=\> (
 \<React.Fragment key\={anchor}\>
 \<Button onClick\={toggleDrawer(anchor, true)}\>{anchor}\</Button\>
 \<Drawer
 anchor\={anchor}
 open\={state\[anchor]}
 onClose\={toggleDrawer(anchor, false)}
 \>
 {list(anchor)}
 \</Drawer\>
 \</React.Fragment\>
))}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon### Swipeable

You can make the drawer swipeable with the `SwipeableDrawer` component.


This component comes with a 2 kB gzipped payload overhead.
Some low\-end mobile devices won't be able to follow the fingers at 60 FPS.
You can use the `disableBackdropTransition` prop to help.


leftrighttopbottomJSTSExpand codeCopy(or Ctrl \+ C)
```
{(['left', 'right', 'top', 'bottom'] as const).map((anchor) => (
  <React.Fragment key={anchor}>
    <Button onClick={toggleDrawer(anchor, true)}>{anchor}</Button>
    <SwipeableDrawer
      anchor={anchor}
      open={state[anchor]}
      onClose={toggleDrawer(anchor, false)}
      onOpen={toggleDrawer(anchor, true)}
    >
      {list(anchor)}
    </SwipeableDrawer>
  </React.Fragment>
))}  

```
{(\['left', 'right', 'top', 'bottom'] as const).map((anchor) \=\> (
 \<React.Fragment key\={anchor}\>
 \<Button onClick\={toggleDrawer(anchor, true)}\>{anchor}\</Button\>
 \<SwipeableDrawer
 anchor\={anchor}
 open\={state\[anchor]}
 onClose\={toggleDrawer(anchor, false)}
 onOpen\={toggleDrawer(anchor, true)}
 \>
 {list(anchor)}
 \</SwipeableDrawer\>
 \</React.Fragment\>
))}Press `Enter` to start editingThe following properties are used in this documentation website for optimal usability of the component:


* iOS is hosted on high\-end devices.
The backdrop transition can be enabled without dropping frames.
The performance will be good enough.
* iOS has a "swipe to go back" feature that interferes
with the discovery feature, so discovery has to be disabled.



```
const iOS =
  typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);

<SwipeableDrawer disableBackdropTransition={!iOS} disableDiscovery={iOS} />;

```
CopyCopied(or Ctrl \+ C)
### Swipeable edge

You can configure the `SwipeableDrawer` to have a visible edge when closed.


If you are on a desktop, you can toggle the drawer with the "OPEN" button.
If you are on mobile, you can open the demo in CodeSandbox ("edit" icon) and swipe.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { Global } from '@emotion/react';
import { styled } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { grey } from '@mui/material/colors';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import Typography from '@mui/material/Typography';
import SwipeableDrawer from '@mui/material/SwipeableDrawer';

const drawerBleeding = 56;

interface Props {
  /**
   * Injected by the documentation to work in an iframe.
   * You won't need it on your project.
   */
  window?:  => Window;
}

const Root = styled('div')(({ theme }) => ({
  height: '100%',
  backgroundColor: grey[100],
  ...theme.applyStyles('dark', {
    backgroundColor: (theme.vars || theme).palette.background.default,
  }),
}));

const StyledBox = styled('div')(({ theme }) => ({
  backgroundColor: '#fff',
  ...theme.applyStyles('dark', {
    backgroundColor: grey[800],
  }),
}));

const Puller = styled('div')(({ theme }) => ({
  width: 30,
  height: 6,
  backgroundColor: grey[300],
  borderRadius: 3,
  position: 'absolute',
  top: 8,
  left: 'calc(50% - 15px)',
  ...theme.applyStyles('dark', {
    backgroundColor: grey[900],
  }),
}));

export default function SwipeableEdgeDrawer(props: Props) {
  const { window } = props;
  const [open, setOpen] = React.useState(false);

  const toggleDrawer = (newOpen: boolean) =>  => {
    setOpen(newOpen);
  };

  // This is used only for the example
  const container = window !== undefined ?  => window.document.body : undefined;

  return (
    <Root>
      <CssBaseline />
      <Global
        styles={{
          '.MuiDrawer-root > .MuiPaper-root': {
            height: `calc(50% - ${drawerBleeding}px)`,
            overflow: 'visible',
          },
        }}
      />
      <Box sx={{ textAlign: 'center', pt: 1 }}>
        <Button onClick={toggleDrawer(true)}>Open</Button>
      </Box>
      <SwipeableDrawer
        container={container}
        anchor="bottom"
        open={open}
        onClose={toggleDrawer(false)}
        onOpen={toggleDrawer(true)}
        swipeAreaWidth={drawerBleeding}
        disableSwipeToOpen={false}
        keepMounted
      >
        <StyledBox
          sx={{
            position: 'absolute',
            top: -drawerBleeding,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            visibility: 'visible',
            right: 0,
            left: 0,
          }}
        >
          <Puller />
          <Typography sx={{ p: 2, color: 'text.secondary' }}>51 results</Typography>
        </StyledBox>
        <StyledBox sx={{ px: 2, pb: 2, height: '100%', overflow: 'auto' }}>
          <Skeleton variant="rectangular" height="100%" />
        </StyledBox>
      </SwipeableDrawer>
    </Root>
  );
}
```
**GetStream.io** \- Built by devs, for devs. Start Coding FREE. No CC requiredad by Carbon### Keep mounted

The Modal used internally by the Swipeable Drawer has the `keepMounted` prop set by default.
This means that the contents of the drawer are always present in the DOM.


You can change this default behavior with the `ModalProps` prop, but you may encounter issues with `keepMounted: false` in React 18\.



```
<Drawer
  variant="temporary"
  ModalProps={{
    keepMounted: false,
  }}
/>

```
CopyCopied(or Ctrl \+ C)
Responsive drawer
-----------------

You can use the `temporary` variant to display a drawer for small screens and `permanent` for a drawer for wider screens.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import CssBaseline from '@mui/material/CssBaseline';
import Divider from '@mui/material/Divider';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import MailIcon from '@mui/icons-material/Mail';
import MenuIcon from '@mui/icons-material/Menu';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';

const drawerWidth = 240;

interface Props {
  /**
   * Injected by the documentation to work in an iframe.
   * Remove this when copying and pasting into your project.
   */
  window?:  => Window;
}

export default function ResponsiveDrawer(props: Props) {
  const { window } = props;
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleDrawerClose =  => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd =  => {
    setIsClosing(false);
  };

  const handleDrawerToggle =  => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };

  const drawer = (
    <div>
      <Toolbar />
      <Divider />
      <List>
        {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
          <ListItem key={text} disablePadding>
            <ListItemButton>
              <ListItemIcon>
                {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
              </ListItemIcon>
              <ListItemText primary={text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      <Divider />
      <List>
        {['All mail', 'Trash', 'Spam'].map((text, index) => (
          <ListItem key={text} disablePadding>
            <ListItemButton>
              <ListItemIcon>
                {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
              </ListItemIcon>
              <ListItemText primary={text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  // Remove this const when copying and pasting into your project.
  const container = window !== undefined ?  => window.document.body : undefined;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Responsive drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        {/* The implementation can be swapped with js to avoid SEO duplication of links. */}
        <Drawer
          container={container}
          variant="temporary"
          open={mobileOpen}
          onTransitionEnd={handleDrawerTransitionEnd}
          onClose={handleDrawerClose}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          slotProps={{
            root: {
              keepMounted: true, // Better open performance on mobile.
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{ flexGrow: 1, p: 3, width: { sm: `calc(100% - ${drawerWidth}px)` } }}
      >
        <Toolbar />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Box>
    </Box>
  );
}
```
**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonPersistent drawer
-----------------

Persistent navigation drawers can toggle open or closed.
The drawer sits on the same surface elevation as the content.
It is closed by default and opens by selecting the menu icon, and stays open until closed by the user.
The state of the drawer is remembered from action to action and session to session.


When the drawer is outside of the page grid and opens, the drawer forces other content to change size and adapt to the smaller viewport.


Persistent navigation drawers are acceptable for all sizes larger than mobile.
They are not recommended for apps with multiple levels of hierarchy that require using an up arrow for navigation.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: `-${drawerWidth}px`,
  variants: [
    {
      props: ({ open }) => open,
      style: {
        transition: theme.transitions.create('margin', {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
        marginLeft: 0,
      },
    },
  ],
}));

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<AppBarProps>(({ theme }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        width: `calc(100% - ${drawerWidth}px)`,
        marginLeft: `${drawerWidth}px`,
        transition: theme.transitions.create(['margin', 'width'], {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}));

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
  justifyContent: 'flex-end',
}));

export default function PersistentDrawerLeft {
  const theme = useTheme;
  const [open, setOpen] = React.useState(false);

  const handleDrawerOpen =  => {
    setOpen(true);
  };

  const handleDrawerClose =  => {
    setOpen(false);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={[
              {
                mr: 2,
              },
              open && { display: 'none' },
            ]}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Persistent drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant="persistent"
        anchor="left"
        open={open}
      >
        <DrawerHeader>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === 'ltr' ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <List>
          {['All mail', 'Trash', 'Spam'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
      <Main open={open}>
        <DrawerHeader />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Main>
    </Box>
  );
}  

```
import \* as React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons\-material/Menu';
import ChevronLeftIcon from '@mui/icons\-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons\-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

const Main \= styled('main', { shouldForwardProp: (prop) \=\> prop !\=\= 'open' })\<{
 open?: boolean;
}\>(({ theme }) \=\> ({
 flexGrow: 1,
 padding: theme.spacing(3\),
 transition: theme.transitions.create('margin', {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 marginLeft: \`\-${drawerWidth}px\`,
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 transition: theme.transitions.create('margin', {
 easing: theme.transitions.easing.easeOut,
 duration: theme.transitions.duration.enteringScreen,
 }),
 marginLeft: 0,
 },
 },
 ],
}));

interface AppBarProps extends MuiAppBarProps {
 open?: boolean;
}

const AppBar \= styled(MuiAppBar, {
 shouldForwardProp: (prop) \=\> prop !\=\= 'open',
})\<AppBarProps\>(({ theme }) \=\> ({
 transition: theme.transitions.create(\['margin', 'width'], {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 width: \`calc(100% \- ${drawerWidth}px)\`,
 marginLeft: \`${drawerWidth}px\`,
 transition: theme.transitions.create(\['margin', 'width'], {
 easing: theme.transitions.easing.easeOut,
 duration: theme.transitions.duration.enteringScreen,
 }),
 },
 },
 ],
}));

const DrawerHeader \= styled('div')(({ theme }) \=\> ({
 display: 'flex',
 alignItems: 'center',
 padding: theme.spacing(0, 1\),
 // necessary for content to be below app bar
 ...theme.mixins.toolbar,
 justifyContent: 'flex\-end',
}));

export default function PersistentDrawerLeft {
 const theme \= useTheme;
 const \[open, setOpen] \= React.useState(false);

 const handleDrawerOpen \=  \=\> {
 setOpen(true);
 };

 const handleDrawerClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar position\="fixed" open\={open}\>
 \<Toolbar\>
 \<IconButton
 color\="inherit"
 aria\-label\="open drawer"
 onClick\={handleDrawerOpen}
 edge\="start"
 sx\={\[
 {
 mr: 2,
 },
 open \&\& { display: 'none' },
 ]}
 \>
 \<MenuIcon /\>
 \</IconButton\>
 \<Typography variant\="h6" noWrap component\="div"\>
 Persistent drawer
 \</Typography\>
 \</Toolbar\>
 \</AppBar\>
 \<Drawer
 sx\={{
 width: drawerWidth,
 flexShrink: 0,
 '\& .MuiDrawer\-paper': {
 width: drawerWidth,
 boxSizing: 'border\-box',
 },
 }}
 variant\="persistent"
 anchor\="left"
 open\={open}
 \>
 \<DrawerHeader\>
 \<IconButton onClick\={handleDrawerClose}\>
 {theme.direction \=\=\= 'ltr' ? \<ChevronLeftIcon /\> : \<ChevronRightIcon /\>}
 \</IconButton\>
 \</DrawerHeader\>
 \<Divider /\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Drawer\>
 \<Main open\={open}\>
 \<DrawerHeader /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Main\>
 \</Box\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by CarbonJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import CssBaseline from '@mui/material/CssBaseline';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginRight: -drawerWidth,
  /**
   * This is necessary to enable the selection of content. In the DOM, the stacking order is determined
   * by the order of appearance. Following this rule, elements appearing later in the markup will overlay
   * those that appear earlier. Since the Drawer comes after the Main content, this adjustment ensures
   * proper interaction with the underlying content.
   */
  position: 'relative',
  variants: [
    {
      props: ({ open }) => open,
      style: {
        transition: theme.transitions.create('margin', {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
        marginRight: 0,
      },
    },
  ],
}));

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<AppBarProps>(({ theme }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        width: `calc(100% - ${drawerWidth}px)`,
        transition: theme.transitions.create(['margin', 'width'], {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
        marginRight: drawerWidth,
      },
    },
  ],
}));

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
  justifyContent: 'flex-start',
}));

export default function PersistentDrawerRight {
  const theme = useTheme;
  const [open, setOpen] = React.useState(false);

  const handleDrawerOpen =  => {
    setOpen(true);
  };

  const handleDrawerClose =  => {
    setOpen(false);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar position="fixed" open={open}>
        <Toolbar>
          <Typography variant="h6" noWrap sx={{ flexGrow: 1 }} component="div">
            Persistent drawer
          </Typography>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="end"
            onClick={handleDrawerOpen}
            sx={[open && { display: 'none' }]}
          >
            <MenuIcon />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Main open={open}>
        <DrawerHeader />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Main>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
          },
        }}
        variant="persistent"
        anchor="right"
        open={open}
      >
        <DrawerHeader>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === 'rtl' ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <List>
          {['All mail', 'Trash', 'Spam'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
    </Box>
  );
}  

```
import \* as React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import CssBaseline from '@mui/material/CssBaseline';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons\-material/Menu';
import ChevronLeftIcon from '@mui/icons\-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons\-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

const Main \= styled('main', { shouldForwardProp: (prop) \=\> prop !\=\= 'open' })\<{
 open?: boolean;
}\>(({ theme }) \=\> ({
 flexGrow: 1,
 padding: theme.spacing(3\),
 transition: theme.transitions.create('margin', {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 marginRight: \-drawerWidth,
 /\*\*
 \* This is necessary to enable the selection of content. In the DOM, the stacking order is determined
 \* by the order of appearance. Following this rule, elements appearing later in the markup will overlay
 \* those that appear earlier. Since the Drawer comes after the Main content, this adjustment ensures
 \* proper interaction with the underlying content.
 \*/
 position: 'relative',
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 transition: theme.transitions.create('margin', {
 easing: theme.transitions.easing.easeOut,
 duration: theme.transitions.duration.enteringScreen,
 }),
 marginRight: 0,
 },
 },
 ],
}));

interface AppBarProps extends MuiAppBarProps {
 open?: boolean;
}

const AppBar \= styled(MuiAppBar, {
 shouldForwardProp: (prop) \=\> prop !\=\= 'open',
})\<AppBarProps\>(({ theme }) \=\> ({
 transition: theme.transitions.create(\['margin', 'width'], {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 width: \`calc(100% \- ${drawerWidth}px)\`,
 transition: theme.transitions.create(\['margin', 'width'], {
 easing: theme.transitions.easing.easeOut,
 duration: theme.transitions.duration.enteringScreen,
 }),
 marginRight: drawerWidth,
 },
 },
 ],
}));

const DrawerHeader \= styled('div')(({ theme }) \=\> ({
 display: 'flex',
 alignItems: 'center',
 padding: theme.spacing(0, 1\),
 // necessary for content to be below app bar
 ...theme.mixins.toolbar,
 justifyContent: 'flex\-start',
}));

export default function PersistentDrawerRight {
 const theme \= useTheme;
 const \[open, setOpen] \= React.useState(false);

 const handleDrawerOpen \=  \=\> {
 setOpen(true);
 };

 const handleDrawerClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar position\="fixed" open\={open}\>
 \<Toolbar\>
 \<Typography variant\="h6" noWrap sx\={{ flexGrow: 1 }} component\="div"\>
 Persistent drawer
 \</Typography\>
 \<IconButton
 color\="inherit"
 aria\-label\="open drawer"
 edge\="end"
 onClick\={handleDrawerOpen}
 sx\={\[open \&\& { display: 'none' }]}
 \>
 \<MenuIcon /\>
 \</IconButton\>
 \</Toolbar\>
 \</AppBar\>
 \<Main open\={open}\>
 \<DrawerHeader /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Main\>
 \<Drawer
 sx\={{
 width: drawerWidth,
 flexShrink: 0,
 '\& .MuiDrawer\-paper': {
 width: drawerWidth,
 },
 }}
 variant\="persistent"
 anchor\="right"
 open\={open}
 \>
 \<DrawerHeader\>
 \<IconButton onClick\={handleDrawerClose}\>
 {theme.direction \=\=\= 'rtl' ? \<ChevronLeftIcon /\> : \<ChevronRightIcon /\>}
 \</IconButton\>
 \</DrawerHeader\>
 \<Divider /\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Drawer\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace has all the tools you need to keep visitors coming back.ad by CarbonMini variant drawer
-------------------

In this variation, the persistent navigation drawer changes its width.
Its resting state is as a mini\-drawer at the same elevation as the content, clipped by the app bar.
When expanded, it appears as the standard persistent navigation drawer.


The mini variant is recommended for apps sections that need quick selection access alongside content.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { styled, useTheme, Theme, CSSObject } from '@mui/material/styles';
import Box from '@mui/material/Box';
import MuiDrawer from '@mui/material/Drawer';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import CssBaseline from '@mui/material/CssBaseline';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

const openedMixin = (theme: Theme): CSSObject => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme: Theme): CSSObject => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up('sm')]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<AppBarProps>(({ theme }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        marginLeft: drawerWidth,
        width: `calc(100% - ${drawerWidth}px)`,
        transition: theme.transitions.create(['width', 'margin'], {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}));

const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme }) => ({
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box',
    variants: [
      {
        props: ({ open }) => open,
        style: {
          ...openedMixin(theme),
          '& .MuiDrawer-paper': openedMixin(theme),
        },
      },
      {
        props: ({ open }) => !open,
        style: {
          ...closedMixin(theme),
          '& .MuiDrawer-paper': closedMixin(theme),
        },
      },
    ],
  }),
);

export default function MiniDrawer {
  const theme = useTheme;
  const [open, setOpen] = React.useState(false);

  const handleDrawerOpen =  => {
    setOpen(true);
  };

  const handleDrawerClose =  => {
    setOpen(false);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={[
              {
                marginRight: 5,
              },
              open && { display: 'none' },
            ]}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Mini variant drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer variant="permanent" open={open}>
        <DrawerHeader>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === 'rtl' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
            <ListItem key={text} disablePadding sx={{ display: 'block' }}>
              <ListItemButton
                sx={[
                  {
                    minHeight: 48,
                    px: 2.5,
                  },
                  open
                    ? {
                        justifyContent: 'initial',
                      }
                    : {
                        justifyContent: 'center',
                      },
                ]}
              >
                <ListItemIcon
                  sx={[
                    {
                      minWidth: 0,
                      justifyContent: 'center',
                    },
                    open
                      ? {
                          mr: 3,
                        }
                      : {
                          mr: 'auto',
                        },
                  ]}
                >
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText
                  primary={text}
                  sx={[
                    open
                      ? {
                          opacity: 1,
                        }
                      : {
                          opacity: 0,
                        },
                  ]}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <List>
          {['All mail', 'Trash', 'Spam'].map((text, index) => (
            <ListItem key={text} disablePadding sx={{ display: 'block' }}>
              <ListItemButton
                sx={[
                  {
                    minHeight: 48,
                    px: 2.5,
                  },
                  open
                    ? {
                        justifyContent: 'initial',
                      }
                    : {
                        justifyContent: 'center',
                      },
                ]}
              >
                <ListItemIcon
                  sx={[
                    {
                      minWidth: 0,
                      justifyContent: 'center',
                    },
                    open
                      ? {
                          mr: 3,
                        }
                      : {
                          mr: 'auto',
                        },
                  ]}
                >
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText
                  primary={text}
                  sx={[
                    open
                      ? {
                          opacity: 1,
                        }
                      : {
                          opacity: 0,
                        },
                  ]}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <DrawerHeader />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Box>
    </Box>
  );
}  

```
import \* as React from 'react';
import { styled, useTheme, Theme, CSSObject } from '@mui/material/styles';
import Box from '@mui/material/Box';
import MuiDrawer from '@mui/material/Drawer';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import CssBaseline from '@mui/material/CssBaseline';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons\-material/Menu';
import ChevronLeftIcon from '@mui/icons\-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons\-material/ChevronRight';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

const openedMixin \= (theme: Theme): CSSObject \=\> ({
 width: drawerWidth,
 transition: theme.transitions.create('width', {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.enteringScreen,
 }),
 overflowX: 'hidden',
});

const closedMixin \= (theme: Theme): CSSObject \=\> ({
 transition: theme.transitions.create('width', {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 overflowX: 'hidden',
 width: \`calc(${theme.spacing(7\)} \+ 1px)\`,
 \[theme.breakpoints.up('sm')]: {
 width: \`calc(${theme.spacing(8\)} \+ 1px)\`,
 },
});

const DrawerHeader \= styled('div')(({ theme }) \=\> ({
 display: 'flex',
 alignItems: 'center',
 justifyContent: 'flex\-end',
 padding: theme.spacing(0, 1\),
 // necessary for content to be below app bar
 ...theme.mixins.toolbar,
}));

interface AppBarProps extends MuiAppBarProps {
 open?: boolean;
}

const AppBar \= styled(MuiAppBar, {
 shouldForwardProp: (prop) \=\> prop !\=\= 'open',
})\<AppBarProps\>(({ theme }) \=\> ({
 zIndex: theme.zIndex.drawer \+ 1,
 transition: theme.transitions.create(\['width', 'margin'], {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.leavingScreen,
 }),
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 marginLeft: drawerWidth,
 width: \`calc(100% \- ${drawerWidth}px)\`,
 transition: theme.transitions.create(\['width', 'margin'], {
 easing: theme.transitions.easing.sharp,
 duration: theme.transitions.duration.enteringScreen,
 }),
 },
 },
 ],
}));

const Drawer \= styled(MuiDrawer, { shouldForwardProp: (prop) \=\> prop !\=\= 'open' })(
 ({ theme }) \=\> ({
 width: drawerWidth,
 flexShrink: 0,
 whiteSpace: 'nowrap',
 boxSizing: 'border\-box',
 variants: \[
 {
 props: ({ open }) \=\> open,
 style: {
 ...openedMixin(theme),
 '\& .MuiDrawer\-paper': openedMixin(theme),
 },
 },
 {
 props: ({ open }) \=\> !open,
 style: {
 ...closedMixin(theme),
 '\& .MuiDrawer\-paper': closedMixin(theme),
 },
 },
 ],
 }),
);

export default function MiniDrawer {
 const theme \= useTheme;
 const \[open, setOpen] \= React.useState(false);

 const handleDrawerOpen \=  \=\> {
 setOpen(true);
 };

 const handleDrawerClose \=  \=\> {
 setOpen(false);
 };

 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar position\="fixed" open\={open}\>
 \<Toolbar\>
 \<IconButton
 color\="inherit"
 aria\-label\="open drawer"
 onClick\={handleDrawerOpen}
 edge\="start"
 sx\={\[
 {
 marginRight: 5,
 },
 open \&\& { display: 'none' },
 ]}
 \>
 \<MenuIcon /\>
 \</IconButton\>
 \<Typography variant\="h6" noWrap component\="div"\>
 Mini variant drawer
 \</Typography\>
 \</Toolbar\>
 \</AppBar\>
 \<Drawer variant\="permanent" open\={open}\>
 \<DrawerHeader\>
 \<IconButton onClick\={handleDrawerClose}\>
 {theme.direction \=\=\= 'rtl' ? \<ChevronRightIcon /\> : \<ChevronLeftIcon /\>}
 \</IconButton\>
 \</DrawerHeader\>
 \<Divider /\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding sx\={{ display: 'block' }}\>
 \<ListItemButton
 sx\={\[
 {
 minHeight: 48,
 px: 2\.5,
 },
 open
 ? {
 justifyContent: 'initial',
 }
 : {
 justifyContent: 'center',
 },
 ]}
 \>
 \<ListItemIcon
 sx\={\[
 {
 minWidth: 0,
 justifyContent: 'center',
 },
 open
 ? {
 mr: 3,
 }
 : {
 mr: 'auto',
 },
 ]}
 \>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText
 primary\={text}
 sx\={\[
 open
 ? {
 opacity: 1,
 }
 : {
 opacity: 0,
 },
 ]}
 /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding sx\={{ display: 'block' }}\>
 \<ListItemButton
 sx\={\[
 {
 minHeight: 48,
 px: 2\.5,
 },
 open
 ? {
 justifyContent: 'initial',
 }
 : {
 justifyContent: 'center',
 },
 ]}
 \>
 \<ListItemIcon
 sx\={\[
 {
 minWidth: 0,
 justifyContent: 'center',
 },
 open
 ? {
 mr: 3,
 }
 : {
 mr: 'auto',
 },
 ]}
 \>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText
 primary\={text}
 sx\={\[
 open
 ? {
 opacity: 1,
 }
 : {
 opacity: 0,
 },
 ]}
 /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Drawer\>
 \<Box component\="main" sx\={{ flexGrow: 1, p: 3 }}\>
 \<DrawerHeader /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Box\>
 \</Box\>
 );
}Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonPermanent drawer
----------------

Permanent navigation drawers are always visible and pinned to the left edge, at the same elevation as the content or background. They cannot be closed.


Permanent navigation drawers are the **recommended default for desktop**.


### Full\-height navigation

Apps focused on information consumption that use a left\-to\-right hierarchy.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

export default function PermanentDrawerLeft {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{ width: `calc(100% - ${drawerWidth}px)`, ml: `${drawerWidth}px` }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div">
            Permanent drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant="permanent"
        anchor="left"
      >
        <Toolbar />
        <Divider />
        <List>
          {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <List>
          {['All mail', 'Trash', 'Spam'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
      <Box
        component="main"
        sx={{ flexGrow: 1, bgcolor: 'background.default', p: 3 }}
      >
        <Toolbar />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Box>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

export default function PermanentDrawerLeft {
 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar
 position\="fixed"
 sx\={{ width: \`calc(100% \- ${drawerWidth}px)\`, ml: \`${drawerWidth}px\` }}
 \>
 \<Toolbar\>
 \<Typography variant\="h6" noWrap component\="div"\>
 Permanent drawer
 \</Typography\>
 \</Toolbar\>
 \</AppBar\>
 \<Drawer
 sx\={{
 width: drawerWidth,
 flexShrink: 0,
 '\& .MuiDrawer\-paper': {
 width: drawerWidth,
 boxSizing: 'border\-box',
 },
 }}
 variant\="permanent"
 anchor\="left"
 \>
 \<Toolbar /\>
 \<Divider /\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Drawer\>
 \<Box
 component\="main"
 sx\={{ flexGrow: 1, bgcolor: 'background.default', p: 3 }}
 \>
 \<Toolbar /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Box\>
 \</Box\>
 );
}Press `Enter` to start editing**CloudBees** \- No more pipeline interruptions. Experience faster delivery with CloudBees CI.ad by CarbonJSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

export default function PermanentDrawerRight {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{ width: `calc(100% - ${drawerWidth}px)`, mr: `${drawerWidth}px` }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div">
            Permanent drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Box
        component="main"
        sx={{ flexGrow: 1, bgcolor: 'background.default', p: 3 }}
      >
        <Toolbar />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Box>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant="permanent"
        anchor="right"
      >
        <Toolbar />
        <Divider />
        <List>
          {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <List>
          {['All mail', 'Trash', 'Spam'].map((text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import CssBaseline from '@mui/material/CssBaseline';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

export default function PermanentDrawerRight {
 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar
 position\="fixed"
 sx\={{ width: \`calc(100% \- ${drawerWidth}px)\`, mr: \`${drawerWidth}px\` }}
 \>
 \<Toolbar\>
 \<Typography variant\="h6" noWrap component\="div"\>
 Permanent drawer
 \</Typography\>
 \</Toolbar\>
 \</AppBar\>
 \<Box
 component\="main"
 sx\={{ flexGrow: 1, bgcolor: 'background.default', p: 3 }}
 \>
 \<Toolbar /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Box\>
 \<Drawer
 sx\={{
 width: drawerWidth,
 flexShrink: 0,
 '\& .MuiDrawer\-paper': {
 width: drawerWidth,
 boxSizing: 'border\-box',
 },
 }}
 variant\="permanent"
 anchor\="right"
 \>
 \<Toolbar /\>
 \<Divider /\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Drawer\>
 \</Box\>
 );
}Press `Enter` to start editing**GetStream.io** \- Built by devs, for devs. Start Coding FREE. No CC requiredad by Carbon### Clipped under the app bar

Apps focused on productivity that require balance across the screen.


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import AppBar from '@mui/material/AppBar';
import CssBaseline from '@mui/material/CssBaseline';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import MailIcon from '@mui/icons-material/Mail';

const drawerWidth = 240;

export default function ClippedDrawer {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <Typography variant="h6" noWrap component="div">
            Clipped drawer
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box' },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) => (
              <ListItem key={text} disablePadding>
                <ListItemButton>
                  <ListItemIcon>
                    {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                  </ListItemIcon>
                  <ListItemText primary={text} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
          <Divider />
          <List>
            {['All mail', 'Trash', 'Spam'].map((text, index) => (
              <ListItem key={text} disablePadding>
                <ListItemButton>
                  <ListItemIcon>
                    {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                  </ListItemIcon>
                  <ListItemText primary={text} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        <Typography sx={{ marginBottom: 2 }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
          enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
          imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
          Convallis convallis tellus id interdum velit laoreet id donec ultrices.
          Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
          adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
          nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
          leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
          feugiat vivamus at augue. At augue eget arcu dictum varius duis at
          consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
          sapien faucibus et molestie ac.
        </Typography>
        <Typography sx={{ marginBottom: 2 }}>
          Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
          eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
          neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
          tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
          sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
          tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
          gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
          et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
          tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
          eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
          posuere sollicitudin aliquam ultrices sagittis orci a.
        </Typography>
      </Box>
    </Box>
  );
}  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import AppBar from '@mui/material/AppBar';
import CssBaseline from '@mui/material/CssBaseline';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import InboxIcon from '@mui/icons\-material/MoveToInbox';
import MailIcon from '@mui/icons\-material/Mail';

const drawerWidth \= 240;

export default function ClippedDrawer {
 return (
 \<Box sx\={{ display: 'flex' }}\>
 \<CssBaseline /\>
 \<AppBar position\="fixed" sx\={{ zIndex: (theme) \=\> theme.zIndex.drawer \+ 1 }}\>
 \<Toolbar\>
 \<Typography variant\="h6" noWrap component\="div"\>
 Clipped drawer
 \</Typography\>
 \</Toolbar\>
 \</AppBar\>
 \<Drawer
 variant\="permanent"
 sx\={{
 width: drawerWidth,
 flexShrink: 0,
 \[\`\& .MuiDrawer\-paper\`]: { width: drawerWidth, boxSizing: 'border\-box' },
 }}
 \>
 \<Toolbar /\>
 \<Box sx\={{ overflow: 'auto' }}\>
 \<List\>
 {\['Inbox', 'Starred', 'Send email', 'Drafts'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \<Divider /\>
 \<List\>
 {\['All mail', 'Trash', 'Spam'].map((text, index) \=\> (
 \<ListItem key\={text} disablePadding\>
 \<ListItemButton\>
 \<ListItemIcon\>
 {index % 2 \=\=\= 0 ? \<InboxIcon /\> : \<MailIcon /\>}
 \</ListItemIcon\>
 \<ListItemText primary\={text} /\>
 \</ListItemButton\>
 \</ListItem\>
 ))}
 \</List\>
 \</Box\>
 \</Drawer\>
 \<Box component\="main" sx\={{ flexGrow: 1, p: 3 }}\>
 \<Toolbar /\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua. Rhoncus dolor purus non
 enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
 imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
 Convallis convallis tellus id interdum velit laoreet id donec ultrices.
 Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
 adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra
 nibh cras. Metus vulputate eu scelerisque felis imperdiet proin fermentum
 leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt lobortis
 feugiat vivamus at augue. At augue eget arcu dictum varius duis at
 consectetur lorem. Velit sed ullamcorper morbi tincidunt. Lorem donec massa
 sapien faucibus et molestie ac.
 \</Typography\>
 \<Typography sx\={{ marginBottom: 2 }}\>
 Consequat mauris nunc congue nisi vitae suscipit. Fringilla est ullamcorper
 eget nulla facilisi etiam dignissim diam. Pulvinar elementum integer enim
 neque volutpat ac tincidunt. Ornare suspendisse sed nisi lacus sed viverra
 tellus. Purus sit amet volutpat consequat mauris. Elementum eu facilisis
 sed odio morbi. Euismod lacinia at quis risus sed vulputate odio. Morbi
 tincidunt ornare massa eget egestas purus viverra accumsan in. In hendrerit
 gravida rutrum quisque non tellus orci ac. Pellentesque nec nam aliquam sem
 et tortor. Habitant morbi tristique senectus et. Adipiscing elit duis
 tristique sollicitudin nibh sit. Ornare aenean euismod elementum nisi quis
 eleifend. Commodo viverra maecenas accumsan lacus vel facilisis. Nulla
 posuere sollicitudin aliquam ultrices sagittis orci a.
 \</Typography\>
 \</Box\>
 \</Box\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace tools make it easy to create a beautiful and unique website.ad by CarbonExperimental APIs \- Toolpad
----------------------------

### Dashboard Layout

The DashboardLayout component from `@toolpad/core` is the starting point for dashboarding applications. It takes care of application layout, theming, navigation, and more. An example usage of this component:


JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { createTheme, styled } from '@mui/material/styles';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import BarChartIcon from '@mui/icons-material/BarChart';
import DescriptionIcon from '@mui/icons-material/Description';
import LayersIcon from '@mui/icons-material/Layers';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import { DashboardLayout } from '@toolpad/core/DashboardLayout';
import { PageContainer } from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';

const NAVIGATION: Navigation = [
  {
    kind: 'header',
    title: 'Main items',
  },
  {
    segment: 'dashboard',
    title: 'Dashboard',
    icon: <DashboardIcon />,
  },
  {
    segment: 'orders',
    title: 'Orders',
    icon: <ShoppingCartIcon />,
  },
  {
    kind: 'divider',
  },
  {
    kind: 'header',
    title: 'Analytics',
  },
  {
    segment: 'reports',
    title: 'Reports',
    icon: <BarChartIcon />,
    children: [
      {
        segment: 'sales',
        title: 'Sales',
        icon: <DescriptionIcon />,
      },
      {
        segment: 'traffic',
        title: 'Traffic',
        icon: <DescriptionIcon />,
      },
    ],
  },
  {
    segment: 'integrations',
    title: 'Integrations',
    icon: <LayersIcon />,
  },
];

const demoTheme = createTheme({
  colorSchemes: { light: true, dark: true },
  cssVariables: {
    colorSchemeSelector: 'class',
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 600,
      lg: 1200,
      xl: 1536,
    },
  },
});

function useDemoRouter(initialPath: string): Router {
  const [pathname, setPathname] = React.useState(initialPath);

  const router = React.useMemo( => {
    return {
      pathname,
      searchParams: new URLSearchParams,
      navigate: (path: string | URL) => setPathname(String(path)),
    };
  }, [pathname]);

  return router;
}

const Skeleton = styled('div')<{ height: number }>(({ theme, height }) => ({
  backgroundColor: theme.palette.action.hover,
  borderRadius: theme.shape.borderRadius,
  height,
  content: '" "',
}));

export default function DashboardLayoutBasic(props: any) {
  const { window } = props;

  const router = useDemoRouter('/dashboard');

  // Remove this const when copying and pasting into your project.
  const demoWindow = window ? window : undefined;

  return (
    <AppProvider
      navigation={NAVIGATION}
      router={router}
      theme={demoTheme}
      window={demoWindow}
    >
      <DashboardLayout>
        <PageContainer>
          <Grid container spacing={1}>
            <Grid size={5} />
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>
            <Grid size={4}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={8}>
              <Skeleton height={100} />
            </Grid>

            <Grid size={12}>
              <Skeleton height={150} />
            </Grid>
            <Grid size={12}>
              <Skeleton height={14} />
            </Grid>

            <Grid size={3}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={3}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={3}>
              <Skeleton height={100} />
            </Grid>
            <Grid size={3}>
              <Skeleton height={100} />
            </Grid>
          </Grid>
        </PageContainer>
      </DashboardLayout>
    </AppProvider>
  );
}  

```
import \* as React from 'react';
import { createTheme, styled } from '@mui/material/styles';
import DashboardIcon from '@mui/icons\-material/Dashboard';
import ShoppingCartIcon from '@mui/icons\-material/ShoppingCart';
import BarChartIcon from '@mui/icons\-material/BarChart';
import DescriptionIcon from '@mui/icons\-material/Description';
import LayersIcon from '@mui/icons\-material/Layers';
import { AppProvider, Navigation, Router } from '@toolpad/core/AppProvider';
import { DashboardLayout } from '@toolpad/core/DashboardLayout';
import { PageContainer } from '@toolpad/core/PageContainer';
import Grid from '@mui/material/Grid';

const NAVIGATION: Navigation \= \[
 {
 kind: 'header',
 title: 'Main items',
 },
 {
 segment: 'dashboard',
 title: 'Dashboard',
 icon: \<DashboardIcon /\>,
 },
 {
 segment: 'orders',
 title: 'Orders',
 icon: \<ShoppingCartIcon /\>,
 },
 {
 kind: 'divider',
 },
 {
 kind: 'header',
 title: 'Analytics',
 },
 {
 segment: 'reports',
 title: 'Reports',
 icon: \<BarChartIcon /\>,
 children: \[
 {
 segment: 'sales',
 title: 'Sales',
 icon: \<DescriptionIcon /\>,
 },
 {
 segment: 'traffic',
 title: 'Traffic',
 icon: \<DescriptionIcon /\>,
 },
 ],
 },
 {
 segment: 'integrations',
 title: 'Integrations',
 icon: \<LayersIcon /\>,
 },
];

const demoTheme \= createTheme({
 colorSchemes: { light: true, dark: true },
 cssVariables: {
 colorSchemeSelector: 'class',
 },
 breakpoints: {
 values: {
 xs: 0,
 sm: 600,
 md: 600,
 lg: 1200,
 xl: 1536,
 },
 },
});

function useDemoRouter(initialPath: string): Router {
 const \[pathname, setPathname] \= React.useState(initialPath);

 const router \= React.useMemo( \=\> {
 return {
 pathname,
 searchParams: new URLSearchParams,
 navigate: (path: string \| URL) \=\> setPathname(String(path)),
 };
 }, \[pathname]);

 return router;
}

const Skeleton \= styled('div')\<{ height: number }\>(({ theme, height }) \=\> ({
 backgroundColor: theme.palette.action.hover,
 borderRadius: theme.shape.borderRadius,
 height,
 content: '" "',
}));

export default function DashboardLayoutBasic(props: any) {
 const { window } \= props;

 const router \= useDemoRouter('/dashboard');

 // Remove this const when copying and pasting into your project.
 const demoWindow \= window ? window : undefined;

 return (
 \<AppProvider
 navigation\={NAVIGATION}
 router\={router}
 theme\={demoTheme}
 window\={demoWindow}
 \>
 \<DashboardLayout\>
 \<PageContainer\>
 \<Grid container spacing\={1}\>
 \<Grid size\={5} /\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>
 \<Grid size\={4}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={8}\>
 \<Skeleton height\={100} /\>
 \</Grid\>

 \<Grid size\={12}\>
 \<Skeleton height\={150} /\>
 \</Grid\>
 \<Grid size\={12}\>
 \<Skeleton height\={14} /\>
 \</Grid\>

 \<Grid size\={3}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={3}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={3}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \<Grid size\={3}\>
 \<Skeleton height\={100} /\>
 \</Grid\>
 \</Grid\>
 \</PageContainer\>
 \</DashboardLayout\>
 \</AppProvider\>
 );
}Press `Enter` to start editing**Squarespace** \- Squarespace makes marketing, customer management, and checkout effortless.ad by CarbonAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Drawer />`
* `<SwipeableDrawer />`



