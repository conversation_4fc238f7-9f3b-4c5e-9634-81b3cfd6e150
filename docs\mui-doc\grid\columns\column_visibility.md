Data Grid \- Column visibility
==============================

Define which columns are visible.


From 3 to 30 engineers in months. And<PERSON>'s adaptive hiring powers business growth.

ads via Carbon



By default, all the columns are visible.
The column's visibility can be switched through the user interface in two ways:


* By opening the column menu and clicking the *Hide* menu item.
* By clicking the *Columns* menu and toggling the columns to show or hide.


You can prevent the user from hiding a column through the user interface by setting the `hideable` in `GridColDef` to `false`.


In the following demo, the "username" column cannot be hidden.


​​usernameagedesk@MUI38D\-546@MUI\-X25D\-042Rows per page:

1001–2 of 2

JSTSExpand codeCopy(or Ctrl \+ C)
```
<DataGrid
  columns={[
    { field: 'username', hideable: false },
    { field: 'age' },
    { field: 'desk' },
  ]}
  rows={rows}
  showToolbar
/>  

```
\<DataGrid
 columns\={\[
 { field: 'username', hideable: false },
 { field: 'age' },
 { field: 'desk' },
 ]}
 rows\={rows}
 showToolbar
/\>Press `Enter` to start editingInitialize the visible columns
------------------------------

To initialize the visible columns without controlling them, provide the model to the `initialState` prop.



Passing the visible columns to the `initialState` prop will only have an impact when the Data Grid is rendered for the first time. In order to update the visible columns after the first render, you need to use the `columnVisibilityModel` prop.



```
<DataGrid
  initialState={{
    columns: {
      columnVisibilityModel: {
        // Hide columns status and traderName, the other columns will remain visible
        status: false,
        traderName: false,
      },
    },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledUnit PriceD\-2172Frozen Concentrated <NAME_EMAIL>17,2550\.29 %23\.69D\-7681MilkJohn Schmidtfeneja@ojvap.gov2,58083\.333 %18\.65D\-128SoybeansSteve Santosdad@iwgov.py61,95721\.751 %15\.74D\-2986<NAME_EMAIL>70,55376\.66 %30\.94D\-6916SoybeansIsabelle Jeffersonmilmu@rirnanel.jo85,82791\.71 %63D\-227WheatMaud Schultzga@cufcisluz.sj81,21287\.751 %76\.1D\-3467SoybeansLawrence McCarthymute@te.vg31,65067\.81 %97\.52Rows per page:

1001–20 of 20

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { useDemoData } from '@mui/x-data-grid-generator';
import { DataGrid } from '@mui/x-data-grid';

export default function VisibleColumnsModelInitialState {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 20,
  });

  return (
    <div style={{ height: 300, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        initialState={{
          ...data.initialState,
          columns: {
            ...data.initialState?.columns,
            columnVisibilityModel: {
              id: false,
              brokerId: false,
              status: false,
            },
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import { DataGrid } from '@mui/x\-data\-grid';

export default function VisibleColumnsModelInitialState {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 20,
 });

 return (
 \<div style\={{ height: 300, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 initialState\={{
 ...data.initialState,
 columns: {
 ...data.initialState?.columns,
 columnVisibilityModel: {
 id: false,
 brokerId: false,
 status: false,
 },
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonControlled visible columns
--------------------------

Use the `columnVisibilityModel` prop to control the visible columns.
You can use the `onColumnVisibilityModelChange` prop to listen to the changes to the visible columns and update the prop accordingly.



```
<DataGrid
  columnVisibilityModel={{
    // Hide columns status and traderName, the other columns will remain visible
    status: false,
    traderName: false,
  }}
/>

```
CopyCopied(or Ctrl \+ C)
DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledUnit PriceD\-7712SoybeansAmanda Weberihjutti@lezorauz.mq82,02573 %15\.37D\-4206<NAME_EMAIL>72,83829\.25 %63\.38D\-2076CocoaJerome Myersfapvoh@acawalze.bd85,11939\.019 %70\.4D\-8291MilkMaude Georgeed@zedbu.gb32,42099\.889 %61\.52D\-3447Cotton No.2Katharine Claytoncimom@jebute.in41,42280\.339 %35\.96D\-555CocoaLillian Alvarezrasapku@laina.za55,45041\.56 %6\.41D\-5283SoybeansFranklin Perezgah@ebi.gw62,16037\.31 %12\.51Rows per page:

1001–20 of 20

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { useDemoData } from '@mui/x-data-grid-generator';
import { DataGrid, GridColumnVisibilityModel } from '@mui/x-data-grid';

export default function VisibleColumnsModelControlled {
  const { data, loading } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 20,
    maxColumns: 20,
  });

  const [columnVisibilityModel, setColumnVisibilityModel] =
    React.useState<GridColumnVisibilityModel>({
      id: false,
      brokerId: false,
      status: false,
    });

  return (
    <div style={{ height: 300, width: '100%' }}>
      <DataGrid
        {...data}
        loading={loading}
        columnVisibilityModel={columnVisibilityModel}
        onColumnVisibilityModelChange={(newModel) =>
          setColumnVisibilityModel(newModel)
        }
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { useDemoData } from '@mui/x\-data\-grid\-generator';
import { DataGrid, GridColumnVisibilityModel } from '@mui/x\-data\-grid';

export default function VisibleColumnsModelControlled {
 const { data, loading } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 20,
 maxColumns: 20,
 });

 const \[columnVisibilityModel, setColumnVisibilityModel] \=
 React.useState\<GridColumnVisibilityModel\>({
 id: false,
 brokerId: false,
 status: false,
 });

 return (
 \<div style\={{ height: 300, width: '100%' }}\>
 \<DataGrid
 {...data}
 loading\={loading}
 columnVisibilityModel\={columnVisibilityModel}
 onColumnVisibilityModelChange\={(newModel) \=\>
 setColumnVisibilityModel(newModel)
 }
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonColumn visibility panel
-----------------------

The column visibility panel allows the user to control which columns are visible in the Data Grid.


The panel can be opened by:


* Clicking the *Columns* button in the toolbar.
* Clicking the *Manage columns* button in the column menu.


​​DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledStatusD\-5189OatsLucas Lanewejifi@pazet.mm11,06133\.397 %OpenD\-4082<NAME_EMAIL>32,26010\.58 %RejectedD\-4597Sugar No.11Leila Barnesrufwaz@if.rw29,03521\.639 %Partially FilledD\-8392<NAME_EMAIL>8,31064\.705 %OpenD\-5695CornAntonio Drakenuuju@wifeziba.lc2,72329\.783 %RejectedD\-812OatsFrancis Murrayboicice@ulu.mw79,1257\.01 %OpenD\-8889OatsRosa Williamsonjugo@lonogu.tr48,1269\.141 %RejectedD\-3257Sugar No.14Joshua Beckerorpabaj@ketcid.ax9,04794\.915 %RejectedRows per page:

1001–10 of 10

JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function ColumnSelectorGrid {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 10,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid {...data} showToolbar />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function ColumnSelectorGrid {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 10,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid {...data} showToolbar /\>
 \</div\>
 );
}Press `Enter` to start editing**Gitlab** \- If you haven't started using AI in your dev process, it's time. If you have, it's time to accelerate.ad by Carbon### Disable the column visibility panel

Sometimes, the intention is to disable the columns panel or control the visible columns programmatically based on the application state.
To disable the column visibility panel, set the prop `disableColumnSelector={true}` and use the `columnVisibilityModel` prop to control the visible columns.



```
<DataGrid disableColumnSelector columnVisibilityModel={columnVisibilityModel} />

```
CopyCopied(or Ctrl \+ C)
In the following demo, the columns panel is disabled, and access to columns `id`, `quantity`, and `filledQuantity` is only allowed for the `Admin` type user.


User TypeRegular UserUser Type​​DeskCommodityTrader NameTrader EmailD\-6788WheatGary <EMAIL>\-9603Sugar No.14Jeremy <EMAIL>\-9512Sugar No.14Ethel <EMAIL>\-2579Cotton No.2Philip <EMAIL>\-3488<NAME_EMAIL>\-1301Sugar No.14Sadie <EMAIL>\-3601CocoaLydia <EMAIL>\-9529Cotton No.2Luke <EMAIL> per page:

1001–10 of 10

JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Stack from '@mui/material/Stack';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import { DataGrid } from '@mui/x-data-grid';
import { useDemoData } from '@mui/x-data-grid-generator';

const UserType = {
  Regular: 0,
  Admin: 1,
};

export default function ColumnSelectorDisabledGrid {
  const [userType, setUserType] = React.useState(UserType.Regular);
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 7,
  });

  const columnVisibilityModel = React.useMemo( => {
    if (userType === UserType.Admin) {
      return {
        quantity: true,
        filledQuantity: true,
        id: true,
      };
    }
    return {
      quantity: false,
      filledQuantity: false,
      id: false,
    };
  }, [userType]);

  return (
    <Stack height="450px" width="100%">
      <FormControl sx={{ width: '200px', pb: 1 }}>
        <InputLabel id="demo-simple-select-label">User Type</InputLabel>
        <Select
          labelId="demo-user-type-label"
          id="demo-user-type"
          value={userType}
          label="User Type"
          onChange={(event: SelectChangeEvent<number>) => {
            setUserType(event.target.value as number);
          }}
        >
          <MenuItem value={UserType.Regular}>Regular User</MenuItem>
          <MenuItem value={UserType.Admin}>Admin</MenuItem>
        </Select>
      </FormControl>
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid
          {...data}
          disableColumnSelector
          columnVisibilityModel={columnVisibilityModel}
          showToolbar
        />
      </div>
    </Stack>
  );
}  

```
import \* as React from 'react';
import Stack from '@mui/material/Stack';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import { DataGrid } from '@mui/x\-data\-grid';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const UserType \= {
 Regular: 0,
 Admin: 1,
};

export default function ColumnSelectorDisabledGrid {
 const \[userType, setUserType] \= React.useState(UserType.Regular);
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 7,
 });

 const columnVisibilityModel \= React.useMemo( \=\> {
 if (userType \=\=\= UserType.Admin) {
 return {
 quantity: true,
 filledQuantity: true,
 id: true,
 };
 }
 return {
 quantity: false,
 filledQuantity: false,
 id: false,
 };
 }, \[userType]);

 return (
 \<Stack height\="450px" width\="100%"\>
 \<FormControl sx\={{ width: '200px', pb: 1 }}\>
 \<InputLabel id\="demo\-simple\-select\-label"\>User Type\</InputLabel\>
 \<Select
 labelId\="demo\-user\-type\-label"
 id\="demo\-user\-type"
 value\={userType}
 label\="User Type"
 onChange\={(event: SelectChangeEvent\<number\>) \=\> {
 setUserType(event.target.value as number);
 }}
 \>
 \<MenuItem value\={UserType.Regular}\>Regular User\</MenuItem\>
 \<MenuItem value\={UserType.Admin}\>Admin\</MenuItem\>
 \</Select\>
 \</FormControl\>
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGrid
 {...data}
 disableColumnSelector
 columnVisibilityModel\={columnVisibilityModel}
 showToolbar
 /\>
 \</div\>
 \</Stack\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Customize the list of columns in columns management

To show or hide specific columns in the column visibility panel, use the `slotProps.columnsManagement.getTogglableColumns` prop. It should return an array of column field names.



```
import {
  DataGridPremium,
  GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD,
} from '@mui/x-data-grid-premium';

// stop `id`, GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD, and `status` columns to be togglable
const hiddenFields = ['id', GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD, 'status'];

const getTogglableColumns = (columns: GridColDef[]) => {
  return columns
    .filter((column) => !hiddenFields.includes(column.field))
    .map((column) => column.field);
};

<DataGridPremium
  showToolbar
  slotProps={{
    columnsManagement: {
      getTogglableColumns,
    },
  }}
/>;

```
CopyCopied(or Ctrl \+ C)
​​StatusDeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledFilled (3\)Rejected (2\)Partially Filled (3\)Open (2\)Total Rows: 4JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import {
  DataGridPremium,
  GridColDef,
  useKeepGroupedColumnsHidden,
  useGridApiRef,
  GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD,
} from '@mui/x-data-grid-premium';
import { useDemoData } from '@mui/x-data-grid-generator';

const hiddenFields = ['id', GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD, 'status'];

const getTogglableColumns = (columns: GridColDef[]) => {
  return columns
    .filter((column) => !hiddenFields.includes(column.field))
    .map((column) => column.field);
};

export default function ColumnSelectorGridCustomizeColumns {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 10,
  });

  const apiRef = useGridApiRef;

  const initialState = useKeepGroupedColumnsHidden({
    apiRef,
    initialState: {
      ...data.initialState,
      rowGrouping: {
        model: ['status'],
      },
    },
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPremium
        apiRef={apiRef}
        {...data}
        initialState={initialState}
        showToolbar
        slotProps={{
          columnsManagement: {
            getTogglableColumns,
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import {
 DataGridPremium,
 GridColDef,
 useKeepGroupedColumnsHidden,
 useGridApiRef,
 GRID\_ROW\_GROUPING\_SINGLE\_GROUPING\_FIELD,
} from '@mui/x\-data\-grid\-premium';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

const hiddenFields \= \['id', GRID\_ROW\_GROUPING\_SINGLE\_GROUPING\_FIELD, 'status'];

const getTogglableColumns \= (columns: GridColDef\[]) \=\> {
 return columns
 .filter((column) \=\> !hiddenFields.includes(column.field))
 .map((column) \=\> column.field);
};

export default function ColumnSelectorGridCustomizeColumns {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 10,
 });

 const apiRef \= useGridApiRef;

 const initialState \= useKeepGroupedColumnsHidden({
 apiRef,
 initialState: {
 ...data.initialState,
 rowGrouping: {
 model: \['status'],
 },
 },
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPremium
 apiRef\={apiRef}
 {...data}
 initialState\={initialState}
 showToolbar
 slotProps\={{
 columnsManagement: {
 getTogglableColumns,
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Bolt.new** \- Join us May 30th for the world’s largest hackathon for non\-devs and vibe coders!ad by Carbon### Disable actions in footer

To disable `Show/Hide All` checkbox or `Reset` button in the footer of the columns management component, pass `disableShowHideToggle` or `disableResetButton` to `slotProps.columnsManagement`.



```
<DataGrid
  showToolbar
  slotProps={{
    columnsManagement: {
      disableShowHideToggle: true,
      disableResetButton: true,
    },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
### Customize action buttons behavior when search is active

By default, the `Show/Hide All` checkbox toggles the visibility of all columns, including the ones that are not visible in the current search results.


To only toggle the visibility of the columns that are present in the current search results, pass `toggleAllMode: 'filteredOnly'` to `slotProps.columnsManagement`.



```
<DataGrid
  slotProps={{
    columnsManagement: {
      toggleAllMode: 'filteredOnly',
    },
  }}
/>

```
CopyCopied(or Ctrl \+ C)
​​DeskCommodityTrader NameTrader EmailQuantityFilled QuantityIs FilledStatusD\-1823<NAME_EMAIL>92,5945\.75 %Partially FilledD\-2586RapeseedJose Williamsonhe@gida.cz24,41092\.95 %Partially FilledD\-2923<NAME_EMAIL>90,89254\.06 %RejectedD\-5562<NAME_EMAIL>30,82322\.72 %FilledD\-5397MilkTerry Marshallejicetjuv@solhopud.cd38,1047\.461 %Partially FilledD\-9457<NAME_EMAIL>87,34898\.83 %Partially FilledD\-460WheatBeulah Warnernuf@metnoni.za10,51066\.803 %Partially FilledD\-1364<NAME_EMAIL>83,9021\.98 %OpenTotal Rows: 10JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import { DataGridPremium } from '@mui/x-data-grid-premium';
import { useDemoData } from '@mui/x-data-grid-generator';

export default function ColumnSelectorGridToggleAllMode {
  const { data } = useDemoData({
    dataSet: 'Commodity',
    rowLength: 10,
    maxColumns: 10,
  });

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGridPremium
        {...data}
        showToolbar
        slotProps={{
          columnsManagement: {
            toggleAllMode: 'filteredOnly',
          },
        }}
      />
    </div>
  );
}  

```
import \* as React from 'react';
import { DataGridPremium } from '@mui/x\-data\-grid\-premium';
import { useDemoData } from '@mui/x\-data\-grid\-generator';

export default function ColumnSelectorGridToggleAllMode {
 const { data } \= useDemoData({
 dataSet: 'Commodity',
 rowLength: 10,
 maxColumns: 10,
 });

 return (
 \<div style\={{ height: 400, width: '100%' }}\>
 \<DataGridPremium
 {...data}
 showToolbar
 slotProps\={{
 columnsManagement: {
 toggleAllMode: 'filteredOnly',
 },
 }}
 /\>
 \</div\>
 );
}Press `Enter` to start editing**Squarespace** \- With Squarespace, you can book projects, send documents, and get paid—all in one place.ad by CarbonAPI
---

* DataGrid
* DataGridPro
* DataGridPremium


Edit this pageWas this page helpful?



---

Column dimensionsCustom columns

---

•

Blog•

Store
