Image List
==========

The Image List displays a collection of images in an organized grid.


All the connections, zero limits. Unlimited Okta and social connections on our Free Plan. Sign up →

ads via Carbon



Image lists represent a collection of items in a repeated pattern. They help improve the visual comprehension of the content they hold.


* Feedback
* Bundle size
* Source
* Material Design
* Figma
* Sketch

Standard image list
-------------------

Standard image lists are best for items of equal importance. They have a uniform container size, ratio, and spacing.


* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<ImageList sx={{ width: 500, height: 450 }} cols={3} rowHeight={164}>
  {itemData.map((item) => (
    <ImageListItem key={item.img}>
      <img
        srcSet={`${item.img}?w=164&h=164&fit=crop&auto=format&dpr=2 2x`}
        src={`${item.img}?w=164&h=164&fit=crop&auto=format`}
        alt={item.title}
        loading="lazy"
      />
    </ImageListItem>
  ))}
</ImageList>  

```
\<ImageList sx\={{ width: 500, height: 450 }} cols\={3} rowHeight\={164}\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=164\&h\=164\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=164\&h\=164\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \</ImageListItem\>
 ))}
\</ImageList\>Press `Enter` to start editingQuilted image list
------------------

Quilted image lists emphasize certain items over others in a collection. They create hierarchy using varied container sizes and ratios.


* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<ImageList
  sx={{ width: 500, height: 450 }}
  variant="quilted"
  cols={4}
  rowHeight={121}
>
  {itemData.map((item) => (
    <ImageListItem key={item.img} cols={item.cols || 1} rows={item.rows || 1}>
      <img
        {...srcset(item.img, 121, item.rows, item.cols)}
        alt={item.title}
        loading="lazy"
      />
    </ImageListItem>
  ))}
</ImageList>  

```
\<ImageList
 sx\={{ width: 500, height: 450 }}
 variant\="quilted"
 cols\={4}
 rowHeight\={121}
\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img} cols\={item.cols \|\| 1} rows\={item.rows \|\| 1}\>
 \<img
 {...srcset(item.img, 121, item.rows, item.cols)}
 alt\={item.title}
 loading\="lazy"
 /\>
 \</ImageListItem\>
 ))}
\</ImageList\>Press `Enter` to start editingWoven image list
----------------

Woven image lists use alternating container ratios to create a rhythmic layout. A woven image list is best for browsing peer content.


* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<ImageList sx={{ width: 500, height: 450 }} variant="woven" cols={3} gap={8}>
  {itemData.map((item) => (
    <ImageListItem key={item.img}>
      <img
        srcSet={`${item.img}?w=161&fit=crop&auto=format&dpr=2 2x`}
        src={`${item.img}?w=161&fit=crop&auto=format`}
        alt={item.title}
        loading="lazy"
      />
    </ImageListItem>
  ))}
</ImageList>  

```
\<ImageList sx\={{ width: 500, height: 450 }} variant\="woven" cols\={3} gap\={8}\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=161\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=161\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \</ImageListItem\>
 ))}
\</ImageList\>Press `Enter` to start editingMasonry image list
------------------

Masonry image lists use dynamically sized container heights that reflect the aspect ratio of each image. This image list is best used for browsing uncropped peer content.


* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
* 
JSTSExpand codeCopy(or Ctrl \+ C)
```
<ImageList variant="masonry" cols={3} gap={8}>
  {itemData.map((item) => (
    <ImageListItem key={item.img}>
      <img
        srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
        src={`${item.img}?w=248&fit=crop&auto=format`}
        alt={item.title}
        loading="lazy"
      />
    </ImageListItem>
  ))}
</ImageList>  

```
\<ImageList variant\="masonry" cols\={3} gap\={8}\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \</ImageListItem\>
 ))}
\</ImageList\>Press `Enter` to start editingImage list with title bars
--------------------------

This example demonstrates the use of the `ImageListItemBar` to add an overlay to each item.
The overlay can accommodate a `title`, `subtitle` and secondary action \- in this example an `IconButton`.


* December
* Breakfast@bkristastucchio
* Burger@rollelflex\_graphy726
* Camera@helloimnik
* Coffee@nolanissac
* Hats@hjrc33
* Honey@arwinneil
* Basketball@tjdragotta
* Fern@katie\_wasserman
* Mushrooms@silverdalex
* Tomato basil@shelleypauls
* Sea star@peterlaster
* Bike@southside\_customs
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';
import ListSubheader from '@mui/material/ListSubheader';
import IconButton from '@mui/material/IconButton';
import InfoIcon from '@mui/icons-material/Info';

export default function TitlebarImageList {
  return (
    <ImageList sx={{ width: 500, height: 450 }}>
      <ImageListItem key="Subheader" cols={2}>
        <ListSubheader component="div">December</ListSubheader>
      </ImageListItem>
      {itemData.map((item) => (
        <ImageListItem key={item.img}>
          <img
            srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
            src={`${item.img}?w=248&fit=crop&auto=format`}
            alt={item.title}
            loading="lazy"
          />
          <ImageListItemBar
            title={item.title}
            subtitle={item.author}
            actionIcon={
              <IconButton
                sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                aria-label={`info about ${item.title}`}
              >
                <InfoIcon />
              </IconButton>
            }
          />
        </ImageListItem>
      ))}
    </ImageList>
  );
}

const itemData = [
  {
    img: '
    title: 'Breakfast',
    author: '@bkristastucchio',
    rows: 2,
    cols: 2,
    featured: true,
  },
  {
    img: '
    title: 'Burger',
    author: '@rollelflex_graphy726',
  },
  {
    img: '
    title: 'Camera',
    author: '@helloimnik',
  },
  {
    img: '
    title: 'Coffee',
    author: '@nolanissac',
    cols: 2,
  },
  {
    img: '
    title: 'Hats',
    author: '@hjrc33',
    cols: 2,
  },
  {
    img: '
    title: 'Honey',
    author: '@arwinneil',
    rows: 2,
    cols: 2,
    featured: true,
  },
  {
    img: '
    title: 'Basketball',
    author: '@tjdragotta',
  },
  {
    img: '
    title: 'Fern',
    author: '@katie_wasserman',
  },
  {
    img: '
    title: 'Mushrooms',
    author: '@silverdalex',
    rows: 2,
    cols: 2,
  },
  {
    img: '
    title: 'Tomato basil',
    author: '@shelleypauls',
  },
  {
    img: '
    title: 'Sea star',
    author: '@peterlaster',
  },
  {
    img: '
    title: 'Bike',
    author: '@southside_customs',
    cols: 2,
  },
];  

```
import \* as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';
import ListSubheader from '@mui/material/ListSubheader';
import IconButton from '@mui/material/IconButton';
import InfoIcon from '@mui/icons\-material/Info';

export default function TitlebarImageList {
 return (
 \<ImageList sx\={{ width: 500, height: 450 }}\>
 \<ImageListItem key\="Subheader" cols\={2}\>
 \<ListSubheader component\="div"\>December\</ListSubheader\>
 \</ImageListItem\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \<ImageListItemBar
 title\={item.title}
 subtitle\={item.author}
 actionIcon\={
 \<IconButton
 sx\={{ color: 'rgba(255, 255, 255, 0\.54\)' }}
 aria\-label\={\`info about ${item.title}\`}
 \>
 \<InfoIcon /\>
 \</IconButton\>
 }
 /\>
 \</ImageListItem\>
 ))}
 \</ImageList\>
 );
}

const itemData \= \[
 {
 img: '
 title: 'Breakfast',
 author: '@bkristastucchio',
 rows: 2,
 cols: 2,
 featured: true,
 },
 {
 img: '
 title: 'Burger',
 author: '@rollelflex\_graphy726',
 },
 {
 img: '
 title: 'Camera',
 author: '@helloimnik',
 },
 {
 img: '
 title: 'Coffee',
 author: '@nolanissac',
 cols: 2,
 },
 {
 img: '
 title: 'Hats',
 author: '@hjrc33',
 cols: 2,
 },
 {
 img: '
 title: 'Honey',
 author: '@arwinneil',
 rows: 2,
 cols: 2,
 featured: true,
 },
 {
 img: '
 title: 'Basketball',
 author: '@tjdragotta',
 },
 {
 img: '
 title: 'Fern',
 author: '@katie\_wasserman',
 },
 {
 img: '
 title: 'Mushrooms',
 author: '@silverdalex',
 rows: 2,
 cols: 2,
 },
 {
 img: '
 title: 'Tomato basil',
 author: '@shelleypauls',
 },
 {
 img: '
 title: 'Sea star',
 author: '@peterlaster',
 },
 {
 img: '
 title: 'Bike',
 author: '@southside\_customs',
 cols: 2,
 },
];Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by Carbon### Title bar below image (standard)

The title bar can be placed below the image.


* Breakfastby: @bkristastucchio
* Burgerby: @rollelflex\_graphy726
* Cameraby: @helloimnik
* Coffeeby: @nolanissac
* Hatsby: @hjrc33
* Honeyby: @arwinneil
* Basketballby: @tjdragotta
* Fernby: @katie\_wasserman
* Mushroomsby: @silverdalex
* Tomato basilby: @shelleypauls
* Sea starby: @peterlaster
* Bikeby: @southside\_customs
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';

export default function TitlebarBelowImageList {
  return (
    <ImageList sx={{ width: 500, height: 450 }}>
      {itemData.map((item) => (
        <ImageListItem key={item.img}>
          <img
            srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
            src={`${item.img}?w=248&fit=crop&auto=format`}
            alt={item.title}
            loading="lazy"
          />
          <ImageListItemBar
            title={item.title}
            subtitle={<span>by: {item.author}</span>}
            position="below"
          />
        </ImageListItem>
      ))}
    </ImageList>
  );
}

const itemData = [
  {
    img: '
    title: 'Breakfast',
    author: '@bkristastucchio',
  },
  {
    img: '
    title: 'Burger',
    author: '@rollelflex_graphy726',
  },
  {
    img: '
    title: 'Camera',
    author: '@helloimnik',
  },
  {
    img: '
    title: 'Coffee',
    author: '@nolanissac',
  },
  {
    img: '
    title: 'Hats',
    author: '@hjrc33',
  },
  {
    img: '
    title: 'Honey',
    author: '@arwinneil',
  },
  {
    img: '
    title: 'Basketball',
    author: '@tjdragotta',
  },
  {
    img: '
    title: 'Fern',
    author: '@katie_wasserman',
  },
  {
    img: '
    title: 'Mushrooms',
    author: '@silverdalex',
  },
  {
    img: '
    title: 'Tomato basil',
    author: '@shelleypauls',
  },
  {
    img: '
    title: 'Sea star',
    author: '@peterlaster',
  },
  {
    img: '
    title: 'Bike',
    author: '@southside_customs',
  },
];  

```
import \* as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';

export default function TitlebarBelowImageList {
 return (
 \<ImageList sx\={{ width: 500, height: 450 }}\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \<ImageListItemBar
 title\={item.title}
 subtitle\={\<span\>by: {item.author}\</span\>}
 position\="below"
 /\>
 \</ImageListItem\>
 ))}
 \</ImageList\>
 );
}

const itemData \= \[
 {
 img: '
 title: 'Breakfast',
 author: '@bkristastucchio',
 },
 {
 img: '
 title: 'Burger',
 author: '@rollelflex\_graphy726',
 },
 {
 img: '
 title: 'Camera',
 author: '@helloimnik',
 },
 {
 img: '
 title: 'Coffee',
 author: '@nolanissac',
 },
 {
 img: '
 title: 'Hats',
 author: '@hjrc33',
 },
 {
 img: '
 title: 'Honey',
 author: '@arwinneil',
 },
 {
 img: '
 title: 'Basketball',
 author: '@tjdragotta',
 },
 {
 img: '
 title: 'Fern',
 author: '@katie\_wasserman',
 },
 {
 img: '
 title: 'Mushrooms',
 author: '@silverdalex',
 },
 {
 img: '
 title: 'Tomato basil',
 author: '@shelleypauls',
 },
 {
 img: '
 title: 'Sea star',
 author: '@peterlaster',
 },
 {
 img: '
 title: 'Bike',
 author: '@southside\_customs',
 },
];Press `Enter` to start editing**Pluralsight** \- Learn five ways to advance your tech career in 2025ad by Carbon### Title bar below image (masonry)

* swabdesign
* Pavel Nekoranec
* Charles Deluvio
* Christian Mackie
* Darren Richardson
* Taylor Simpson
* Ben Kolde
* Philipp Berndt
* Jen P.
* Douglas Sheppard
* Fi Bell
* Hutomo Abrianto
JSTSCollapse codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import Box from '@mui/material/Box';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';

export default function TitlebarBelowMasonryImageList {
  return (
    <Box sx={{ width: 500, height: 450, overflowY: 'scroll' }}>
      <ImageList variant="masonry" cols={3} gap={8}>
        {itemData.map((item) => (
          <ImageListItem key={item.img}>
            <img
              srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
              src={`${item.img}?w=248&fit=crop&auto=format`}
              alt={item.title}
              loading="lazy"
            />
            <ImageListItemBar position="below" title={item.author} />
          </ImageListItem>
        ))}
      </ImageList>
    </Box>
  );
}

const itemData = [
  {
    img: '
    title: 'Bed',
    author: 'swabdesign',
  },
  {
    img: '
    title: 'Books',
    author: 'Pavel Nekoranec',
  },
  {
    img: '
    title: 'Sink',
    author: 'Charles Deluvio',
  },
  {
    img: '
    title: 'Kitchen',
    author: 'Christian Mackie',
  },
  {
    img: '
    title: 'Blinds',
    author: 'Darren Richardson',
  },
  {
    img: '
    title: 'Chairs',
    author: 'Taylor Simpson',
  },
  {
    img: '
    title: 'Laptop',
    author: 'Ben Kolde',
  },
  {
    img: '
    title: 'Doors',
    author: 'Philipp Berndt',
  },
  {
    img: '
    title: 'Coffee',
    author: 'Jen P.',
  },
  {
    img: '
    title: 'Storage',
    author: 'Douglas Sheppard',
  },
  {
    img: '
    title: 'Candle',
    author: 'Fi Bell',
  },
  {
    img: '
    title: 'Coffee table',
    author: 'Hutomo Abrianto',
  },
];  

```
import \* as React from 'react';
import Box from '@mui/material/Box';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';

export default function TitlebarBelowMasonryImageList {
 return (
 \<Box sx\={{ width: 500, height: 450, overflowY: 'scroll' }}\>
 \<ImageList variant\="masonry" cols\={3} gap\={8}\>
 {itemData.map((item) \=\> (
 \<ImageListItem key\={item.img}\>
 \<img
 srcSet\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\&dpr\=2 2x\`}
 src\={\`${item.img}?w\=248\&fit\=crop\&auto\=format\`}
 alt\={item.title}
 loading\="lazy"
 /\>
 \<ImageListItemBar position\="below" title\={item.author} /\>
 \</ImageListItem\>
 ))}
 \</ImageList\>
 \</Box\>
 );
}

const itemData \= \[
 {
 img: '
 title: 'Bed',
 author: 'swabdesign',
 },
 {
 img: '
 title: 'Books',
 author: 'Pavel Nekoranec',
 },
 {
 img: '
 title: 'Sink',
 author: 'Charles Deluvio',
 },
 {
 img: '
 title: 'Kitchen',
 author: 'Christian Mackie',
 },
 {
 img: '
 title: 'Blinds',
 author: 'Darren Richardson',
 },
 {
 img: '
 title: 'Chairs',
 author: 'Taylor Simpson',
 },
 {
 img: '
 title: 'Laptop',
 author: 'Ben Kolde',
 },
 {
 img: '
 title: 'Doors',
 author: 'Philipp Berndt',
 },
 {
 img: '
 title: 'Coffee',
 author: 'Jen P.',
 },
 {
 img: '
 title: 'Storage',
 author: 'Douglas Sheppard',
 },
 {
 img: '
 title: 'Candle',
 author: 'Fi Bell',
 },
 {
 img: '
 title: 'Coffee table',
 author: 'Hutomo Abrianto',
 },
];Press `Enter` to start editing**Gitlab** \- Use GitLab end to end. From security to development to operations. It's all here.ad by CarbonCustom image list
-----------------

In this example the items have a customized titlebar, positioned at the top and with a custom gradient `titleBackground`.
The secondary action `IconButton` is positioned on the left. The `gap` prop is used to adjust the gap between items.


* Breakfast
* Burger
* Camera
* Coffee
* Hats
* Honey
* Basketball
* Fern
* Mushrooms
* Tomato basil
* Sea star
* Bike
JSTSHide codeCopy(or Ctrl \+ C)
```
import * as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';
import IconButton from '@mui/material/IconButton';
import StarBorderIcon from '@mui/icons-material/StarBorder';

function srcset(image: string, width: number, height: number, rows = 1, cols = 1) {
  return {
    src: `${image}?w=${width * cols}&h=${height * rows}&fit=crop&auto=format`,
    srcSet: `${image}?w=${width * cols}&h=${
      height * rows
    }&fit=crop&auto=format&dpr=2 2x`,
  };
}

export default function CustomImageList {
  return (
    <ImageList
      sx={{
        width: 500,
        height: 450,
        // Promote the list into its own layer in Chrome. This costs memory, but helps keeping high FPS.
        transform: 'translateZ(0)',
      }}
      rowHeight={200}
      gap={1}
    >
      {itemData.map((item) => {
        const cols = item.featured ? 2 : 1;
        const rows = item.featured ? 2 : 1;

        return (
          <ImageListItem key={item.img} cols={cols} rows={rows}>
            <img
              {...srcset(item.img, 250, 200, rows, cols)}
              alt={item.title}
              loading="lazy"
            />
            <ImageListItemBar
              sx={{
                background:
                  'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, ' +
                  'rgba(0,0,0,0.3) 70%, rgba(0,0,0,0) 100%)',
              }}
              title={item.title}
              position="top"
              actionIcon={
                <IconButton
                  sx={{ color: 'white' }}
                  aria-label={`star ${item.title}`}
                >
                  <StarBorderIcon />
                </IconButton>
              }
              actionPosition="left"
            />
          </ImageListItem>
        );
      })}
    </ImageList>
  );
}

const itemData = [
  {
    img: '
    title: 'Breakfast',
    author: '@bkristastucchio',
    featured: true,
  },
  {
    img: '
    title: 'Burger',
    author: '@rollelflex_graphy726',
  },
  {
    img: '
    title: 'Camera',
    author: '@helloimnik',
  },
  {
    img: '
    title: 'Coffee',
    author: '@nolanissac',
  },
  {
    img: '
    title: 'Hats',
    author: '@hjrc33',
  },
  {
    img: '
    title: 'Honey',
    author: '@arwinneil',
    featured: true,
  },
  {
    img: '
    title: 'Basketball',
    author: '@tjdragotta',
  },
  {
    img: '
    title: 'Fern',
    author: '@katie_wasserman',
  },
  {
    img: '
    title: 'Mushrooms',
    author: '@silverdalex',
  },
  {
    img: '
    title: 'Tomato basil',
    author: '@shelleypauls',
  },
  {
    img: '
    title: 'Sea star',
    author: '@peterlaster',
  },
  {
    img: '
    title: 'Bike',
    author: '@southside_customs',
  },
];  

```
import \* as React from 'react';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import ImageListItemBar from '@mui/material/ImageListItemBar';
import IconButton from '@mui/material/IconButton';
import StarBorderIcon from '@mui/icons\-material/StarBorder';

function srcset(image: string, width: number, height: number, rows \= 1, cols \= 1\) {
 return {
 src: \`${image}?w\=${width \* cols}\&h\=${height \* rows}\&fit\=crop\&auto\=format\`,
 srcSet: \`${image}?w\=${width \* cols}\&h\=${
 height \* rows
 }\&fit\=crop\&auto\=format\&dpr\=2 2x\`,
 };
}

export default function CustomImageList {
 return (
 \<ImageList
 sx\={{
 width: 500,
 height: 450,
 // Promote the list into its own layer in Chrome. This costs memory, but helps keeping high FPS.
 transform: 'translateZ(0\)',
 }}
 rowHeight\={200}
 gap\={1}
 \>
 {itemData.map((item) \=\> {
 const cols \= item.featured ? 2 : 1;
 const rows \= item.featured ? 2 : 1;

 return (
 \<ImageListItem key\={item.img} cols\={cols} rows\={rows}\>
 \<img
 {...srcset(item.img, 250, 200, rows, cols)}
 alt\={item.title}
 loading\="lazy"
 /\>
 \<ImageListItemBar
 sx\={{
 background:
 'linear\-gradient(to bottom, rgba(0,0,0,0\.7\) 0%, ' \+
 'rgba(0,0,0,0\.3\) 70%, rgba(0,0,0,0\) 100%)',
 }}
 title\={item.title}
 position\="top"
 actionIcon\={
 \<IconButton
 sx\={{ color: 'white' }}
 aria\-label\={\`star ${item.title}\`}
 \>
 \<StarBorderIcon /\>
 \</IconButton\>
 }
 actionPosition\="left"
 /\>
 \</ImageListItem\>
 );
 })}
 \</ImageList\>
 );
}

const itemData \= \[
 {
 img: '
 title: 'Breakfast',
 author: '@bkristastucchio',
 featured: true,
 },
 {
 img: '
 title: 'Burger',
 author: '@rollelflex\_graphy726',
 },
 {
 img: '
 title: 'Camera',
 author: '@helloimnik',
 },
 {
 img: '
 title: 'Coffee',
 author: '@nolanissac',
 },
 {
 img: '
 title: 'Hats',
 author: '@hjrc33',
 },
 {
 img: '
 title: 'Honey',
 author: '@arwinneil',
 featured: true,
 },
 {
 img: '
 title: 'Basketball',
 author: '@tjdragotta',
 },
 {
 img: '
 title: 'Fern',
 author: '@katie\_wasserman',
 },
 {
 img: '
 title: 'Mushrooms',
 author: '@silverdalex',
 },
 {
 img: '
 title: 'Tomato basil',
 author: '@shelleypauls',
 },
 {
 img: '
 title: 'Sea star',
 author: '@peterlaster',
 },
 {
 img: '
 title: 'Bike',
 author: '@southside\_customs',
 },
];Press `Enter` to start editing**Auth0** \- Our Auth0 plans just got a major boost! Enjoy Custom Domain, Passwordless, and up to 25,000 MAUs.ad by CarbonAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<ImageList />`
* `<ImageListItem />`
* `<ImageListItemBar />`



