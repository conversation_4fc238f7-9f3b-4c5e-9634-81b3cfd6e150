import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';
import { useDarkStore } from '@/stores/darkStore';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Box,
  useTheme
} from '@mui/material';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';

const Header: React.FC = () => {
  const user = useAuthStore(state => state.user);
  const logout = useAuthStore(state => state.logout);
  const { isDarkMode, toggleDarkMode } = useDarkStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const theme = useTheme();

  return (
    <AppBar
      position="static"
      color="default"
      elevation={1}
      sx={{
        height: 64,
        bgcolor: isDarkMode ? theme.palette.background.default : '#fff',
        borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.12)',
      }}
    >
      <Toolbar sx={{ height: '100%', justifyContent: 'space-between' }}>
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: 'bold',
            fontFamily: "'Microsoft YaHei', sans-serif",
            color: isDarkMode ? '#fff' : '#000'
          }}
        >
          AgentFlow
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            onClick={toggleDarkMode}
            aria-label="Toggle dark mode"
            size="small"
            sx={{
              color: 'inherit',
              '&:hover': {
                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            {isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
          </IconButton>

          {user && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 500,
                  color: isDarkMode ? '#fff' : 'inherit',
                  fontFamily: "'Microsoft YaHei', sans-serif",
                }}
              >
                {user.username}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={handleLogout}
                sx={{
                  color: isDarkMode ? '#fff' : 'inherit',
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  fontFamily: "'Microsoft YaHei', sans-serif",
                  fontSize: '14px'
                }}
              >
                Logout
              </Button>
            </Box>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
