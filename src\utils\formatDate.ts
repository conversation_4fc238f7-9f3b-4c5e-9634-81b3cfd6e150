import moment from 'moment';

/**
 * Format a date string to a human-readable format
 * 
 * @param dateString - ISO date string
 * @param format - Optional format string (default: 'YYYY-MM-DD HH:mm:ss')
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return moment(dateString).format(format);
};

/**
 * Get a relative time string (e.g., "2 hours ago", "3 days ago")
 * 
 * @param dateString - ISO date string
 * @returns Relative time string
 */
export const getRelativeTime = (dateString: string): string => {
  return moment(dateString).fromNow();
};

export default formatDate;
