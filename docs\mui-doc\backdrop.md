Backdrop
========

The Backdrop component narrows the user's focus to a particular element on the screen.Help us keep runningIf you don't mind tech\-related ads (no tracking or remarketing), and want to keep us running, please whitelist us in your blocker.Thank you! ❤️


The Backdrop signals a state change within the application and can be used for creating loaders, dialogs, and more.
In its simplest form, the Backdrop component will add a dimmed layer over your application.


* Feedback
* Bundle size
* Source
* Figma
* Sketch

Example
-------

The demo below shows a basic Backdrop with a Circular Progress component in the foreground to indicate a loading state.
After clicking **Show Backdrop**, you can click anywhere on the page to close it.


Show backdropJSTSExpand codeCopy(or Ctrl \+ C)
```
<Button onClick={handleOpen}>Show backdrop</Button>
<Backdrop
  sx={(theme) => ({ color: '#fff', zIndex: theme.zIndex.drawer + 1 })}
  open={open}
  onClick={handleClose}
>
  <CircularProgress color="inherit" />
</Backdrop>  

```
\<Button onClick\={handleOpen}\>Show backdrop\</Button\>
\<Backdrop
 sx\={(theme) \=\> ({ color: '\#fff', zIndex: theme.zIndex.drawer \+ 1 })}
 open\={open}
 onClick\={handleClose}
\>
 \<CircularProgress color\="inherit" /\>
\</Backdrop\>Press `Enter` to start editingAPI
---

See the documentation below for a complete reference to all of the props and classes available to the components mentioned here.


* `<Backdrop />`



